[{"document_type": "APS", "cards": [{"card_name": "Insured Details", "validation_fields": [{"field_name": "Full Name", "field_id": "insured_details.name", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "First Name", "field_id": "insured_details.name.given_name", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Last Name", "field_id": "insured_details.name.family_name", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Insured Type", "field_id": "insured_details.insured_type", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Date of Birth", "field_id": "insured_details.date_of_birth", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Birth State", "field_id": "insured_details.birth_state", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Birth Country", "field_id": "insured_details.birth_country", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Gender", "field_id": "insured_details.gender", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Address", "field_id": "insured_details.address", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Address: Street Line", "field_id": "insured_details.address.line", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Address: City", "field_id": "insured_details.address.city", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Address: State", "field_id": "insured_details.address.state", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Address: Zip Code", "field_id": "insured_details.address.zipcode", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Address: Country", "field_id": "insured_details.address.country", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Marital Status", "field_id": "insured_details.marital_status", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Occupation", "field_id": "insured_details.occupation", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Weight", "field_id": "insured_details.weight", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Height", "field_id": "insured_details.height", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Alcohol", "field_id": "insured_details.alcohol", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Smoker Status", "field_id": "insured_details.smoking", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Drugs", "field_id": "insured_details.drugs", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Avocation", "field_id": "insured_details.avocation", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Travel", "field_id": "insured_details.travel", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Behavior", "field_id": "insured_details.behavior", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Social Security Number", "field_id": "insured_details.ssn", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Owner Relationship", "field_id": "insured_details.owner_relationship", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Beneficiary Relationship", "field_id": "insured_details.beneficiary_relationship", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Citizen of", "field_id": "insured_details.citizenship", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Citizen of the US?", "field_id": "insured_details.us_citizen", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Resident of", "field_id": "insured_details.residentship", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Resident of the US?", "field_id": "insured_details.us_resident", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Driver's License #", "field_id": "insured_details.license_number", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Driver's License State", "field_id": "insured_details.license_state", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Income / Salary", "field_id": "insured_details.income", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Application Sign Date", "field_id": "insured_details.sign_date", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Application Sign State", "field_id": "insured_details.sign_state", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Client Company Assessment", "field_id": "insured_details.client_company_assessment", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Net Worth", "field_id": "insured_details.net_worth", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}]}, {"card_name": "Technical Details", "validation_fields": [{"field_name": "Cedent Product Type", "field_id": "technical_details.product_type", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Lump Sum / Face Amount", "field_id": "technical_details.sum_insured", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Benefit Term Type", "field_id": "technical_details.duration_type", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Benefit Term", "field_id": "technical_details.duration", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Benefit Start Date", "field_id": "technical_details.policy_start_date", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "<PERSON><PERSON><PERSON><PERSON>", "field_id": "technical_details.currency", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Premium Payment Period", "field_id": "technical_details.payment_period", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Annuity", "field_id": "technical_details.annuity", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Annuity Duration Type", "field_id": "technical_details.annuity_duration_type", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Annuity Duration", "field_id": "technical_details.annuity_duration", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Annuity End Date", "field_id": "technical_details.annuity_end_date", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Cedent Product Name", "field_id": "technical_details.plan_type", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Cedent Location", "field_id": "technical_details.cedent_location", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Cedent Person ID", "field_id": "technical_details.cedent_person_id", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Cedent Notification Date", "field_id": "technical_details.cedent_notification_date", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "GenRe Notification Date", "field_id": "technical_details.genre_notification_date", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "<PERSON><PERSON><PERSON> Amou<PERSON>", "field_id": "technical_details.premium_amount", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Reinsurance Amount", "field_id": "technical_details.reinsurance_amount", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Ultimate Risk Amount", "field_id": "technical_details.risk_amount", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Indexation", "field_id": "technical_details.indexation", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Waiting/Deferred Period", "field_id": "technical_details.deferment_period", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}]}, {"card_name": "<PERSON><PERSON><PERSON>", "validation_fields": [{"field_name": "Policy Number", "field_id": "claim_details.policy_number", "match_type": "non-empty", "high": null, "low": null, "fieldConfidence": 100.0, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Claim Number", "field_id": "claim_details.claim_number", "match_type": "non-empty", "high": null, "low": null, "fieldConfidence": 100.0, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Policy Effective Date", "field_id": "claim_details.policy_effective_date", "match_type": "non-empty", "high": null, "low": null, "fieldConfidence": 100.0, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Claimed Date of Loss", "field_id": "claim_details.date_of_loss", "match_type": "non-empty", "high": null, "low": null, "fieldConfidence": 100.0, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Primary Diagnosis", "field_id": "claim_details.primary_diagnosis", "match_type": "non-empty", "high": null, "low": null, "fieldConfidence": 100.0, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Autopsy Performed", "field_id": "claim_details.autopsy_performed", "match_type": "non-empty", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Policy Expiration Date", "field_id": "claim_details.policy_exp_date", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Original sum at risk", "field_id": "claim_details.sum_at_risk", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}]}, {"card_name": "Vital Signs", "validation_fields": [{"field_name": "Date", "field_id": "vital_signs.date", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Blood Pressure", "field_id": "vital_signs.blood_pressure", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Weight", "field_id": "vital_signs.weight", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Height", "field_id": "vital_signs.height", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "BMI", "field_id": "vital_signs.bmi", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Pulse", "field_id": "vital_signs.pulse", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Oxygen", "field_id": "vital_signs.oxygen", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Respiration", "field_id": "vital_signs.respiration", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Temperature", "field_id": "vital_signs.temperature", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}]}, {"card_name": "List of Events", "validation_fields": []}, {"card_name": "Diagnoses", "validation_fields": []}, {"card_name": "Reason of Visit", "validation_fields": []}, {"card_name": "Medication History", "validation_fields": []}, {"card_name": "Medication Plan", "validation_fields": []}, {"card_name": "Encounter Details", "validation_fields": []}, {"card_name": "Social History", "validation_fields": []}, {"card_name": "Family History", "validation_fields": []}, {"card_name": "Physical Exams", "validation_fields": []}, {"card_name": "Imaging Study", "validation_fields": []}, {"card_name": "Medical Equipments", "validation_fields": []}, {"card_name": "Abnormal Observations", "validation_fields": [{"field_name": "Specimen", "field_id": "abnormal_observations.specimen", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Date", "field_id": "abnormal_observations.date", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Laboratory", "field_id": "abnormal_observations.laboratory", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "LOINC", "field_id": "abnormal_observations.loinc", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Fasting Status", "field_id": "abnormal_observations.fasting", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}]}, {"card_name": "Medical Equipment", "validation_fields": []}, {"card_name": "Rx <PERSON>ails", "validation_fields": []}, {"card_name": "<PERSON><PERSON><PERSON>", "validation_fields": [{"field_name": "Type Of Service", "field_id": "claims.type_of_service", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Date Of Service", "field_id": "claims.date_of_service", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "COB Amount", "field_id": "claims.cob_amount", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Quantity", "field_id": "claims.quantity", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Patient Name", "field_id": "claims.patient_name", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Total Amount", "field_id": "claims.total_amount", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Doctor Name", "field_id": "claims.doctor_name", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}]}, {"card_name": "Dental Details", "validation_fields": []}, {"card_name": "Provider Details", "validation_fields": []}, {"card_name": "Application Details", "validation_fields": [{"field_name": "Date Received", "field_id": "application_details.date_received", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": " External Application ID", "field_id": "application_details.application_number", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "<PERSON><PERSON>", "field_id": "application_details.cedent_name", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Client Company Policy #", "field_id": "application_details.application_id", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Client Company Underwriter", "field_id": "application_details.company_underwriter", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}]}, {"card_name": "Allergens", "validation_fields": [{"field_name": "Allergen", "field_id": "allergens.allergen", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Type", "field_id": "allergens.type", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Date", "field_id": "allergens.date", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "SNOMED", "field_id": "allergens.snomed", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}]}, {"card_name": "Death claims", "validation_fields": [{"field_name": "Beneficiary name(given)", "field_id": "death_claims.beneficiary_name(given)", "match_type": "non-empty", "high": null, "low": null, "fieldConfidence": null, "message": "\n", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Beneficiary name(family)", "field_id": "death_claims.beneficiary_name(family)", "match_type": "non-empty", "high": null, "low": null, "fieldConfidence": null, "message": "\n", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Beneficiary date of birth", "field_id": "death_claims.beneficiary_date_of_birth", "match_type": "non-empty", "high": null, "low": null, "fieldConfidence": null, "message": "\n", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Beneficiary state of residence", "field_id": "death_claims.beneficiary_state_of_residence", "match_type": "non-empty", "high": null, "low": null, "fieldConfidence": null, "message": "\n", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Beneficiary country of residence", "field_id": "death_claims.beneficiary_country_of_residence", "match_type": "date", "high": null, "low": null, "fieldConfidence": null, "message": "\n", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}]}, {"card_name": "Allergies", "validation_fields": [{"field_name": "Unit", "field_id": "allergies.unit", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Test Value", "field_id": "allergies.test_value", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}]}]}, {"document_type": "Insurance Schedule", "cards": [{"card_name": "Insured Details", "validation_fields": []}, {"card_name": "Technical Details", "validation_fields": []}, {"card_name": "<PERSON><PERSON><PERSON>", "validation_fields": [{"field_name": "Policy Number", "field_id": "claim_details.policy_number", "match_type": "non-empty", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Claim Number", "field_id": "claim_details.claim_number", "match_type": "non-empty", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Primary Diagnosis", "field_id": "claim_details.primary_diagnosis", "match_type": "non-empty", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Autopsy Performed", "field_id": "claim_details.autopsy_performed", "match_type": "non-empty", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Policy Effective Date", "field_id": "claim_details.policy_effective_date", "match_type": "non-empty", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Claimed Date of Loss", "field_id": "claim_details.date_of_loss", "match_type": "non-empty", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}]}, {"card_name": "Vital Signs", "validation_fields": [{"field_name": "Date", "field_id": "vital_signs.date", "match_type": "date", "high": null, "low": null, "fieldConfidence": null, "message": null, "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Blood Pressure", "field_id": "vital_signs.blood_pressure", "match_type": "non-empty", "high": null, "low": null, "fieldConfidence": null, "message": null, "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "BMI", "field_id": "vital_signs.bmi", "match_type": "range", "high": 55, "low": 12, "fieldConfidence": null, "message": null, "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Pulse", "field_id": "vital_signs.pulse", "match_type": "range", "high": 100, "low": 50, "fieldConfidence": null, "message": null, "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Oxygen", "field_id": "vital_signs.oxygen", "match_type": "range", "high": 100, "low": 80, "fieldConfidence": null, "message": null, "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Respiration", "field_id": "vital_signs.respiration", "match_type": "range", "high": 25, "low": 8, "fieldConfidence": null, "message": null, "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}]}, {"card_name": "List of Events", "validation_fields": [{"field_name": "Date", "field_id": null, "match_type": "date", "high": null, "low": null, "fieldConfidence": null, "message": null, "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}]}, {"card_name": "Diagnoses", "validation_fields": [{"field_name": "Visit Date", "field_id": "diagnoses.date", "match_type": "date", "high": null, "low": null, "fieldConfidence": null, "message": null, "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}]}, {"card_name": "Reason of Visit", "validation_fields": [{"field_name": "Date", "field_id": "reason_of_visit.date", "match_type": "date", "high": null, "low": null, "fieldConfidence": null, "message": null, "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}]}, {"card_name": "Medication History", "validation_fields": []}, {"card_name": "Medication Plan", "validation_fields": [{"field_name": "Start", "field_id": "medication_plan.start", "match_type": "date", "high": null, "low": null, "fieldConfidence": null, "message": null, "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "End", "field_id": "medication_plan.end", "match_type": "date", "high": null, "low": null, "fieldConfidence": null, "message": null, "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}]}, {"card_name": "Encounter Details", "validation_fields": []}, {"card_name": "Social History", "validation_fields": []}, {"card_name": "Abnormal Observations", "validation_fields": [{"field_name": "Specimen", "field_id": "abnormal_observations.specimen", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Date", "field_id": "abnormal_observations.date", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Laboratory", "field_id": "abnormal_observations.laboratory", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "LOINC", "field_id": "abnormal_observations.loinc", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Fasting Status", "field_id": "abnormal_observations.fasting", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Name", "field_id": "abnormal_observations.name", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Value", "field_id": "abnormal_observations.value", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Low", "field_id": "abnormal_observations.low", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}]}]}, {"document_type": "Test Type 1", "cards": [{"card_name": "Insured Details", "validation_fields": []}, {"card_name": "Technical Details", "validation_fields": []}]}, {"document_type": "Test Type ", "cards": [{"card_name": "Insured Details", "validation_fields": []}]}, {"document_type": "New Business", "cards": [{"card_name": "Insured Details", "validation_fields": []}, {"card_name": "Technical Details", "validation_fields": []}, {"card_name": "Family History", "validation_fields": []}, {"card_name": "Abnormal Observations", "validation_fields": []}, {"card_name": "List Of Events", "validation_fields": []}, {"card_name": "Physical Exams", "validation_fields": []}, {"card_name": "Social History", "validation_fields": []}, {"card_name": "Diagnoses", "validation_fields": [{"field_name": "Diagnosis", "field_id": "diagnoses.diagnosis", "match_type": "range", "high": 100, "low": 0, "fieldConfidence": 78.0, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}]}, {"card_name": "Imaging Study", "validation_fields": [{"field_name": "Exam", "field_id": "imaging_study.exam", "match_type": "range", "high": 100, "low": 0, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}]}, {"card_name": "<PERSON><PERSON><PERSON>", "validation_fields": []}]}, {"document_type": "Presentation", "cards": [{"card_name": "Insured Details", "validation_fields": []}, {"card_name": "Vital Signs", "validation_fields": [{"field_name": "Oxygen", "field_id": "vital_signs.oxygen", "match_type": "range", "high": 90, "low": 80, "fieldConfidence": null, "message": "Oxygen Level needs to be between 80 to 90", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Date", "field_id": "vital_signs.date", "match_type": "non-empty", "high": null, "low": null, "fieldConfidence": null, "message": "Value needs to be in date format", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "BMI", "field_id": "vital_signs.bmi", "match_type": "range", "high": 60, "low": 50, "fieldConfidence": null, "message": "BMI value needs to be in between 50 and 60", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Blood Pressure", "field_id": "vital_signs.blood_pressure", "match_type": "range", "high": 11, "low": 10, "fieldConfidence": null, "message": "BMI values needs to be submit between 10 and 11", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Respiration", "field_id": "vital_signs.respiration", "match_type": "range", "high": 15, "low": 10, "fieldConfidence": null, "message": "Respiration value to be between 10 to 15", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}]}]}, {"document_type": "This is test document type long text document type not small type", "cards": [{"card_name": "Insured Details", "validation_fields": []}, {"card_name": "Vital Signs", "validation_fields": [{"field_name": "Oxygen", "field_id": "vital_signs.oxygen", "match_type": "range", "high": 100, "low": 80, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Date", "field_id": "vital_signs.date", "match_type": "date", "high": null, "low": null, "fieldConfidence": 100.0, "message": "Percentage score should be 100%", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}]}]}, {"document_type": "QA-3", "cards": [{"card_name": "Insured Details", "validation_fields": []}]}, {"document_type": "QA-2", "cards": [{"card_name": "Insured Details", "validation_fields": []}]}, {"document_type": "<PERSON>f", "cards": [{"card_name": "Insured Details", "validation_fields": [{"field_name": "Citizen of", "field_id": "insured_details.citizenship", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Resident of", "field_id": "insured_details.residentship", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Driver's License #", "field_id": "insured_details.license_number", "match_type": "non-empty", "high": null, "low": null, "fieldConfidence": 100.0, "message": "License # is required", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Driver's License State", "field_id": "insured_details.license_state", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "First Name", "field_id": "insured_details.name.given_name", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Last Name", "field_id": "insured_details.name.family_name", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Avocation", "field_id": "insured_details.avocation", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Behavior", "field_id": "insured_details.behavior", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Citizen of the US?", "field_id": "insured_details.us_citizen", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Application Sign Date", "field_id": "insured_details.sign_date", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Full Name", "field_id": "insured_details.name", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Birth Country", "field_id": "insured_details.birth_country", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Gender", "field_id": "insured_details.gender", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Address", "field_id": "insured_details.address", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Address: Street Line", "field_id": "insured_details.address.line", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Address: City", "field_id": "insured_details.address.city", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Address: State", "field_id": "insured_details.address.state", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Address: Zip Code", "field_id": "insured_details.address.zipcode", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Address: Country", "field_id": "insured_details.address.country", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Marital Status", "field_id": "insured_details.marital_status", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Occupation", "field_id": "insured_details.occupation", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Weight", "field_id": "insured_details.weight", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Height", "field_id": "insured_details.height", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Alcohol", "field_id": "insured_details.alcohol", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Drugs", "field_id": "insured_details.drugs", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Travel", "field_id": "insured_details.travel", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Social Security Number", "field_id": "insured_details.ssn", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Owner Relationship", "field_id": "insured_details.owner_relationship", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Beneficiary Relationship", "field_id": "insured_details.beneficiary_relationship", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Insured Type", "field_id": "insured_details.insured_type", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Date of Birth", "field_id": "insured_details.date_of_birth", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Birth State", "field_id": "insured_details.birth_state", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Smoker Status", "field_id": "insured_details.smoking", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Resident of the US?", "field_id": "insured_details.us_resident", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Income / Salary", "field_id": "insured_details.income", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Application Sign State", "field_id": "insured_details.sign_state", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Client Company Assessment", "field_id": "insured_details.client_company_assessment", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}]}, {"card_name": "Technical Details", "validation_fields": []}, {"card_name": "Vital Signs", "validation_fields": [{"field_name": "Date", "field_id": "vital_signs.date", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Blood Pressure", "field_id": "vital_signs.blood_pressure", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "BMI", "field_id": "vital_signs.bmi", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Pulse", "field_id": "vital_signs.pulse", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Oxygen", "field_id": "vital_signs.oxygen", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Respiration", "field_id": "vital_signs.respiration", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}]}, {"card_name": "Physical Exams", "validation_fields": []}, {"card_name": "Medication History", "validation_fields": []}, {"card_name": "Sample Horizontal", "validation_fields": []}, {"card_name": "Abnormal Observations", "validation_fields": [{"field_name": "Specimen", "field_id": "abnormal_observations.specimen", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Date", "field_id": "abnormal_observations.date", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Laboratory", "field_id": "abnormal_observations.laboratory", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "LOINC", "field_id": "abnormal_observations.loinc", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Fasting Status", "field_id": "abnormal_observations.fasting", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Name", "field_id": "abnormal_observations.name", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Value", "field_id": "abnormal_observations.value", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Low", "field_id": "abnormal_observations.low", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Panel", "field_id": "abnormal_observations.panel", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Rating", "field_id": "abnormal_observations.rating", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "High", "field_id": "abnormal_observations.high", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Descriptive Reference", "field_id": "abnormal_observations.descriptive_reference", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Unit", "field_id": "abnormal_observations.units", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}]}, {"card_name": "Application Details", "validation_fields": [{"field_name": "Date Received", "field_id": "application_details.date_received", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": " External Application ID", "field_id": "application_details.application_number", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "<PERSON><PERSON>", "field_id": "application_details.cedent_name", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Client Company Policy #", "field_id": "application_details.application_id", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Client Company Underwriter", "field_id": "application_details.company_underwriter", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}]}, {"card_name": "Imaging Study", "validation_fields": [{"field_name": "Date", "field_id": "imaging_study.date", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Exam", "field_id": "imaging_study.exam", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Reason", "field_id": "imaging_study.reason", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Finding", "field_id": "imaging_study.finding", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Impression", "field_id": "imaging_study.impression", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Conclusion", "field_id": "imaging_study.conclusion", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}]}, {"card_name": "JoeTest", "validation_fields": [{"field_name": "First Name", "field_id": "joetest.first_name", "match_type": "non-empty", "high": null, "low": null, "fieldConfidence": 90.0, "message": "Hey, something's wrong. First name is required!", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Last Name", "field_id": "joetest.last_name", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Address", "field_id": "joetest.address", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}]}, {"card_name": "Allergies", "validation_fields": []}, {"card_name": "Allergens", "validation_fields": [{"field_name": "Allergen", "field_id": "allergens.allergen", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Type", "field_id": "allergens.type", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Date", "field_id": "allergens.date", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "SNOMED", "field_id": "allergens.snomed", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}]}]}, {"document_type": "QA-K", "cards": [{"card_name": "Insured Details", "validation_fields": []}, {"card_name": "Technical Details", "validation_fields": []}, {"card_name": "Family History", "validation_fields": []}, {"card_name": "List Of Events", "validation_fields": []}, {"card_name": "<PERSON><PERSON><PERSON>", "validation_fields": [{"field_name": "Policy Effective Date", "field_id": "claim_details.policy_effective_date", "match_type": "date", "high": null, "low": null, "fieldConfidence": 85.0, "message": "Policy Effective Date confidence is very low", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Policy Number", "field_id": "claim_details.policy_number", "match_type": "non-empty", "high": null, "low": null, "fieldConfidence": 85.0, "message": "Policy Number field confidence is very low ", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}]}, {"card_name": "Physical Exams", "validation_fields": []}, {"card_name": "Vital Signs", "validation_fields": [{"field_name": "Date", "field_id": "vital_signs.date", "match_type": "date", "high": null, "low": null, "fieldConfidence": 85.0, "message": "Date field confidence is very low", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}]}, {"card_name": "Diagnoses", "validation_fields": [{"field_name": "Visit Date", "field_id": "diagnoses.date", "match_type": "date", "high": null, "low": null, "fieldConfidence": 85.0, "message": "Visit Date field confidence is very low", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}]}, {"card_name": "<PERSON><PERSON><PERSON>", "validation_fields": [{"field_name": "Patient Name", "field_id": "claims.patient_name", "match_type": "non-empty", "high": null, "low": null, "fieldConfidence": 85.0, "message": "Patient Name field confidence is very low", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Type Of Service", "field_id": "claims.type_of_service", "match_type": "non-empty", "high": null, "low": null, "fieldConfidence": 85.0, "message": "Type Of Service field confidence is very low", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Doctor Name", "field_id": "claims.doctor_name", "match_type": "non-empty", "high": null, "low": null, "fieldConfidence": 85.0, "message": "doctor name field confidence is very low ", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}]}]}, {"document_type": "Dummy Underwriting", "cards": [{"card_name": "Insured Details", "validation_fields": [{"field_name": "Citizen of", "field_id": "insured_details.citizenship", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Resident of", "field_id": "insured_details.residentship", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Driver's License #", "field_id": "insured_details.license_number", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Driver's License State", "field_id": "insured_details.license_state", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Citizen of the US?", "field_id": "insured_details.us_citizen", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Application Sign Date", "field_id": "insured_details.sign_date", "match_type": "non-empty", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "First Name", "field_id": "insured_details.name.given_name", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Last Name", "field_id": "insured_details.name.family_name", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Birth Country", "field_id": "insured_details.birth_country", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Address: Zip Code", "field_id": "insured_details.address.zipcode", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Marital Status", "field_id": "insured_details.marital_status", "match_type": "non-empty", "high": null, "low": null, "fieldConfidence": 90.0, "message": "required field", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Occupation", "field_id": "insured_details.occupation", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Weight", "field_id": "insured_details.weight", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Height", "field_id": "insured_details.height", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Alcohol", "field_id": "insured_details.alcohol", "match_type": "non-empty", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Drugs", "field_id": "insured_details.drugs", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Avocation", "field_id": "insured_details.avocation", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Travel", "field_id": "insured_details.travel", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Behavior", "field_id": "insured_details.behavior", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Social Security Number", "field_id": "insured_details.ssn", "match_type": "non-empty", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Owner Relationship", "field_id": "insured_details.owner_relationship", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Beneficiary Relationship", "field_id": "insured_details.beneficiary_relationship", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Gender", "field_id": "insured_details.gender", "match_type": "non-empty", "high": null, "low": null, "fieldConfidence": 100.0, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Full Name", "field_id": "insured_details.name", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Address", "field_id": "insured_details.address", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Address: Street Line", "field_id": "insured_details.address.line", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Address: City", "field_id": "insured_details.address.city", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Address: State", "field_id": "insured_details.address.state", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Address: Country", "field_id": "insured_details.address.country", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Insured Type", "field_id": "insured_details.insured_type", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Date of Birth", "field_id": "insured_details.date_of_birth", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Birth State", "field_id": "insured_details.birth_state", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Application Sign State", "field_id": "insured_details.sign_state", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Client Company Assessment", "field_id": "insured_details.client_company_assessment", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}]}, {"card_name": "Technical Details", "validation_fields": [{"field_name": "Annuity", "field_id": "technical_details.annuity", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "<PERSON><PERSON><PERSON><PERSON>", "field_id": "technical_details.currency", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Annuity Duration Type", "field_id": "technical_details.annuity_duration_type", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Annuity Duration", "field_id": "technical_details.annuity_duration", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Annuity End Date", "field_id": "technical_details.annuity_end_date", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Cedent Location", "field_id": "technical_details.cedent_location", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Cedent Person ID", "field_id": "technical_details.cedent_person_id", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Cedent Notification Date", "field_id": "technical_details.cedent_notification_date", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "GenRe Notification Date", "field_id": "technical_details.genre_notification_date", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}]}, {"card_name": "Diagnoses", "validation_fields": []}, {"card_name": "Encounter Details", "validation_fields": []}, {"card_name": "Abnormal Observations", "validation_fields": [{"field_name": "Specimen", "field_id": "abnormal_observations.specimen", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Date", "field_id": "abnormal_observations.date", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Laboratory", "field_id": "abnormal_observations.laboratory", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "LOINC", "field_id": "abnormal_observations.loinc", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Fasting Status", "field_id": "abnormal_observations.fasting", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Name", "field_id": "abnormal_observations.name", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Value", "field_id": "abnormal_observations.value", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Low", "field_id": "abnormal_observations.low", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Panel", "field_id": "abnormal_observations.panel", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Rating", "field_id": "abnormal_observations.rating", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "High", "field_id": "abnormal_observations.high", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Descriptive Reference", "field_id": "abnormal_observations.descriptive_reference", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Unit", "field_id": "abnormal_observations.units", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}]}, {"card_name": "Application Details", "validation_fields": [{"field_name": "Date Received", "field_id": "application_details.date_received", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": " External Application ID", "field_id": "application_details.application_number", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "<PERSON><PERSON>", "field_id": "application_details.cedent_name", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Client Company Policy #", "field_id": "application_details.application_id", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Client Company Underwriter", "field_id": "application_details.company_underwriter", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}]}, {"card_name": "Vital Signs", "validation_fields": [{"field_name": "Date", "field_id": "vital_signs.date", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Blood Pressure", "field_id": "vital_signs.blood_pressure", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "BMI", "field_id": "vital_signs.bmi", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Pulse", "field_id": "vital_signs.pulse", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Oxygen", "field_id": "vital_signs.oxygen", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Respiration", "field_id": "vital_signs.respiration", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Weight", "field_id": "vital_signs.weight", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Height", "field_id": "vital_signs.height", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Temperature", "field_id": "vital_signs.temperature", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}]}, {"card_name": "Encounter Dx Details", "validation_fields": [{"field_name": "Diagnosis", "field_id": "encounter_dx_details.diagnosis", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Type", "field_id": "encounter_dx_details.type", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "ICD-10", "field_id": "encounter_dx_details.icd_10", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Visit Date", "field_id": "encounter_dx_details.date", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Reason of Visit", "field_id": "encounter_dx_details.reason_of_visit", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Diagnosis Date", "field_id": "encounter_dx_details.diag_date", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Performer Name", "field_id": "encounter_dx_details.performer_name", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Performer Address", "field_id": "encounter_dx_details.performer_address", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}]}, {"card_name": "Imaging Study", "validation_fields": [{"field_name": "Date", "field_id": "imaging_study.date", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Exam", "field_id": "imaging_study.exam", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Reason", "field_id": "imaging_study.reason", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Finding", "field_id": "imaging_study.finding", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Impression", "field_id": "imaging_study.impression", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Conclusion", "field_id": "imaging_study.conclusion", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}]}, {"card_name": "Social History", "validation_fields": []}]}, {"document_type": "QA-ARSALAN", "cards": [{"card_name": "Insured Details", "validation_fields": []}, {"card_name": "Technical Details", "validation_fields": []}, {"card_name": "List Of Events", "validation_fields": []}, {"card_name": "Reason Of Visit", "validation_fields": [{"field_name": "Date", "field_id": "reason_of_visit.date", "match_type": "non-empty", "high": null, "low": null, "fieldConfidence": 85.0, "message": "Field Confidence is low", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Reason", "field_id": "reason_of_visit.reason", "match_type": "non-empty", "high": null, "low": null, "fieldConfidence": 85.0, "message": "Field Confidence is low", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Plan", "field_id": "reason_of_visit.plan", "match_type": "non-empty", "high": null, "low": null, "fieldConfidence": 85.0, "message": "Field Confidence is low", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}]}, {"card_name": "Encounter Details", "validation_fields": [{"field_name": "Date", "field_id": "encounter_details.date", "match_type": "non-empty", "high": null, "low": null, "fieldConfidence": 85.0, "message": "Field Confidence is low", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Type", "field_id": "encounter_details.type", "match_type": "non-empty", "high": null, "low": null, "fieldConfidence": 85.0, "message": "Field Confidence is low", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Department", "field_id": "encounter_details.department", "match_type": "non-empty", "high": null, "low": null, "fieldConfidence": 85.0, "message": "Field Confidence is low", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}]}, {"card_name": "Diagnoses", "validation_fields": [{"field_name": "Diagnosis", "field_id": "diagnoses.diagnosis", "match_type": "non-empty", "high": null, "low": null, "fieldConfidence": 85.0, "message": "Field Confidence is low", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "ICD-10", "field_id": "diagnoses.icd_10", "match_type": "non-empty", "high": null, "low": null, "fieldConfidence": 85.0, "message": "Field Confidence is low", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Visit Date", "field_id": "diagnoses.date", "match_type": "non-empty", "high": null, "low": null, "fieldConfidence": 85.0, "message": "Field Confidence is low", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}]}]}, {"document_type": "Du<PERSON>", "cards": [{"card_name": "Insured Details", "validation_fields": []}, {"card_name": "Technical Details", "validation_fields": []}, {"card_name": "List Of Events", "validation_fields": []}, {"card_name": "Diagnoses", "validation_fields": []}, {"card_name": "Medical Equipment", "validation_fields": []}, {"card_name": "Abnormal Observations", "validation_fields": [{"field_name": "Specimen", "field_id": "abnormal_observations.specimen", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Date", "field_id": "abnormal_observations.date", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Laboratory", "field_id": "abnormal_observations.laboratory", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "LOINC", "field_id": "abnormal_observations.loinc", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Fasting Status", "field_id": "abnormal_observations.fasting", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}]}, {"card_name": "Application Details", "validation_fields": []}]}, {"document_type": "Underwriting", "cards": [{"card_name": "Insured Details", "validation_fields": [{"field_name": "Citizen of", "field_id": "insured_details.citizenship", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Resident of", "field_id": "insured_details.residentship", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Driver's License #", "field_id": "insured_details.license_number", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Driver's License State", "field_id": "insured_details.license_state", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Full Name", "field_id": "insured_details.name", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "First Name", "field_id": "insured_details.name.given_name", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Last Name", "field_id": "insured_details.name.family_name", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Insured Type", "field_id": "insured_details.insured_type", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Date of Birth", "field_id": "insured_details.date_of_birth", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Birth State", "field_id": "insured_details.birth_state", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Birth Country", "field_id": "insured_details.birth_country", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Gender", "field_id": "insured_details.gender", "match_type": "non-empty", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Address", "field_id": "insured_details.address", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Address: Street Line", "field_id": "insured_details.address.line", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Address: City", "field_id": "insured_details.address.city", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Address: State", "field_id": "insured_details.address.state", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Address: Zip Code", "field_id": "insured_details.address.zipcode", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Address: Country", "field_id": "insured_details.address.country", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Marital Status", "field_id": "insured_details.marital_status", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Occupation", "field_id": "insured_details.occupation", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Weight", "field_id": "insured_details.weight", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Height", "field_id": "insured_details.height", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Alcohol", "field_id": "insured_details.alcohol", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Smoker Status", "field_id": "insured_details.smoking", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Drugs", "field_id": "insured_details.drugs", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Avocation", "field_id": "insured_details.avocation", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Travel", "field_id": "insured_details.travel", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Behavior", "field_id": "insured_details.behavior", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Social Security Number", "field_id": "insured_details.ssn", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Owner Relationship", "field_id": "insured_details.owner_relationship", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Beneficiary Relationship", "field_id": "insured_details.beneficiary_relationship", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Citizen of the US?", "field_id": "insured_details.us_citizen", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Resident of the US?", "field_id": "insured_details.us_resident", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Income / Salary", "field_id": "insured_details.income", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Application Sign Date", "field_id": "insured_details.sign_date", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Application Sign State", "field_id": "insured_details.sign_state", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Client Company Assessment", "field_id": "insured_details.client_company_assessment", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Net Worth", "field_id": "insured_details.net_worth", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}]}, {"card_name": "Abnormal Observations", "validation_fields": [{"field_name": "Specimen", "field_id": "abnormal_observations.specimen", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Date", "field_id": "abnormal_observations.date", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Laboratory", "field_id": "abnormal_observations.laboratory", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "LOINC", "field_id": "abnormal_observations.loinc", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Fasting Status", "field_id": "abnormal_observations.fasting", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}]}, {"card_name": "Application Details", "validation_fields": [{"field_name": "Date Received", "field_id": "application_details.date_received", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": " External Application ID", "field_id": "application_details.application_number", "match_type": "non-empty", "high": null, "low": null, "fieldConfidence": 99.0, "message": "Application ID is required ", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "<PERSON><PERSON>", "field_id": "application_details.cedent_name", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Client Company Policy #", "field_id": "application_details.application_id", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Client Company Underwriter", "field_id": "application_details.company_underwriter", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}]}, {"card_name": "Technical Details", "validation_fields": [{"field_name": "Annuity", "field_id": "technical_details.annuity", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "<PERSON><PERSON><PERSON><PERSON>", "field_id": "technical_details.currency", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Annuity Duration Type", "field_id": "technical_details.annuity_duration_type", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Annuity Duration", "field_id": "technical_details.annuity_duration", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Annuity End Date", "field_id": "technical_details.annuity_end_date", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Cedent Product Name", "field_id": "technical_details.plan_type", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Cedent Location", "field_id": "technical_details.cedent_location", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Cedent Person ID", "field_id": "technical_details.cedent_person_id", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Cedent Notification Date", "field_id": "technical_details.cedent_notification_date", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "GenRe Notification Date", "field_id": "technical_details.genre_notification_date", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Cedent Product Type", "field_id": "technical_details.product_type", "match_type": "non-empty", "high": null, "low": null, "fieldConfidence": 85.0, "message": "Product type is required ", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Lump Sum / Face Amount", "field_id": "technical_details.sum_insured", "match_type": "none", "high": null, "low": null, "fieldConfidence": 99.0, "message": "Lump sum is required ", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Benefit Term Type", "field_id": "technical_details.duration_type", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Benefit Term", "field_id": "technical_details.duration", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Benefit Start Date", "field_id": "technical_details.policy_start_date", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Premium Payment Period", "field_id": "technical_details.payment_period", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "<PERSON><PERSON><PERSON> Amou<PERSON>", "field_id": "technical_details.premium_amount", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Reinsurance Amount", "field_id": "technical_details.reinsurance_amount", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Ultimate Risk Amount", "field_id": "technical_details.risk_amount", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Indexation", "field_id": "technical_details.indexation", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Waiting/Deferred Period", "field_id": "technical_details.deferment_period", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}]}, {"card_name": "Allergens", "validation_fields": [{"field_name": "Allergen", "field_id": "allergens.allergen", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Type", "field_id": "allergens.type", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Date", "field_id": "allergens.date", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "SNOMED", "field_id": "allergens.snomed", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}]}, {"card_name": "Vital Signs", "validation_fields": [{"field_name": "Date", "field_id": "vital_signs.date", "match_type": "date", "high": null, "low": null, "fieldConfidence": 90.0, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Blood Pressure", "field_id": "vital_signs.blood_pressure", "match_type": "non-empty", "high": null, "low": null, "fieldConfidence": 90.0, "message": "Bp is required", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Weight", "field_id": "vital_signs.weight", "match_type": "non-empty", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Height", "field_id": "vital_signs.height", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "BMI", "field_id": "vital_signs.bmi", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Pulse", "field_id": "vital_signs.pulse", "match_type": "non-empty", "high": null, "low": null, "fieldConfidence": 90.0, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Oxygen", "field_id": "vital_signs.oxygen", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Respiration", "field_id": "vital_signs.respiration", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Temperature", "field_id": "vital_signs.temperature", "match_type": "non-empty", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}]}, {"card_name": "insured demographics", "validation_fields": [{"field_name": "first name", "field_id": "insured_demographics.first_name", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "city", "field_id": "insured_demographics.city", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}]}]}, {"document_type": "Toronto Underwriting", "cards": [{"card_name": "Insured Details", "validation_fields": []}, {"card_name": "Technical Details", "validation_fields": []}, {"card_name": "Physical Exams", "validation_fields": []}]}, {"document_type": "QA - M", "cards": [{"card_name": "Insured Details", "validation_fields": []}]}, {"document_type": "claims", "cards": [{"card_name": "Insured Details", "validation_fields": []}, {"card_name": "<PERSON><PERSON><PERSON>", "validation_fields": [{"field_name": "Policy Number", "field_id": "claim_details.policy_number", "match_type": "non-empty", "high": null, "low": null, "fieldConfidence": 80.0, "message": "This is a required field", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}]}]}, {"document_type": "Test J", "cards": [{"card_name": "Insured Details", "validation_fields": []}, {"card_name": "Technical Details", "validation_fields": []}]}, {"document_type": "QA", "cards": [{"card_name": "Insured Details", "validation_fields": [{"field_name": "Citizen of", "field_id": "insured_details.citizenship", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Resident of", "field_id": "insured_details.residentship", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Driver's License #", "field_id": "insured_details.license_number", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Driver's License State", "field_id": "insured_details.license_state", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}]}, {"card_name": "Abnormal Observations", "validation_fields": [{"field_name": "Specimen", "field_id": "abnormal_observations.specimen", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Date", "field_id": "abnormal_observations.date", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Laboratory", "field_id": "abnormal_observations.laboratory", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "LOINC", "field_id": "abnormal_observations.loinc", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Fasting Status", "field_id": "abnormal_observations.fasting", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Name", "field_id": "abnormal_observations.name", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Value", "field_id": "abnormal_observations.value", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Low", "field_id": "abnormal_observations.low", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Panel", "field_id": "abnormal_observations.panel", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Rating", "field_id": "abnormal_observations.rating", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "High", "field_id": "abnormal_observations.high", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Descriptive Reference", "field_id": "abnormal_observations.descriptive_reference", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Unit", "field_id": "abnormal_observations.units", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}]}, {"card_name": "Application Details", "validation_fields": []}, {"card_name": "Diagnoses", "validation_fields": [{"field_name": "Diagnosis", "field_id": "diagnoses.diagnosis", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Type", "field_id": "diagnoses.type", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "ICD-10", "field_id": "diagnoses.icd_10", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Visit Date", "field_id": "diagnoses.date", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Diagnosis Date", "field_id": "diagnoses.diag_date", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}]}, {"card_name": "Allergies", "validation_fields": [{"field_name": "Unit", "field_id": "allergies.unit", "match_type": "non-empty", "high": null, "low": null, "fieldConfidence": 98.0, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Test Value", "field_id": "allergies.test_value", "match_type": "date", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}]}]}, {"document_type": "test", "cards": [{"card_name": "Insured Details", "validation_fields": []}, {"card_name": "Abnormal Observations", "validation_fields": [{"field_name": "Specimen", "field_id": "abnormal_observations.specimen", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Date", "field_id": "abnormal_observations.date", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Laboratory", "field_id": "abnormal_observations.laboratory", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "LOINC", "field_id": "abnormal_observations.loinc", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Fasting Status", "field_id": "abnormal_observations.fasting", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}]}]}, {"document_type": "Z-QA", "cards": [{"card_name": "Insured Details", "validation_fields": []}]}, {"document_type": "Dev_TEST_Type1", "cards": []}, {"document_type": "Test-QA", "cards": [{"card_name": "Insured Details", "validation_fields": []}]}, {"document_type": "Stamford Claims", "cards": [{"card_name": "Insured Details", "validation_fields": []}, {"card_name": "Technical Details", "validation_fields": []}]}, {"document_type": "TestDocNew", "cards": [{"card_name": "Insured Details", "validation_fields": []}]}, {"document_type": "annotations", "cards": [{"card_name": "Insured Details", "validation_fields": [{"field_name": "Citizen of", "field_id": "insured_details.citizenship", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Resident of", "field_id": "insured_details.residentship", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Driver's License #", "field_id": "insured_details.license_number", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Driver's License State", "field_id": "insured_details.license_state", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Citizen of the US?", "field_id": "insured_details.us_citizen", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Application Sign Date", "field_id": "insured_details.sign_date", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "First Name", "field_id": "insured_details.name.given_name", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Last Name", "field_id": "insured_details.name.family_name", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Birth Country", "field_id": "insured_details.birth_country", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Address: Zip Code", "field_id": "insured_details.address.zipcode", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Marital Status", "field_id": "insured_details.marital_status", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Occupation", "field_id": "insured_details.occupation", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Weight", "field_id": "insured_details.weight", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Height", "field_id": "insured_details.height", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Alcohol", "field_id": "insured_details.alcohol", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Drugs", "field_id": "insured_details.drugs", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Avocation", "field_id": "insured_details.avocation", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Travel", "field_id": "insured_details.travel", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Behavior", "field_id": "insured_details.behavior", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Social Security Number", "field_id": "insured_details.ssn", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Owner Relationship", "field_id": "insured_details.owner_relationship", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Beneficiary Relationship", "field_id": "insured_details.beneficiary_relationship", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Full Name", "field_id": "insured_details.name", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Gender", "field_id": "insured_details.gender", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}]}, {"card_name": "Technical Details", "validation_fields": [{"field_name": "Annuity", "field_id": "technical_details.annuity", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "<PERSON><PERSON><PERSON><PERSON>", "field_id": "technical_details.currency", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Annuity Duration Type", "field_id": "technical_details.annuity_duration_type", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Annuity Duration", "field_id": "technical_details.annuity_duration", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Annuity End Date", "field_id": "technical_details.annuity_end_date", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Cedent Location", "field_id": "technical_details.cedent_location", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Cedent Person ID", "field_id": "technical_details.cedent_person_id", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Cedent Notification Date", "field_id": "technical_details.cedent_notification_date", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "GenRe Notification Date", "field_id": "technical_details.genre_notification_date", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Cedent Product Name", "field_id": "technical_details.plan_type", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}]}, {"card_name": "Abnormal Observations", "validation_fields": [{"field_name": "Specimen", "field_id": "abnormal_observations.specimen", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Date", "field_id": "abnormal_observations.date", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Laboratory", "field_id": "abnormal_observations.laboratory", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "LOINC", "field_id": "abnormal_observations.loinc", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Fasting Status", "field_id": "abnormal_observations.fasting", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Value", "field_id": "abnormal_observations.value", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Name", "field_id": "abnormal_observations.name", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Low", "field_id": "abnormal_observations.low", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Panel", "field_id": "abnormal_observations.panel", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Rating", "field_id": "abnormal_observations.rating", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "High", "field_id": "abnormal_observations.high", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Descriptive Reference", "field_id": "abnormal_observations.descriptive_reference", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Unit", "field_id": "abnormal_observations.units", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}]}, {"card_name": "Application Details", "validation_fields": [{"field_name": "Date Received", "field_id": "application_details.date_received", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}]}, {"card_name": "Vital Signs", "validation_fields": [{"field_name": "Date", "field_id": "vital_signs.date", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Blood Pressure", "field_id": "vital_signs.blood_pressure", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Weight", "field_id": "vital_signs.weight", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Height", "field_id": "vital_signs.height", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "BMI", "field_id": "vital_signs.bmi", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Pulse", "field_id": "vital_signs.pulse", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Oxygen", "field_id": "vital_signs.oxygen", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Respiration", "field_id": "vital_signs.respiration", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Temperature", "field_id": "vital_signs.temperature", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}]}]}, {"document_type": "Singapore Underwriting", "cards": [{"card_name": "Insured Details", "validation_fields": [{"field_name": "Citizen of", "field_id": "insured_details.citizenship", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Resident of", "field_id": "insured_details.residentship", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Driver's License #", "field_id": "insured_details.license_number", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Full Name", "field_id": "insured_details.name", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "First Name", "field_id": "insured_details.name.given_name", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Last Name", "field_id": "insured_details.name.family_name", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Birth Country", "field_id": "insured_details.birth_country", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Gender", "field_id": "insured_details.gender", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Address", "field_id": "insured_details.address", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Address: Street Line", "field_id": "insured_details.address.line", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Address: City", "field_id": "insured_details.address.city", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Address: State", "field_id": "insured_details.address.state", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Address: Zip Code", "field_id": "insured_details.address.zipcode", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Address: Country", "field_id": "insured_details.address.country", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Marital Status", "field_id": "insured_details.marital_status", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Occupation", "field_id": "insured_details.occupation", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Weight", "field_id": "insured_details.weight", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Height", "field_id": "insured_details.height", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Alcohol", "field_id": "insured_details.alcohol", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Drugs", "field_id": "insured_details.drugs", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Avocation", "field_id": "insured_details.avocation", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Travel", "field_id": "insured_details.travel", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Behavior", "field_id": "insured_details.behavior", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Social Security Number", "field_id": "insured_details.ssn", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Owner Relationship", "field_id": "insured_details.owner_relationship", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Beneficiary Relationship", "field_id": "insured_details.beneficiary_relationship", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Driver's License State", "field_id": "insured_details.license_state", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Citizen of the US?", "field_id": "insured_details.us_citizen", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Application Sign Date", "field_id": "insured_details.sign_date", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Insured Type", "field_id": "insured_details.insured_type", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Date of Birth", "field_id": "insured_details.date_of_birth", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Birth State", "field_id": "insured_details.birth_state", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}]}, {"card_name": "Abnormal Observations", "validation_fields": [{"field_name": "Specimen", "field_id": "abnormal_observations.specimen", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Date", "field_id": "abnormal_observations.date", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Laboratory", "field_id": "abnormal_observations.laboratory", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "LOINC", "field_id": "abnormal_observations.loinc", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Fasting Status", "field_id": "abnormal_observations.fasting", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Name", "field_id": "abnormal_observations.name", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Value", "field_id": "abnormal_observations.value", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Rating", "field_id": "abnormal_observations.rating", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Low", "field_id": "abnormal_observations.low", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "High", "field_id": "abnormal_observations.high", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Descriptive Reference", "field_id": "abnormal_observations.descriptive_reference", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Unit", "field_id": "abnormal_observations.units", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Panel", "field_id": "abnormal_observations.panel", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}]}, {"card_name": "Technical Details", "validation_fields": [{"field_name": "Annuity", "field_id": "technical_details.annuity", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "<PERSON><PERSON><PERSON><PERSON>", "field_id": "technical_details.currency", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Annuity Duration Type", "field_id": "technical_details.annuity_duration_type", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Annuity Duration", "field_id": "technical_details.annuity_duration", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Annuity End Date", "field_id": "technical_details.annuity_end_date", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Cedent Location", "field_id": "technical_details.cedent_location", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Cedent Person ID", "field_id": "technical_details.cedent_person_id", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Cedent Notification Date", "field_id": "technical_details.cedent_notification_date", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "GenRe Notification Date", "field_id": "technical_details.genre_notification_date", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Cedent Product Type", "field_id": "technical_details.product_type", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Lump Sum / Face Amount", "field_id": "technical_details.sum_insured", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Benefit Term Type", "field_id": "technical_details.duration_type", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Benefit Term", "field_id": "technical_details.duration", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Benefit Start Date", "field_id": "technical_details.policy_start_date", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Premium Payment Period", "field_id": "technical_details.payment_period", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Cedent Product Name", "field_id": "technical_details.plan_type", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "<PERSON><PERSON><PERSON> Amou<PERSON>", "field_id": "technical_details.premium_amount", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Reinsurance Amount", "field_id": "technical_details.reinsurance_amount", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Ultimate Risk Amount", "field_id": "technical_details.risk_amount", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Indexation", "field_id": "technical_details.indexation", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Waiting/Deferred Period", "field_id": "technical_details.deferment_period", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}]}, {"card_name": "Application Details", "validation_fields": [{"field_name": "Date Received", "field_id": "application_details.date_received", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": " External Application ID", "field_id": "application_details.application_number", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "<PERSON><PERSON>", "field_id": "application_details.cedent_name", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Client Company Policy #", "field_id": "application_details.application_id", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Client Company Underwriter", "field_id": "application_details.company_underwriter", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}]}]}, {"document_type": "London Underwriting", "cards": [{"card_name": "Insured Details", "validation_fields": [{"field_name": "Citizen of", "field_id": "insured_details.citizenship", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Resident of", "field_id": "insured_details.residentship", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Driver's License #", "field_id": "insured_details.license_number", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Driver's License State", "field_id": "insured_details.license_state", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Citizen of the US?", "field_id": "insured_details.us_citizen", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Application Sign Date", "field_id": "insured_details.sign_date", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Full Name", "field_id": "insured_details.name", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "First Name", "field_id": "insured_details.name.given_name", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Last Name", "field_id": "insured_details.name.family_name", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Birth Country", "field_id": "insured_details.birth_country", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Gender", "field_id": "insured_details.gender", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Address: Zip Code", "field_id": "insured_details.address.zipcode", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Marital Status", "field_id": "insured_details.marital_status", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Occupation", "field_id": "insured_details.occupation", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Weight", "field_id": "insured_details.weight", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Height", "field_id": "insured_details.height", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Alcohol", "field_id": "insured_details.alcohol", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Drugs", "field_id": "insured_details.drugs", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Avocation", "field_id": "insured_details.avocation", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Travel", "field_id": "insured_details.travel", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Behavior", "field_id": "insured_details.behavior", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Social Security Number", "field_id": "insured_details.ssn", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Owner Relationship", "field_id": "insured_details.owner_relationship", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Beneficiary Relationship", "field_id": "insured_details.beneficiary_relationship", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Address", "field_id": "insured_details.address", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Address: Street Line", "field_id": "insured_details.address.line", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Address: City", "field_id": "insured_details.address.city", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Address: State", "field_id": "insured_details.address.state", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Address: Country", "field_id": "insured_details.address.country", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Insured Type", "field_id": "insured_details.insured_type", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Date of Birth", "field_id": "insured_details.date_of_birth", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Birth State", "field_id": "insured_details.birth_state", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Application Sign State", "field_id": "insured_details.sign_state", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Client Company Assessment", "field_id": "insured_details.client_company_assessment", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}]}, {"card_name": "Abnormal Observations", "validation_fields": [{"field_name": "Specimen", "field_id": "abnormal_observations.specimen", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Date", "field_id": "abnormal_observations.date", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Laboratory", "field_id": "abnormal_observations.laboratory", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "LOINC", "field_id": "abnormal_observations.loinc", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Fasting Status", "field_id": "abnormal_observations.fasting", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Name", "field_id": "abnormal_observations.name", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Value", "field_id": "abnormal_observations.value", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Low", "field_id": "abnormal_observations.low", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Panel", "field_id": "abnormal_observations.panel", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Rating", "field_id": "abnormal_observations.rating", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "High", "field_id": "abnormal_observations.high", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Descriptive Reference", "field_id": "abnormal_observations.descriptive_reference", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Unit", "field_id": "abnormal_observations.units", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}]}, {"card_name": "Technical Details", "validation_fields": [{"field_name": "Annuity", "field_id": "technical_details.annuity", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "<PERSON><PERSON><PERSON><PERSON>", "field_id": "technical_details.currency", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Annuity Duration Type", "field_id": "technical_details.annuity_duration_type", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Annuity Duration", "field_id": "technical_details.annuity_duration", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Annuity End Date", "field_id": "technical_details.annuity_end_date", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Cedent Location", "field_id": "technical_details.cedent_location", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Cedent Person ID", "field_id": "technical_details.cedent_person_id", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Cedent Notification Date", "field_id": "technical_details.cedent_notification_date", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "GenRe Notification Date", "field_id": "technical_details.genre_notification_date", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Cedent Product Name", "field_id": "technical_details.plan_type", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}]}, {"card_name": "Application Details", "validation_fields": [{"field_name": "Date Received", "field_id": "application_details.date_received", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}]}]}, {"document_type": "Cologne Germany Underwriting", "cards": [{"card_name": "Insured Details", "validation_fields": [{"field_name": "Citizen of", "field_id": "insured_details.citizenship", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Resident of", "field_id": "insured_details.residentship", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Driver's License #", "field_id": "insured_details.license_number", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Driver's License State", "field_id": "insured_details.license_state", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "First Name", "field_id": "insured_details.name.given_name", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Last Name", "field_id": "insured_details.name.family_name", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Birth Country", "field_id": "insured_details.birth_country", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Address: Zip Code", "field_id": "insured_details.address.zipcode", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Marital Status", "field_id": "insured_details.marital_status", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Occupation", "field_id": "insured_details.occupation", "match_type": "non-empty", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Weight", "field_id": "insured_details.weight", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Height", "field_id": "insured_details.height", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Alcohol", "field_id": "insured_details.alcohol", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Drugs", "field_id": "insured_details.drugs", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Avocation", "field_id": "insured_details.avocation", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Travel", "field_id": "insured_details.travel", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Behavior", "field_id": "insured_details.behavior", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Social Security Number", "field_id": "insured_details.ssn", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Owner Relationship", "field_id": "insured_details.owner_relationship", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Beneficiary Relationship", "field_id": "insured_details.beneficiary_relationship", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Citizen of the US?", "field_id": "insured_details.us_citizen", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Application Sign Date", "field_id": "insured_details.sign_date", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Full Name", "field_id": "insured_details.name", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Gender", "field_id": "insured_details.gender", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Address", "field_id": "insured_details.address", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Address: Street Line", "field_id": "insured_details.address.line", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Address: City", "field_id": "insured_details.address.city", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Address: State", "field_id": "insured_details.address.state", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Address: Country", "field_id": "insured_details.address.country", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Insured Type", "field_id": "insured_details.insured_type", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Date of Birth", "field_id": "insured_details.date_of_birth", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Birth State", "field_id": "insured_details.birth_state", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Application Sign State", "field_id": "insured_details.sign_state", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Client Company Assessment", "field_id": "insured_details.client_company_assessment", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Smoker Status", "field_id": "insured_details.smoking", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Resident of the US?", "field_id": "insured_details.us_resident", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Income / Salary", "field_id": "insured_details.income", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Net Worth", "field_id": "insured_details.net_worth", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}]}, {"card_name": "Technical Details", "validation_fields": [{"field_name": "Annuity", "field_id": "technical_details.annuity", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "<PERSON><PERSON><PERSON><PERSON>", "field_id": "technical_details.currency", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Annuity Duration Type", "field_id": "technical_details.annuity_duration_type", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Annuity Duration", "field_id": "technical_details.annuity_duration", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Annuity End Date", "field_id": "technical_details.annuity_end_date", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Cedent Location", "field_id": "technical_details.cedent_location", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Cedent Person ID", "field_id": "technical_details.cedent_person_id", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Cedent Notification Date", "field_id": "technical_details.cedent_notification_date", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "GenRe Notification Date", "field_id": "technical_details.genre_notification_date", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Cedent Product Name", "field_id": "technical_details.plan_type", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Cedent Product Type", "field_id": "technical_details.product_type", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Lump Sum / Face Amount", "field_id": "technical_details.sum_insured", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Benefit Term Type", "field_id": "technical_details.duration_type", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Benefit Term", "field_id": "technical_details.duration", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Benefit Start Date", "field_id": "technical_details.policy_start_date", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Premium Payment Period", "field_id": "technical_details.payment_period", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "<PERSON><PERSON><PERSON> Amou<PERSON>", "field_id": "technical_details.premium_amount", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Reinsurance Amount", "field_id": "technical_details.reinsurance_amount", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Ultimate Risk Amount", "field_id": "technical_details.risk_amount", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Indexation", "field_id": "technical_details.indexation", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Waiting/Deferred Period", "field_id": "technical_details.deferment_period", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}]}, {"card_name": "<PERSON><PERSON><PERSON>", "validation_fields": []}, {"card_name": "Physical Exams", "validation_fields": []}, {"card_name": "Vital Signs", "validation_fields": []}, {"card_name": "Diagnoses", "validation_fields": []}, {"card_name": "Reason Of Visit", "validation_fields": []}, {"card_name": "Medication Plan", "validation_fields": []}, {"card_name": "Imaging Study", "validation_fields": []}, {"card_name": "Medication History", "validation_fields": []}, {"card_name": "Encounter Details", "validation_fields": []}, {"card_name": "Medical Equipment", "validation_fields": []}, {"card_name": "Abnormal Observations", "validation_fields": [{"field_name": "Specimen", "field_id": "abnormal_observations.specimen", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Date", "field_id": "abnormal_observations.date", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Laboratory", "field_id": "abnormal_observations.laboratory", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "LOINC", "field_id": "abnormal_observations.loinc", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Fasting Status", "field_id": "abnormal_observations.fasting", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}]}, {"card_name": "Social History", "validation_fields": []}, {"card_name": "<PERSON><PERSON><PERSON>", "validation_fields": []}, {"card_name": "Application Details", "validation_fields": [{"field_name": "Date Received", "field_id": "application_details.date_received", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": " External Application ID", "field_id": "application_details.application_number", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "<PERSON><PERSON>", "field_id": "application_details.cedent_name", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Client Company Policy #", "field_id": "application_details.application_id", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Client Company Underwriter", "field_id": "application_details.company_underwriter", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}]}]}, {"document_type": "Sydney Underwriting", "cards": [{"card_name": "Insured Details", "validation_fields": [{"field_name": "Full Name", "field_id": "insured_details.name", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "First Name", "field_id": "insured_details.name.given_name", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Last Name", "field_id": "insured_details.name.family_name", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Birth Country", "field_id": "insured_details.birth_country", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Gender", "field_id": "insured_details.gender", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Address", "field_id": "insured_details.address", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Address: Street Line", "field_id": "insured_details.address.line", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Address: City", "field_id": "insured_details.address.city", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Address: State", "field_id": "insured_details.address.state", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Address: Zip Code", "field_id": "insured_details.address.zipcode", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Address: Country", "field_id": "insured_details.address.country", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Marital Status", "field_id": "insured_details.marital_status", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Occupation", "field_id": "insured_details.occupation", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Weight", "field_id": "insured_details.weight", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Height", "field_id": "insured_details.height", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Alcohol", "field_id": "insured_details.alcohol", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Drugs", "field_id": "insured_details.drugs", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Avocation", "field_id": "insured_details.avocation", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Travel", "field_id": "insured_details.travel", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Behavior", "field_id": "insured_details.behavior", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Social Security Number", "field_id": "insured_details.ssn", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Owner Relationship", "field_id": "insured_details.owner_relationship", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Beneficiary Relationship", "field_id": "insured_details.beneficiary_relationship", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Citizen of", "field_id": "insured_details.citizenship", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Resident of", "field_id": "insured_details.residentship", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Driver's License #", "field_id": "insured_details.license_number", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Driver's License State", "field_id": "insured_details.license_state", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Citizen of the US?", "field_id": "insured_details.us_citizen", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Application Sign Date", "field_id": "insured_details.sign_date", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Insured Type", "field_id": "insured_details.insured_type", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Date of Birth", "field_id": "insured_details.date_of_birth", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Birth State", "field_id": "insured_details.birth_state", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Smoker Status", "field_id": "insured_details.smoking", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Resident of the US?", "field_id": "insured_details.us_resident", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Income / Salary", "field_id": "insured_details.income", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Application Sign State", "field_id": "insured_details.sign_state", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Client Company Assessment", "field_id": "insured_details.client_company_assessment", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Net Worth", "field_id": "insured_details.net_worth", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}]}, {"card_name": "Abnormal Observations", "validation_fields": [{"field_name": "Name", "field_id": "abnormal_observations.name", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Value", "field_id": "abnormal_observations.value", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Low", "field_id": "abnormal_observations.low", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Panel", "field_id": "abnormal_observations.panel", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Specimen", "field_id": "abnormal_observations.specimen", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Date", "field_id": "abnormal_observations.date", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Laboratory", "field_id": "abnormal_observations.laboratory", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "LOINC", "field_id": "abnormal_observations.loinc", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Fasting Status", "field_id": "abnormal_observations.fasting", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Rating", "field_id": "abnormal_observations.rating", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "High", "field_id": "abnormal_observations.high", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Descriptive Reference", "field_id": "abnormal_observations.descriptive_reference", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Unit", "field_id": "abnormal_observations.units", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}]}, {"card_name": "Technical Details", "validation_fields": [{"field_name": "Annuity", "field_id": "technical_details.annuity", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "<PERSON><PERSON><PERSON><PERSON>", "field_id": "technical_details.currency", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Annuity Duration Type", "field_id": "technical_details.annuity_duration_type", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Annuity Duration", "field_id": "technical_details.annuity_duration", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Annuity End Date", "field_id": "technical_details.annuity_end_date", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Cedent Product Name", "field_id": "technical_details.plan_type", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Cedent Location", "field_id": "technical_details.cedent_location", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Cedent Person ID", "field_id": "technical_details.cedent_person_id", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Cedent Notification Date", "field_id": "technical_details.cedent_notification_date", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "GenRe Notification Date", "field_id": "technical_details.genre_notification_date", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Cedent Product Type", "field_id": "technical_details.product_type", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Lump Sum / Face Amount", "field_id": "technical_details.sum_insured", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Benefit Term Type", "field_id": "technical_details.duration_type", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Benefit Term", "field_id": "technical_details.duration", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Benefit Start Date", "field_id": "technical_details.policy_start_date", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Premium Payment Period", "field_id": "technical_details.payment_period", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "<PERSON><PERSON><PERSON> Amou<PERSON>", "field_id": "technical_details.premium_amount", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Reinsurance Amount", "field_id": "technical_details.reinsurance_amount", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Ultimate Risk Amount", "field_id": "technical_details.risk_amount", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Indexation", "field_id": "technical_details.indexation", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Waiting/Deferred Period", "field_id": "technical_details.deferment_period", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}]}, {"card_name": "Application Details", "validation_fields": [{"field_name": " External Application ID", "field_id": "application_details.application_number", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "<PERSON><PERSON>", "field_id": "application_details.cedent_name", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Client Company Policy #", "field_id": "application_details.application_id", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Date Received", "field_id": "application_details.date_received", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Client Company Underwriter", "field_id": "application_details.company_underwriter", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}]}]}, {"document_type": "Cologne Germany Claims", "cards": [{"card_name": "Insured Details", "validation_fields": []}]}, {"document_type": "Cape Town and Johannesburg Claims", "cards": [{"card_name": "Insured Details", "validation_fields": []}, {"card_name": "<PERSON><PERSON><PERSON>", "validation_fields": [{"field_name": "Policy Number", "field_id": "claim_details.policy_number", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Claim Number", "field_id": "claim_details.claim_number", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Policy Effective Date", "field_id": "claim_details.policy_effective_date", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Claimed Date of Loss", "field_id": "claim_details.date_of_loss", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Primary Diagnosis", "field_id": "claim_details.primary_diagnosis", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Autopsy Performed", "field_id": "claim_details.autopsy_performed", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Policy Expiration Date", "field_id": "claim_details.policy_exp_date", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Original sum at risk", "field_id": "claim_details.sum_at_risk", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}]}]}, {"document_type": "Test-Doc", "cards": [{"card_name": "Insured Details", "validation_fields": []}]}, {"document_type": "<PERSON>", "cards": [{"card_name": "Insured Details", "validation_fields": [{"field_name": "First Name", "field_id": "insured_details.name.given_name", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Last Name", "field_id": "insured_details.name.family_name", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Citizen of", "field_id": "insured_details.citizenship", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Resident of", "field_id": "insured_details.residentship", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Driver's License #", "field_id": "insured_details.license_number", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Driver's License State", "field_id": "insured_details.license_state", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Citizen of the US?", "field_id": "insured_details.us_citizen", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Application Sign Date", "field_id": "insured_details.sign_date", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Birth Country", "field_id": "insured_details.birth_country", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Address: Zip Code", "field_id": "insured_details.address.zipcode", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Marital Status", "field_id": "insured_details.marital_status", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Occupation", "field_id": "insured_details.occupation", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Weight", "field_id": "insured_details.weight", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Height", "field_id": "insured_details.height", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Alcohol", "field_id": "insured_details.alcohol", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Drugs", "field_id": "insured_details.drugs", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Avocation", "field_id": "insured_details.avocation", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Travel", "field_id": "insured_details.travel", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Behavior", "field_id": "insured_details.behavior", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Social Security Number", "field_id": "insured_details.ssn", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Owner Relationship", "field_id": "insured_details.owner_relationship", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Beneficiary Relationship", "field_id": "insured_details.beneficiary_relationship", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}]}, {"card_name": "Abnormal Observations", "validation_fields": [{"field_name": "Specimen", "field_id": "abnormal_observations.specimen", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Date", "field_id": "abnormal_observations.date", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Laboratory", "field_id": "abnormal_observations.laboratory", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "LOINC", "field_id": "abnormal_observations.loinc", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Fasting Status", "field_id": "abnormal_observations.fasting", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Name", "field_id": "abnormal_observations.name", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Value", "field_id": "abnormal_observations.value", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Low", "field_id": "abnormal_observations.low", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Panel", "field_id": "abnormal_observations.panel", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Rating", "field_id": "abnormal_observations.rating", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "High", "field_id": "abnormal_observations.high", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Descriptive Reference", "field_id": "abnormal_observations.descriptive_reference", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Unit", "field_id": "abnormal_observations.units", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}]}, {"card_name": "Application Details", "validation_fields": [{"field_name": "Date Received", "field_id": "application_details.date_received", "match_type": "date", "high": null, "low": null, "fieldConfidence": null, "message": "Valid date is required", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": " External Application ID", "field_id": "application_details.application_number", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "<PERSON><PERSON>", "field_id": "application_details.cedent_name", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Client Company Policy #", "field_id": "application_details.application_id", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Client Company Underwriter", "field_id": "application_details.company_underwriter", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}]}]}, {"document_type": "Colonge_Germany_Underwriting", "cards": [{"card_name": "Abnormal Observations", "validation_fields": [{"field_name": "Specimen", "field_id": "abnormal_observations.specimen", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Date", "field_id": "abnormal_observations.date", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Laboratory", "field_id": "abnormal_observations.laboratory", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "LOINC", "field_id": "abnormal_observations.loinc", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Fasting Status", "field_id": "abnormal_observations.fasting", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Name", "field_id": "abnormal_observations.name", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Value", "field_id": "abnormal_observations.value", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Low", "field_id": "abnormal_observations.low", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Panel", "field_id": "abnormal_observations.panel", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}]}, {"card_name": "Insured Details", "validation_fields": [{"field_name": "Citizen of", "field_id": "insured_details.citizenship", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Resident of", "field_id": "insured_details.residentship", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Driver's License #", "field_id": "insured_details.license_number", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Driver's License State", "field_id": "insured_details.license_state", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Full Name", "field_id": "insured_details.name", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "First Name", "field_id": "insured_details.name.given_name", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Last Name", "field_id": "insured_details.name.family_name", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Insured Type", "field_id": "insured_details.insured_type", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Date of Birth", "field_id": "insured_details.date_of_birth", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Birth State", "field_id": "insured_details.birth_state", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Birth Country", "field_id": "insured_details.birth_country", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Gender", "field_id": "insured_details.gender", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Address", "field_id": "insured_details.address", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Address: Street Line", "field_id": "insured_details.address.line", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Address: City", "field_id": "insured_details.address.city", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Address: State", "field_id": "insured_details.address.state", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Address: Zip Code", "field_id": "insured_details.address.zipcode", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Address: Country", "field_id": "insured_details.address.country", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Marital Status", "field_id": "insured_details.marital_status", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Occupation", "field_id": "insured_details.occupation", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Weight", "field_id": "insured_details.weight", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Height", "field_id": "insured_details.height", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Alcohol", "field_id": "insured_details.alcohol", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Smoker Status", "field_id": "insured_details.smoking", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Drugs", "field_id": "insured_details.drugs", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Avocation", "field_id": "insured_details.avocation", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Travel", "field_id": "insured_details.travel", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Behavior", "field_id": "insured_details.behavior", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Social Security Number", "field_id": "insured_details.ssn", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Owner Relationship", "field_id": "insured_details.owner_relationship", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Beneficiary Relationship", "field_id": "insured_details.beneficiary_relationship", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Citizen of the US?", "field_id": "insured_details.us_citizen", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Resident of the US?", "field_id": "insured_details.us_resident", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Income / Salary", "field_id": "insured_details.income", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Application Sign Date", "field_id": "insured_details.sign_date", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Application Sign State", "field_id": "insured_details.sign_state", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Client Company Assessment", "field_id": "insured_details.client_company_assessment", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}]}, {"card_name": "Diagnoses", "validation_fields": [{"field_name": "Diagnosis", "field_id": "diagnoses.diagnosis", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Type", "field_id": "diagnoses.type", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "ICD-10", "field_id": "diagnoses.icd_10", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Visit Date", "field_id": "diagnoses.date", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Diagnosis Date", "field_id": "diagnoses.diag_date", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}]}, {"card_name": "Application Details", "validation_fields": [{"field_name": " External Application ID", "field_id": "application_details.application_number", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "<PERSON><PERSON>", "field_id": "application_details.cedent_name", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Client Company Policy #", "field_id": "application_details.application_id", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Date Received", "field_id": "application_details.date_received", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Client Company Underwriter", "field_id": "application_details.company_underwriter", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}]}, {"card_name": "Technical Details", "validation_fields": []}]}, {"document_type": "Test C", "cards": [{"card_name": "Abnormal Observations", "validation_fields": [{"field_name": "Specimen", "field_id": "abnormal_observations.specimen", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Date", "field_id": "abnormal_observations.date", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Laboratory", "field_id": "abnormal_observations.laboratory", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "LOINC", "field_id": "abnormal_observations.loinc", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Fasting Status", "field_id": "abnormal_observations.fasting", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Name", "field_id": "abnormal_observations.name", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Value", "field_id": "abnormal_observations.value", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Rating", "field_id": "abnormal_observations.rating", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Low", "field_id": "abnormal_observations.low", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "High", "field_id": "abnormal_observations.high", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Descriptive Reference", "field_id": "abnormal_observations.descriptive_reference", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Unit", "field_id": "abnormal_observations.units", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Panel", "field_id": "abnormal_observations.panel", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}]}, {"card_name": "Vital Signs", "validation_fields": [{"field_name": "Date", "field_id": "vital_signs.date", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Blood Pressure", "field_id": "vital_signs.blood_pressure", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "BMI", "field_id": "vital_signs.bmi", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Pulse", "field_id": "vital_signs.pulse", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Oxygen", "field_id": "vital_signs.oxygen", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Respiration", "field_id": "vital_signs.respiration", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Weight", "field_id": "vital_signs.weight", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Height", "field_id": "vital_signs.height", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Temperature", "field_id": "vital_signs.temperature", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}]}, {"card_name": "CCDA Vital Signs", "validation_fields": [{"field_name": "Type", "field_id": "ccda_vital_signs.type", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Value", "field_id": "ccda_vital_signs.value", "match_type": "non-empty", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Unit", "field_id": "ccda_vital_signs.unit", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Date", "field_id": "ccda_vital_signs.date", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "LOINC", "field_id": "ccda_vital_signs.loinc", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}]}, {"card_name": "CCDA Vitals", "validation_fields": [{"field_name": "Type", "field_id": "ccda_vitals.type", "match_type": "non-empty", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Value", "field_id": "ccda_vitals.value", "match_type": "non-empty", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Unit", "field_id": "ccda_vitals.unit", "match_type": "non-empty", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Date", "field_id": "ccda_vitals.date", "match_type": "date", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "LOINC", "field_id": "ccda_vitals.loinc", "match_type": "non-empty", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}]}]}, {"document_type": "riversource uw", "cards": [{"card_name": "Abnormal Observations", "validation_fields": [{"field_name": "Specimen", "field_id": "abnormal_observations.specimen", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Date", "field_id": "abnormal_observations.date", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Laboratory", "field_id": "abnormal_observations.laboratory", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "LOINC", "field_id": "abnormal_observations.loinc", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Fasting Status", "field_id": "abnormal_observations.fasting", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}]}, {"card_name": "Insured Details", "validation_fields": [{"field_name": "Citizen of", "field_id": "insured_details.citizenship", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Resident of", "field_id": "insured_details.residentship", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Driver's License #", "field_id": "insured_details.license_number", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Driver's License State", "field_id": "insured_details.license_state", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}]}, {"card_name": "Application Details", "validation_fields": []}, {"card_name": "Technical Details", "validation_fields": []}]}, {"document_type": "london uw", "cards": [{"card_name": "Abnormal Observations", "validation_fields": [{"field_name": "Specimen", "field_id": "abnormal_observations.specimen", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Date", "field_id": "abnormal_observations.date", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Laboratory", "field_id": "abnormal_observations.laboratory", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "LOINC", "field_id": "abnormal_observations.loinc", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Fasting Status", "field_id": "abnormal_observations.fasting", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}]}, {"card_name": "Insured Details", "validation_fields": [{"field_name": "Citizen of", "field_id": "insured_details.citizenship", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Resident of", "field_id": "insured_details.residentship", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Driver's License #", "field_id": "insured_details.license_number", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Driver's License State", "field_id": "insured_details.license_state", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Full Name", "field_id": "insured_details.name", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "First Name", "field_id": "insured_details.name.given_name", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Last Name", "field_id": "insured_details.name.family_name", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Insured Type", "field_id": "insured_details.insured_type", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Date of Birth", "field_id": "insured_details.date_of_birth", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Birth State", "field_id": "insured_details.birth_state", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Birth Country", "field_id": "insured_details.birth_country", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Gender", "field_id": "insured_details.gender", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Address", "field_id": "insured_details.address", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Address: Street Line", "field_id": "insured_details.address.line", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Address: City", "field_id": "insured_details.address.city", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Address: State", "field_id": "insured_details.address.state", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Address: Zip Code", "field_id": "insured_details.address.zipcode", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Address: Country", "field_id": "insured_details.address.country", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Marital Status", "field_id": "insured_details.marital_status", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Occupation", "field_id": "insured_details.occupation", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Weight", "field_id": "insured_details.weight", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Height", "field_id": "insured_details.height", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Alcohol", "field_id": "insured_details.alcohol", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Smoker Status", "field_id": "insured_details.smoking", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Drugs", "field_id": "insured_details.drugs", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Avocation", "field_id": "insured_details.avocation", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Travel", "field_id": "insured_details.travel", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Behavior", "field_id": "insured_details.behavior", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Social Security Number", "field_id": "insured_details.ssn", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Owner Relationship", "field_id": "insured_details.owner_relationship", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Beneficiary Relationship", "field_id": "insured_details.beneficiary_relationship", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Citizen of the US?", "field_id": "insured_details.us_citizen", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Resident of the US?", "field_id": "insured_details.us_resident", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Income / Salary", "field_id": "insured_details.income", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Application Sign Date", "field_id": "insured_details.sign_date", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Application Sign State", "field_id": "insured_details.sign_state", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Client Company Assessment", "field_id": "insured_details.client_company_assessment", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}]}, {"card_name": "Application Details", "validation_fields": [{"field_name": " External Application ID", "field_id": "application_details.application_number", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "<PERSON><PERSON>", "field_id": "application_details.cedent_name", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Client Company Policy #", "field_id": "application_details.application_id", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Date Received", "field_id": "application_details.date_received", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Client Company Underwriter", "field_id": "application_details.company_underwriter", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}]}]}, {"document_type": "Stamford Underwriting", "cards": [{"card_name": "Abnormal Observations", "validation_fields": [{"field_name": "Specimen", "field_id": "abnormal_observations.specimen", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Date", "field_id": "abnormal_observations.date", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Laboratory", "field_id": "abnormal_observations.laboratory", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "LOINC", "field_id": "abnormal_observations.loinc", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Fasting Status", "field_id": "abnormal_observations.fasting", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}]}, {"card_name": "Vital Signs", "validation_fields": [{"field_name": "Date", "field_id": "vital_signs.date", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Blood Pressure", "field_id": "vital_signs.blood_pressure", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "BMI", "field_id": "vital_signs.bmi", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Pulse", "field_id": "vital_signs.pulse", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Oxygen", "field_id": "vital_signs.oxygen", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Respiration", "field_id": "vital_signs.respiration", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Weight", "field_id": "vital_signs.weight", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Height", "field_id": "vital_signs.height", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Temperature", "field_id": "vital_signs.temperature", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}]}, {"card_name": "Imaging Study", "validation_fields": [{"field_name": "Date", "field_id": "imaging_study.date", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Exam", "field_id": "imaging_study.exam", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Reason", "field_id": "imaging_study.reason", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Finding", "field_id": "imaging_study.finding", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Impression", "field_id": "imaging_study.impression", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Conclusion", "field_id": "imaging_study.conclusion", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}]}, {"card_name": "Insured Details", "validation_fields": [{"field_name": "Citizen of", "field_id": "insured_details.citizenship", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Resident of", "field_id": "insured_details.residentship", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Driver's License #", "field_id": "insured_details.license_number", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Driver's License State", "field_id": "insured_details.license_state", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "First Name", "field_id": "insured_details.name.given_name", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Last Name", "field_id": "insured_details.name.family_name", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Citizen of the US?", "field_id": "insured_details.us_citizen", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Application Sign Date", "field_id": "insured_details.sign_date", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Full Name", "field_id": "insured_details.name", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Insured Type", "field_id": "insured_details.insured_type", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Date of Birth", "field_id": "insured_details.date_of_birth", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Birth State", "field_id": "insured_details.birth_state", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Birth Country", "field_id": "insured_details.birth_country", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Gender", "field_id": "insured_details.gender", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Address", "field_id": "insured_details.address", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Address: Street Line", "field_id": "insured_details.address.line", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Address: City", "field_id": "insured_details.address.city", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Address: State", "field_id": "insured_details.address.state", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Address: Zip Code", "field_id": "insured_details.address.zipcode", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Address: Country", "field_id": "insured_details.address.country", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Marital Status", "field_id": "insured_details.marital_status", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Occupation", "field_id": "insured_details.occupation", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Weight", "field_id": "insured_details.weight", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Height", "field_id": "insured_details.height", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Alcohol", "field_id": "insured_details.alcohol", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Drugs", "field_id": "insured_details.drugs", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Avocation", "field_id": "insured_details.avocation", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Travel", "field_id": "insured_details.travel", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Behavior", "field_id": "insured_details.behavior", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Social Security Number", "field_id": "insured_details.ssn", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Owner Relationship", "field_id": "insured_details.owner_relationship", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Beneficiary Relationship", "field_id": "insured_details.beneficiary_relationship", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Smoker Status", "field_id": "insured_details.smoking", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Resident of the US?", "field_id": "insured_details.us_resident", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Income / Salary", "field_id": "insured_details.income", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Application Sign State", "field_id": "insured_details.sign_state", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Client Company Assessment", "field_id": "insured_details.client_company_assessment", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}]}, {"card_name": "Application Details", "validation_fields": [{"field_name": "Date Received", "field_id": "application_details.date_received", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": " External Application ID", "field_id": "application_details.application_number", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "<PERSON><PERSON>", "field_id": "application_details.cedent_name", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Client Company Policy #", "field_id": "application_details.application_id", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Client Company Underwriter", "field_id": "application_details.company_underwriter", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}]}, {"card_name": "Technical Details", "validation_fields": [{"field_name": "Cedent Product Type", "field_id": "technical_details.product_type", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Lump Sum / Face Amount", "field_id": "technical_details.sum_insured", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Benefit Term Type", "field_id": "technical_details.duration_type", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Benefit Term", "field_id": "technical_details.duration", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Benefit Start Date", "field_id": "technical_details.policy_start_date", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "<PERSON><PERSON><PERSON><PERSON>", "field_id": "technical_details.currency", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Premium Payment Period", "field_id": "technical_details.payment_period", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Annuity", "field_id": "technical_details.annuity", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Annuity Duration Type", "field_id": "technical_details.annuity_duration_type", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Annuity Duration", "field_id": "technical_details.annuity_duration", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Annuity End Date", "field_id": "technical_details.annuity_end_date", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Cedent Product Name", "field_id": "technical_details.plan_type", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Cedent Location", "field_id": "technical_details.cedent_location", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Cedent Person ID", "field_id": "technical_details.cedent_person_id", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Cedent Notification Date", "field_id": "technical_details.cedent_notification_date", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "GenRe Notification Date", "field_id": "technical_details.genre_notification_date", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}]}]}, {"document_type": "Cape Town and Johannesburg Underwriting", "cards": [{"card_name": "Abnormal Observations", "validation_fields": [{"field_name": "Specimen", "field_id": "abnormal_observations.specimen", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Date", "field_id": "abnormal_observations.date", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Laboratory", "field_id": "abnormal_observations.laboratory", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "LOINC", "field_id": "abnormal_observations.loinc", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Fasting Status", "field_id": "abnormal_observations.fasting", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}]}, {"card_name": "Application Details", "validation_fields": [{"field_name": " External Application ID", "field_id": "application_details.application_number", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "<PERSON><PERSON>", "field_id": "application_details.cedent_name", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Client Company Policy #", "field_id": "application_details.application_id", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Date Received", "field_id": "application_details.date_received", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Client Company Underwriter", "field_id": "application_details.company_underwriter", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}]}, {"card_name": "Insured Details", "validation_fields": [{"field_name": "Full Name", "field_id": "insured_details.name", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "First Name", "field_id": "insured_details.name.given_name", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Last Name", "field_id": "insured_details.name.family_name", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Insured Type", "field_id": "insured_details.insured_type", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Date of Birth", "field_id": "insured_details.date_of_birth", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Birth State", "field_id": "insured_details.birth_state", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Birth Country", "field_id": "insured_details.birth_country", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Gender", "field_id": "insured_details.gender", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Address", "field_id": "insured_details.address", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Address: Street Line", "field_id": "insured_details.address.line", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Address: City", "field_id": "insured_details.address.city", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Address: State", "field_id": "insured_details.address.state", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Address: Zip Code", "field_id": "insured_details.address.zipcode", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Address: Country", "field_id": "insured_details.address.country", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Marital Status", "field_id": "insured_details.marital_status", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Occupation", "field_id": "insured_details.occupation", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Weight", "field_id": "insured_details.weight", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Height", "field_id": "insured_details.height", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Alcohol", "field_id": "insured_details.alcohol", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Smoker Status", "field_id": "insured_details.smoking", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Drugs", "field_id": "insured_details.drugs", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Avocation", "field_id": "insured_details.avocation", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Travel", "field_id": "insured_details.travel", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Behavior", "field_id": "insured_details.behavior", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Social Security Number", "field_id": "insured_details.ssn", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Owner Relationship", "field_id": "insured_details.owner_relationship", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Beneficiary Relationship", "field_id": "insured_details.beneficiary_relationship", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Citizen of", "field_id": "insured_details.citizenship", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Citizen of the US?", "field_id": "insured_details.us_citizen", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Resident of", "field_id": "insured_details.residentship", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Resident of the US?", "field_id": "insured_details.us_resident", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Driver's License #", "field_id": "insured_details.license_number", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Driver's License State", "field_id": "insured_details.license_state", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Income / Salary", "field_id": "insured_details.income", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Net Worth", "field_id": "insured_details.net_worth", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Application Sign Date", "field_id": "insured_details.sign_date", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Application Sign State", "field_id": "insured_details.sign_state", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Client Company Assessment", "field_id": "insured_details.client_company_assessment", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}]}, {"card_name": "Technical Details", "validation_fields": [{"field_name": "Cedent Product Type", "field_id": "technical_details.product_type", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Lump Sum / Face Amount", "field_id": "technical_details.sum_insured", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Benefit Term Type", "field_id": "technical_details.duration_type", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Benefit Term", "field_id": "technical_details.duration", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Benefit Start Date", "field_id": "technical_details.policy_start_date", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "<PERSON><PERSON><PERSON><PERSON>", "field_id": "technical_details.currency", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Premium Payment Period", "field_id": "technical_details.payment_period", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Annuity", "field_id": "technical_details.annuity", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Annuity Duration Type", "field_id": "technical_details.annuity_duration_type", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Annuity Duration", "field_id": "technical_details.annuity_duration", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Annuity End Date", "field_id": "technical_details.annuity_end_date", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Cedent Product Name", "field_id": "technical_details.plan_type", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Cedent Location", "field_id": "technical_details.cedent_location", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Cedent Person ID", "field_id": "technical_details.cedent_person_id", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Cedent Notification Date", "field_id": "technical_details.cedent_notification_date", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "GenRe Notification Date", "field_id": "technical_details.genre_notification_date", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "<PERSON><PERSON><PERSON> Amou<PERSON>", "field_id": "technical_details.premium_amount", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Reinsurance Amount", "field_id": "technical_details.reinsurance_amount", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Ultimate Risk Amount", "field_id": "technical_details.risk_amount", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Indexation", "field_id": "technical_details.indexation", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Waiting/Deferred Period", "field_id": "technical_details.deferment_period", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}]}]}, {"document_type": "North America", "cards": [{"card_name": "Abnormal Observations", "validation_fields": [{"field_name": "Name", "field_id": "abnormal_observations.name", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Value", "field_id": "abnormal_observations.value", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Low", "field_id": "abnormal_observations.low", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Panel", "field_id": "abnormal_observations.panel", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Specimen", "field_id": "abnormal_observations.specimen", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Date", "field_id": "abnormal_observations.date", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Laboratory", "field_id": "abnormal_observations.laboratory", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "LOINC", "field_id": "abnormal_observations.loinc", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Fasting Status", "field_id": "abnormal_observations.fasting", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Rating", "field_id": "abnormal_observations.rating", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "High", "field_id": "abnormal_observations.high", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Descriptive Reference", "field_id": "abnormal_observations.descriptive_reference", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Unit", "field_id": "abnormal_observations.units", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}]}, {"card_name": "Insured Details", "validation_fields": [{"field_name": "First Name", "field_id": "insured_details.name.given_name", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Last Name", "field_id": "insured_details.name.family_name", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Birth Country", "field_id": "insured_details.birth_country", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Gender", "field_id": "insured_details.gender", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Address: Zip Code", "field_id": "insured_details.address.zipcode", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Marital Status", "field_id": "insured_details.marital_status", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Occupation", "field_id": "insured_details.occupation", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Weight", "field_id": "insured_details.weight", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Height", "field_id": "insured_details.height", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Alcohol", "field_id": "insured_details.alcohol", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Drugs", "field_id": "insured_details.drugs", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Avocation", "field_id": "insured_details.avocation", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Travel", "field_id": "insured_details.travel", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Behavior", "field_id": "insured_details.behavior", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Social Security Number", "field_id": "insured_details.ssn", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Owner Relationship", "field_id": "insured_details.owner_relationship", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Beneficiary Relationship", "field_id": "insured_details.beneficiary_relationship", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Citizen of", "field_id": "insured_details.citizenship", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Resident of", "field_id": "insured_details.residentship", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Driver's License #", "field_id": "insured_details.license_number", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Driver's License State", "field_id": "insured_details.license_state", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Citizen of the US?", "field_id": "insured_details.us_citizen", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Application Sign Date", "field_id": "insured_details.sign_date", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Full Name", "field_id": "insured_details.name", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Address", "field_id": "insured_details.address", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Address: Street Line", "field_id": "insured_details.address.line", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Address: City", "field_id": "insured_details.address.city", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Address: State", "field_id": "insured_details.address.state", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Address: Country", "field_id": "insured_details.address.country", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Insured Type", "field_id": "insured_details.insured_type", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Date of Birth", "field_id": "insured_details.date_of_birth", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Birth State", "field_id": "insured_details.birth_state", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Smoker Status", "field_id": "insured_details.smoking", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Resident of the US?", "field_id": "insured_details.us_resident", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Income / Salary", "field_id": "insured_details.income", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Application Sign State", "field_id": "insured_details.sign_state", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Client Company Assessment", "field_id": "insured_details.client_company_assessment", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}]}, {"card_name": "Application Details", "validation_fields": [{"field_name": "Date Received", "field_id": "application_details.date_received", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": " External Application ID", "field_id": "application_details.application_number", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "<PERSON><PERSON>", "field_id": "application_details.cedent_name", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Client Company Policy #", "field_id": "application_details.application_id", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Client Company Underwriter", "field_id": "application_details.company_underwriter", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}]}, {"card_name": "Technical Details", "validation_fields": [{"field_name": "Annuity", "field_id": "technical_details.annuity", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "<PERSON><PERSON><PERSON><PERSON>", "field_id": "technical_details.currency", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Annuity Duration Type", "field_id": "technical_details.annuity_duration_type", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Annuity Duration", "field_id": "technical_details.annuity_duration", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Annuity End Date", "field_id": "technical_details.annuity_end_date", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Cedent Location", "field_id": "technical_details.cedent_location", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Cedent Person ID", "field_id": "technical_details.cedent_person_id", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Cedent Notification Date", "field_id": "technical_details.cedent_notification_date", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "GenRe Notification Date", "field_id": "technical_details.genre_notification_date", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}]}]}, {"document_type": "ForQA_1", "cards": [{"card_name": "Insured Details", "validation_fields": [{"field_name": "Full Name", "field_id": "insured_details.name", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "First Name", "field_id": "insured_details.name.given_name", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Last Name", "field_id": "insured_details.name.family_name", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Birth Country", "field_id": "insured_details.birth_country", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Gender", "field_id": "insured_details.gender", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Address: Zip Code", "field_id": "insured_details.address.zipcode", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Marital Status", "field_id": "insured_details.marital_status", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Occupation", "field_id": "insured_details.occupation", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Weight", "field_id": "insured_details.weight", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Height", "field_id": "insured_details.height", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Alcohol", "field_id": "insured_details.alcohol", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Drugs", "field_id": "insured_details.drugs", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Avocation", "field_id": "insured_details.avocation", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Travel", "field_id": "insured_details.travel", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Behavior", "field_id": "insured_details.behavior", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Social Security Number", "field_id": "insured_details.ssn", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Owner Relationship", "field_id": "insured_details.owner_relationship", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Beneficiary Relationship", "field_id": "insured_details.beneficiary_relationship", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Citizen of", "field_id": "insured_details.citizenship", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Resident of", "field_id": "insured_details.residentship", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Driver's License #", "field_id": "insured_details.license_number", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Driver's License State", "field_id": "insured_details.license_state", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Citizen of the US?", "field_id": "insured_details.us_citizen", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Application Sign Date", "field_id": "insured_details.sign_date", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}]}]}, {"document_type": "For_QA_Activities", "cards": [{"card_name": "Abnormal Observations", "validation_fields": [{"field_name": "Name", "field_id": "abnormal_observations.name", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Value", "field_id": "abnormal_observations.value", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Rating", "field_id": "abnormal_observations.rating", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Low", "field_id": "abnormal_observations.low", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "High", "field_id": "abnormal_observations.high", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Descriptive Reference", "field_id": "abnormal_observations.descriptive_reference", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Unit", "field_id": "abnormal_observations.units", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Panel", "field_id": "abnormal_observations.panel", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Specimen", "field_id": "abnormal_observations.specimen", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Date", "field_id": "abnormal_observations.date", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Laboratory", "field_id": "abnormal_observations.laboratory", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "LOINC", "field_id": "abnormal_observations.loinc", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Fasting Status", "field_id": "abnormal_observations.fasting", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}]}, {"card_name": "Patient Details", "validation_fields": [{"field_name": "Given Name", "field_id": "patient_details.given_name", "match_type": "non-empty", "high": null, "low": null, "fieldConfidence": 100.0, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Family Name", "field_id": "patient_details.family_name", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Date of Birth", "field_id": "patient_details.date_of_birth", "match_type": "date", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Place of Birth", "field_id": "patient_details.place_of_birth", "match_type": "non-empty", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Gender", "field_id": "patient_details.gender", "match_type": "non-empty", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Street Line", "field_id": "patient_details.street_line", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "City", "field_id": "patient_details.city", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "State", "field_id": "patient_details.state", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Country", "field_id": "patient_details.country", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Zipcode", "field_id": "patient_details.zipcode", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Marital Status", "field_id": "patient_details.marital_status", "match_type": "non-empty", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Occupation", "field_id": "patient_details.occupation", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Weight", "field_id": "patient_details.weight", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Height", "field_id": "patient_details.height", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}]}, {"card_name": "Insured Details", "validation_fields": [{"field_name": "Full Name", "field_id": "insured_details.name", "match_type": "non-empty", "high": null, "low": null, "fieldConfidence": null, "message": "Full Name is required , field can not be blank", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "First Name", "field_id": "insured_details.name.given_name", "match_type": "non-empty", "high": null, "low": null, "fieldConfidence": null, "message": "First Name is required , field can not be blank", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Last Name", "field_id": "insured_details.name.family_name", "match_type": "non-empty", "high": null, "low": null, "fieldConfidence": null, "message": "Last Name is required , field can not be blank", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Birth Country", "field_id": "insured_details.birth_country", "match_type": "non-empty", "high": null, "low": null, "fieldConfidence": null, "message": "Birth Country is required , field can not be blank", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Gender", "field_id": "insured_details.gender", "match_type": "non-empty", "high": null, "low": null, "fieldConfidence": null, "message": "Gender is required , field can not be blank", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Address", "field_id": "insured_details.address", "match_type": "non-empty", "high": null, "low": null, "fieldConfidence": null, "message": "Address is required , field can not be blank", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Address: Street Line", "field_id": "insured_details.address.line", "match_type": "non-empty", "high": null, "low": null, "fieldConfidence": null, "message": "Address: Street Line is required , field can not be blank", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Address: City", "field_id": "insured_details.address.city", "match_type": "non-empty", "high": null, "low": null, "fieldConfidence": null, "message": "Address: City is required , field can not be blank", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Address: State", "field_id": "insured_details.address.state", "match_type": "non-empty", "high": null, "low": null, "fieldConfidence": null, "message": "Address: State is required , field can not be blank", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Address: Zip Code", "field_id": "insured_details.address.zipcode", "match_type": "non-empty", "high": null, "low": null, "fieldConfidence": null, "message": "Address: State is required , field can not be blank", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Address: Country", "field_id": "insured_details.address.country", "match_type": "non-empty", "high": null, "low": null, "fieldConfidence": null, "message": "Address: Country is required , field can not be blank", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Marital Status", "field_id": "insured_details.marital_status", "match_type": "non-empty", "high": null, "low": null, "fieldConfidence": null, "message": "Marital Status is required , field can not be blank", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Occupation", "field_id": "insured_details.occupation", "match_type": "non-empty", "high": null, "low": null, "fieldConfidence": null, "message": "Occupation is required , field can not be blank", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Weight", "field_id": "insured_details.weight", "match_type": "range", "high": 100, "low": 0, "fieldConfidence": null, "message": "Weight is required , field can not be blank", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Height", "field_id": "insured_details.height", "match_type": "range", "high": 100, "low": 0, "fieldConfidence": null, "message": "Height is required , field can not be blank", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Alcohol", "field_id": "insured_details.alcohol", "match_type": "non-empty", "high": null, "low": null, "fieldConfidence": null, "message": "Alcohol is required , field can not be blank", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Drugs", "field_id": "insured_details.drugs", "match_type": "non-empty", "high": null, "low": null, "fieldConfidence": null, "message": "Drugs is required , field can not be blank", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Avocation", "field_id": "insured_details.avocation", "match_type": "non-empty", "high": null, "low": null, "fieldConfidence": null, "message": "Avocationis required , field can not be blank", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Travel", "field_id": "insured_details.travel", "match_type": "non-empty", "high": null, "low": null, "fieldConfidence": null, "message": "Travel is required , field can not be blank", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Behavior", "field_id": "insured_details.behavior", "match_type": "non-empty", "high": null, "low": null, "fieldConfidence": null, "message": "Behavior  is required , field can not be blank\n", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Social Security Number", "field_id": "insured_details.ssn", "match_type": "non-empty", "high": null, "low": null, "fieldConfidence": null, "message": "Social Security Number   is required , field can not be blank", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Owner Relationship", "field_id": "insured_details.owner_relationship", "match_type": "non-empty", "high": null, "low": null, "fieldConfidence": null, "message": "Owner Relationship  is required , field can not be blank", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Beneficiary Relationship", "field_id": "insured_details.beneficiary_relationship", "match_type": "non-empty", "high": null, "low": null, "fieldConfidence": null, "message": "Beneficiary Relationship  is required , field can not be blank", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Citizen of", "field_id": "insured_details.citizenship", "match_type": "non-empty", "high": null, "low": null, "fieldConfidence": null, "message": "Citizen of  is required , field can not be blank\n", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Resident of", "field_id": "insured_details.residentship", "match_type": "non-empty", "high": null, "low": null, "fieldConfidence": null, "message": "Resident of  is required , field can not be blank", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Driver's License #", "field_id": "insured_details.license_number", "match_type": "non-empty", "high": null, "low": null, "fieldConfidence": null, "message": "Driver's License #   is required , field can not be blank\n", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Driver's License State", "field_id": "insured_details.license_state", "match_type": "non-empty", "high": null, "low": null, "fieldConfidence": null, "message": "Driver's License State  is required , field can not be blank", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Citizen of the US?", "field_id": "insured_details.us_citizen", "match_type": "non-empty", "high": null, "low": null, "fieldConfidence": null, "message": "Citizen of the US?  is required , field can not be blank\n", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Application Sign Date", "field_id": "insured_details.sign_date", "match_type": "non-empty", "high": null, "low": null, "fieldConfidence": null, "message": "Application Sign Date  is required , field can not be blank", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Insured Type", "field_id": "insured_details.insured_type", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Date of Birth", "field_id": "insured_details.date_of_birth", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Birth State", "field_id": "insured_details.birth_state", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Application Sign State", "field_id": "insured_details.sign_state", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Client Company Assessment", "field_id": "insured_details.client_company_assessment", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}]}, {"card_name": "Allergies", "validation_fields": [{"field_name": "Unit", "field_id": "allergies.unit", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Test Value", "field_id": "allergies.test_value", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}]}, {"card_name": "Technical Details", "validation_fields": [{"field_name": "<PERSON><PERSON><PERSON><PERSON>", "field_id": "technical_details.currency", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Annuity", "field_id": "technical_details.annuity", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Annuity Duration Type", "field_id": "technical_details.annuity_duration_type", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Annuity Duration", "field_id": "technical_details.annuity_duration", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Annuity End Date", "field_id": "technical_details.annuity_end_date", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Cedent Product Name", "field_id": "technical_details.plan_type", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Cedent Location", "field_id": "technical_details.cedent_location", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Cedent Person ID", "field_id": "technical_details.cedent_person_id", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Cedent Notification Date", "field_id": "technical_details.cedent_notification_date", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "GenRe Notification Date", "field_id": "technical_details.genre_notification_date", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}]}, {"card_name": "Application Details", "validation_fields": [{"field_name": " External Application ID", "field_id": "application_details.application_number", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "<PERSON><PERSON>", "field_id": "application_details.cedent_name", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Client Company Policy #", "field_id": "application_details.application_id", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Date Received", "field_id": "application_details.date_received", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Client Company Underwriter", "field_id": "application_details.company_underwriter", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}]}]}, {"document_type": "Sydney UW", "cards": [{"card_name": "Application Details", "validation_fields": [{"field_name": " External Application ID", "field_id": "application_details.application_number", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "<PERSON><PERSON>", "field_id": "application_details.cedent_name", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Client Company Policy #", "field_id": "application_details.application_id", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Date Received", "field_id": "application_details.date_received", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Client Company Underwriter", "field_id": "application_details.company_underwriter", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}]}]}, {"document_type": "London International Underwriting", "cards": [{"card_name": "Application Details", "validation_fields": [{"field_name": " External Application ID", "field_id": "application_details.application_number", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "<PERSON><PERSON>", "field_id": "application_details.cedent_name", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Client Company Policy #", "field_id": "application_details.application_id", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Date Received", "field_id": "application_details.date_received", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Client Company Underwriter", "field_id": "application_details.company_underwriter", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}]}, {"card_name": "Insured Details", "validation_fields": [{"field_name": "Full Name", "field_id": "insured_details.name", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "First Name", "field_id": "insured_details.name.given_name", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Last Name", "field_id": "insured_details.name.family_name", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Insured Type", "field_id": "insured_details.insured_type", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Date of Birth", "field_id": "insured_details.date_of_birth", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Birth State", "field_id": "insured_details.birth_state", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Birth Country", "field_id": "insured_details.birth_country", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Gender", "field_id": "insured_details.gender", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Address", "field_id": "insured_details.address", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Address: Street Line", "field_id": "insured_details.address.line", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Address: City", "field_id": "insured_details.address.city", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Address: State", "field_id": "insured_details.address.state", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Address: Zip Code", "field_id": "insured_details.address.zipcode", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Address: Country", "field_id": "insured_details.address.country", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Marital Status", "field_id": "insured_details.marital_status", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Occupation", "field_id": "insured_details.occupation", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Weight", "field_id": "insured_details.weight", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Height", "field_id": "insured_details.height", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Alcohol", "field_id": "insured_details.alcohol", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Smoker Status", "field_id": "insured_details.smoking", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Drugs", "field_id": "insured_details.drugs", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Avocation", "field_id": "insured_details.avocation", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Travel", "field_id": "insured_details.travel", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Behavior", "field_id": "insured_details.behavior", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Social Security Number", "field_id": "insured_details.ssn", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Owner Relationship", "field_id": "insured_details.owner_relationship", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Beneficiary Relationship", "field_id": "insured_details.beneficiary_relationship", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Citizen of", "field_id": "insured_details.citizenship", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Citizen of the US?", "field_id": "insured_details.us_citizen", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Resident of", "field_id": "insured_details.residentship", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Resident of the US?", "field_id": "insured_details.us_resident", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Driver's License #", "field_id": "insured_details.license_number", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Driver's License State", "field_id": "insured_details.license_state", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Income / Salary", "field_id": "insured_details.income", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Application Sign Date", "field_id": "insured_details.sign_date", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Application Sign State", "field_id": "insured_details.sign_state", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Client Company Assessment", "field_id": "insured_details.client_company_assessment", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Net Worth", "field_id": "insured_details.net_worth", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}]}, {"card_name": "Technical Details", "validation_fields": [{"field_name": "Cedent Product Type", "field_id": "technical_details.product_type", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Lump Sum / Face Amount", "field_id": "technical_details.sum_insured", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Benefit Term Type", "field_id": "technical_details.duration_type", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Benefit Term", "field_id": "technical_details.duration", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Benefit Start Date", "field_id": "technical_details.policy_start_date", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "<PERSON><PERSON><PERSON><PERSON>", "field_id": "technical_details.currency", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Premium Payment Period", "field_id": "technical_details.payment_period", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Annuity", "field_id": "technical_details.annuity", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Annuity Duration Type", "field_id": "technical_details.annuity_duration_type", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Annuity Duration", "field_id": "technical_details.annuity_duration", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Annuity End Date", "field_id": "technical_details.annuity_end_date", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Cedent Product Name", "field_id": "technical_details.plan_type", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Cedent Location", "field_id": "technical_details.cedent_location", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Cedent Person ID", "field_id": "technical_details.cedent_person_id", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Cedent Notification Date", "field_id": "technical_details.cedent_notification_date", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "GenRe Notification Date", "field_id": "technical_details.genre_notification_date", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "<PERSON><PERSON><PERSON> Amou<PERSON>", "field_id": "technical_details.premium_amount", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Reinsurance Amount", "field_id": "technical_details.reinsurance_amount", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Ultimate Risk Amount", "field_id": "technical_details.risk_amount", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Indexation", "field_id": "technical_details.indexation", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Waiting/Deferred Period", "field_id": "technical_details.deferment_period", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}]}]}, {"document_type": "test-new-dedup", "cards": [{"card_name": "Insured Details", "validation_fields": [{"field_name": "Full Name", "field_id": "insured_details.name", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "First Name", "field_id": "insured_details.name.given_name", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Last Name", "field_id": "insured_details.name.family_name", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Insured Type", "field_id": "insured_details.insured_type", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Date of Birth", "field_id": "insured_details.date_of_birth", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Birth State", "field_id": "insured_details.birth_state", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Birth Country", "field_id": "insured_details.birth_country", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Gender", "field_id": "insured_details.gender", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Address", "field_id": "insured_details.address", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Address: Street Line", "field_id": "insured_details.address.line", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Address: City", "field_id": "insured_details.address.city", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Address: State", "field_id": "insured_details.address.state", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Address: Zip Code", "field_id": "insured_details.address.zipcode", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Address: Country", "field_id": "insured_details.address.country", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Marital Status", "field_id": "insured_details.marital_status", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Occupation", "field_id": "insured_details.occupation", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Weight", "field_id": "insured_details.weight", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Height", "field_id": "insured_details.height", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Alcohol", "field_id": "insured_details.alcohol", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Smoker Status", "field_id": "insured_details.smoking", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Drugs", "field_id": "insured_details.drugs", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Avocation", "field_id": "insured_details.avocation", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Travel", "field_id": "insured_details.travel", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Behavior", "field_id": "insured_details.behavior", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Social Security Number", "field_id": "insured_details.ssn", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Owner Relationship", "field_id": "insured_details.owner_relationship", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Beneficiary Relationship", "field_id": "insured_details.beneficiary_relationship", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Citizen of", "field_id": "insured_details.citizenship", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Citizen of the US?", "field_id": "insured_details.us_citizen", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Resident of", "field_id": "insured_details.residentship", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Resident of the US?", "field_id": "insured_details.us_resident", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Driver's License #", "field_id": "insured_details.license_number", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Driver's License State", "field_id": "insured_details.license_state", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Income / Salary", "field_id": "insured_details.income", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Application Sign Date", "field_id": "insured_details.sign_date", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Application Sign State", "field_id": "insured_details.sign_state", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Client Company Assessment", "field_id": "insured_details.client_company_assessment", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}]}]}, {"document_type": "London UK Underwriting", "cards": [{"card_name": "Application Details", "validation_fields": [{"field_name": " External Application ID", "field_id": "application_details.application_number", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "<PERSON><PERSON>", "field_id": "application_details.cedent_name", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Client Company Policy #", "field_id": "application_details.application_id", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Date Received", "field_id": "application_details.date_received", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Client Company Underwriter", "field_id": "application_details.company_underwriter", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}]}, {"card_name": "Insured Details", "validation_fields": [{"field_name": "Full Name", "field_id": "insured_details.name", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "First Name", "field_id": "insured_details.name.given_name", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Last Name", "field_id": "insured_details.name.family_name", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Insured Type", "field_id": "insured_details.insured_type", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Date of Birth", "field_id": "insured_details.date_of_birth", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Birth State", "field_id": "insured_details.birth_state", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Birth Country", "field_id": "insured_details.birth_country", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Gender", "field_id": "insured_details.gender", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Address", "field_id": "insured_details.address", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Address: Street Line", "field_id": "insured_details.address.line", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Address: City", "field_id": "insured_details.address.city", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Address: State", "field_id": "insured_details.address.state", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Address: Zip Code", "field_id": "insured_details.address.zipcode", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Address: Country", "field_id": "insured_details.address.country", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Marital Status", "field_id": "insured_details.marital_status", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Occupation", "field_id": "insured_details.occupation", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Weight", "field_id": "insured_details.weight", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Height", "field_id": "insured_details.height", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Alcohol", "field_id": "insured_details.alcohol", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Smoker Status", "field_id": "insured_details.smoking", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Drugs", "field_id": "insured_details.drugs", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Avocation", "field_id": "insured_details.avocation", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Travel", "field_id": "insured_details.travel", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Behavior", "field_id": "insured_details.behavior", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Social Security Number", "field_id": "insured_details.ssn", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Owner Relationship", "field_id": "insured_details.owner_relationship", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Beneficiary Relationship", "field_id": "insured_details.beneficiary_relationship", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Citizen of", "field_id": "insured_details.citizenship", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Citizen of the US?", "field_id": "insured_details.us_citizen", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Resident of", "field_id": "insured_details.residentship", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Resident of the US?", "field_id": "insured_details.us_resident", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Driver's License #", "field_id": "insured_details.license_number", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Driver's License State", "field_id": "insured_details.license_state", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Income / Salary", "field_id": "insured_details.income", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Application Sign Date", "field_id": "insured_details.sign_date", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Application Sign State", "field_id": "insured_details.sign_state", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Client Company Assessment", "field_id": "insured_details.client_company_assessment", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Net Worth", "field_id": "insured_details.net_worth", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}]}, {"card_name": "Technical Details", "validation_fields": [{"field_name": "Cedent Product Type", "field_id": "technical_details.product_type", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Lump Sum / Face Amount", "field_id": "technical_details.sum_insured", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Benefit Term Type", "field_id": "technical_details.duration_type", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Benefit Term", "field_id": "technical_details.duration", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Benefit Start Date", "field_id": "technical_details.policy_start_date", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "<PERSON><PERSON><PERSON><PERSON>", "field_id": "technical_details.currency", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Premium Payment Period", "field_id": "technical_details.payment_period", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Annuity", "field_id": "technical_details.annuity", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Annuity Duration Type", "field_id": "technical_details.annuity_duration_type", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Annuity Duration", "field_id": "technical_details.annuity_duration", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Annuity End Date", "field_id": "technical_details.annuity_end_date", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Cedent Product Name", "field_id": "technical_details.plan_type", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Cedent Location", "field_id": "technical_details.cedent_location", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Cedent Person ID", "field_id": "technical_details.cedent_person_id", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Cedent Notification Date", "field_id": "technical_details.cedent_notification_date", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "GenRe Notification Date", "field_id": "technical_details.genre_notification_date", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "<PERSON><PERSON><PERSON> Amou<PERSON>", "field_id": "technical_details.premium_amount", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Reinsurance Amount", "field_id": "technical_details.reinsurance_amount", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Ultimate Risk Amount", "field_id": "technical_details.risk_amount", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Indexation", "field_id": "technical_details.indexation", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Waiting/Deferred Period", "field_id": "technical_details.deferment_period", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}]}]}, {"document_type": "New Doc Type", "cards": [{"card_name": "Allergies", "validation_fields": [{"field_name": "Unit", "field_id": "allergies.unit", "match_type": "non-empty", "high": null, "low": null, "fieldConfidence": 40.0, "message": "Field cannot be empty", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Test Value", "field_id": "allergies.test_value", "match_type": "non-empty", "high": null, "low": null, "fieldConfidence": 60.0, "message": "Field cannot be empty", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}]}, {"card_name": "Insured Details", "validation_fields": [{"field_name": "Full Name", "field_id": "insured_details.name", "match_type": "non-empty", "high": null, "low": null, "fieldConfidence": null, "message": "Field cannot be empty", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "First Name", "field_id": "insured_details.name.given_name", "match_type": "non-empty", "high": null, "low": null, "fieldConfidence": null, "message": "Field cannot be empty", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Last Name", "field_id": "insured_details.name.family_name", "match_type": "non-empty", "high": null, "low": null, "fieldConfidence": null, "message": "Field cannot be empty", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Insured Type", "field_id": "insured_details.insured_type", "match_type": "non-empty", "high": null, "low": null, "fieldConfidence": null, "message": "Field cannot be empty", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Date of Birth", "field_id": "insured_details.date_of_birth", "match_type": "non-empty", "high": null, "low": null, "fieldConfidence": null, "message": "Field cannot be empty", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Birth State", "field_id": "insured_details.birth_state", "match_type": "non-empty", "high": null, "low": null, "fieldConfidence": null, "message": "Field cannot be empty", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Birth Country", "field_id": "insured_details.birth_country", "match_type": "non-empty", "high": null, "low": null, "fieldConfidence": null, "message": "Field cannot be empty", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Gender", "field_id": "insured_details.gender", "match_type": "non-empty", "high": null, "low": null, "fieldConfidence": null, "message": "Field cannot be empty", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Address", "field_id": "insured_details.address", "match_type": "non-empty", "high": null, "low": null, "fieldConfidence": null, "message": "Field cannot be empty", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Address: Street Line", "field_id": "insured_details.address.line", "match_type": "non-empty", "high": null, "low": null, "fieldConfidence": null, "message": "Field cannot be empty", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Address: City", "field_id": "insured_details.address.city", "match_type": "non-empty", "high": null, "low": null, "fieldConfidence": null, "message": "Field cannot be empty", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Address: State", "field_id": "insured_details.address.state", "match_type": "non-empty", "high": null, "low": null, "fieldConfidence": null, "message": "Field cannot be empty", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Address: Zip Code", "field_id": "insured_details.address.zipcode", "match_type": "non-empty", "high": null, "low": null, "fieldConfidence": null, "message": "Field cannot be empty", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Address: Country", "field_id": "insured_details.address.country", "match_type": "non-empty", "high": null, "low": null, "fieldConfidence": null, "message": "Field cannot be empty", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Marital Status", "field_id": "insured_details.marital_status", "match_type": "non-empty", "high": null, "low": null, "fieldConfidence": null, "message": "Field cannot be empty", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Occupation", "field_id": "insured_details.occupation", "match_type": "non-empty", "high": null, "low": null, "fieldConfidence": null, "message": "Field cannot be empty", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Weight", "field_id": "insured_details.weight", "match_type": "non-empty", "high": null, "low": null, "fieldConfidence": null, "message": "Field cannot be empty", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Height", "field_id": "insured_details.height", "match_type": "non-empty", "high": null, "low": null, "fieldConfidence": null, "message": "Field cannot be empty", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Alcohol", "field_id": "insured_details.alcohol", "match_type": "non-empty", "high": null, "low": null, "fieldConfidence": null, "message": "Field cannot be empty", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Smoker Status", "field_id": "insured_details.smoking", "match_type": "non-empty", "high": null, "low": null, "fieldConfidence": null, "message": "Field cannot be empty", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Drugs", "field_id": "insured_details.drugs", "match_type": "non-empty", "high": null, "low": null, "fieldConfidence": null, "message": "Field cannot be empty", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Avocation", "field_id": "insured_details.avocation", "match_type": "non-empty", "high": null, "low": null, "fieldConfidence": null, "message": "Field cannot be empty", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Travel", "field_id": "insured_details.travel", "match_type": "non-empty", "high": null, "low": null, "fieldConfidence": null, "message": "Field cannot be empty", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Behavior", "field_id": "insured_details.behavior", "match_type": "non-empty", "high": null, "low": null, "fieldConfidence": null, "message": "Field cannot be empty", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Social Security Number", "field_id": "insured_details.ssn", "match_type": "non-empty", "high": null, "low": null, "fieldConfidence": null, "message": "Field cannot be empty", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Owner Relationship", "field_id": "insured_details.owner_relationship", "match_type": "non-empty", "high": null, "low": null, "fieldConfidence": null, "message": "Field cannot be empty", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Beneficiary Relationship", "field_id": "insured_details.beneficiary_relationship", "match_type": "non-empty", "high": null, "low": null, "fieldConfidence": null, "message": "Field cannot be empty", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Citizen of", "field_id": "insured_details.citizenship", "match_type": "non-empty", "high": null, "low": null, "fieldConfidence": null, "message": "Field cannot be empty", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Citizen of the US?", "field_id": "insured_details.us_citizen", "match_type": "non-empty", "high": null, "low": null, "fieldConfidence": null, "message": "Field cannot be empty", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Resident of", "field_id": "insured_details.residentship", "match_type": "non-empty", "high": null, "low": null, "fieldConfidence": null, "message": "Field cannot be empty", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Resident of the US?", "field_id": "insured_details.us_resident", "match_type": "non-empty", "high": null, "low": null, "fieldConfidence": null, "message": "Field cannot be empty", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Driver's License #", "field_id": "insured_details.license_number", "match_type": "non-empty", "high": null, "low": null, "fieldConfidence": null, "message": "Field cannot be empty", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Driver's License State", "field_id": "insured_details.license_state", "match_type": "non-empty", "high": null, "low": null, "fieldConfidence": null, "message": "Field cannot be empty", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Income / Salary", "field_id": "insured_details.income", "match_type": "non-empty", "high": null, "low": null, "fieldConfidence": null, "message": "Field cannot be empty", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Application Sign Date", "field_id": "insured_details.sign_date", "match_type": "non-empty", "high": null, "low": null, "fieldConfidence": null, "message": "Field cannot be empty", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Application Sign State", "field_id": "insured_details.sign_state", "match_type": "non-empty", "high": null, "low": null, "fieldConfidence": null, "message": "Field cannot be empty", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Client Company Assessment", "field_id": "insured_details.client_company_assessment", "match_type": "non-empty", "high": null, "low": null, "fieldConfidence": null, "message": "Field cannot be empty", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}]}, {"card_name": "Allergens", "validation_fields": [{"field_name": "Allergen", "field_id": "allergens.allergen", "match_type": "non-empty", "high": null, "low": null, "fieldConfidence": 50.0, "message": "Field cannot be empty", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Type", "field_id": "allergens.type", "match_type": "non-empty", "high": null, "low": null, "fieldConfidence": 60.0, "message": "Field cannot be empty", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Date", "field_id": "allergens.date", "match_type": "non-empty", "high": null, "low": null, "fieldConfidence": 70.0, "message": "Field cannot be empty", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "SNOMED", "field_id": "allergens.snomed", "match_type": "non-empty", "high": null, "low": null, "fieldConfidence": 80.0, "message": "Field cannot be empty", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}]}, {"card_name": "Application Details", "validation_fields": [{"field_name": " External Application ID", "field_id": "application_details.application_number", "match_type": "non-empty", "high": null, "low": null, "fieldConfidence": 50.0, "message": "Field cannot be empty", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "<PERSON><PERSON>", "field_id": "application_details.cedent_name", "match_type": "non-empty", "high": null, "low": null, "fieldConfidence": 60.0, "message": "Field cannot be empty", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Client Company Policy #", "field_id": "application_details.application_id", "match_type": "non-empty", "high": null, "low": null, "fieldConfidence": 70.0, "message": "Field cannot be empty", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Date Received", "field_id": "application_details.date_received", "match_type": "non-empty", "high": null, "low": null, "fieldConfidence": 80.0, "message": "Field cannot be empty", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Client Company Underwriter", "field_id": "application_details.company_underwriter", "match_type": "non-empty", "high": null, "low": null, "fieldConfidence": 90.0, "message": "Field cannot be empty", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}]}, {"card_name": "Beneficiary Details", "validation_fields": [{"field_name": "First Name", "field_id": "beneficiary_details.first_name", "match_type": "non-empty", "high": null, "low": null, "fieldConfidence": 10.0, "message": "Field cannot be empty", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Family Name ", "field_id": "beneficiary_details.family_name_", "match_type": "non-empty", "high": null, "low": null, "fieldConfidence": 20.0, "message": "Field cannot be empty", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Date of Birth", "field_id": "beneficiary_details.date_of_birth", "match_type": "non-empty", "high": null, "low": null, "fieldConfidence": 30.0, "message": "Field cannot be empty", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Address Line", "field_id": "beneficiary_details.address_line", "match_type": "non-empty", "high": null, "low": null, "fieldConfidence": 40.0, "message": "Field cannot be empty", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Address City", "field_id": "beneficiary_details.address_city", "match_type": "non-empty", "high": null, "low": null, "fieldConfidence": 50.0, "message": "Field cannot be empty", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Address State", "field_id": "beneficiary_details.address_state", "match_type": "non-empty", "high": null, "low": null, "fieldConfidence": 60.0, "message": "Field cannot be empty", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "ZipCode", "field_id": "beneficiary_details.zipcode", "match_type": "non-empty", "high": null, "low": null, "fieldConfidence": 70.0, "message": "Field cannot be empty", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Deceased's Occupation", "field_id": "beneficiary_details.deceased's_occupation", "match_type": "non-empty", "high": null, "low": null, "fieldConfidence": 80.0, "message": "Field cannot be empty", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Death State", "field_id": "beneficiary_details.death_state", "match_type": "non-empty", "high": null, "low": null, "fieldConfidence": 90.0, "message": "Field cannot be empty", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}]}, {"card_name": "<PERSON><PERSON><PERSON>", "validation_fields": [{"field_name": "Policy Number", "field_id": "claim_details.policy_number", "match_type": "non-empty", "high": null, "low": null, "fieldConfidence": 10.0, "message": "Field cannot be empty", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Claim Number", "field_id": "claim_details.claim_number", "match_type": "non-empty", "high": null, "low": null, "fieldConfidence": 20.0, "message": "Field cannot be empty", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Policy Effective Date", "field_id": "claim_details.policy_effective_date", "match_type": "non-empty", "high": null, "low": null, "fieldConfidence": 30.0, "message": "Field cannot be empty", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Claimed Date of Loss", "field_id": "claim_details.date_of_loss", "match_type": "non-empty", "high": null, "low": null, "fieldConfidence": 40.0, "message": "Field cannot be empty", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Primary Diagnosis", "field_id": "claim_details.primary_diagnosis", "match_type": "non-empty", "high": null, "low": null, "fieldConfidence": 50.0, "message": "Field cannot be empty", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Autopsy Performed", "field_id": "claim_details.autopsy_performed", "match_type": "non-empty", "high": null, "low": null, "fieldConfidence": 60.0, "message": "Field cannot be empty", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Policy Expiration Date", "field_id": "claim_details.policy_exp_date", "match_type": "non-empty", "high": null, "low": null, "fieldConfidence": 70.0, "message": "Field cannot be empty", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Original sum at risk", "field_id": "claim_details.sum_at_risk", "match_type": "non-empty", "high": null, "low": null, "fieldConfidence": 80.0, "message": "Field cannot be empty", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}]}, {"card_name": "<PERSON><PERSON><PERSON>", "validation_fields": [{"field_name": "Type Of Service", "field_id": "claims.type_of_service", "match_type": "non-empty", "high": null, "low": null, "fieldConfidence": 10.0, "message": "Field cannot be empty", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Date Of Service", "field_id": "claims.date_of_service", "match_type": "non-empty", "high": null, "low": null, "fieldConfidence": 20.0, "message": "Field cannot be empty", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "COB Amount", "field_id": "claims.cob_amount", "match_type": "non-empty", "high": null, "low": null, "fieldConfidence": 30.0, "message": "Field cannot be empty", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Quantity", "field_id": "claims.quantity", "match_type": "non-empty", "high": null, "low": null, "fieldConfidence": 40.0, "message": "Field cannot be empty", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Patient Name", "field_id": "claims.patient_name", "match_type": "non-empty", "high": null, "low": null, "fieldConfidence": 50.0, "message": "Field cannot be empty", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Total Amount", "field_id": "claims.total_amount", "match_type": "non-empty", "high": null, "low": null, "fieldConfidence": 60.0, "message": "Field cannot be empty", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Doctor Name", "field_id": "claims.doctor_name", "match_type": "non-empty", "high": null, "low": null, "fieldConfidence": 70.0, "message": "Field cannot be empty", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}]}, {"card_name": "Death claims", "validation_fields": [{"field_name": "Beneficiary name(given)", "field_id": "death_claims.beneficiary_name(given)", "match_type": "non-empty", "high": null, "low": null, "fieldConfidence": 10.0, "message": "Field cannot be empty", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Beneficiary name(family)", "field_id": "death_claims.beneficiary_name(family)", "match_type": "non-empty", "high": null, "low": null, "fieldConfidence": 20.0, "message": "Field cannot be empty", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Beneficiary date of birth", "field_id": "death_claims.beneficiary_date_of_birth", "match_type": "non-empty", "high": null, "low": null, "fieldConfidence": 30.0, "message": "Field cannot be empty", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Beneficiary state of residence", "field_id": "death_claims.beneficiary_state_of_residence", "match_type": "non-empty", "high": null, "low": null, "fieldConfidence": 40.0, "message": "Field cannot be empty", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Beneficiary country of residence", "field_id": "death_claims.beneficiary_country_of_residence", "match_type": "non-empty", "high": null, "low": null, "fieldConfidence": 50.0, "message": "Field cannot be empty", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}]}, {"card_name": "Dental Details", "validation_fields": [{"field_name": "Type Of Service", "field_id": "dental_details.type_of_service", "match_type": "non-empty", "high": null, "low": null, "fieldConfidence": 10.0, "message": "Field cannot be empty", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Date Of Service.date_of_service", "field_id": "dental_details.", "match_type": "non-empty", "high": null, "low": null, "fieldConfidence": 20.0, "message": "Field cannot be empty", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Tooth Code", "field_id": "dental_details.tooth_code", "match_type": "non-empty", "high": null, "low": null, "fieldConfidence": 30.0, "message": "Field cannot be empty", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Tooth Surfaces", "field_id": "dental_details.tooth_surfaces", "match_type": "non-empty", "high": null, "low": null, "fieldConfidence": 40.0, "message": "Field cannot be empty", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "COB Amount", "field_id": "dental_details.cob_amount", "match_type": "non-empty", "high": null, "low": null, "fieldConfidence": 50.0, "message": "Field cannot be empty", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Quantity", "field_id": "dental_details.quantity", "match_type": "non-empty", "high": null, "low": null, "fieldConfidence": 60.0, "message": "Field cannot be empty", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Lab Charge", "field_id": "dental_details.lab_charge", "match_type": "non-empty", "high": null, "low": null, "fieldConfidence": 70.0, "message": "Field cannot be empty", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Total Amount", "field_id": "dental_details.total_amount", "match_type": "non-empty", "high": null, "low": null, "fieldConfidence": 80.0, "message": "Field cannot be empty", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Procedure Code", "field_id": "dental_details.procedure_code", "match_type": "non-empty", "high": null, "low": null, "fieldConfidence": 90.0, "message": "Field cannot be empty", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Tax", "field_id": "dental_details.tax", "match_type": "non-empty", "high": null, "low": null, "fieldConfidence": 80.0, "message": "Field cannot be empty", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Units", "field_id": "dental_details.units", "match_type": "non-empty", "high": null, "low": null, "fieldConfidence": 80.0, "message": "Field cannot be empty", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}]}, {"card_name": "Diabetes Management", "validation_fields": [{"field_name": "Date", "field_id": "diabetes_management.date", "match_type": "non-empty", "high": null, "low": null, "fieldConfidence": 10.0, "message": "Field cannot be empty", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Treatment Compliance", "field_id": "diabetes_management.treatment_compliance", "match_type": "non-empty", "high": null, "low": null, "fieldConfidence": 20.0, "message": "Field cannot be empty", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Blood sugar testing", "field_id": "diabetes_management.blood_sugar_testing", "match_type": "non-empty", "high": null, "low": null, "fieldConfidence": 30.0, "message": "Field cannot be empty", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Urine testing", "field_id": "diabetes_management.urine_testing", "match_type": "non-empty", "high": null, "low": null, "fieldConfidence": 40.0, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Blood sugar plan compliance", "field_id": "diabetes_management.blood_sugar_plan_compliance", "match_type": "non-empty", "high": null, "low": null, "fieldConfidence": 50.0, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Current diet", "field_id": "diabetes_management.current_diet", "match_type": "non-empty", "high": null, "low": null, "fieldConfidence": 60.0, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Meal planning", "field_id": "diabetes_management.meal_planning", "match_type": "non-empty", "high": null, "low": null, "fieldConfidence": 70.0, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Exercise", "field_id": "diabetes_management.exercise", "match_type": "non-empty", "high": null, "low": null, "fieldConfidence": 80.0, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Dietician visit", "field_id": "diabetes_management.dietician_visit", "match_type": "non-empty", "high": null, "low": null, "fieldConfidence": 90.0, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Eye exam", "field_id": "diabetes_management.eye_exam", "match_type": "non-empty", "high": null, "low": null, "fieldConfidence": 10.0, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Podiatrist visit", "field_id": "diabetes_management.podiatrist_visit", "match_type": "non-empty", "high": null, "low": null, "fieldConfidence": 20.0, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Blood sugar trend", "field_id": "diabetes_management.blood_sugar_trend", "match_type": "non-empty", "high": null, "low": null, "fieldConfidence": 30.0, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Blood Sugar Level (Breakfast)", "field_id": "diabetes_management.blood_sugar_level_(breakfast)", "match_type": "non-empty", "high": null, "low": null, "fieldConfidence": 40.0, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Blood Sugar Level (Lunch)", "field_id": "diabetes_management.blood_sugar_level_(lunch)", "match_type": "non-empty", "high": null, "low": null, "fieldConfidence": 50.0, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Blood Sugar Level (Dinner)", "field_id": "diabetes_management.blood_sugar_level_(dinner)", "match_type": "non-empty", "high": null, "low": null, "fieldConfidence": 60.0, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Blood Sugar Level (High)", "field_id": "diabetes_management.blood_sugar_level_(high)", "match_type": "non-empty", "high": null, "low": null, "fieldConfidence": 70.0, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Blood Sugar Level (Overall)", "field_id": "diabetes_management.blood_sugar_level_(overall)", "match_type": "non-empty", "high": null, "low": null, "fieldConfidence": 80.0, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Weight trend", "field_id": "diabetes_management.weight_trend", "match_type": "non-empty", "high": null, "low": null, "fieldConfidence": 90.0, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}]}]}, {"document_type": "<PERSON><PERSON><PERSON>", "cards": [{"card_name": "Application Details", "validation_fields": [{"field_name": " External Application ID", "field_id": "application_details.application_number", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "<PERSON><PERSON>", "field_id": "application_details.cedent_name", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Client Company Policy #", "field_id": "application_details.application_id", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Date Received", "field_id": "application_details.date_received", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Client Company Underwriter", "field_id": "application_details.company_underwriter", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}]}, {"card_name": "Insured Details", "validation_fields": [{"field_name": "Full Name", "field_id": "insured_details.name", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "First Name", "field_id": "insured_details.name.given_name", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Last Name", "field_id": "insured_details.name.family_name", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Insured Type", "field_id": "insured_details.insured_type", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Date of Birth", "field_id": "insured_details.date_of_birth", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Birth State", "field_id": "insured_details.birth_state", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Birth Country", "field_id": "insured_details.birth_country", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Gender", "field_id": "insured_details.gender", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Address", "field_id": "insured_details.address", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Address: Street Line", "field_id": "insured_details.address.line", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Address: City", "field_id": "insured_details.address.city", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Address: State", "field_id": "insured_details.address.state", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Address: Zip Code", "field_id": "insured_details.address.zipcode", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Address: Country", "field_id": "insured_details.address.country", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Marital Status", "field_id": "insured_details.marital_status", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Occupation", "field_id": "insured_details.occupation", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Weight", "field_id": "insured_details.weight", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Height", "field_id": "insured_details.height", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Alcohol", "field_id": "insured_details.alcohol", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Smoker Status", "field_id": "insured_details.smoking", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Drugs", "field_id": "insured_details.drugs", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Avocation", "field_id": "insured_details.avocation", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Travel", "field_id": "insured_details.travel", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Behavior", "field_id": "insured_details.behavior", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Social Security Number", "field_id": "insured_details.ssn", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Owner Relationship", "field_id": "insured_details.owner_relationship", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Beneficiary Relationship", "field_id": "insured_details.beneficiary_relationship", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Citizen of", "field_id": "insured_details.citizenship", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Citizen of the US?", "field_id": "insured_details.us_citizen", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Resident of", "field_id": "insured_details.residentship", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Resident of the US?", "field_id": "insured_details.us_resident", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Driver's License #", "field_id": "insured_details.license_number", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Driver's License State", "field_id": "insured_details.license_state", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Income / Salary", "field_id": "insured_details.income", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Net Worth", "field_id": "insured_details.net_worth", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Application Sign Date", "field_id": "insured_details.sign_date", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Application Sign State", "field_id": "insured_details.sign_state", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Client Company Assessment", "field_id": "insured_details.client_company_assessment", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}]}]}, {"document_type": "Mumbai Underwriting", "cards": [{"card_name": "Application Details", "validation_fields": [{"field_name": " External Application ID", "field_id": "application_details.application_number", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "<PERSON><PERSON>", "field_id": "application_details.cedent_name", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Client Company Policy #", "field_id": "application_details.application_id", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Date Received", "field_id": "application_details.date_received", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Client Company Underwriter", "field_id": "application_details.company_underwriter", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}]}, {"card_name": "Insured Details", "validation_fields": [{"field_name": "Full Name", "field_id": "insured_details.name", "match_type": "non-empty", "high": null, "low": null, "fieldConfidence": 85.0, "message": "Name is required", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "First Name", "field_id": "insured_details.name.given_name", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Last Name", "field_id": "insured_details.name.family_name", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Insured Type", "field_id": "insured_details.insured_type", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Date of Birth", "field_id": "insured_details.date_of_birth", "match_type": "non-empty", "high": null, "low": null, "fieldConfidence": 85.0, "message": "Date of Birth is required", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Birth State", "field_id": "insured_details.birth_state", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Birth Country", "field_id": "insured_details.birth_country", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Gender", "field_id": "insured_details.gender", "match_type": "non-empty", "high": null, "low": null, "fieldConfidence": 85.0, "message": "Gender is required ", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Address", "field_id": "insured_details.address", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Address: Street Line", "field_id": "insured_details.address.line", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Address: City", "field_id": "insured_details.address.city", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Address: State", "field_id": "insured_details.address.state", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Address: Zip Code", "field_id": "insured_details.address.zipcode", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Address: Country", "field_id": "insured_details.address.country", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Marital Status", "field_id": "insured_details.marital_status", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Occupation", "field_id": "insured_details.occupation", "match_type": "non-empty", "high": null, "low": null, "fieldConfidence": 85.0, "message": "occupation is required", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Weight", "field_id": "insured_details.weight", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Height", "field_id": "insured_details.height", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Alcohol", "field_id": "insured_details.alcohol", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Smoker Status", "field_id": "insured_details.smoking", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Drugs", "field_id": "insured_details.drugs", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Avocation", "field_id": "insured_details.avocation", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Travel", "field_id": "insured_details.travel", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Behavior", "field_id": "insured_details.behavior", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Social Security Number", "field_id": "insured_details.ssn", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Owner Relationship", "field_id": "insured_details.owner_relationship", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Beneficiary Relationship", "field_id": "insured_details.beneficiary_relationship", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Citizen of", "field_id": "insured_details.citizenship", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Citizen of the US?", "field_id": "insured_details.us_citizen", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Resident of", "field_id": "insured_details.residentship", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Resident of the US?", "field_id": "insured_details.us_resident", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Driver's License #", "field_id": "insured_details.license_number", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Driver's License State", "field_id": "insured_details.license_state", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Income / Salary", "field_id": "insured_details.income", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Net Worth", "field_id": "insured_details.net_worth", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Application Sign Date", "field_id": "insured_details.sign_date", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Application Sign State", "field_id": "insured_details.sign_state", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Client Company Assessment", "field_id": "insured_details.client_company_assessment", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}]}, {"card_name": "Technical Details", "validation_fields": [{"field_name": "Cedent Product Type", "field_id": "technical_details.product_type", "match_type": "non-empty", "high": null, "low": null, "fieldConfidence": 85.0, "message": "Producttype is required ", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Lump Sum / Face Amount", "field_id": "technical_details.sum_insured", "match_type": "non-empty", "high": null, "low": null, "fieldConfidence": 85.0, "message": "Lum sum is required ", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Benefit Term Type", "field_id": "technical_details.duration_type", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Benefit Term", "field_id": "technical_details.duration", "match_type": "non-empty", "high": null, "low": null, "fieldConfidence": 85.0, "message": "is required ", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Benefit Start Date", "field_id": "technical_details.policy_start_date", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "<PERSON><PERSON><PERSON><PERSON>", "field_id": "technical_details.currency", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Premium Payment Period", "field_id": "technical_details.payment_period", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Annuity", "field_id": "technical_details.annuity", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Annuity Duration Type", "field_id": "technical_details.annuity_duration_type", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Annuity Duration", "field_id": "technical_details.annuity_duration", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Annuity End Date", "field_id": "technical_details.annuity_end_date", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Cedent Product Name", "field_id": "technical_details.plan_type", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Cedent Location", "field_id": "technical_details.cedent_location", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Cedent Person ID", "field_id": "technical_details.cedent_person_id", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Cedent Notification Date", "field_id": "technical_details.cedent_notification_date", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "GenRe Notification Date", "field_id": "technical_details.genre_notification_date", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "<PERSON><PERSON><PERSON> Amou<PERSON>", "field_id": "technical_details.premium_amount", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Reinsurance Amount", "field_id": "technical_details.reinsurance_amount", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Ultimate Risk Amount", "field_id": "technical_details.risk_amount", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Indexation", "field_id": "technical_details.indexation", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Waiting/Deferred Period", "field_id": "technical_details.deferment_period", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}]}]}, {"document_type": "prajwal_test", "cards": [{"card_name": "Application Details", "validation_fields": [{"field_name": " External Application ID", "field_id": "application_details.application_number", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "<PERSON><PERSON>", "field_id": "application_details.cedent_name", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Client Company Policy #", "field_id": "application_details.application_id", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Date Received", "field_id": "application_details.date_received", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Client Company Underwriter", "field_id": "application_details.company_underwriter", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}]}, {"card_name": "Technical Details", "validation_fields": [{"field_name": "Cedent Product Type", "field_id": "technical_details.product_type", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Lump Sum / Face Amount", "field_id": "technical_details.sum_insured", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Benefit Term Type", "field_id": "technical_details.duration_type", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Benefit Term", "field_id": "technical_details.duration", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Benefit Start Date", "field_id": "technical_details.policy_start_date", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "<PERSON><PERSON><PERSON><PERSON>", "field_id": "technical_details.currency", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Premium Payment Period", "field_id": "technical_details.payment_period", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Annuity", "field_id": "technical_details.annuity", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Annuity Duration Type", "field_id": "technical_details.annuity_duration_type", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Annuity Duration", "field_id": "technical_details.annuity_duration", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Annuity End Date", "field_id": "technical_details.annuity_end_date", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Cedent Product Name", "field_id": "technical_details.plan_type", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Cedent Location", "field_id": "technical_details.cedent_location", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Cedent Person ID", "field_id": "technical_details.cedent_person_id", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Cedent Notification Date", "field_id": "technical_details.cedent_notification_date", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "GenRe Notification Date", "field_id": "technical_details.genre_notification_date", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "<PERSON><PERSON><PERSON> Amou<PERSON>", "field_id": "technical_details.premium_amount", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Reinsurance Amount", "field_id": "technical_details.reinsurance_amount", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Ultimate Risk Amount", "field_id": "technical_details.risk_amount", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Indexation", "field_id": "technical_details.indexation", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Waiting/Deferred Period", "field_id": "technical_details.deferment_period", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}]}, {"card_name": "Insured Details", "validation_fields": [{"field_name": "Full Name", "field_id": "insured_details.name", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "First Name", "field_id": "insured_details.name.given_name", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Last Name", "field_id": "insured_details.name.family_name", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Insured Type", "field_id": "insured_details.insured_type", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Date of Birth", "field_id": "insured_details.date_of_birth", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Birth State", "field_id": "insured_details.birth_state", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Birth Country", "field_id": "insured_details.birth_country", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Gender", "field_id": "insured_details.gender", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Address", "field_id": "insured_details.address", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Address: Street Line", "field_id": "insured_details.address.line", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Address: City", "field_id": "insured_details.address.city", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Address: State", "field_id": "insured_details.address.state", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Address: Zip Code", "field_id": "insured_details.address.zipcode", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Address: Country", "field_id": "insured_details.address.country", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Marital Status", "field_id": "insured_details.marital_status", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Occupation", "field_id": "insured_details.occupation", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Weight", "field_id": "insured_details.weight", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Height", "field_id": "insured_details.height", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Alcohol", "field_id": "insured_details.alcohol", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Smoker Status", "field_id": "insured_details.smoking", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Drugs", "field_id": "insured_details.drugs", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Avocation", "field_id": "insured_details.avocation", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Travel", "field_id": "insured_details.travel", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Behavior", "field_id": "insured_details.behavior", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Social Security Number", "field_id": "insured_details.ssn", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Owner Relationship", "field_id": "insured_details.owner_relationship", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Beneficiary Relationship", "field_id": "insured_details.beneficiary_relationship", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Citizen of", "field_id": "insured_details.citizenship", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Citizen of the US?", "field_id": "insured_details.us_citizen", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Resident of", "field_id": "insured_details.residentship", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Resident of the US?", "field_id": "insured_details.us_resident", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Driver's License #", "field_id": "insured_details.license_number", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Driver's License State", "field_id": "insured_details.license_state", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Income / Salary", "field_id": "insured_details.income", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": true, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Net Worth", "field_id": "insured_details.net_worth", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Application Sign Date", "field_id": "insured_details.sign_date", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Application Sign State", "field_id": "insured_details.sign_state", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}, {"field_name": "Client Company Assessment", "field_id": "insured_details.client_company_assessment", "match_type": "none", "high": null, "low": null, "fieldConfidence": null, "message": "", "enabled": false, "eitherRequiredField": null, "eitherRequiredFieldName": null}]}]}]
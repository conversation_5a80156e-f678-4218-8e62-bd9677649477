{"no impairments list": {"description": "Original prompt without the standard impairments list", "prompt": "Create a JSON object that maps each diagnosis to an impairment category. The JSON should be a simple key-value dictionary where keys are diagnoses and values are impairment categories.\n\nDiagnoses to map:\n{diagnoses}\n\nExample format:\n{{\n  \"Diabetes Type 2\": \"Endocrine\",\n  \"Hypertension\": \"Cardiovascular\"\n}}\n\nRules:\n- Use simple, widely recognized impairment categories\n- Be consistent with similar diagnoses\n- Use null for unmappable diagnoses\n- Response must be ONLY the JSON object\n- No additional text or explanations"}, "fixed impairments list": {"description": "Prompt that includes the standard list of impairments to choose from", "prompt": "Create a JSON object that maps each diagnosis to an impairment category from the provided list. The JSON should be a simple key-value dictionary where keys are diagnoses and values are impairment categories.\n\nAvailable impairment categories:\n{impairments_list}\n\nDiagnoses to map:\n{diagnoses}\n\nExample format:\n{{\n  \"Diabetes Type 2\": \"Endocrine\",\n  \"Hypertension\": \"Cardiovascular\"\n}}\n\nRules:\n- Only use impairment categories from the provided list\n- Use null for unmappable diagnoses\n- Response must be ONLY the JSON object\n- No additional text or explanations"}, "clarice": {"description": "Original prompt created by <PERSON><PERSON><PERSON>", "prompt": "Reword each **Diagnosis Group Name** into a clear, commonly understood **Diagnosis Impairment Category**.\n\n**Instructions:**\n- Do **not** modify the diagnosis names.\n- Use simple, widely recognized terms for the impairment category.\n- If all diagnoses are similar, generalize the impairment category based on them.\n- Ensure the impairment category accurately reflects the diagnoses.\n- Prioritize the most clinically relevant condition when multiple diagnoses are listed.\n- Respond **only** in JSON format.\n\n**ICD Sections with Diagnoses:**\n```\n{input_output}\n```"}}
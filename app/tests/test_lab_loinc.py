import unittest
from lab_loinc import LOINC
from medical_codes.coding import CodeOutput, Laboratory
import pandas as pd

ground_truth = [
    {"name": "VERYLOW DENSITY LIPO. VLDL", "panel": "CARDIAC RISK", "unit": "mg/dL", "specimen": "serum plasma", "gt_loinc": "13458-5"},
    {"name": "THIAZIDE DIURETIC", "panel": "albumin random", "unit": "", "specimen": "urine", "gt_loinc": "19306-0"},
    {"name": "diuretic agents diu", "panel": "miscellaneus urine test", "unit": "ngml", "specimen": "urine", "gt_loinc": "32676-9"},
    {"name": "creatinine", "panel": "URINALYSIS", "unit": "mgdl", "specimen": "urine", "gt_loinc": "2161-8"},
    {"name": "hemoglobin screen", "panel": "URINALYSIS", "unit": "", "specimen": "urine", "gt_loinc": "5794-3"},
    {"name": "TSH", "panel": "TSH", "unit": "ulu/ml", "specimen": "serum plasma", "gt_loinc": "3016-3"},
    {"name": "RBC", "panel": "Urinalysis Dip", "unit": "/HPF", "specimen": "urine sediment", "gt_loinc": "46419-8"},
    {"name": "SQUAMOUS EPITHELIAL CELLS", "panel": "Urinalysis Dip", "unit": "/HPF", "specimen": "urine sediment", "gt_loinc": "33219-7"},
    {"name": "Vitamin B12", "panel": "VITAMIN B12", "unit": "pg/ml", "specimen": "serum plasma", "gt_loinc": "2132-9"},
    {"name": "Ferritin", "panel": "Ferritin", "unit": "ng/dL", "specimen": "serum plasma", "gt_loinc": '2276-4'},
    {"name": "gran", "panel": "", "unit": "%", "specimen": "blood", "gt_loinc": "30395-8"},
    {"name": "a/g ratio", "panel": "", "unit": "gdl", "specimen": "serum plasma", "gt_loinc": "1759-0"},
    {"name": "Foiale", "panel": "", "unit": "ngml", "specimen": "serum plasma", "gt_loinc": "2284-8"},
    {"name": "plt", "panel": "", "unit": "10^3", "specimen": "blood", "gt_loinc": "777-3"},
    {"name": "high - density lipoprotein HDL", "panel": "", "unit": "", "specimen": "serum plasma", "gt_loinc": "2085-9"},
    {"name": "lymphs", "panel": "", "unit": "%", "specimen": "blood", "gt_loinc": "26478-8"},
    {"name": "mono", "panel": "", "unit": "%", "specimen": "blood", "gt_loinc": "26485-3"},
    {"name": "gfr", "panel": "", "unit": "", "specimen": "serum plasma blood", "gt_loinc": "62238-1"},
    {"name": "Alb/Creat Ratio", "panel": "Albumin/Creatinine Ratio,Urine or Microalbumin", "unit": "creat", "specimen": "urine", "gt_loinc": "14958-3"},
    {"name": "wbc", "panel": "cbc", "unit": "x103ul", "specimen": "blood", "gt_loinc": "6690-2"},
    {"name": "rbc", "panel": "cbc", "unit": "x106ul", "specimen": "blood", "gt_loinc": "789-8"},
    {"name": "hgb", "panel": "cbc", "unit": "gdl", "specimen": "blood", "gt_loinc": "718-7"},
    {"name": "ldl calculated", "panel": "", "unit": "", "specimen": "serum plasma", "gt_loinc": "13457-7"},
    {"name": "triglycerides", "panel": "", "unit": "mgdl", "specimen": "serum plasma", "gt_loinc": "2571-8"},
    {"name": "cholesterol in hdl", "panel": "", "unit": "mgdl", "specimen": "serum plasma", "gt_loinc": "2085-9"},
    {"name": "AFP (alpha-fetoprotein)", "panel": "Alpha 1 panel", "unit": "ng/mgdl", "specimen": "serum plasma",   "gt_loinc": "1834-1"},
    {"name": "Cocaine Metabolites", "panel": "", "unit": "ngml", "specimen": "urine", "gt_loinc": "53743-1"},
    {"name": "Testosterone", "panel": "", "unit": "ngdl", "specimen": "serum plasma", "gt_loinc": "2986-8"},
    {"name": "Troponin", "panel": "", "unit": "ngml", "specimen": "serum plasma", "gt_loinc": "10839-9"},
    {"name": "eos", "panel": "", "unit": "%", "specimen": "blood", "gt_loinc": "26450-7"},
    {"name": "LDL Cholesterol Calc", "panel": "", "unit": "mgdl", "specimen": "serum plasma", "gt_loinc": "13457-7"},
    {"name": "AST", "panel": "", "unit": "IUL", "specimen": "serum plasma", "gt_loinc": "1920-8"},
    {"name": "ag ratio", "panel": "", "unit": "", "specimen": "serum plasma", "gt_loinc": "1759-0"},
    {"name": "plt", "panel": "", "unit": "10*9/uL", "specimen": "blood", "gt_loinc": "777-3"},
    {"name": "neut", "panel": "", "unit": "10*9/uL", "specimen": "blood", "gt_loinc": "751-8"},
    {"name": "gran, imm", "panel": "", "unit": "10*9/uL", "specimen": "blood", "gt_loinc": "51584-1"},
    {"name": "Lymph", "panel": "", "unit": "10*9/uL", "specimen": "blood", "gt_loinc": "731-0"},
    {"name": "eos", "panel": "", "unit": "10*9/uL", "specimen": "blood", "gt_loinc": "711-2"},
    {"name": "baso", "panel": "", "unit": "10*9/uL", "specimen": "blood", "gt_loinc": "704-7"},
    {"name": "U/L", "panel": "COMPREHENSIVE METABOLIC PANEL", "unit": "U/L", "specimen": "", "gt_loinc": ""},
    {"name": "BILI. TOT", "panel": "", "unit": "(MG/DL)", "specimen": "serum plasma", "gt_loinc": "1975-2"},
    {"name": "GGT", "panel": "BLOOD CHEMISTRY PROFILE", "unit": "(U/L)", "specimen": "serum plasma", "gt_loinc": "2324-2"},
    {"name": "body surface area", "panel": "cardiac markers", "unit": "not found", "specimen": "^patient", "gt_loinc": "8277-6"},
    {"name": "PROT/CREATININE RATIO", "panel": "URINALYSIS", "unit": "(MG/GMCR)", "specimen": "urine", "gt_loinc": "2890-2"},
    {"name": "cotinine nic", "panel": "MISCELLANEOUS URINE TESTS", "unit": "(MCG/ML)", "specimen": "urine", "gt_loinc": "10366-3"},
    {"name": "DIURETIC AGENTS DIU", "panel": "ADULTERANT RESULTS", "unit": "(MG/DL)", "specimen": "urine", "gt_loinc": '32676-9'},
    {"name": "temperature", "panel": "ADULTERANT RESULTS", "unit": "FAHR", "specimen": "urine", "gt_loinc": '58681-8'},
    {"name": "white blood cells", "panel": "URINALYSIS", "unit": "(/HPF)", "specimen": "urine sediment", "gt_loinc": '46702-7'},
    {"name": "red blood cells", "panel": "URINALYSIS", "unit": "(/HPF)", "specimen": "urine sediment", "gt_loinc": '46419-8'},
    # #https://friendlyhealth.atlassian.net/browse/ICR-19000
    {"name": "creatinine", "panel": "urinalysis", "unit": "mg/dl", "specimen": "urine", "gt_loinc": '2161-8'},
    {"name": "creatinine", "panel": "blood chemistry profile", "unit": "mg/dl", "specimen": "serum plasma", "gt_loinc": '2160-0'},
    {"name": "ckdepi - creatinine", "panel": "blood chemistry profile", "unit": "ml/min", "specimen": "serum plasma blood", "gt_loinc": '62238-1'},
    {"name": "creatinine", "panel": "urinalysis", "unit": "mg/dl", "specimen": "urine", "gt_loinc": '2161-8'},
    # #https://friendlyhealth.atlassian.net/browse/ICR-19001
    {"name": "gran. cast", "panel": "microscopic exam", "unit": "lpf", "specimen": "urine sediment", "gt_loinc": '5793-5'},
    {"name": "hyal. cast", "panel": "microscopic exam", "unit": "lpf", "specimen": "urine sediment", "gt_loinc": '5796-8'},
    {"name": "globulin", "panel": "blood chemistry profile", "unit": "g/dl", "specimen": "serum", "gt_loinc": '2336-6'},
    # {"name": "globulin", "panel": "heart", "unit": "", "specimen": "", "gt_loinc": ''},
    {"name": "leukocyte screen", "panel": "urinalysis", "unit": "", "specimen": "urine", "gt_loinc": '53316-6'},
    {"name": "psa", "panel": "blood chemistry profile", "unit": "ng/ml", "specimen": "serum plasma", "gt_loinc": '2857-1'},
    {"name": "hemoglobin screen", "panel": "", "unit": "urine", "specimen": "blood", "gt_loinc": '718-7'},
    {"name": "cocaine", "panel": "urine drug profile", "unit": "ng/ml", "specimen": "urine", "gt_loinc": '20519-5'},
    {"name": "cocaine", "panel": "miscellaneous urine tests", "unit": "ng/ml", "specimen": "urine", "gt_loinc": '20519-5'},
    {"name": "specific gravity", "panel": "urinalysis", "unit": "", "specimen": "urine", "gt_loinc": '2965-2'},
    {"name": "leukocyte", "panel": "urinalysis", "unit": "", "specimen": "urine", "gt_loinc": '53316-6'},
    {"name": "white blood count", "panel": "urinalysis", "unit": "HPF", "specimen": "urine sediment", "gt_loinc": '46702-7'},
    {"name": "red blood count", "panel": "urinalysis", "unit": "hpf", "specimen": "urine sediment", "gt_loinc": '46419-8'},
    {"name": "blood", "panel": "urinalysis", "unit": "", "specimen": "urine", "gt_loinc": '53292-9'},
    #https://friendlyhealth.atlassian.net/browse/ICR-21701
    {"name": "PERCENTAGE EOsinophil", "panel": "cbc w/ autodiff, complete blood count", "unit": "%", "specimen": "blood", "gt_loinc": '711-2'},
    {"name": "ifobt screening", "panel": "", "unit": "", "specimen": "stool", "gt_loinc": '29771-3'},
    {"name": "vitamin b12", "panel": "", "unit": "", "specimen": "serum plasma", "gt_loinc": '2132-9'},
    {"name": "Eosinophil count", "panel": "", "unit": "", "specimen": "blood", "gt_loinc": '711-2'},
]

loinc_description_gt = [
    ("12293-7", "cotinine"),
    ("12294-5", "nicotine"),
    ("58952-3", "testosterone free total panel"),
    ("aaa", ""),
    ("3122", ""),
]

class TestLOINC(unittest.TestCase):
    @classmethod
    def setUpClass(cls) -> None:
        cls.loinc_service = LOINC()

    def test_lab_entries(self):
        for loinc_entry in ground_truth:
            lab_instance = Laboratory(
                name=loinc_entry["name"],
                unit=loinc_entry["unit"],
                specimen=loinc_entry["specimen"],
                panel=loinc_entry["panel"]
            )
            predicted_loinc: CodeOutput = self.loinc_service.process(lab_instance)
            gt_loinc_code = loinc_entry["gt_loinc"]
            self.assertEqual(predicted_loinc.medical_code, gt_loinc_code)

    def test_loinc_specimen(self):
        for loinc_entry in ground_truth:
            loinc_code = loinc_entry["gt_loinc"]
            specimen = loinc_entry["specimen"]
            complete_specimen_loinc = self.loinc_service.get_specimen_by_code(loinc_code)
            self.assertEqual(complete_specimen_loinc, specimen)

    def test_common_laboratory_entries(self):
        test_file = pd.read_excel(io="tests/LOINC.xlsx", sheet_name="test_file")
        test_file = test_file.where(pd.notnull(test_file), "")
        overall_score = 0
        lab_count = 0
        for index, data in test_file.iterrows():
            lab_instance = Laboratory(
                name=data["name"],
                unit=data["unit"],
                specimen="",
                panel=data["panel"]
            )
            lab_output: CodeOutput = self.loinc_service.process(lab_instance)
            if lab_output.score != 0:
                lab_count += 1
                overall_score += lab_output.score
                
        self.assertGreater(overall_score/lab_count, 60)
        
    def test_panel_entries(self):
        test_file = pd.read_excel(io="tests/LOINC.xlsx", sheet_name="loinc_panels")
        test_file = test_file.where(pd.notnull(test_file), "")
        overall_score = 0
        lab_count = 0
        for index, data in test_file.iterrows():
            panel_description = data['Laboratory Panel']
            lab_output: CodeOutput = self.loinc_service.process_panel(panel_description)
            if lab_output.score != 0:
                lab_count += 1
                overall_score += lab_output.score
            # comp_val = self.loinc_service.get_component_by_code(loinc_code)
            # test_file.at[index, 'loinc_code'] = loinc_code
            # test_file.at[index, 'loinc_score'] = diag_score
            # test_file.at[index, 'loinc_gt_name'] = comp_val
        # output_excel_filename = "tests/loinc_panel_output.xlsx"
        # test_file.to_excel(output_excel_filename, index=False)
        # print(f"DataFrame saved to {output_excel_filename}")
        self.assertGreater(overall_score/lab_count, 60)
        
        
    def test_common_labs(self):
        test_file = pd.read_excel(io="tests/LOINC.xlsx", sheet_name="common_labs")
        test_file = test_file.where(pd.notnull(test_file), "")
        overall_score = 0
        lab_count = 0
        for index, data in test_file.iterrows():
            panel_description = str(data['Panel'])
            name = str(data['Name'])
            unit = str(data['Unit'])
            specimen = str(data['Specimen'])
            # lab = {
            #     "panel" : str(panel_description),
            #     "name" : str(name),
            #     "unit" : str(unit),
            #     "specimen" : ""
            # }
            lab_instance = Laboratory(
                name=panel_description,
                unit=unit,
                specimen="",
                panel=panel_description
            )
            lab_output: CodeOutput = self.loinc_service.process(lab_instance)
            if lab_output.score != 0:
                lab_count += 1
                overall_score += lab_output.score
        #     test_file.at[index, 'loinc_code'] = loinc_code
        #     test_file.at[index, 'loinc_score'] = lab_score
        #     test_file.at[index, 'loinc_gt_name'] = comp_val
        # output_excel_filename = "tests/loinc_ner_gt_f3.xlsx"
        # test_file.to_excel(output_excel_filename, index=False)
        # print(f"DataFrame saved to {output_excel_filename}")
        self.assertGreater(overall_score/lab_count, 60)
        
    def test_loinc_description(self):
        for description, loinc_gt in loinc_description_gt: 
            loinc_component = self.loinc_service.get_component_by_code(description)
            self.assertEqual(loinc_component, loinc_gt)
import traceback
from collections import defaultdict
from datetime import datetime

from friendlylib.iterators import flatten, lfilter, find
from friendlylib.strings import remove_prefix_with_delimiter

from elements.edocument import EDocument

smoking_sub_tags = ["smoking.status.yes", "smoking.status.no", "smoking.status.former", "smoking.status.passive",
                    "smoking.device", "smoking.quantity", "smoking.end_date", "smoking.start_date", "smoking.duration",
                    "tobacco.status.yes", "tobacco.status.no", "tobacco.status.former", "tobacco.status.passive",
                    "tobacco.product", "tobacco.quantity", "tobacco.end_date" "tobacco.start_date", "tobacco.duration"]

application_date_sub_tags = ["application.date"]


def get_date_obj_from_partial_dates(date_str):
    try:
        separators = ["-", "/", "."]
        month, year = None, None

        for separator in separators:
            if separator in date_str:
                parts = date_str.split(separator)
                if len(parts) == 2:
                    if len(parts[0]) == 4:
                        year, month = map(int, parts)
                    else:
                        month, year = map(int, parts)
                break
        else:
            year = int(date_str)
            month = 1

        try:
            return datetime(year, month, 1)
        except:
            return None
    except:
        return None


def get_unified_format_date(date):
    date_formats = ["%Y-%m-%d", "%m/%d/%Y", "%d-%b-%Y", "%d %B, %Y", "%Y%m%d", "%d.%m.%Y", "%Y/%m/%d", "%d-%m-%Y",
                    "%m-%d-%Y"]
    if not date:
        return None

    for fmt in date_formats:
        try:
            parsed_date = datetime.strptime(date.strip(), fmt)
            break
        except ValueError:
            continue
    else:
        return get_date_obj_from_partial_dates(date)

    return parsed_date


def check_less_than_one_year(app_date_obj, smoking_end_date_obj):
    if app_date_obj and smoking_end_date_obj and app_date_obj > smoking_end_date_obj:
        return (app_date_obj-smoking_end_date_obj).days < 365
    else:
        return False


def get_field_mapping_metadata(edoc: EDocument):
    field_mapping = defaultdict(list)
    smoking_end_dates = {}
    application_dates = {}
    date_obj_datavalue_mapping = {}
    latest_smoking_end_date = None
    latest_app_date = None
    for page_of_lines in edoc:
        page_of_entities = flatten(page_of_lines)
        smoking_related_entity_list = lfilter(
            lambda entity_: remove_prefix_with_delimiter(entity_['type']) in smoking_sub_tags or entity_[
                'type'] in application_date_sub_tags,
            page_of_entities
        )
        if not smoking_related_entity_list:
            continue

        for entity in smoking_related_entity_list:
            if entity['type'] in application_date_sub_tags:
                field_mapping.setdefault('application_date', []).append(entity)
                unified_date_obj = get_unified_format_date(
                    entity["codified_as"] if entity.get("codified_as") else entity["text"])
                if unified_date_obj:
                    date_obj_datavalue_mapping[unified_date_obj] = entity['id']
                    application_dates.setdefault(entity['claimid'], []).append(unified_date_obj)
                    latest_app_date = max(latest_app_date or datetime(1900, 1, 1), unified_date_obj)
            elif remove_prefix_with_delimiter(entity['type']) in ["smoking.end_date"]:
                field_mapping.setdefault('smoking_end_date', []).append(entity)
                unified_date_obj = get_unified_format_date(
                    entity["codified_as"] if entity.get("codified_as") else entity["text"])
                if unified_date_obj:
                    date_obj_datavalue_mapping[unified_date_obj] = entity['id']
                    smoking_end_dates.setdefault(entity['claimid'], []).append(unified_date_obj)
                    latest_smoking_end_date = max(latest_smoking_end_date or datetime(1900, 1, 1), unified_date_obj)
            elif remove_prefix_with_delimiter(entity['type']) in ["smoking.start_date"]:
                field_mapping.setdefault('smoking_start_date', []).append(entity)
            elif remove_prefix_with_delimiter(entity['type']) in ["smoking.duration"]:
                field_mapping.setdefault('smoking_duration', []).append(entity)
            elif remove_prefix_with_delimiter(entity['type']) in ["smoking.status.yes", "smoking.device",
                                                                  "smoking.quantity"]:
                field_mapping.setdefault('smoking_yes', []).append(entity)
            else:
                field_mapping.setdefault('smoking_others', []).append(entity)

    return {
        'field_mapping': field_mapping,
        'smoking_end_dates': smoking_end_dates,
        'application_dates': application_dates,
        'latest_smoking_end_date': latest_smoking_end_date,
        'latest_app_date': latest_app_date,
        'date_obj_datavalue_mapping': date_obj_datavalue_mapping
    }


def calculate_smoking_status(edoc, card_references):
    try:
        # insured_smoking_priority = find(lambda item: item.field_id == 'insured_details.smoking', retrieved_data.priorities, None)
        # if insured_smoking_priority is not None and '*' not in insured_smoking_priority.page_class_list:
        #     return
        field_mapping_metadata = get_field_mapping_metadata(edoc)
        field_mapping = field_mapping_metadata.get('field_mapping')
        smoking_end_dates = field_mapping_metadata.get('smoking_end_dates')
        application_dates = field_mapping_metadata.get('application_dates')
        latest_smoking_end_date = field_mapping_metadata.get('latest_smoking_end_date')
        latest_app_date = field_mapping_metadata.get('latest_app_date')
        date_obj_datavalue_mapping = field_mapping_metadata.get('date_obj_datavalue_mapping')
        
        card_references.informals.application_date = field_mapping_metadata.get('latest_app_date')
        card_references.informals.last_smoked_date = field_mapping_metadata.get('latest_smoking_end_date')

        if field_mapping.get('application_date') and (field_mapping.get('smoking_end_date')):
            file_id_dates_mapping = {}
            for file_id, dates_list in smoking_end_dates.items():
                if file_id in application_dates:
                    file_id_dates_mapping[file_id] = {
                        "latest_application_date": sorted(application_dates[file_id])[-1],
                        "latest_smoking_end_date": sorted(smoking_end_dates[file_id])[-1]
                    }

            for entity in field_mapping['smoking_others']:
                if entity["claimid"] in file_id_dates_mapping:
                    app_date = file_id_dates_mapping[entity["claimid"]]["latest_application_date"]
                    smoking_end_date = file_id_dates_mapping[entity["claimid"]]["latest_smoking_end_date"]
                    if check_less_than_one_year(app_date, smoking_end_date):
                        entity['text'] = 'yes'
                    else:
                        entity['text'] = 'former'
                    entity['metadata'].update({"info": f"application_date:{app_date.date()},application_date_id:{date_obj_datavalue_mapping.get(app_date)},smoking_end_date:{smoking_end_date.date()},smoking_end_date_id:{date_obj_datavalue_mapping.get(smoking_end_date)}"})
                elif latest_smoking_end_date and latest_app_date:
                    entity['metadata'].update({"info": f"application_date:{latest_app_date.date()},application_date_id:{date_obj_datavalue_mapping.get(latest_app_date)},smoking_end_date:{latest_smoking_end_date.date()},smoking_end_date_id:{date_obj_datavalue_mapping.get(latest_smoking_end_date)}"})

            for entity in field_mapping["smoking_yes"]:
                if entity["claimid"] in file_id_dates_mapping:
                    app_date = file_id_dates_mapping[entity["claimid"]]["latest_application_date"]
                    smoking_end_date = file_id_dates_mapping[entity["claimid"]]["latest_smoking_end_date"]
                    if check_less_than_one_year(app_date, smoking_end_date):
                        entity['text'] = 'yes'
                    else:
                        entity['text'] = 'former'
                    entity['metadata'].update({"info": f"application_date:{app_date.date()},application_date_id:{date_obj_datavalue_mapping.get(app_date)},smoking_end_date:{smoking_end_date.date()},smoking_end_date_id:{date_obj_datavalue_mapping.get(smoking_end_date)}"})
                elif latest_smoking_end_date and latest_app_date:
                    entity['metadata'].update({"info": f"application_date:{latest_app_date.date()},application_date_id:{date_obj_datavalue_mapping.get(latest_app_date)},smoking_end_date:{latest_smoking_end_date.date()},smoking_end_date_id:{date_obj_datavalue_mapping.get(latest_smoking_end_date)}"})
    except Exception as e:
        print(f'ERROR: Smoking Status Calculation Failed: {traceback.format_exc()}')

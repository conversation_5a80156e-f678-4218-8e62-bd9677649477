from typing_extensions import List, Union, TypedDict, Dict, Any


class Entity(TypedDict):
    type: str
    id: str
    text: str
    word_ids: List[str]
    confidence: float
    confidence_threshold: int
    page_num: int
    line_num: int
    entity_num: int
    left: int
    top: int
    right: int
    bottom: int
    height: int
    width: int
    codified_as: str
    codification_category: str
    codify: Union[None, str]
    codified_datetime_obj: Union[None, str]
    section_tag: List[str]
    detected_lang: Dict[str, float]
    metadata: Union[None, str]
    word_type: Union[None, str]
    entry_id: Union[None, str]
    source: Union[None, str]
    layout_uuid: str
    ui_id: Union[None, str]
    xpath: str
    context: Union[None, str]
    abs_page: int
    claimid: str
    extra: bool
    document_id: Union[None, str]
    doc_num: int
    page_class: str
    python_codified_as: str
    section_info: Dict[str, Any]
    is_skipped: Union[None, bool]


# List of Entities makes up an EPageLine
EPageLine = List[Entity]

# List of EPageLines makes up an EPageData
EPageData = List[EPageLine]

# List of EPageData makes up an EDocument
EDocument = List[EPageData]


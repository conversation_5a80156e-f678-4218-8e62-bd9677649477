import time
from collections import defaultdict

from friendlylib.boxes import euclidean_center, euclidean_left_top

import constants as const
from utils.open_ai import OpenAIRequestHandler


def calculate_euclidean_distance(child_entity, mother_entity):
    bbox1 = {
        'top': child_entity['top'] + (child_entity['abs_page'] * 3500),
        'bottom': child_entity['bottom'] + (child_entity['abs_page'] * 3500),
        'left': child_entity['left'],
        'right': child_entity['right']
    }
    bbox2 = {
        'top': mother_entity['top'] + (mother_entity['abs_page'] * 3500),
        'bottom': mother_entity['bottom'] + (mother_entity['abs_page'] * 3500),
        'left': mother_entity['left'],
        'right': mother_entity['right']
    }

    distance = euclidean_left_top(bbox1, bbox2)
    return distance


def get_dictionary_from_class_object(class_obj):
    dict_ = {
        key: value for key, value in class_obj.__dict__.items() if value is not None
    }

    return dict_


def count_none_attributes(obj):
    return sum(value is None for value in obj.__dict__.values())


def count_not_none_attributes(obj):
    if obj is None:
        return 0
    return sum(value is not None for value in obj.__dict__.values())


def none_attributes_of_class_object(class_obj):
    none_attributes = []
    for attribute_name, attribute_value in class_obj.__dict__.items():
        if attribute_value is None:
            none_attributes.append(attribute_name)
    return none_attributes


def get_record_locations_list(group_members):
    group_locations_list = []
    for member in group_members:
        member_loc_list = member.get_attribute(const.RECORD_LOCATIONS)
        group_locations_list.extend(member_loc_list)
    return group_locations_list


def group_objects_by_mother_field(list_of_objects, mother_field):
    grouped_obj_dict = {}
    for obj in list_of_objects:
        mother_entity = obj.get_attribute(mother_field)[0]
        mother_text_value = mother_entity.get("codified_as", "") or mother_entity["text"].lower().strip()
        grouped_obj_dict.setdefault(mother_text_value, []).append(obj)

    # Merge objects within each group
    merged_obj_list = []
    for group_name, group_members in grouped_obj_dict.items():
        # sort group members and start with the member with the least number of NONE values
        group_members = sorted(group_members, key=lambda member_: len(none_attributes_of_class_object(member_)))
        merged_obj = group_members[0]

        # Combine record locations of every  group member and set to merged obj record locations
        record_locations_list = get_record_locations_list(group_members)
        merged_obj.set_attribute(const.RECORD_LOCATIONS, record_locations_list)

        none_attributes = none_attributes_of_class_object(merged_obj)
        if none_attributes:
            for member in group_members[1:]:
                for attribute_name, attribute_value in member.__dict__.items():
                    if attribute_value is None:
                        continue
                    if merged_obj.get_attribute(attribute_name) is None or (attribute_name in none_attributes and
                                                                            attribute_value[0]["confidence"] >
                                                                            merged_obj.get_attribute(attribute_name)[0][
                                                                                "confidence"]):
                        merged_obj.set_attribute(attribute_name, attribute_value)

        merged_obj_list.append(merged_obj)

    sorted_merged_obj_list = sorted(merged_obj_list, key=lambda member_: len(none_attributes_of_class_object(member_)))
    return sorted_merged_obj_list


def get_page_class_priority_list(field_id, priorities):
    for priority in priorities:
        if field_id == priority.field_id:
            return priority.page_class_list, priority.page_class_exclusion_list
    return ['*'], []


def filter_sub_cards_based_on_priority(sub_card_list, priorities, field_id):
    page_class_priority_list, page_class_exclusion_list = get_page_class_priority_list(field_id, priorities)
    field_name = field_id.split('.')[-1]
    # Exclude subcards if page class is in exclusion list
    sub_card_list = [sub_card for sub_card in sub_card_list if (sub_card.get_attribute(field_name) and
                                                                sub_card.get_attribute(field_name)[0][
                                                                    "page_class"] not in page_class_exclusion_list)]

    sub_card_list = [sub_card for sub_card in sub_card_list if (sub_card.get_attribute(field_name) and
                                                                sub_card.get_attribute(field_name)[0][
                                                                    "page_class"] in page_class_priority_list) or
                     '*' in page_class_priority_list]
    return sub_card_list


def group_common_entities_by_mother(input_data, mother_field):
    grouped_data = defaultdict(lambda: defaultdict(list))

    # Iterate through the input data and merge based on the "mother_field" key
    for item in input_data:
        mother_text = item.get(mother_field, {}).get("codified_as") or item.get(mother_field, {}).get("text")
        for key, value in item.items():
            if key == const.RECORD_LOCATIONS:
                grouped_data[mother_text][key].extend(value)
            else:
                grouped_data[mother_text][key].append(value)

    return grouped_data


def get_point_from_entity_dict(obj: dict):
    return obj['left'], obj['top'], obj['right'], obj['bottom']


def get_point_from_entity_clas_obj(cls_object, entity_field):
    left = cls_object.get_attribute(entity_field)[0]["left"]
    top = cls_object.get_attribute(entity_field)[0]["top"]
    right = cls_object.get_attribute(entity_field)[0]["right"]
    bottom = cls_object.get_attribute(entity_field)[0]["bottom"]
    return left, top, right, bottom


def is_above_line(child_point: list, mother_point: list) -> bool:
    return child_point[1] < mother_point[1]


def is_cell_1_height_within_cell_2_height(cell_1_point: list, cell_2_point: list) -> bool:
    cell_1_height = (cell_1_point[1] > cell_2_point[1]) and \
                    (cell_1_point[3] < cell_2_point[3])
    return cell_1_height


def is_cell_1_height_within_cell_2_height_column(cell_1_point: list, cell_2_point: list) -> bool:
    cell_1_height = (cell_1_point[0] > cell_2_point[0]) and \
                    (cell_1_point[2] < cell_2_point[2])
    return cell_1_height


def is_same_line(child_point: list, mother_point: list) -> bool:
    margin = 20
    top_disp = abs(child_point[1] - mother_point[1])
    bot_disp = abs(child_point[3] - mother_point[3])

    height_check = (is_cell_1_height_within_cell_2_height(child_point, mother_point) or
                    is_cell_1_height_within_cell_2_height(mother_point, child_point))

    return (top_disp <= margin) or (bot_disp <= margin) or height_check


def summarize_rdx_using_openai(content):
    input_prompt = f"""
        Given a dictionary with keys as reason, diagnosis or plan and value as description for the keys.
        Summarize the description into terse, clinical-style 3-4 lines short paragraph while preserving all key medical information.
        Give the output in JSON format with same keys as input dictionary and summarized description as its value.
        Return the result in JSON format:
            - Keys in the output must be exactly the same as the input.
            - Output must start with ```json and be a valid JSON block.
            - Summarized description must be a paragraph
        Input data: {content}
    """
    try:
        start_time = time.time()

        print("Initializing OpenAI handler...")
        openai_handler = OpenAIRequestHandler()
        openai_handler.initialize()

        print("Sending request to OpenAI...")
        response = openai_handler.get_openai_response(input_prompt, encoded_image_list=[])

        end_time = time.time()
        print(f"Total Time taken for OpenAI request: {end_time - start_time:.2f} seconds")
        return response

    except Exception as e:
        print(f"An error occurred during OpenAI request: {e}")
        return content


def summarize_data_using_genai(list_of_object_dicts: list):
    start_time = time.time()
    for obj_dict in list_of_object_dicts:
        content = {}
        for field_id, entity_object in obj_dict.items():
            if field_id in ['date', 'record_locations']:
                continue
            content[field_id] = entity_object['text']

        summarized_content = summarize_rdx_using_openai(content)
        for field_id, content_text in summarized_content.items():
            if obj_dict.get(field_id):
                obj_dict[field_id]['text'] = content_text

    end_time = time.time()
    print(f"Total Time taken to summarize RDx: {end_time - start_time:.2f} seconds")

import traceback
import uuid
from copy import deepcopy

from friendlylib import storage
from friendlylib.iterators import lfilter, get_first
from medical_codes.coding import CodeOutput
from medical_codes.medical_coding_utility import MedicalCodingUtility
from elements.edocument import Entity
from miscellaneous import MiscUtility
import constants as const
import titles as Title
import table_utils
import ui_output_dicts
from edoc_utility import edoc_to_entities, merge_entities
from user_defined_cards.utility import (calculate_euclidean_distance, get_dictionary_from_class_object,
                                        group_objects_by_mother_field, filter_sub_cards_based_on_priority,
                                        get_page_class_priority_list, get_point_from_entity_dict,
                                        get_point_from_entity_clas_obj, is_same_line, group_common_entities_by_mother,
                                        count_not_none_attributes, summarize_data_using_genai)
from utils.gemini_utils import get_gemini_results_rx

main_blueprint = {
    "user_defined_cards": [
        {
            "card_name": "Sample Vertical",
            "card_id": "sample_vertical",
            "card_layout": "display_vertically",
            "fields": [
                {
                    "field_name": "Given Name",
                    "field_id": "sample_vertical.given_name",
                    "role": "Mother",
                    "entity_ids": [
                        "insured_1.name.given_name",
                        "patient.name.given_name",
                        "insured_2.name.given_name"
                    ],
                    "search_method": "",
                    "isEnabled": True
                },
                {
                    "field_name": "Family Name",
                    "field_id": "sample_vertical.family_name",
                    "role": "Child",
                    "entity_ids": [
                        "insured_1.name.family_name",
                        "patient.name.family_name",
                        "insured_2.name.family_name"
                    ],
                    "search_method": "nearest_in_page",
                    "isEnabled": True
                },
                {
                    "field_name": "Birth",
                    "field_id": "sample_vertical.birth",
                    "role": "Child",
                    "entity_ids": [
                        "patient.birth",
                        "insured_2.birth",
                        "insured_1.birth"
                    ],
                    "search_method": "nearest_in_page",
                    "isEnabled": True
                },
                {
                    "field_name": "Occupation",
                    "field_id": "sample_vertical.occupation",
                    "role": "Child",
                    "entity_ids": [
                        "insured_1.occupation",
                        "insured_2.occupation",
                        "patient.occupation"
                    ],
                    "search_method": "nearest_in_file",
                    "isEnabled": True
                }
            ],
            "id": "54a77054-79fc-44bb-9838-37c2664a0795",
            "sequence": 7,
            "isEnabled": True
        },
        {
            "card_name": "Sample Horizontal",
            "card_id": "sample_horizontal",
            "card_layout": "display_horizontal",
            "fields": [
                {
                    "field_name": "Test Name",
                    "field_id": "sample_horizontal.test_name",
                    "role": "Mother",
                    "entity_ids": [
                        "o.name"
                    ],
                    "search_method": "",
                    "isEnabled": True
                },
                {
                    "field_name": "Test Value",
                    "field_id": "sample_horizontal.test_value",
                    "role": "Child",
                    "entity_ids": [
                        "o.value"
                    ],
                    "search_method": "same_line",
                    "isEnabled": True
                },
                {
                    "field_name": "Unit",
                    "field_id": "sample_horizontal.unit",
                    "role": "Child",
                    "entity_ids": [
                        "o.unit"
                    ],
                    "search_method": "same_line",
                    "isEnabled": True
                }
            ],
            "id": "70a6d720-192f-4136-b448-1f5e84fecfc4",
            "sequence": 9,
            "isEnabled": True
        }
    ]
}


class UserDefinedCard:
    def __init__(self, fields):
        for field in fields:
            setattr(self, field['field_id'].split('.')[-1], None)
        self.record_locations = []

    def set_attribute(self, attr_name, value):
        if hasattr(self, attr_name):
            setattr(self, attr_name, value)
        else:
            print(f"Attribute '{attr_name}' does not exist in this class.")

    def get_attribute(self, attr_name):
        if hasattr(self, attr_name):
            return getattr(self, attr_name)
        else:
            print(f"Attribute '{attr_name}' does not exist in this class.")
            return None

    def append_attribute_to_list(self, attr_name, value):
        if hasattr(self, attr_name):
            attr_value_list = getattr(self, attr_name)
            attr_value_list.append(value)
            setattr(self, attr_name, attr_value_list)
        else:
            print(f"Attribute '{attr_name}' does not exist in this class.")


def get_consolidated_distance(entity1, entity2, search_method):
    if search_method == const.SAME_PAGE_OR_AFTER and entity1['abs_page'] < entity2['abs_page']:
        return float('inf')
    elif search_method == const.SAME_PAGE_OR_BEFORE and entity1['abs_page'] > entity2['abs_page']:
        return float('inf')
    elif search_method == const.SAME_LINE_OR_AFTER and (entity1['abs_page'] < entity2['abs_page'] or
                                                        (entity1['abs_page'] == entity2['abs_page'] and
                                                         entity1['top'] < entity2['top'])):
        return float('inf')
    elif search_method == const.SAME_LINE_OR_BEFORE and (entity1['abs_page'] > entity2['abs_page'] or
                                                         (entity1['abs_page'] == entity2['abs_page'] and
                                                          entity1['top'] > entity2['top'])):
        return float('inf')
    elif search_method == const.SAME_LINE_OR_BELOW and entity1['top'] < entity2['top']:
        return float('inf')
    elif search_method == const.SAME_LINE_OR_ABOVE and entity1['top'] > entity2['top']:
        return float('inf')
    else:
        return calculate_euclidean_distance(entity1, entity2)


def get_anywhere_best_match(mother_entity, child_entities, used_ids):
    pass


def get_nearest_card(child_entity, mother_card_list, mother_field, search_method):
    nearest_card = None
    min_distance = float('inf')
    child_file_id = child_entity['claimid']
    is_same_line_method = False
    for card in mother_card_list:
        mother_entity = card.get_attribute(mother_field)[0]
        file_id = mother_entity["claimid"]
        if file_id != child_file_id:
            continue
        if (search_method in [const.SAME_LINE_OR_ABOVE, const.SAME_LINE_OR_BELOW] and
                child_entity['abs_page'] != mother_entity['abs_page']):
            continue
        if 'same_line' in search_method:
            child_point = get_point_from_entity_dict(child_entity)
            mother_point = get_point_from_entity_dict(mother_entity)
            if is_same_line(child_point, mother_point):
                distance = get_consolidated_distance(child_entity, mother_entity, search_method)
                if not is_same_line_method or (distance < min_distance and is_same_line_method):
                    min_distance = distance
                    nearest_card = card
                is_same_line_method = True
                continue
        distance = get_consolidated_distance(child_entity, mother_entity, search_method)
        if distance < min_distance and not is_same_line_method:
            min_distance = distance
            nearest_card = card
    return nearest_card, min_distance, is_same_line_method


def get_nearest_by_file_best_match_many(child_entity, mother_card_list, mother_field, child_field, search_method):
    nearest_card, min_distance, is_same_line_method = get_nearest_card(child_entity, mother_card_list, mother_field, search_method)
    if not nearest_card:
        return
    if nearest_card.get_attribute(child_field) is None:
        nearest_card.set_attribute(child_field, [child_entity])
    else:
        nearest_card.append_attribute_to_list(child_field, child_entity)


def get_nearest_by_file_best_match(child_entity, mother_card_list, mother_field, child_field, search_method):
    nearest_card, min_distance, is_same_line_method = get_nearest_card(child_entity, mother_card_list, mother_field, search_method)
    if not nearest_card:
        return
    if nearest_card.get_attribute(child_field) is None:
        nearest_card.set_attribute(child_field, [child_entity])
    else:
        is_same_line_method_old = False
        old_entity = nearest_card.get_attribute(child_field)[0]
        mother_entity = nearest_card.get_attribute(mother_field)[0]
        if 'same_line' in search_method:
            old_child_point = get_point_from_entity_dict(old_entity)
            mother_point = get_point_from_entity_dict(mother_entity)
            if is_same_line(old_child_point, mother_point):
                is_same_line_method_old = True

        if not is_same_line_method and is_same_line_method_old:
            return

        if is_same_line_method and not is_same_line_method_old:
            nearest_card.set_attribute(child_field, [child_entity])
            return
        old_distance = get_consolidated_distance(old_entity, mother_entity, search_method)
        if min_distance < old_distance:
            nearest_card.set_attribute(child_field, [child_entity])


def get_nearest_by_page_best_match(child_entity, mother_card_list, mother_field, child_field):
    nearest_card = None
    min_distance = float('inf')
    child_page_num = child_entity['abs_page']
    child_file_id = child_entity['claimid']
    for card in mother_card_list:
        mother_entity = card.get_attribute(mother_field)[0]
        page_num = mother_entity.get("abs_page")
        file_id = mother_entity.get("claimid")
        if page_num == child_page_num and file_id == child_file_id:
            distance = calculate_euclidean_distance(child_entity, mother_entity)
            if distance < min_distance:
                min_distance = distance
                nearest_card = card
    if nearest_card is not None:
        if nearest_card.get_attribute(child_field) is None:
            nearest_card.set_attribute(child_field, [child_entity])
        else:
            old_entity = nearest_card.get_attribute(child_field)[0]
            mother_entity = nearest_card.get_attribute(mother_field)[0]
            old_distance = calculate_euclidean_distance(old_entity, mother_entity)
            if min_distance < old_distance:
                nearest_card.set_attribute(child_field, [child_entity])


def get_same_line_best_match(child_entity, mother_card_list, mother_field, child_field):
    child_page_num = child_entity['abs_page']
    child_file_id = child_entity['claimid']
    child_point = get_point_from_entity_dict(child_entity)
    for card in mother_card_list:
        mother_point = get_point_from_entity_clas_obj(card, mother_field)
        mother_entity = card.get_attribute(mother_field)[0]
        page_num = mother_entity.get("abs_page")
        file_id = mother_entity.get("claimid")
        if is_same_line(child_point, mother_point) and page_num == child_page_num and file_id == child_file_id:
            card.set_attribute(child_field, [child_entity])


def get_best_match(child_entity, mother_card_list, search_method, mother_field, child_field, data_aggregation):
    if search_method == const.SAME_LINE.replace(' ', '_'):
        get_same_line_best_match(child_entity, mother_card_list, mother_field, child_field)
    elif search_method == const.NEAREST_IN_PAGE:
        get_nearest_by_page_best_match(child_entity, mother_card_list, mother_field, child_field)
    elif search_method == const.NEAREST_IN_FILE:
        if data_aggregation and data_aggregation == 'merge-all':
            get_nearest_by_file_best_match_many(child_entity, mother_card_list, mother_field, child_field,
                                                search_method)
        else:
            get_nearest_by_file_best_match(child_entity, mother_card_list, mother_field, child_field, search_method)
    elif search_method == const.ANYWHERE:
        get_anywhere_best_match(child_entity, mother_card_list, mother_field)
    else:
        if data_aggregation and data_aggregation == 'merge-all':
            get_nearest_by_file_best_match_many(child_entity, mother_card_list, mother_field, child_field,
                                                search_method)
        else:
            get_nearest_by_file_best_match(child_entity, mother_card_list, mother_field, child_field, search_method)


def get_field_id_entity_type_mapping(card_fields):
    mapping_dict = {}
    for field in card_fields:
        for entity_id in field['entity_ids']:
            mapping_dict[entity_id] = field['field_id']
            mapping_dict[const.MERGED_ENTITY + entity_id] = field['field_id']
    return mapping_dict


def insert_cell_in_smart_ui_card(fields_list, cell_entity, row_num, col_ix):
    if cell_entity:
        ec = table_utils.entity_to_ui_cell(cell_entity, row_num, col_ix)
        row_num += 1
        fields_list.append(ec)
    return row_num


def map_header_cell_to_table_cell(card_bp):
    header_cells = []
    header_ners = {entity["field_name"]: entity["entity_ids"] for entity in card_bp["fields"]}
    col_num = -1
    for header_name, header_entity_type in header_ners.items():
        col_num += 1
        ec = deepcopy(ui_output_dicts.empty_cell)
        ec['row']['value'][0]['value_name'] = header_name
        ec['row']['value'][0]['entity_type'] = header_entity_type[0]
        ec['row']['type'] = 'header'
        ec['row']['r'] = 0
        ec['row']['c'] = col_num
        header_cells.append(ec)
    return header_cells


def build_smart_ui_card(dict_of_object, card_bp, destination_card, mother_field, index, groups=None):
    header_list = [entity_bp['field_id'].split('.')[-1] for entity_bp in card_bp["fields"]]
    card_name = card_bp.get('card_name', '')
    fields_list = map_header_cell_to_table_cell(card_bp)
    row_num = 0
    new_row_num = 0
    max_new_row_num = 0
    record_loc = []
    for row_obj in dict_of_object:
        record_loc.extend(row_obj.pop(const.RECORD_LOCATIONS, []))
        row_num = max_new_row_num + 1
        for col_ix, col_name in enumerate(header_list):
            max_new_row_num = max(new_row_num, max_new_row_num)
            new_row_num = insert_cell_in_smart_ui_card(fields_list, row_obj.get(col_name), row_num, col_ix)
    for cell in fields_list:
        cell['table_info']['row_count'] = row_num
        cell['table_info']['col_count'] = len(header_list)

    ui_card = {"card": {"card_display": card_name, "sub_card": ""}, "fields_list": fields_list}
    ui_card['card']['schema'] = {}
    ui_card['card']['card_layout'] = card_bp['card_layout']
    ui_card['card']['card_type'] = 'new'
    ui_card['card']['sub_card'] = f'{card_name}-{index + 1}'
    ui_card['card']['field_id_entity_type_mapping'] = get_field_id_entity_type_mapping(card_bp['fields'])
    ui_card['card']['destination_card'] = destination_card
    ui_card['card']['record_locations'] = record_loc
    ui_card['card']['mother_field'] = mother_field
    ui_card['card']['groups'] = groups
    return ui_card


def update_card_layout(card_bp):
    fields_with_codes = []
    mapped_layout = user_defined_card_headers(card_bp)
    if not mapped_layout:
        return
    for card_field in card_bp['fields']:
        card_id = card_field['field_id'].split('.')[-1]
        fields_with_codes.append(card_field)
        if card_id not in mapped_layout:
            continue
        for medical_code, code_field in mapped_layout[card_id].items():
            medical_code_layout = {
                'field_name': code_field['field_name'],
                'field_id': card_bp['card_id'] + '.' + code_field['field_name'].lower().replace(' ', '_'),
                'role': 'child',
                'entity_ids': [const.GENERATED_ENTITY + code_field['medical_code_type']],
                'search_method': '', 'is_enabled': True, 'entity_list': []
            }
            fields_with_codes.append(medical_code_layout)
    card_bp['fields'] = fields_with_codes
    return


def user_defined_card_headers(card_bp):
    if not card_bp['user_defined_card_medical_codes']:
        return {}
    medical_codes = lfilter(lambda x: x['is_enabled'] is True, card_bp['user_defined_card_medical_codes'])
    if not medical_codes:
        return {}
    mapped_codes = {}
    for item in medical_codes:
        linked_field = item['linked_field']
        field = get_first(lfilter(lambda x: x['field_name'] == linked_field, card_bp['fields']), {})
        field_description = field['field_id'].split('.')[-1]
        if linked_field not in mapped_codes:
            mapped_codes[field_description] = {}
        mapped_codes[field_description][item['medical_code_type']] = {k: v for k, v in item.items()}
    return mapped_codes


def populate_medical_codes_horizontal(list_of_objects, card_bp, medical_coding_service: MedicalCodingUtility):
    mapped_medical_codes = user_defined_card_headers(card_bp)
    if not mapped_medical_codes:
        return list_of_objects
    new_objects = []
    for object in list_of_objects:
        new_obj = {}
        for header, entry in object.items():
            new_obj[header] = entry
            if header not in mapped_medical_codes:
                continue
            text_entry = entry['text']
            for medical_code, medical_code_info in mapped_medical_codes[header].items():
                code_output: CodeOutput = medical_coding_service._get_medical_code(text_entry, medical_code)
                if code_output.medical_code:
                    medical_code_entity: Entity = create_medical_code_entity(entry,
                                                                             medical_code_info['medical_code_type'],
                                                                             code_output)
                    new_obj[medical_code_info['field_name'].lower().replace(' ', '_')] = medical_code_entity
                    append_fields_to_entity(card_bp['fields'], medical_code_info['field_name'], medical_code_entity)
        new_objects.append(new_obj)
    return new_objects


def create_medical_code_entity(entry: Entity, code_type: str, code_output: CodeOutput):
    new_val: Entity = deepcopy(entry)
    new_val['id'] = str(uuid.uuid4())
    new_val['text'] = code_output.medical_code
    new_val['confidence'] = code_output.score
    new_val['metadata'] = {"info": f"{code_output.description}", "type": "medical_code"}
    new_val['type'] = const.GENERATED_ENTITY + code_type
    return new_val


def append_fields_to_entity(fields, medical_code_field, medical_code_entity):
    for field in fields:
        if field['field_name'] != medical_code_field:
            continue
        field['entity_list'].append(medical_code_entity)
        return


def remove_duplicates(list_of_object_dicts):
    unique_list_of_object_dicts = {}
    for obj_dict in list_of_object_dicts:
        dict_key = ((key, val['text'].lower().strip()) for key, val in obj_dict.items() if
                    key != const.RECORD_LOCATIONS)
        sorted_dict_key = tuple(sorted(dict_key, key=lambda x: x[0]))
        if sorted_dict_key in unique_list_of_object_dicts:
            continue
        unique_list_of_object_dicts[sorted_dict_key] = obj_dict
    return list(unique_list_of_object_dicts.values())


def merge_field_wise_list_of_entities(field_dict, mother_field):
    merged_item = {}
    for field, value_list in field_dict.items():
        if field == mother_field:
            text = value_list[0]["text"]
            type_ = value_list[0]["type"]
            entity_type = type_ if const.MERGED_ENTITY in type_ else f'{const.MERGED_ENTITY}{type_}'
            merged_item[field] = merge_entities(list_of_entities=value_list, entity_type=entity_type, text=text)
        elif field == const.RECORD_LOCATIONS:
            merged_item[field] = value_list
        else:
            text = "\n\n".join(["- " + v["text"] for v in value_list]) if len(value_list) > 1 else value_list[0]["text"]
            type_ = value_list[0]["type"]
            entity_type = type_ if const.MERGED_ENTITY in type_ else f'{const.MERGED_ENTITY}{type_}'
            merged_item[field] = merge_entities(list_of_entities=value_list, entity_type=entity_type, text=text)
    return merged_item


def get_merged_field_wise_list_of_entities(list_of_object_dicts, mother_field):
    merged_list = []
    for field_dict in list_of_object_dicts:
        merged_item = merge_field_wise_list_of_entities(field_dict, mother_field)
        merged_list.append(merged_item)
    return merged_list


def merge_objects_with_same_mother(unique_list_of_object_dicts, mother_field):
    merged_list = []
    grouped_data = group_common_entities_by_mother(unique_list_of_object_dicts, mother_field)

    for mother_text, field_dict in grouped_data.items():
        merged_item = merge_field_wise_list_of_entities(field_dict, mother_field)
        merged_list.append(merged_item)
    return merged_list


def sort_by_prefix_type_and_confidence(item):
    type_order = {'insured_1': 1, 'insured_2': 2, 'patient': 3, 'applicant': 4}
    type_prefix = item['type'].split('.')[0]
    return type_order.get(type_prefix, 5), -item['confidence'], item['abs_page'], item['line_num']


def get_sub_cards_from_mother_entities(field_schema_list, field_entity_id_mapping):
    sub_card_list = []
    field_id = None
    for field_id, field_details in field_entity_id_mapping.items():
        if field_details["role"].lower() == "mother":
            for entity in field_details["entity_list"]:
                card = UserDefinedCard(field_schema_list)
                card.set_attribute(field_id, [entity])
                card.set_attribute(const.RECORD_LOCATIONS, [f'{entity["claimid"]}${entity["abs_page"]}'])
                sub_card_list.append(card)
            break

    return sub_card_list, field_id


def create_field_entity_id_mapping(entity_id_to_field_schema, list_of_entities):
    field_entity_id_mapping = {}

    for entity in list_of_entities:
        entity_id = entity["type"]
        if entity_id in entity_id_to_field_schema:
            field_schema_dict = entity_id_to_field_schema[entity_id]
            field_id = field_schema_dict["field_id"].split(".")[-1]
            if field_id not in field_entity_id_mapping:
                field_entity_id_mapping[field_id] = field_schema_dict
            field_entity_id_mapping[field_id].setdefault('entity_list', []).append(entity)

    return field_entity_id_mapping


def get_mother_field_name(card_bp):
    for field in card_bp["fields"]:
        if field["role"].lower() == "mother":
            return field["field_name"]


def get_entity_id_to_field_schema_mapping(field_schema_list):
    entity_id_to_field_schema = {}
    for field_schema in field_schema_list:
        for entity_id in field_schema["entity_ids"]:
            entity_id_to_field_schema[entity_id] = field_schema
    return entity_id_to_field_schema


def get_list_of_objects(edoc, card_bp, priorities):
    field_schema_list = card_bp["fields"]
    card_layout = card_bp.get("card_layout", "")

    entity_id_to_field_schema = get_entity_id_to_field_schema_mapping(field_schema_list)
    edoc_entities = edoc_to_entities(edoc)
    field_entity_id_mapping = create_field_entity_id_mapping(entity_id_to_field_schema, edoc_entities)
    sub_card_list, mother_field = get_sub_cards_from_mother_entities(field_schema_list, field_entity_id_mapping)
    mother_field_id = f"{card_bp['card_id']}.{mother_field}"
    sub_card_list = filter_sub_cards_based_on_priority(sub_card_list, priorities, mother_field_id)
    for field, field_details in field_entity_id_mapping.items():
        if field_details["role"].lower() == "mother":
            continue
        data_aggregation = field_details.get("data_aggregation")
        search_method = field_details["search_method"]
        page_class_priority_list, page_class_exclusion_list = get_page_class_priority_list(field_details["field_id"],
                                                                                           priorities)
        for entity in field_details["entity_list"]:
            if entity["page_class"] in page_class_priority_list or ('*' in page_class_priority_list and
                                                                    entity[
                                                                        "page_class"] not in page_class_exclusion_list):
                get_best_match(entity, sub_card_list, search_method, mother_field, field, data_aggregation)
    return sub_card_list, mother_field


def build_vertical_and_horizontal_card(edoc, card_bp, priorities, failed_cards_list, feature_flags):
    return []


def build_vertical_section_card(edoc, card_bp, priorities, failed_cards_list, feature_flags):
    return []


def build_horizontal_card(edoc, card_bp, priorities, medical_coding_service: MedicalCodingUtility, failed_cards_list, feature_flags):
    card_name = card_bp.get('card_name', '')
    groups = None
    try:
        list_of_object_dicts = []
        merge_same_mother_field = card_bp.get('mergeCellsForSameMotherField')
        destination_card = card_bp.get('destinationCard')
        update_card_layout(card_bp)
        required_fields = [field['field_id'].split('.')[-1] for field in card_bp['fields'] if
                           field['role'] == 'requiredChild']
        list_of_objects, mother_field = get_list_of_objects(edoc, card_bp, priorities)
        for obj in list_of_objects:
            entity_dict = get_dictionary_from_class_object(obj)
            list_of_object_dicts.append(entity_dict)

        # This merges multiple candidates for one mother
        list_of_object_dicts = get_merged_field_wise_list_of_entities(list_of_object_dicts, mother_field)
        unique_list_of_object_dicts = remove_duplicates(list_of_object_dicts)

        # This merges the rows with same mother field
        if merge_same_mother_field:
            unique_list_of_object_dicts = merge_objects_with_same_mother(unique_list_of_object_dicts, mother_field)

        mother_field_name = get_mother_field_name(card_bp)
        if 'date' in mother_field_name.lower():
            # exclude objects with only date and relationship field
            unique_list_of_object_dicts = lfilter(lambda obj_dict: len(obj_dict) > 2, unique_list_of_object_dicts)
        if required_fields:
            unique_list_of_object_dicts = lfilter(lambda obj_dict: any(field in obj_dict for field in required_fields),
                                                  unique_list_of_object_dicts)
        unique_list_of_object_dicts = MiscUtility().remove_duplicates_by_subtext_other_format(
            unique_list_of_object_dicts, mother_field, {},
            ["record_locations"])
        unique_list_of_object_dicts = populate_medical_codes_horizontal(unique_list_of_object_dicts, card_bp,
                                                                        medical_coding_service)
        if card_name == 'Reason Dx Plan':
            if Title.SUMMARIZE_RDX in feature_flags:
                summarize_data_using_genai(unique_list_of_object_dicts)
            elif Title.FOLLOWUP_SUMMARY in feature_flags:
                unique_list_of_object_dicts, groups = get_gemini_results_rx(unique_list_of_object_dicts)

        if len(unique_list_of_object_dicts) == 0:
            return []
        new_card = build_smart_ui_card(unique_list_of_object_dicts, card_bp, destination_card, mother_field, index=0, groups=groups)
        return [new_card]
    except Exception as e:
        print(f'ERROR: Build {card_name} Card Failed: {e}')
        failed_cards_list.append({'card_name': f'UDC: {card_name}', 'message': f'{traceback.format_exc()}'})
        return []


def build_vertical_card(edoc, card_bp, priorities, medical_coding_service: MedicalCodingUtility, failed_cards_list, feature_flags):
    vertical_cards = []
    card_name = card_bp.get('card_name', '')
    try:
        vertical_card_limit = card_bp.get('limitNoOfVerticalCard') or 1
        destination_card = card_bp.get('destinationCard')
        required_fields = [field['field_id'].split('.')[-1] for field in card_bp['fields'] if
                           field['role'] == 'requiredChild']
        list_of_objects, mother_field = get_list_of_objects(edoc, card_bp, priorities)
        unique_group_of_objects = group_objects_by_mother_field(list_of_objects, mother_field)
        if mother_field and 'date' in mother_field.lower():
            # exclude objects with only date and relationship field
            unique_group_of_objects = lfilter(lambda obj_: count_not_none_attributes(obj_) > 2, unique_group_of_objects)
        if required_fields:
            unique_group_of_objects = lfilter(lambda obj_: any(hasattr(obj_, field) and getattr(obj_, field) is not None for field in required_fields),
                                              unique_group_of_objects)
        unique_group_of_objects = unique_group_of_objects[:vertical_card_limit]
        update_card_layout(card_bp)
        # if len(unique_group_of_objects) == 0:  # create empty card
        #     card = build_smart_ui_card([], card_bp, destination_card, mother_field, index=0)
        #     vertical_cards.append(card)
        for index, obj in enumerate(unique_group_of_objects):
            entity_dict = {}
            for key, value in obj.__dict__.items():
                if value is None:
                    continue
                if key == const.RECORD_LOCATIONS:
                    entity_dict[key] = value
                else:
                    entity_dict[key] = value[0]
            entity_dict = populate_medical_codes_horizontal([entity_dict], card_bp, medical_coding_service)[0]
            card = build_smart_ui_card([entity_dict], card_bp, destination_card, mother_field, index)
            vertical_cards.append(card)
    except Exception as e:
        print(f'ERROR: Build {card_name} Card Failed: {e}')
        failed_cards_list.append({'card_name': f'UDC: {card_name}', 'message': f'{traceback.format_exc()}'})

    return vertical_cards


def build_card(edoc, card_bp, priorities, medical_coding_service: MedicalCodingUtility, failed_cards_list, feature_flags):
    new_cards = []
    card_layout = card_bp.get("card_layout", "")
    if card_layout == "display_vertically":
        new_cards = build_vertical_card(edoc, card_bp, priorities, medical_coding_service, failed_cards_list, feature_flags)
    elif card_layout == "display_horizontal":
        new_cards = build_horizontal_card(edoc, card_bp, priorities, medical_coding_service, failed_cards_list, feature_flags)
    elif card_layout == "display_vertical_sections":
        new_cards = build_vertical_section_card(edoc, card_bp, priorities, failed_cards_list, feature_flags)
    elif card_layout == "display_vertical_and_horizontal":
        new_cards = build_vertical_and_horizontal_card(edoc, card_bp, priorities, failed_cards_list, feature_flags)

    return new_cards


def create_user_defined_cards(edoc, user_defined_card_url, priorities, failed_cards_list, selected_recall_list,
                              medical_coding_service: MedicalCodingUtility, feature_flags):
    cards = []
    try:
        if not user_defined_card_url:
            return cards
        new_card_schema = storage.download_json(user_defined_card_url)["user_defined_cards"]
        if len(new_card_schema) == 0:
            return cards
        # new_card_schema = main_blueprint["user_defined_cards"]
        for card_bp in new_card_schema:
            if not card_bp.get('isEnabled'):
                continue
            if selected_recall_list and card_bp['card_id'] not in selected_recall_list:
                continue
            new_cards = build_card(edoc, card_bp, priorities, medical_coding_service, failed_cards_list, feature_flags)
            cards.extend(new_cards)
    except Exception as e:
        print(f'ERROR: Create User Defined Cards Failed: {e}')
        failed_cards_list.append({'card_name': 'Used Defined Cards', 'message': f'{traceback.format_exc()}'})
    return cards

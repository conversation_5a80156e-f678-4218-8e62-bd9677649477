[{"schema_id": "insured_details", "layout": "display_vertically", "display_name": "Insured Details", "description": "Basic details of the insured person", "show_relationships": true, "fields": [{"name": "Full Name", "field_id": "insured_details.name", "entity_type": "insured_1.name", "type": "single-value"}, {"name": "First Name", "field_id": "insured_details.name.given_name", "entity_type": "insured_1.name.given_name", "type": "single-value", "disable_priority": true}, {"name": "Last Name", "field_id": "insured_details.name.family_name", "entity_type": "insured_1.name.family_name", "type": "single-value", "disable_priority": true}, {"name": "Insured Type", "field_id": "insured_details.insured_type", "entity_type": "insured_1.cards_generated_insured_type", "type": "single-value"}, {"name": "Date of Birth", "field_id": "insured_details.date_of_birth", "entity_type": "insured_1.birth", "type": "single-value"}, {"name": "Age", "field_id": "insured_details.age", "entity_type": "insured_1.age", "type": "single-value"}, {"name": "Birth State", "field_id": "insured_details.birth_state", "entity_type": "insured_1.birth.state", "type": "single-value"}, {"name": "Birth Country", "field_id": "insured_details.birth_country", "entity_type": "insured_1.place_of_birth", "type": "single-value"}, {"name": "Gender", "field_id": "insured_details.gender", "entity_type": "insured_1.gender", "type": "single-value"}, {"name": "Address", "field_id": "insured_details.address", "entity_type": "insured_1.address", "type": "single-value"}, {"name": "Address: Street Line", "field_id": "insured_details.address.line", "entity_type": "insured_1.address.line", "type": "single-value", "disable_priority": true}, {"name": "Address: City", "field_id": "insured_details.address.city", "entity_type": "insured_1.address.city", "type": "single-value", "disable_priority": true}, {"name": "Address: State", "field_id": "insured_details.address.state", "entity_type": "insured_1.address.state", "type": "single-value", "disable_priority": true}, {"name": "Address: Zip Code", "field_id": "insured_details.address.zipcode", "entity_type": "insured_1.address.zipcode", "type": "single-value", "disable_priority": true}, {"name": "Address: Country", "field_id": "insured_details.address.country", "entity_type": "insured_1.address.country", "type": "single-value", "disable_priority": true}, {"name": "Marital Status", "field_id": "insured_details.marital_status", "entity_type": "insured_1.marital", "type": "single-value"}, {"name": "Occupation", "field_id": "insured_details.occupation", "entity_type": "insured_1.occupation", "type": "single-value"}, {"name": "Occupation Duty", "field_id": "insured_details.occupation_duty", "entity_type": "insured_1.occupation.duty", "type": "single-value"}, {"name": "Occupation Risk", "field_id": "insured_details.occupation_risk", "entity_type": "insured_1.occupation.risk", "type": "single-value"}, {"name": "Weight", "field_id": "insured_details.weight", "entity_type": "insured_1.weight_kg", "type": "single-value"}, {"name": "Height", "field_id": "insured_details.height", "entity_type": "insured_1.height_cm", "type": "single-value"}, {"name": "Alcohol", "field_id": "insured_details.alcohol", "entity_type": "insured_1.alcohol", "type": "single-value"}, {"name": "Smoker Status", "field_id": "insured_details.smoking", "entity_type": "insured_1.smoking", "type": "single-value"}, {"name": "Drugs", "field_id": "insured_details.drugs", "entity_type": "insured_1.drug", "type": "single-value"}, {"name": "Marijuana", "field_id": "insured_details.marijuana", "entity_type": "insured_1.marijuana", "type": "single-value"}, {"name": "Avocation", "field_id": "insured_details.avocation", "entity_type": "avocation.highrisk", "type": "single-value"}, {"name": "Travel", "field_id": "insured_details.travel", "entity_type": "travel.international", "type": "single-value"}, {"name": "Behavior", "field_id": "insured_details.behavior", "entity_type": "lifestyle", "type": "single-value"}, {"name": "Social Security Number", "field_id": "insured_details.ssn", "entity_type": "insured_1.ssn", "type": "single-value"}, {"name": "Owner Relationship", "field_id": "insured_details.owner_relationship", "entity_type": "payor.relationship_with_insured_1", "type": "single-value"}, {"name": "Beneficiary Relationship", "field_id": "insured_details.beneficiary_relationship", "entity_type": "beneficiary.relationship_with_insured_1", "type": "single-value"}, {"name": "Citizen of", "field_id": "insured_details.citizenship", "entity_type": "insured_1.citizenship", "type": "single-value"}, {"name": "Citizen of the US?", "field_id": "insured_details.us_citizen", "entity_type": "insured_1.us_citizen", "type": "single-value"}, {"name": "Resident of", "field_id": "insured_details.residentship", "entity_type": "insured_1.residentship", "type": "single-value"}, {"name": "Resident of the US?", "field_id": "insured_details.us_resident", "entity_type": "insured_1.cards_generated_us_resident", "type": "single-value"}, {"name": "Driver's License #", "field_id": "insured_details.license_number", "entity_type": "insured_1.license.number", "type": "single-value"}, {"name": "Driver's License State", "field_id": "insured_details.license_state", "entity_type": "insured_1.license.state", "type": "single-value"}, {"name": "Income / Salary", "field_id": "insured_details.income", "entity_type": "insured_1.income", "type": "single-value"}, {"name": "Net Worth", "field_id": "insured_details.net_worth", "entity_type": "insured_1.net_worth", "type": "single-value"}, {"name": "Previously Rated/Declined", "field_id": "insured_details.insurance_rated_differentialy_or_declined", "entity_type": "insured_1.insurance_rated_differentialy_or_declined.yes", "type": "single-value"}, {"name": "Filed Bankruptcy", "field_id": "insured_details.filed_bankruptcy", "entity_type": "insured_1.filed_bankruptcy.yes", "type": "single-value"}, {"name": "Application Sign Date", "field_id": "insured_details.sign_date", "entity_type": "insured_1.sign.date", "type": "single-value"}, {"name": "Application Sign State", "field_id": "insured_details.sign_state", "entity_type": "insured_1.sign.state", "type": "single-value"}, {"name": "Client Company Assessment", "field_id": "insured_details.client_company_assessment", "entity_type": "insured_1.policy.decision", "type": "single-value"}, {"name": "Policy Reason", "field_id": "insured_details.policy_reason", "entity_type": "insured_1.policy.reason", "type": "single-value"}]}, {"schema_id": "claim_details", "layout": "display_vertically", "display_name": "<PERSON><PERSON><PERSON>", "description": "Claim details of the insured person", "fields": [{"name": "Policy Number", "field_id": "claim_details.policy_number", "entity_type": "policy_number", "type": "single-value"}, {"name": "Claim Number", "field_id": "claim_details.claim_number", "entity_type": "claim_number", "type": "single-value"}, {"name": "Policy Effective Date", "field_id": "claim_details.policy_effective_date", "entity_type": "policy.effect_date", "type": "single-value"}, {"name": "Claimed Date of Loss", "field_id": "claim_details.date_of_loss", "entity_type": "application.date", "type": "single-value"}, {"name": "Primary Diagnosis", "field_id": "claim_details.primary_diagnosis", "entity_type": "primary.diagnosis", "type": "single-value"}, {"name": "Autopsy Performed", "field_id": "claim_details.autopsy_performed", "entity_type": "autopsy.performed", "type": "single-value"}, {"name": "Policy Expiration Date", "field_id": "claim_details.policy_exp_date", "entity_type": "dummy.need_to_update", "type": "placeholder"}, {"name": "Original sum at risk", "field_id": "claim_details.sum_at_risk", "entity_type": "dummy.need_to_update", "type": "placeholder"}]}, {"schema_id": "technical_details", "layout": "display_vertically", "display_name": "Technical Details", "description": "Policy details of the insured person", "show_relationships": true, "fields": [{"name": "Cedent Product Type", "field_id": "technical_details.product_type", "entity_type": "product_type", "type": "single-value"}, {"name": "Lump Sum / Face Amount", "field_id": "technical_details.sum_insured", "entity_type": "sum_insured", "type": "single-value"}, {"name": "Benefit Term Type", "field_id": "technical_details.duration_type", "entity_type": "duration.type", "type": "single-value"}, {"name": "Benefit Term", "field_id": "technical_details.duration", "entity_type": "duration", "type": "single-value"}, {"name": "Benefit Start Date", "field_id": "technical_details.policy_start_date", "entity_type": "policy.start_date", "type": "single-value"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "field_id": "technical_details.currency", "entity_type": "currency", "type": "single-value"}, {"name": "Premium Payment Period", "field_id": "technical_details.payment_period", "entity_type": "policy.product.premium.payment_period.age", "type": "single-value"}, {"name": "Annuity", "field_id": "technical_details.annuity", "entity_type": "annuity", "type": "single-value"}, {"name": "Annuity Duration Type", "field_id": "technical_details.annuity_duration_type", "entity_type": "annuity_duration.type", "type": "single-value"}, {"name": "Annuity Duration", "field_id": "technical_details.annuity_duration", "entity_type": "policy.annuity.duration.payment_period.end.age", "type": "single-value"}, {"name": "Annuity End Date", "field_id": "technical_details.annuity_end_date", "entity_type": "policy.annuity.duration.payment_period.end.date", "type": "single-value"}, {"name": "Cedent Product Name", "field_id": "technical_details.plan_type", "entity_type": "policy.product.name", "type": "single-value"}, {"name": "Cedent Location", "field_id": "technical_details.cedent_location", "entity_type": "dummy.need_to_update", "type": "placeholder"}, {"name": "Cedent Person ID", "field_id": "technical_details.cedent_person_id", "entity_type": "dummy.need_to_update", "type": "placeholder"}, {"name": "Cedent Notification Date", "field_id": "technical_details.cedent_notification_date", "entity_type": "dummy.need_to_update", "type": "placeholder"}, {"name": "GenRe Notification Date", "field_id": "technical_details.genre_notification_date", "entity_type": "dummy.need_to_update", "type": "placeholder"}, {"name": "<PERSON><PERSON><PERSON> Amou<PERSON>", "field_id": "technical_details.premium_amount", "entity_type": "policy.product.premium.amount", "type": "single-value"}, {"name": "Reinsurance Amount", "field_id": "technical_details.reinsurance_amount", "entity_type": "reinsurance.requested_amount", "type": "single-value"}, {"name": "Ultimate Risk Amount", "field_id": "technical_details.risk_amount", "entity_type": "reinsurance.total_amount", "type": "single-value"}, {"name": "Indexation", "field_id": "technical_details.indexation", "entity_type": "policy.indexation.value", "type": "single-value"}, {"name": "Waiting/Deferred Period", "field_id": "technical_details.deferment_period", "entity_type": "benefit.deferment", "type": "single-value"}, {"name": "Policy Purpose List", "field_id": "technical_details.policy_purpose", "entity_type": "merged_policy.purpose", "type": "single-value"}]}, {"schema_id": "application_details", "layout": "display_vertically", "display_name": "Application Details", "description": "Application details of the insured person", "fields": [{"name": "External Application ID", "field_id": "application_details.application_number", "entity_type": "application.number", "type": "single-value"}, {"name": "<PERSON><PERSON>", "field_id": "application_details.cedent_name", "entity_type": "company.name", "type": "single-value"}, {"name": "Client Company Policy #", "field_id": "application_details.application_id", "entity_type": "policy_number", "type": "single-value"}, {"name": "Date Received", "field_id": "application_details.date_received", "entity_type": "application.date", "type": "single-value"}, {"name": "Client Company Underwriter", "field_id": "application_details.company_underwriter", "entity_type": "underwriter.name", "type": "single-value"}]}, {"schema_id": "physical_exams", "layout": "display_horizontal", "display_name": "Physical Exams", "description": "Physical exams performed on the insured person", "fields": [{"name": "Site", "field_id": "physical_exams.site", "entity_type": "pe.name", "type": "single-value"}, {"name": "Observation", "field_id": "physical_exams.observation", "entity_type": "pe.value", "type": "single-value"}, {"name": "Visit Date", "field_id": "physical_exams.date", "entity_type": "encounter.date", "type": "single-value"}]}, {"schema_id": "vital_signs", "layout": "display_horizontal", "display_name": "Vital Signs", "description": "Vital details of the insured person", "show_relationships": true, "fields": [{"name": "Date", "field_id": "vital_signs.date", "entity_type": "encounter.date", "type": "single-value", "width": "90px"}, {"name": "Blood Pressure", "field_id": "vital_signs.blood_pressure", "entity_type": "vital.blood", "type": "single-value", "width": "115px"}, {"name": "Weight", "field_id": "vital_signs.weight", "entity_type": "vital.weight_lbs", "type": "single-value", "width": "115px"}, {"name": "Height", "field_id": "vital_signs.height", "entity_type": "vital.height_in", "type": "single-value", "width": "115px"}, {"name": "BMI", "field_id": "vital_signs.bmi", "entity_type": "vital.bmi", "type": "single-value", "width": "90px"}, {"name": "Pulse", "field_id": "vital_signs.pulse", "entity_type": "vital.heart_rate", "type": "single-value", "width": "100px"}, {"name": "Oxygen", "field_id": "vital_signs.oxygen", "entity_type": "vital.oxygen", "type": "single-value", "width": "100px"}, {"name": "Respiration", "field_id": "vital_signs.respiration", "entity_type": "vital.respiration", "type": "single-value", "width": "120px"}, {"name": "Temperature", "field_id": "vital_signs.temperature", "entity_type": "vital.temperature_f", "type": "single-value", "width": "120px"}]}, {"schema_id": "ccda_vitals", "layout": "display_horizontal", "display_name": "CCDA Vitals", "description": "Vital Signs of the insured person (CCDA case)", "show_relationships": true, "fields": [{"name": "Type", "field_id": "ccda_vitals.type", "entity_type": "", "type": "single-value", "width": "90px"}, {"name": "Value", "field_id": "ccda_vitals.value", "entity_type": "", "type": "single-value", "width": "115px"}, {"name": "Unit", "field_id": "ccda_vitals.unit", "entity_type": "", "type": "single-value", "width": "115px"}, {"name": "Date", "field_id": "ccda_vitals.date", "entity_type": "", "type": "single-value", "width": "115px"}, {"name": "LOINC", "field_id": "ccda_vitals.loinc", "entity_type": "", "type": "single-value", "width": "90px"}]}, {"schema_id": "diagnoses", "layout": "display_horizontal", "display_name": "Diagnoses", "description": "List of Diagnosis of the insured person", "fields": [{"name": "Diagnosis", "field_id": "diagnoses.diagnosis", "entity_type": "history.diag", "type": "single-value"}, {"name": "Type", "field_id": "diagnoses.type", "entity_type": "history.diag", "type": "single-value"}, {"name": "ICD-10", "field_id": "diagnoses.icd_10", "entity_type": "icd10", "type": "single-value"}, {"name": "Condition", "field_id": "diagnoses.condition", "entity_type": "history.diag", "type": "single-value"}, {"name": "ICD-10 Name", "field_id": "diagnoses.description", "entity_type": "diagnoses.description", "type": "single-value"}, {"name": "Diagnosis Category", "field_id": "diagnoses.chapter", "entity_type": "diagnoses_chapter", "type": "single-value", "allow_filter": true}, {"name": "ICD Section", "field_id": "diagnoses.section", "entity_type": "diagnoses_section", "type": "single-value", "allow_filter": true}, {"name": "Impairment", "field_id": "diagnoses.impairment", "entity_type": "diagnoses_impairment", "type": "single-value", "allow_filter": true}, {"name": "SNOMED", "field_id": "diagnoses.snomed", "entity_type": "", "type": "single-value"}, {"name": "Visit Date", "field_id": "diagnoses.date", "entity_type": "encounter.date", "type": "single-value"}, {"name": "Diagnosis Date", "field_id": "diagnoses.diag_date", "entity_type": "diagnosis.date", "type": "single-value"}]}, {"schema_id": "encounter_dx_details", "layout": "display_horizontal", "display_name": "Encounter Dx Details", "description": "Information related to Doctor's visit and Diagnosis", "fields": [{"name": "Diagnosis", "field_id": "encounter_dx_details.diagnosis", "entity_type": "history.diag", "type": "single-value"}, {"name": "Type", "field_id": "encounter_dx_details.type", "entity_type": "history.diag", "type": "single-value"}, {"name": "ICD-10", "field_id": "encounter_dx_details.icd_10", "entity_type": "icd10", "type": "single-value"}, {"name": "SNOMED", "field_id": "encounter_dx_details.SNOMED", "entity_type": "SNOMED", "type": "single-value"}, {"name": "Visit Date", "field_id": "encounter_dx_details.date", "entity_type": "encounter.date", "type": "single-value"}, {"name": "Reason of Visit", "field_id": "encounter_dx_details.reason_of_visit", "entity_type": "doctor.address", "type": "single-value"}, {"name": "Diagnosis Date", "field_id": "encounter_dx_details.diag_date", "entity_type": "reason_of_visit", "type": "single-value"}, {"name": "Performer Name", "field_id": "encounter_dx_details.performer_name", "entity_type": "doctor.name", "type": "single-value"}, {"name": "Performer Address", "field_id": "encounter_dx_details.performer_address", "entity_type": "doctor.address", "type": "single-value"}]}, {"schema_id": "claimed_injury_details", "layout": "display_horizontal", "display_name": "Clai<PERSON>", "description": "Injury related diagnosis details of the insured person", "fields": [{"name": "<PERSON><PERSON><PERSON>", "field_id": "claimed_injury_details.claimed_injury", "entity_type": "hpi.symptom.positive", "type": "single-value"}, {"name": "ICD-10", "field_id": "claimed_injury_details.icd_10", "entity_type": "icd10", "type": "single-value"}, {"name": "Date", "field_id": "claimed_injury_details.date", "entity_type": "encounter.date", "type": "single-value"}]}, {"schema_id": "medication_plan", "layout": "display_horizontal", "display_name": "Medication Plan", "description": "Medication plan and history of the insured person", "fields": [{"name": "Condition", "field_id": "medication_plan.condition", "entity_type": "Condition", "type": "single-value"}, {"name": "Medication", "field_id": "medication_plan.medication", "entity_type": "current.medication", "type": "single-value"}, {"name": "Instruction", "field_id": "medication_plan.instruction", "entity_type": "medication.instruction", "type": "single-value"}, {"name": "Duration", "field_id": "medication_plan.duration", "entity_type": "medication.duration", "type": "single-value"}, {"name": "Dosage", "field_id": "medication_plan.dosage", "entity_type": "medication.dosage", "type": "single-value"}, {"name": "Unit", "field_id": "medication_plan.unit", "entity_type": "medication.unit", "type": "single-value"}, {"name": "Start", "field_id": "medication_plan.start", "entity_type": "medication.start", "type": "single-value"}, {"name": "End", "field_id": "medication_plan.end", "entity_type": "medication.end", "type": "single-value"}, {"name": "Rx Code", "field_id": "medication_plan.rx_code", "entity_type": "medication.rx_code", "type": "single-value"}, {"name": "SNOMED", "field_id": "medication_plan.snomed", "entity_type": "medication.snomed", "type": "single-value"}]}, {"schema_id": "medications", "layout": "display_horizontal", "display_name": "Medications", "description": "Medications taken by the insured person", "fields": [{"name": "Condition", "field_id": "medications.condition", "entity_type": "illness.condition", "type": "single-value"}, {"name": "Type", "field_id": "medications.type", "entity_type": "medications.type", "type": "single-value"}, {"name": "Medication", "field_id": "medications.medication", "entity_type": "current.medication", "type": "single-value"}, {"name": "Instruction", "field_id": "medications.instruction", "entity_type": "medication.instruction", "type": "single-value"}, {"name": "Duration", "field_id": "medications.duration", "entity_type": "medication.duration", "type": "single-value"}, {"name": "Quantity", "field_id": "medications.quantity", "entity_type": "medication.quantity", "type": "single-value"}, {"name": "Dosage", "field_id": "medications.dosage", "entity_type": "medication.dosage", "type": "single-value"}, {"name": "Unit", "field_id": "medications.unit", "entity_type": "medication.unit", "type": "single-value"}, {"name": "Start", "field_id": "medications.start", "entity_type": "medication.start", "type": "single-value"}, {"name": "End", "field_id": "medications.end", "entity_type": "medication.end", "type": "single-value"}, {"name": "Mentioned Date", "field_id": "medications.date", "entity_type": "medication.date", "type": "single-value"}, {"name": "Rx Code", "field_id": "medications.rx_code", "entity_type": "medication.rx_code", "type": "single-value"}, {"name": "SNOMED", "field_id": "medications.snomed", "entity_type": "medication.snomed", "type": "single-value"}, {"name": "Category", "field_id": "medications.category", "entity_type": "medication.category", "type": "single-value"}, {"name": "Provider", "field_id": "medications.provider", "entity_type": "medication.provider", "type": "single-value"}]}, {"schema_id": "encounter_details", "layout": "display_horizontal", "display_name": "Encounter Details", "description": "Doctor's visit information", "fields": [{"name": "Date", "field_id": "encounter_details.date", "entity_type": "encounter.date", "type": "single-value"}, {"name": "Type", "field_id": "encounter_details.type", "entity_type": "encounter.date", "type": "single-value"}, {"name": "Department", "field_id": "encounter_details.department", "entity_type": "encounter.performer", "type": "single-value"}, {"name": "Description", "field_id": "encounter_details.description", "entity_type": "assessment.diag", "type": "single-value"}]}, {"schema_id": "provider_details", "layout": "display_horizontal", "display_name": "Provider Details", "description": "Details of the doctors and medical facilities", "fields": [{"name": "Doctor Name", "field_id": "provider_details.doctor_name", "entity_type": "doctor.name", "type": "single-value"}, {"name": "Doctor Registration Number", "field_id": "provider_details.doctor_registration_numer", "entity_type": "doctor.license_number", "type": "single-value"}, {"name": "Facility Name", "field_id": "provider_details.facility_name", "entity_type": "encounter.performer", "type": "single-value"}, {"name": "Facility Address", "field_id": "provider_details.facility_address", "entity_type": "encounter.address", "type": "single-value"}]}, {"schema_id": "laboratory_results", "layout": "display_horizontal", "display_name": "Laboratory Results", "description": "List of lab tests and results of the insured person", "show_relationships": true, "fields": [{"name": "Name", "field_id": "laboratory_results.name", "entity_type": "o.name", "type": "single-value", "width": "152px", "allow_sorting": true, "allow_filter": true}, {"name": "Value", "field_id": "laboratory_results.value", "entity_type": "o.value", "type": "single-value", "width": "72px"}, {"name": "Rating", "field_id": "laboratory_results.rating", "entity_type": "o.rating", "type": "single-value", "width": "72px"}, {"name": "Low", "field_id": "laboratory_results.low", "entity_type": "o.ref_low", "type": "single-value", "width": "72px"}, {"name": "High", "field_id": "laboratory_results.high", "entity_type": "o.ref_high", "type": "single-value", "width": "72px"}, {"name": "Descriptive Reference", "field_id": "laboratory_results.descriptive_reference", "entity_type": "o.ref", "type": "single-value", "width": "144px"}, {"name": "Unit", "field_id": "laboratory_results.units", "entity_type": "o.unit", "type": "single-value", "width": "80px"}, {"name": "Panel", "field_id": "laboratory_results.panel", "entity_type": "lab.panel", "type": "single-value", "width": "140px"}, {"name": "Specimen", "field_id": "laboratory_results.specimen", "entity_type": "lab.specimen", "type": "single-value", "width": "110px"}, {"name": "Date", "field_id": "laboratory_results.date", "entity_type": "lab.collected", "type": "single-value", "width": "100px"}, {"name": "Laboratory", "field_id": "laboratory_results.laboratory", "entity_type": "lab.performer", "type": "single-value", "width": "132px", "allow_filter": true}, {"name": "LOINC", "field_id": "laboratory_results.loinc", "entity_type": "loinc", "type": "single-value", "width": "80px"}, {"name": "Fasting Status", "field_id": "laboratory_results.fasting", "entity_type": "fasting.status.yes", "type": "placeholder", "width": "95px"}, {"name": "Impairment", "field_id": "laboratory_results.impairment", "entity_type": "laboratory_results_impairment", "type": "single-value", "allow_filter": true}]}, {"schema_id": "abnormal_observations", "layout": "display_horizontal", "display_name": "Abnormal Observations", "description": "Lab results whose rating is high/low/abnormal", "fields": [{"name": "Name", "field_id": "abnormal_observations.name", "entity_type": "o.name", "type": "single-value", "width": "152px"}, {"name": "Value", "field_id": "abnormal_observations.value", "entity_type": "o.value", "type": "single-value", "width": "72px"}, {"name": "Rating", "field_id": "abnormal_observations.rating", "entity_type": "o.rating", "type": "single-value", "width": "72px"}, {"name": "Low", "field_id": "abnormal_observations.low", "entity_type": "o.ref_low", "type": "single-value", "width": "72px"}, {"name": "High", "field_id": "abnormal_observations.high", "entity_type": "o.ref_high", "type": "single-value", "width": "72px"}, {"name": "Descriptive Reference", "field_id": "abnormal_observations.descriptive_reference", "entity_type": "o.ref", "type": "single-value", "width": "144px"}, {"name": "Unit", "field_id": "abnormal_observations.units", "entity_type": "o.unit", "type": "single-value", "width": "80px"}, {"name": "Panel", "field_id": "abnormal_observations.panel", "entity_type": "lab.panel", "type": "single-value", "width": "140px"}, {"name": "Specimen", "field_id": "abnormal_observations.specimen", "entity_type": "lab.specimen", "type": "single-value", "width": "110px"}, {"name": "Date", "field_id": "abnormal_observations.date", "entity_type": "lab.collected", "type": "single-value", "width": "100px"}, {"name": "Laboratory", "field_id": "abnormal_observations.laboratory", "entity_type": "lab.performer", "type": "single-value", "width": "132px"}, {"name": "LOINC", "field_id": "abnormal_observations.loinc", "entity_type": "loinc", "type": "single-value", "width": "80px"}, {"name": "LOINC Panel", "field_id": "abnormal_observations.panel_loinc", "entity_type": "", "type": "single-value", "width": "140px"}, {"name": "Fasting Status", "field_id": "abnormal_observations.fasting", "entity_type": "fasting.status.yes", "type": "placeholder", "width": "95px"}, {"name": "Impairment", "field_id": "abnormal_observations.impairment", "entity_type": "abnormal_observations_impairment", "type": "single-value", "allow_filter": true}]}, {"schema_id": "reason_of_visit", "layout": "display_horizontal", "display_name": "Reason Of Visit", "description": "Doctor's Visit details of the insured person", "fields": [{"name": "Date", "field_id": "reason_of_visit.date", "entity_type": "encounter.date", "type": "single-value"}, {"name": "Reason", "field_id": "reason_of_visit.reason", "entity_type": "reason", "type": "single-value"}, {"name": "Plan", "field_id": "reason_of_visit.plan", "entity_type": "plan.surgery", "type": "single-value"}]}, {"schema_id": "family_history", "layout": "display_horizontal", "display_name": "Family History", "description": "Diagnosis History of family members of the insured person", "fields": [{"name": "Family", "field_id": "family_history.family", "entity_type": "", "type": "single-value"}, {"name": "Diagnosis", "field_id": "family_history.diagnosis", "entity_type": "", "type": "single-value"}, {"name": "Age Diagnosed", "field_id": "family_history.age_diagnosed", "entity_type": "", "type": "single-value"}, {"name": "Age of Death", "field_id": "family_history.death_age", "entity_type": "", "type": "single-value"}, {"name": "SNOMED", "field_id": "family_history.snomed", "entity_type": "", "type": "single-value"}]}, {"schema_id": "list_of_events", "layout": "display_vertical_and_horizontal", "display_name": "List Of Events", "description": "List of events of the insured person", "record_schema_list": [{"record_schema_id": "list_of_events.header", "fields": [{"name": "Event Type", "field_id": "", "entity_type": "", "type": "static"}, {"name": "Date", "field_id": "", "entity_type": "", "type": "static"}, {"name": "Performer", "field_id": "", "entity_type": "", "type": "static"}]}, {"record_schema_id": "list_of_events.lab_collected", "fields": [{"name": "Laboratory Visit", "field_id": "list_of_events.event_type", "entity_type": "", "type": "static"}, {"name": "Date", "field_id": "list_of_events.date", "entity_type": "lab.collected", "type": "single-value"}, {"name": "Performer", "field_id": "list_of_events.performer", "entity_type": "encounter.performer", "type": "single-value"}]}, {"record_schema_id": "list_of_events.lab_issued", "fields": [{"name": "Laboratory Visit", "field_id": "list_of_events.event_type", "entity_type": "", "type": "static"}, {"name": "Date", "field_id": "list_of_events.date", "entity_type": "lab.issued", "type": "single-value"}, {"name": "Performer", "field_id": "list_of_events.performer", "entity_type": "encounter.performer", "type": "single-value"}]}, {"record_schema_id": "list_of_events.encounter_date", "fields": [{"name": "Office Visit", "field_id": "list_of_events.event_type", "entity_type": "", "type": "static"}, {"name": "Date", "field_id": "list_of_events.date", "entity_type": "encounter.date", "type": "single-value"}, {"name": "Performer", "field_id": "list_of_events.performer", "entity_type": "encounter.performer", "type": "single-value"}]}, {"record_schema_id": "list_of_events.o_issued", "fields": [{"name": "Laboratory Visit", "field_id": "list_of_events.event_type", "entity_type": "", "type": "static"}, {"name": "Date", "field_id": "list_of_events.date", "entity_type": "o.issued", "type": "single-value"}, {"name": "Performer", "field_id": "list_of_events.performer", "entity_type": "encounter.performer", "type": "single-value"}]}, {"record_schema_id": "list_of_events.surgery_date", "fields": [{"name": "Surgery", "field_id": "list_of_events.event_type", "entity_type": "", "type": "static"}, {"name": "Date", "field_id": "list_of_events.date", "entity_type": "surgery.date", "type": "single-value"}, {"name": "Performer", "field_id": "list_of_events.performer", "entity_type": "encounter.performer", "type": "single-value"}]}, {"record_schema_id": "list_of_events.imaging_date", "fields": [{"name": "Office Visit", "field_id": "list_of_events.event_type", "entity_type": "", "type": "static"}, {"name": "Date", "field_id": "list_of_events.date", "entity_type": "imaging.date", "type": "single-value"}, {"name": "Performer", "field_id": "list_of_events.performer", "entity_type": "encounter.performer", "type": "single-value"}]}, {"record_schema_id": "list_of_events.lab_received", "fields": [{"name": "Laboratory Visit", "field_id": "list_of_events.event_type", "entity_type": "", "type": "static"}, {"name": "Date", "field_id": "list_of_events.date", "entity_type": "imaging.date", "type": "single-value"}, {"name": "Performer", "field_id": "list_of_events.performer", "entity_type": "encounter.performer", "type": "single-value"}]}]}, {"schema_id": "social_history", "layout": "display_vertical_and_horizontal", "display_name": "Social History", "description": "Substance (drugs, alcohol, smoking, etc) use history of the insured person", "show_relationships": true, "record_schema_list": [{"record_schema_id": "social_history.header", "fields": [{"name": "Type", "field_id": "", "entity_type": "", "type": "static", "width": "120px"}, {"name": "Report Date", "field_id": "", "entity_type": "", "type": "static", "width": "110px"}, {"name": "Desc.", "field_id": "", "entity_type": "", "type": "static", "width": "100px"}, {"name": "<PERSON><PERSON>", "field_id": "", "entity_type": "", "type": "static", "width": "120px"}, {"name": "Qty.", "field_id": "", "entity_type": "", "type": "static", "width": "60px"}, {"name": "Unit", "field_id": "", "entity_type": "", "type": "static", "width": "60px"}, {"name": "Start", "field_id": "", "entity_type": "", "type": "static", "width": "85px"}, {"name": "End", "field_id": "", "entity_type": "", "type": "static", "width": "85px"}, {"name": "Duration", "field_id": "", "entity_type": "", "type": "static", "width": "85px"}, {"name": "Frequency", "field_id": "", "entity_type": "", "type": "static", "width": "85px"}, {"name": "SNOMED", "field_id": "", "entity_type": "", "type": "static", "width": "150px"}]}, {"record_schema_id": "social_history.smoking_status", "fields": [{"name": "Smoking", "field_id": "social_history.smoking_status", "entity_type": "social_history.smoking.status", "type": "static"}, {"name": "Date", "field_id": "social_history.date", "entity_type": "", "type": "single-value"}, {"name": "Description", "field_id": "social_history.description", "entity_type": "social_history.description", "type": "single-value"}, {"name": "<PERSON><PERSON>", "field_id": "social_history.device", "entity_type": "social_history.device", "type": "single-value"}, {"name": "Quantity", "field_id": "social_history.quantity", "entity_type": "social_history.quantity", "type": ""}, {"name": "Unit", "field_id": "social_history.unit", "entity_type": "social_history.unit", "type": ""}, {"name": "Start Date", "field_id": "social_history.start_date", "entity_type": "social_history.start_date", "type": ""}, {"name": "End Date", "field_id": "social_history.end_date", "entity_type": "social_history.duration", "type": ""}, {"name": "Duration", "field_id": "social_history.duration", "entity_type": "social_history.duration", "type": ""}, {"name": "Frequency", "field_id": "social_history.frequency", "entity_type": "social_history.frequency", "type": ""}, {"name": "SNOMED", "field_id": "social_history.snomed", "entity_type": "snomed", "type": ""}]}, {"record_schema_id": "social_history.social_smoking", "fields": [{"name": "Smoking", "field_id": "social_history.addiction", "entity_type": "", "type": "static"}, {"name": "Date", "field_id": "social_history.date", "entity_type": "vital.date", "type": "single-value"}, {"name": "Description", "field_id": "social_history.status", "entity_type": "social.smoking", "type": "single-value"}, {"name": "<PERSON><PERSON>", "field_id": "social_history.device", "entity_type": "social_history.device", "type": "single-value"}, {"name": "Quantity", "field_id": "social_history.quantity", "entity_type": "social_history.quantity", "type": ""}, {"name": "Unit", "field_id": "social_history.unit", "entity_type": "social_history.unit", "type": ""}, {"name": "Start Date", "field_id": "social_history.start_date", "entity_type": "social_history.start_date", "type": ""}, {"name": "End Date", "field_id": "social_history.end_date", "entity_type": "social_history.duration", "type": ""}, {"name": "Duration", "field_id": "social_history.duration", "entity_type": "social_history.duration", "type": ""}, {"name": "Frequency", "field_id": "social_history.frequency", "entity_type": "social_history.frequency", "type": ""}, {"name": "SNOMED", "field_id": "social_history.snomed", "entity_type": "snomed", "type": ""}]}, {"record_schema_id": "social_history.tobacco_status", "fields": [{"name": "Tobacco", "field_id": "social_history.tobacco_status", "entity_type": "social_history.tobacco.status", "type": "static"}, {"name": "Date", "field_id": "social_history.date", "entity_type": "", "type": "single-value"}, {"name": "Description", "field_id": "social_history.description", "entity_type": "social_history.description", "type": "single-value"}, {"name": "<PERSON><PERSON>", "field_id": "social_history.device", "entity_type": "social_history.device", "type": "single-value"}, {"name": "Quantity", "field_id": "social_history.quantity", "entity_type": "social_history.quantity", "type": ""}, {"name": "Unit", "field_id": "social_history.unit", "entity_type": "social_history.unit", "type": ""}, {"name": "Start Date", "field_id": "social_history.start_date", "entity_type": "social_history.start_date", "type": ""}, {"name": "End Date", "field_id": "social_history.end_date", "entity_type": "social_history.duration", "type": ""}, {"name": "Duration", "field_id": "social_history.duration", "entity_type": "social_history.duration", "type": ""}, {"name": "Frequency", "field_id": "social_history.frequency", "entity_type": "social_history.frequency", "type": ""}, {"name": "SNOMED", "field_id": "social_history.snomed", "entity_type": "snomed", "type": ""}]}, {"record_schema_id": "social_history.alcohol_status", "fields": [{"name": "Alcohol", "field_id": "social_history.alcohol_status", "entity_type": "social_history.alcohol.status", "type": "static"}, {"name": "Date", "field_id": "social_history.date", "entity_type": "", "type": "single-value"}, {"name": "Description", "field_id": "social_history.description", "entity_type": "social_history.description", "type": "single-value"}, {"name": "<PERSON><PERSON>", "field_id": "social_history.device", "entity_type": "social_history.device", "type": "single-value"}, {"name": "Quantity", "field_id": "social_history.quantity", "entity_type": "social_history.quantity", "type": ""}, {"name": "Unit", "field_id": "social_history.unit", "entity_type": "social_history.unit", "type": ""}, {"name": "Start Date", "field_id": "social_history.start_date", "entity_type": "social_history.start_date", "type": ""}, {"name": "End Date", "field_id": "social_history.end_date", "entity_type": "social_history.duration", "type": ""}, {"name": "Duration", "field_id": "social_history.duration", "entity_type": "social_history.duration", "type": ""}, {"name": "Frequency", "field_id": "social_history.frequency", "entity_type": "social_history.frequency", "type": ""}, {"name": "SNOMED", "field_id": "social_history.snomed", "entity_type": "snomed", "type": ""}]}, {"record_schema_id": "social_history.social_alcohol", "fields": [{"name": "Alcohol", "field_id": "social_history.addiction", "entity_type": "", "type": "static"}, {"name": "Date", "field_id": "social_history.date", "entity_type": "vital.date", "type": "single-value"}, {"name": "Description", "field_id": "social_history.status", "entity_type": "social.alcohol", "type": "single-value"}, {"name": "<PERSON><PERSON>", "field_id": "social_history.device", "entity_type": "social_history.device", "type": "single-value"}, {"name": "Quantity", "field_id": "social_history.quantity", "entity_type": "social_history.quantity", "type": ""}, {"name": "Unit", "field_id": "social_history.unit", "entity_type": "social_history.unit", "type": ""}, {"name": "Start Date", "field_id": "social_history.start_date", "entity_type": "social_history.start_date", "type": ""}, {"name": "End Date", "field_id": "social_history.end_date", "entity_type": "social_history.duration", "type": ""}, {"name": "Duration", "field_id": "social_history.duration", "entity_type": "social_history.duration", "type": ""}, {"name": "Frequency", "field_id": "social_history.frequency", "entity_type": "social_history.frequency", "type": ""}, {"name": "SNOMED", "field_id": "social_history.snomed", "entity_type": "snomed", "type": ""}]}, {"record_schema_id": "social_history.drugs_status", "fields": [{"name": "Drugs", "field_id": "social_history.drugs_status", "entity_type": "social_history.drugs.status", "type": "static"}, {"name": "Date", "field_id": "social_history.date", "entity_type": "", "type": "single-value"}, {"name": "Description", "field_id": "social_history.description", "entity_type": "social_history.description", "type": "single-value"}, {"name": "<PERSON><PERSON>", "field_id": "social_history.device", "entity_type": "social_history.device", "type": "single-value"}, {"name": "Quantity", "field_id": "social_history.quantity", "entity_type": "social_history.quantity", "type": ""}, {"name": "Unit", "field_id": "social_history.unit", "entity_type": "social_history.unit", "type": ""}, {"name": "Start Date", "field_id": "social_history.start_date", "entity_type": "social_history.start_date", "type": ""}, {"name": "End Date", "field_id": "social_history.end_date", "entity_type": "social_history.duration", "type": ""}, {"name": "Duration", "field_id": "social_history.duration", "entity_type": "social_history.duration", "type": ""}, {"name": "Frequency", "field_id": "social_history.frequency", "entity_type": "social_history.frequency", "type": ""}, {"name": "SNOMED", "field_id": "social_history.snomed", "entity_type": "snomed", "type": ""}]}, {"record_schema_id": "social_history.social_drug", "fields": [{"name": "Drugs", "field_id": "social_history.addiction", "entity_type": "", "type": "static"}, {"name": "Date", "field_id": "social_history.date", "entity_type": "vital.date", "type": "single-value"}, {"name": "Description", "field_id": "social_history.status", "entity_type": "social.drug", "type": "single-value"}, {"name": "<PERSON><PERSON>", "field_id": "social_history.device", "entity_type": "social_history.device", "type": "single-value"}, {"name": "Quantity", "field_id": "social_history.quantity", "entity_type": "social_history.quantity", "type": ""}, {"name": "Unit", "field_id": "social_history.unit", "entity_type": "social_history.unit", "type": ""}, {"name": "Start Date", "field_id": "social_history.start_date", "entity_type": "social_history.start_date", "type": ""}, {"name": "End Date", "field_id": "social_history.end_date", "entity_type": "social_history.duration", "type": ""}, {"name": "Duration", "field_id": "social_history.duration", "entity_type": "social_history.duration", "type": ""}, {"name": "Frequency", "field_id": "social_history.frequency", "entity_type": "social_history.frequency", "type": ""}, {"name": "SNOMED", "field_id": "social_history.snomed", "entity_type": "snomed", "type": ""}]}, {"record_schema_id": "social_history.marijuana_status", "fields": [{"name": "Marijuana", "field_id": "social_history.marijuana_status", "entity_type": "social_history.marijuana.status", "type": "static"}, {"name": "Date", "field_id": "social_history.date", "entity_type": "vital.date", "type": "single-value"}, {"name": "Description", "field_id": "social_history.description", "entity_type": "social_history.description", "type": "single-value"}, {"name": "<PERSON><PERSON>", "field_id": "social_history.device", "entity_type": "social_history.device", "type": "single-value"}, {"name": "Quantity", "field_id": "social_history.quantity", "entity_type": "social_history.quantity", "type": ""}, {"name": "Unit", "field_id": "social_history.unit", "entity_type": "social_history.unit", "type": ""}, {"name": "Start Date", "field_id": "social_history.start_date", "entity_type": "social_history.start_date", "type": ""}, {"name": "End Date", "field_id": "social_history.end_date", "entity_type": "social_history.end_date", "type": ""}, {"name": "Duration", "field_id": "social_history.duration", "entity_type": "social_history.duration", "type": ""}, {"name": "Frequency", "field_id": "social_history.frequency", "entity_type": "social_history.frequency", "type": ""}, {"name": "SNOMED", "field_id": "social_history.snomed", "entity_type": "snomed", "type": ""}]}]}, {"schema_id": "dental_details", "layout": "display_horizontal", "display_name": "Dental Details", "description": "Dental details of the insured person", "fields": [{"name": "Type Of Service", "field_id": "dental_details.type_of_service", "entity_type": "type_of_service", "type": "single-value", "width": "100px"}, {"name": "Date Of Service.date_of_service", "field_id": "dental_details.", "entity_type": "encounter.date", "type": "single-value", "width": "155px"}, {"name": "Tooth Code", "field_id": "dental_details.tooth_code", "entity_type": "tooth_code", "type": "single-value", "width": "84px"}, {"name": "Tooth Surfaces", "field_id": "dental_details.tooth_surfaces", "entity_type": "tooth_surfaces", "type": "single-value", "width": "100px"}, {"name": "COB Amount", "field_id": "dental_details.cob_amount", "entity_type": "cob.amount", "type": "single-value", "width": "92px"}, {"name": "Quantity", "field_id": "dental_details.quantity", "entity_type": "quantity", "type": "single-value"}, {"name": "Lab Charge", "field_id": "dental_details.lab_charge", "entity_type": "lab_charge", "type": "single-value", "width": "80px"}, {"name": "Total Amount", "field_id": "dental_details.total_amount", "entity_type": "total_amount", "type": "single-value", "width": "93px"}, {"name": "Procedure Code", "field_id": "dental_details.procedure_code", "entity_type": "procedure.code", "type": "single-value", "width": "110px"}, {"name": "Tax", "field_id": "dental_details.tax", "entity_type": "tax", "type": "single-value", "width": "65px"}, {"name": "Units", "field_id": "dental_details.units", "entity_type": "units", "type": "single-value", "width": "65px"}]}, {"schema_id": "rx_details", "layout": "display_horizontal", "display_name": "Rx <PERSON>ails", "description": "RX details of the insured person", "fields": [{"name": "Type Of Service", "field_id": "rx_details.type_of_service", "entity_type": "type_of_service", "type": "single-value", "width": "110px"}, {"name": "Date Of Service", "field_id": "rx_details.date_of_service", "entity_type": "encounter.date", "type": "single-value", "width": "100px"}, {"name": "Fee", "field_id": "rx_details.fee", "entity_type": "rx.fee", "type": "single-value", "width": "65px"}, {"name": "Cost", "field_id": "rx_details.cost", "entity_type": "rx.cost", "type": "single-value", "width": "65px"}, {"name": "DIN Number", "field_id": "rx_details.din_number", "entity_type": "rx.din", "type": "single-value", "width": "100px"}, {"name": "COB Amount", "field_id": "rx_details.cob_amount", "entity_type": "cob.amount", "type": "single-value", "width": "95px"}, {"name": "Quantity", "field_id": "rx_details.quantity", "entity_type": "quantity", "type": "single-value", "width": "95px"}, {"name": "Patient Name", "field_id": "rx_details.patient_name", "entity_type": "patient.name", "type": "single-value", "width": "125px"}, {"name": "Total Amount", "field_id": "rx_details.total_amount", "entity_type": "total_amount", "type": "single-value", "width": "110px"}, {"name": "Doctor Name", "field_id": "rx_details.doctor_name", "entity_type": "doctor.name", "type": "single-value", "width": "125px"}, {"name": "Tax", "field_id": "rx_details.tax", "entity_type": "tax", "type": "single-value", "width": "80px"}, {"name": "Units", "field_id": "rx_details.units", "entity_type": "units", "type": "single-value", "width": "80px"}]}, {"schema_id": "claims", "layout": "display_horizontal", "display_name": "<PERSON><PERSON><PERSON>", "description": "Claim details of the insured person", "fields": [{"name": "Type Of Service", "field_id": "claims.type_of_service", "entity_type": "type_of_service", "type": "single-value"}, {"name": "Date Of Service", "field_id": "claims.date_of_service", "entity_type": "encounter.date", "type": "single-value"}, {"name": "COB Amount", "field_id": "claims.cob_amount", "entity_type": "cob.amount", "type": "single-value"}, {"name": "Quantity", "field_id": "claims.quantity", "entity_type": "quantity", "type": "single-value"}, {"name": "Patient Name", "field_id": "claims.patient_name", "entity_type": "patient.name", "type": "single-value"}, {"name": "Total Amount", "field_id": "claims.total_amount", "entity_type": "total_amount", "type": "single-value"}, {"name": "Doctor Name", "field_id": "claims.doctor_name", "entity_type": "doctor.name", "type": "single-value"}]}, {"schema_id": "medication_history", "layout": "display_horizontal", "display_name": "Medication History", "description": "Medication History of the insured person", "fields": [{"name": "Condition", "field_id": "medication_history.condition", "entity_type": "illness.condition", "type": "single-value"}, {"name": "Medication", "field_id": "medication_history.medication", "entity_type": "current.medication", "type": "single-value"}, {"name": "Instruction", "field_id": "medication_history.instruction", "entity_type": "medication.instruction", "type": "single-value"}, {"name": "Duration", "field_id": "medication_history.duration", "entity_type": "medication.duration", "type": "single-value"}, {"name": "Dosage", "field_id": "medication_history.dosage", "entity_type": "medication.dosage", "type": "single-value"}, {"name": "Unit", "field_id": "medication_history.unit", "entity_type": "medication.unit", "type": "single-value"}, {"name": "Start", "field_id": "medication_history.start", "entity_type": "medication.start", "type": "single-value"}, {"name": "End", "field_id": "medication_history.end", "entity_type": "medication.end", "type": "single-value"}, {"name": "Rx Code", "field_id": "medication_history.rx_code", "entity_type": "medication.rx_code", "type": "single-value"}, {"name": "SNOMED", "field_id": "medication_history.snomed", "entity_type": "medication.snomed", "type": "single-value"}]}, {"schema_id": "imaging_study", "layout": "display_vertical_sections", "display_name": "Imaging Study", "description": "History of Imaging tests performed on the insured person", "fields": [{"name": "Date", "field_id": "imaging_study.date", "entity_type": "imaging.date", "type": "single-value"}, {"name": "Exam", "field_id": "imaging_study.exam", "entity_type": "imaging.exam", "type": "single-value"}, {"name": "Reason", "field_id": "imaging_study.reason", "entity_type": "reason", "type": "single-value"}, {"name": "Finding", "field_id": "imaging_study.finding", "entity_type": "imaging.finding", "type": "single-value"}, {"name": "Impression", "field_id": "imaging_study.impression", "entity_type": "imaging.impression", "type": "single-value"}, {"name": "Conclusion", "field_id": "imaging_study.conclusion", "entity_type": "imaging.conclusion", "type": "single-value"}, {"name": "Impairment", "field_id": "imaging_study.impairment", "entity_type": "imaging_study_impairment", "type": "single-value", "allow_filter": true}]}, {"schema_id": "medical_equipment", "layout": "display_horizontal", "display_name": "Medical Equipment", "description": "Medical equipments used by the insured person", "fields": [{"name": "Equipment", "field_id": "medical_equipment.equipment", "entity_type": "medical.equipment", "type": "single-value"}, {"name": "Date", "field_id": "medical_equipment.date", "entity_type": "equipment.date", "type": "single-value"}]}, {"schema_id": "subjective_details", "layout": "display_horizontal", "display_name": "Subjective Details", "description": "Subjective Findings of the insured person", "fields": [{"name": "Date", "field_id": "subjective_details.date", "entity_type": "encounter.date", "type": "single-value"}, {"name": "Subjective Findings", "field_id": "subjective_details.subjective_findings", "entity_type": "subjective.findings", "type": "single-value"}]}, {"schema_id": "diagnostic_procedures", "layout": "display_horizontal", "display_name": "Diagnostic Procedures", "description": "Diagnostic Procedures of the insured person", "fields": [{"name": "Date", "field_id": "diagnostic_procedures.date", "entity_type": "encounter.date", "type": "single-value"}, {"name": "Type", "field_id": "diagnostic_procedures.type", "entity_type": "encounter.date", "type": "single-value"}, {"name": "Department", "field_id": "diagnostic_procedures.department", "entity_type": "encounter.performer", "type": "single-value"}, {"name": "Description", "field_id": "diagnostic_procedures.description", "entity_type": "assessment", "type": "single-value"}]}, {"schema_id": "procedures", "layout": "display_horizontal", "display_name": "Procedures", "description": "List of procedures undergone by the insured person", "fields": [{"name": "Visit Type", "field_id": "procedures.visit_type", "entity_type": "procedures.visit_type", "type": "single-value"}, {"name": "Procedure", "field_id": "procedures.procedure", "entity_type": "history.diag", "type": "single-value"}, {"name": "Type", "field_id": "procedures.type", "entity_type": "procedures.type", "type": "single-value"}, {"name": "SNOMED", "field_id": "procedures.snomed", "entity_type": "snomed", "type": "single-value"}, {"name": "SNOMED Description", "field_id": "procedures.description", "entity_type": "procedures.description", "type": "single-value"}, {"name": "CPT Code", "field_id": "procedures.cpt", "entity_type": "cpt", "type": "single-value"}, {"name": "CPT Description", "field_id": "procedures.cpt_description", "entity_type": "cpt", "type": "single-value"}, {"name": "Visit Date", "field_id": "procedures.date", "entity_type": "encounter.date", "type": "single-value"}, {"name": "Findings", "field_id": "procedures.finding", "entity_type": "encounter.finding", "type": "single-value"}, {"name": "Impairment", "field_id": "procedures.impairment", "entity_type": "procedures_impairment", "type": "single-value", "allow_filter": true}]}, {"schema_id": "treatments", "layout": "display_horizontal", "display_name": "Treatments", "description": "List of treatments undergone by the insured person", "fields": [{"name": "Visit Type", "field_id": "treatments.visit_type", "entity_type": "", "type": "single-value"}, {"name": "Procedure", "field_id": "treatments.procedure", "entity_type": "", "type": "single-value"}, {"name": "Type", "field_id": "treatments.type", "entity_type": "", "type": "single-value"}, {"name": "SNOMED", "field_id": "<PERSON>.snomed", "entity_type": "snomed", "type": "single-value"}, {"name": "SNOMED Description", "field_id": "treatments.description", "entity_type": "", "type": "single-value"}, {"name": "CPT Code", "field_id": "treatments.cpt", "entity_type": "cpt", "type": "single-value"}, {"name": "CPT Description", "field_id": "treatments.cpt_description", "entity_type": "cpt", "type": "single-value"}, {"name": "Visit Date", "field_id": "treatments.date", "entity_type": "", "type": "single-value"}, {"name": "Findings", "field_id": "treatments.finding", "entity_type": "", "type": "single-value"}, {"name": "Impairment", "field_id": "treatments.impairment", "entity_type": "", "type": "single-value", "allow_filter": true}]}, {"schema_id": "allergens", "layout": "display_horizontal", "display_name": "Allergens", "description": "Allergy history of the insured person", "fields": [{"name": "Allergen", "field_id": "allergens.allergen", "entity_type": "", "type": "single-value"}, {"name": "Type", "field_id": "allergens.type", "entity_type": "", "type": "single-value"}, {"name": "Date", "field_id": "allergens.date", "entity_type": "", "type": "single-value"}, {"name": "SNOMED", "field_id": "allergens.snomed", "entity_type": "", "type": "single-value"}]}, {"schema_id": "immunizations", "layout": "display_horizontal", "display_name": "Immunizations", "description": "Immunization details of the insured person", "fields": [{"name": "Name", "field_id": "immunizations.name", "entity_type": "", "type": "single-value"}, {"name": "Date", "field_id": "immunizations.date", "entity_type": "", "type": "single-value"}]}, {"schema_id": "ccda_immunizations", "layout": "display_horizontal", "display_name": "CCDA Immunizations", "description": "Immunization details of the insured person", "fields": [{"name": "Name", "field_id": "ccda_immunizations.name", "entity_type": "", "type": "single-value"}, {"name": "Date", "field_id": "ccda_immunizations.date", "entity_type": "", "type": "single-value"}, {"name": "RX Code", "field_id": "ccda_immunizations.rx_code", "entity_type": "", "type": "single-value"}, {"name": "CVX Code", "field_id": "ccda_immunizations.cvx_code", "entity_type": "", "type": "single-value"}]}, {"schema_id": "diabetes", "layout": "display_horizontal", "display_name": "Diabetes", "description": "Summary of diabetes related diagnosis, lab tests and procedures", "fields": [{"name": "Risk Factor", "field_id": "diabetes.risk_factor", "entity_type": "o.name", "type": "single-value"}, {"name": "Category", "field_id": "diabetes.category", "entity_type": "cards_generated_category", "type": "single-value", "allow_sorting": true, "allow_filter": true}, {"name": "Medical Code", "field_id": "diabetes.medical_code", "entity_type": "cards_generated_medical_code", "type": "single-value"}, {"name": "Medical Code Type", "field_id": "diabetes.medical_code_type", "entity_type": "cards_generated_medical_code_type", "type": "single-value"}, {"name": "Findings", "field_id": "diabetes.findings", "entity_type": "o.value", "type": "single-value"}, {"name": "Visit Date", "field_id": "diabetes.date", "entity_type": "encounter.date", "type": "single-value"}]}, {"schema_id": "cancer", "layout": "display_horizontal", "display_name": "Cancer", "description": "Summary of cancer related diagnosis, lab tests and procedures", "fields": [{"name": "Risk Factor", "field_id": "cancer.risk_factor", "entity_type": "o.name", "type": "single-value"}, {"name": "Category", "field_id": "cancer.category", "entity_type": "cards_generated_category", "type": "single-value", "allow_sorting": true, "allow_filter": true}, {"name": "Medical Code", "field_id": "cancer.medical_code", "entity_type": "cards_generated_medical_code", "type": "single-value"}, {"name": "Medical Code Type", "field_id": "cancer.medical_code_type", "entity_type": "cards_generated_medical_code_type", "type": "single-value"}, {"name": "Findings", "field_id": "cancer.findings", "entity_type": "o.value", "type": "single-value"}, {"name": "Visit Date", "field_id": "cancer.date", "entity_type": "encounter.date", "type": "single-value"}]}, {"schema_id": "cardiovascular", "layout": "display_horizontal", "display_name": "Cardiovascular", "description": "Summary of heart related diagnosis, lab tests and procedures", "fields": [{"name": "Risk Factor", "field_id": "cardiovascular.risk_factor", "entity_type": "o.name", "type": "single-value"}, {"name": "Category", "field_id": "cardiovascular.category", "entity_type": "cards_generated_category", "type": "single-value", "allow_sorting": true, "allow_filter": true}, {"name": "Medical Code", "field_id": "cardiovascular.medical_code", "entity_type": "cards_generated_medical_code", "type": "single-value"}, {"name": "Medical Code Type", "field_id": "cardiovascular.medical_code_type", "entity_type": "cards_generated_medical_code_type", "type": "single-value"}, {"name": "Findings", "field_id": "cardiovascular.findings", "entity_type": "o.value", "type": "single-value"}, {"name": "Visit Date", "field_id": "cardiovascular.date", "entity_type": "encounter.date", "type": "single-value"}]}, {"schema_id": "mental_nervous_disorder", "layout": "display_horizontal", "display_name": "Mental Nervous Disorder", "description": "Summary of mental health related diagnosis, lab tests and procedures", "fields": [{"name": "Risk Factor", "field_id": "mental_nervous_disorder.risk_factor", "entity_type": "o.name", "type": "single-value"}, {"name": "Category", "field_id": "mental_nervous_disorder.category", "entity_type": "cards_generated_category", "type": "single-value", "allow_sorting": true, "allow_filter": true}, {"name": "Medical Code", "field_id": "mental_nervous_disorder.medical_code", "entity_type": "cards_generated_medical_code", "type": "single-value"}, {"name": "Medical Code Type", "field_id": "mental_nervous_disorder.medical_code_type", "entity_type": "cards_generated_medical_code_type", "type": "single-value"}, {"name": "Findings", "field_id": "mental_nervous_disorder.findings", "entity_type": "o.value", "type": "single-value"}, {"name": "Visit Date", "field_id": "mental_nervous_disorder.date", "entity_type": "encounter.date", "type": "single-value"}]}, {"schema_id": "build", "layout": "display_horizontal", "display_name": "Build", "description": "Build history of the insured person", "fields": [{"name": "Risk Factor", "field_id": "build.risk_factor", "entity_type": "o.name", "type": "single-value"}, {"name": "Category", "field_id": "build.category", "entity_type": "cards_generated_category", "type": "single-value", "allow_sorting": true, "allow_filter": true}, {"name": "Medical Code", "field_id": "build.medical_code", "entity_type": "cards_generated_medical_code", "type": "single-value"}, {"name": "Medical Code Type", "field_id": "build.medical_code_type", "entity_type": "cards_generated_medical_code_type", "type": "single-value"}, {"name": "Findings", "field_id": "build.findings", "entity_type": "o.value", "type": "single-value"}, {"name": "Visit Date", "field_id": "build.date", "entity_type": "encounter.date", "type": "single-value"}]}, {"schema_id": "informals", "layout": "display_horizontal", "display_name": "Informals", "show_relationships": true, "fields": [{"name": "Criteria", "field_id": "informals.criteria", "entity_type": "", "type": "single-value"}, {"name": "Value", "field_id": "informals.value", "entity_type": "", "type": "single-value"}, {"name": "Classification", "field_id": "informals.classification", "entity_type": "", "type": "single-value"}]}]
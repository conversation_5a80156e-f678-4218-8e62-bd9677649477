import json
import time
import traceback
from copy import deepcopy
from itertools import chain
import polars as pl
from typing import Dict, List

from friendlylib.iterators import lfilter, flatten

import constants as const
import table_utils
import titles as Title
import ui_output_dicts
from blueprints import BLUEPRINTS, ENTITY_ROLE
from cards_schemas import card_schemas
from edoc_utility import get_new_entity
from elements.edocument import EDocument
from utils.open_ai import OpenAIRequestHandler


class StudyUtility:
    def __init__(self):
        self.misc = object
        self.retrieved_data = None
        self.blueprint_data = {}

        # -----------------------------------------------------------------------------

    def set_dependencies(self, retrieved_data, objMisc):
        self.retrieved_data = retrieved_data
        self.misc = objMisc

    # -----------------------------------------------------------------------------------------------------------------------

    def get_row_header_name(self) -> list:
        row = BLUEPRINTS[Title.IMAGING_STUDY]['row']
        empty_imaging_row = {}
        for r in row:
            empty_imaging_row[r['display_key']] = []
        return empty_imaging_row

    # -----------------------------------------------------------------------------------------------------------------------

    # noinspection PyUnusedLocal
    def _insert_break_row_cell(self, fields_list: list, cell, row_num):
        ec = deepcopy(ui_output_dicts.empty_cell)
        ec['row']['value'][0]['value_name'] = const.EMPTY_STRING
        ec['row']['value'][0]['entity_type'] = 'break'
        ec['row']['type'] = 'header'
        ec['row']['r'] = row_num
        ec['row']['c'] = 0
        ec2 = deepcopy(ec)
        ec2['row']['c'] = 1
        fields_list.extend([ec, ec2])
        return fields_list

    # -----------------------------------------------------------------------------------------------------------------------

    def _map_header_cell_to_table_cell(self, cell, row_num, col_num, flag_new_row, rSpan):
        ec = deepcopy(ui_output_dicts.empty_cell)
        if flag_new_row:
            value_name = self.blueprint_data['format'][cell['type']]
        else:
            value_name = const.EMPTY_STRING
        ec['row']['value'][0]['value_name'] = value_name
        ec['row']['value'][0]['entity_type'] = cell['type']
        ec['row']['type'] = 'header'
        ec['row']['r'] = row_num
        ec['row']['c'] = col_num
        ec['row']['r_s'] = rSpan
        return ec

    # -----------------------------------------------------------------------------------------------------------------------

    def _build_ui_card(self, list_of_rows, card_display):
        fields_list = []
        row_num = 0
        for big_rows in list_of_rows:
            flag_new_row = True
            for row in big_rows:
                rSpan = len(row)
                if flag_new_row:
                    header_cell = self._map_header_cell_to_table_cell(big_rows[row][0], row_num, 0,
                                                                      flag_new_row, rSpan)
                    fields_list.append(header_cell)
                    flag_new_row = False
                if big_rows[row]:
                    data_cell = table_utils.entity_to_ui_cell(big_rows[row][0], row_num, 1)
                    fields_list.append(data_cell)
                row_num += 1
            fields_list = self._insert_break_row_cell(fields_list, big_rows[row], row_num)
            row_num += 1

        for cell in fields_list:
            cell['table_info']['row_count'] = row_num
            cell['table_info']['col_count'] = 2
        ui_card = {"card": {"card_display": card_display, "sub_card": "", "schema": card_schemas[card_display]},
                   "fields_list": fields_list}
        return ui_card

    # -----------------------------------------------------------------------------------------------------------------------

    def _prepare_blueprint_data(self):
        self.blueprint_data = {}
        row_blueprint = BLUEPRINTS[Title.IMAGING_STUDY]["row"]
        mother = [e["ner.keys"] for e in row_blueprint if e["role"] == ENTITY_ROLE.MOTHER]
        children = [e["ner.keys"] for e in row_blueprint if
                    e["role"] in (ENTITY_ROLE.CHILD, ENTITY_ROLE.REQUIRED_CHILD)]
        mother = list(chain(*mother))
        children = list(chain(*children))
        self.blueprint_data['row_blueprint'] = row_blueprint
        self.blueprint_data['mother'] = mother
        self.blueprint_data['children'] = [x for x in children if 'date' not in x]
        self.blueprint_data['children_headers'] = [e["display_key"] for e in row_blueprint if
                                                   e["role"] in (
                                                   ENTITY_ROLE.CHILD, ENTITY_ROLE.REQUIRED_CHILD) and 'Date' not in e[
                                                       "display_key"]]
        self.blueprint_data['date'] = [x for x in children if 'date' in x]
        self.blueprint_data['format'] = {key: item['display_key'] for item in row_blueprint for key in item['ner.keys']}
        # -----------------------------------------------------------------------------------------------------------------------

    def _edoc_to_page_wise(self, edoc: EDocument) -> dict:
        pages = {}
        valid_tags = list(self.blueprint_data['format'].keys())
        for cell in self.misc.browse_edoc_pages(edoc):
            if cell["type"] in valid_tags:
                doc_num = str(cell["doc_num"])
                page_num = str(cell['abs_page'])
                if doc_num not in pages:
                    pages[doc_num] = {}
                if page_num not in pages[doc_num]:
                    pages[doc_num][page_num] = {'mother': [], 'children': []}
                if cell["type"] in self.blueprint_data['mother']:
                    pages[doc_num][page_num]['mother'].append(cell)
                if cell["type"] in self.blueprint_data['children'] or cell["type"] in self.blueprint_data['date']:
                    pages[doc_num][page_num]['children'].append(cell)
        return pages

    # -----------------------------------------------------------------------------------------------------------------------

    def _create_list_of_container(self, page_data, height_cumulative, key, entities):
        return \
            [
                {
                    'point': (
                        item["left"],
                        height_cumulative + item["top"],
                        item["right"],
                        height_cumulative + item["bottom"]
                    ),
                    "doc_num": item['doc_num'],
                    "abs_page": item["abs_page"],

                    "item": item
                }
                for item in page_data[key] if item["type"] in entities
            ]

    # -----------------------------------------------------------------------------------------------------------------------

    def _append_children_to_exam(self, list_of_rows, list_of_children, child_index_begin, child_index_end):
        if not list_of_rows:
            return
        children = list_of_rows[-1][0]["children"]
        children.extend([
            list_of_children[i]["item"] for i in range(child_index_begin, child_index_end)
        ])
        list_of_rows[-1][0]["children"] = children

    # -----------------------------------------------------------------------------------------------------------------------

    def _extract_study_rows(self, edoc: EDocument, empty_blueprint: Dict[str, List]):
        documents = self._edoc_to_page_wise(edoc)
        list_of_rows = []
        list_of_exams = {key: [] for key in documents.keys()}
        list_of_dates = deepcopy(list_of_exams)
        list_of_children = deepcopy(list_of_exams)

        for doc_num in documents:
            height_page = 5000
            height_cumulative = 0
            for page_num in documents[doc_num]:
                page_data = documents[doc_num][page_num]
                list_of_exams[doc_num].extend(
                    self._create_list_of_container(page_data, height_cumulative, "mother",
                                                   self.blueprint_data['mother']))
                list_of_dates[doc_num].extend(
                    self._create_list_of_container(page_data, height_cumulative, "children",
                                                   self.blueprint_data['date']))
                list_of_children[doc_num].extend(
                    self._create_list_of_container(page_data, height_cumulative, "children",
                                                   self.blueprint_data['children']))
                height_cumulative += height_page

        same_page_entities = ["Reason"]
        for doc_num in list_of_exams:
            for exam_index, exam in enumerate(list_of_exams[doc_num]):
                imaging_row = deepcopy(empty_blueprint)
                imaging_row['Exam'] = [exam['item']]
                has_children = False
                exam_date = self._estimate_cell_attachment(list_of_dates[doc_num], exam)
                if exam_date:
                    imaging_row['Date'] = [exam_date['item']]
                for child in list_of_children[doc_num]:
                    if not self._is_other_exam_in_between(list_of_exams[doc_num][exam_index:], exam, child):
                        continue
                    if self.blueprint_data['format'][child['item']['type']] in same_page_entities:
                        child_candidate = self._estimate_children_attachment(exam, child, True)
                    else:
                        child_candidate = self._estimate_children_attachment(exam, child)
                    if child_candidate:
                        has_children = True
                        imaging_row[self.blueprint_data['format'][child['item']['type']]].append(child['item'])
                if has_children:
                    list_of_rows.append(imaging_row)

        return list_of_rows

    # -----------------------------------------------------------------------------------------------------------------------

    def _estimate_cell_attachment(self, list_of_candidates, exam):
        if len(list_of_candidates) == 0:
            return None
        exam_point = exam['point']
        positions = [const.SAME_LINE, const.ABOVE_LINE, const.BELOW_LINE]
        list_of_candidates = lfilter(lambda candidates: candidates['abs_page'] == exam["abs_page"],
                                     list_of_candidates)

        imaging_date_ners = ['imaging.date', 'encounter.date', 'hospitalization.date']

        for date_type in imaging_date_ners:
            date_type_candidates = lfilter(
                lambda cell: cell['item']['type'] == date_type, list_of_candidates
            )
            max_distance = 5000

            for position in positions:
                max_distance = 100 if position == const.BELOW_LINE else 5000
                candidates_by_position = lfilter(
                    lambda candidate:
                    self.misc.is_required_position(position, exam_point, candidate['point']) and
                    self.misc.rect_distance(exam_point, candidate['point']) <= max_distance,
                    date_type_candidates
                )
                if len(candidates_by_position) != 0:
                    closest_date = min(
                        candidates_by_position,
                        key=lambda cell:
                        self.misc.rect_distance(cell['point'], exam_point)
                    )
                    return closest_date

        return None

    def _estimate_children_attachment(self, exam, child, is_same_page=False):
        if is_same_page is True:
            if exam['abs_page'] != child['abs_page']:
                return None
        positions = [const.SAME_LINE, const.ABOVE_LINE]
        for position in positions:
            if self.misc.is_required_position(position, child['point'], exam['point']):
                return child
        return None

    def _is_other_exam_in_between(self, list_of_exams, exam, child):
        exam_point = exam['point']
        child_point = child['point']

        if exam['abs_page'] > child['abs_page']:
            return False
        for exam_candidate in list_of_exams:
            if exam_candidate == exam or exam_candidate['abs_page'] > child['abs_page']:
                continue
            if (
                    self.misc.is_same_line(child_point, exam_candidate['point']) and
                    self.misc.is_same_line(child_point, exam_point) and
                    exam_candidate['point'][0] > exam_point[0]
            ):
                return False
            if (
                    self.misc.is_above_line(exam_candidate['point'], child_point) and
                    self.misc.is_above_line(exam_point, child_point) and
                    exam_candidate['point'][1] > exam_point[1]
            ):
                return False
            if (
                    self.misc.is_same_line(child_point, exam_candidate['point']) and
                    self.misc.is_above_line(exam_point, child_point) and
                    exam_candidate['point'][1] > exam_point[1]
            ):
                return False
        return True

    # -----------------------------------------------------------------------------------------------------------------------

    def _combine_all_rows(self, list_of_rows, ordered_ner_keys):
        list_of_new_rows = []
        for rows in list_of_rows:
            row = rows[0]
            date = row.get('Date', {})
            children = row.get('children', [])
            mother = row.get('mother', {})
            ordered_children_map = {key: [] for key in ordered_ner_keys}
            for child in children:
                is_duplicate = False if child['text'] and child['text'].strip() else True
                for prev_child in ordered_children_map[child['type']]:
                    if is_duplicate:
                        break
                    if prev_child['text'].strip() == child['text'].strip():
                        is_duplicate = True
                        break
                if not is_duplicate:
                    ordered_children_map[child['type']].append(child)
            new_row = [[mother]]
            if date:
                new_row.append([date])
            for key in ordered_ner_keys:
                if ordered_children_map[key]:
                    new_row.append(ordered_children_map[key])
            list_of_new_rows.append(new_row)
        return list_of_new_rows

    # -----------------------------------------------------------------------------------------------------------------------

    def _remove_duplicates(self, list_of_rows: list) -> list:
        unique_row_text = {}
        unique_list_of_rows = []
        for row in list_of_rows:
            row_list = []
            for cell in row:
                if row[cell] is None:
                    continue
                for study in row[cell]:
                    row_list.append(study['text'].lower().strip())
            row_text = ' '.join(row_list)
            if row_text not in unique_row_text:
                unique_row_text[row_text] = ''
                unique_list_of_rows.append(row)
        return unique_list_of_rows

    # -----------------------------------------------------------------------------------------------------------------------

    def _update_conclusion(self, list_of_rows, summarized_conclusion_response):
        for idx, imaging_grp in enumerate(list_of_rows):
            summarized_conclusion_text = summarized_conclusion_response[idx].get('Summary')
            if not (imaging_grp.get('Finding') or imaging_grp.get('Impression')):
                continue
            if not summarized_conclusion_text or imaging_grp['Conclusion']:
                continue
            conclusion_entity = get_new_entity(imaging_grp['Exam'][0], 'diagnostic_tests.conclusion', summarized_conclusion_text)
            imaging_grp['Conclusion'].append(conclusion_entity)

    def _summarize_imaging_data_using_openai(self, list_of_rows):
        list_of_imaging_groups = []
        for imaging_grp in list_of_rows:
            data_dict = {}
            for field, entity_list in imaging_grp.items():
                if entity_list and field in ['Date', 'Exam', 'Reason', 'Finding', 'Impression']:
                    data_dict[field] = entity_list[0]['text']
            list_of_imaging_groups.append(data_dict)
        if not list_of_imaging_groups:
            return list_of_rows

        expected_input_format = [
            {
                "Date": "1/9/2017 at 6:01PM ",
                "Exam": "Carotid US",
                "Findings": "Normal",
                "Impression": "No abnormalities detected"
            },
            {
                "Date": "10/19/2018",
                "Exam": "X-ray Left Foot",
                "Finding": "Mildly displaced extra-articular oblique fracture of the fifth proximal phalanx. Soft tissues are normal.",
                "Impression": "Fracture confirmed."
            }
        ]
        expected_output_format = [
            {
                "Date": "1/9/2017",
                "Exam": "Carotid US",
                "Summary": "Everything is normal."
            },
            {
                "Date": "10/19/2018",
                "Exam": "X-ray Left Foot",
                "Summary": "X-ray of the left foot showed a mildly displaced extra-articular oblique fracture of the fifth proximal phalanx."
            }
        ]
        input_prompt = f"""
        You are an expert in summarizing medical reports. Given multiple sets of medical records containing Exam, Findings, Reason, and Impression, 
        generate a short summary of the doctor’s interpretation for each record.

        Rules:
        - Format the date in MM/DD/YYYY if Date is present
        - Do not modify or clean Exam, Findings, Reason, and Impression. Keep it exactly same in the response.
        - If everything is normal, state: "Everything is normal."
        - If there are abnormalities, summarize and list only the abnormal medical conditions and avoid mentioning normal observations in {self.retrieved_data.language}  language.
        - The response should be in json format not python code  
        
        Example Input Format:
        {expected_input_format}
        
        Expected Output Format:
        {expected_output_format}
        
        Now, process the following records:
        {list_of_imaging_groups}
        """

        try:
            start_time = time.time()

            print("Initializing OpenAI handler...")
            openai_handler = OpenAIRequestHandler()
            openai_handler.initialize()

            print("Sending request to OpenAI...")
            print(f"Total Imaging groups sent :: {len(list_of_imaging_groups)}")
            response = openai_handler.get_openai_response(input_prompt, encoded_image_list=[])

            if response:
                print("Processing response...")
                self._update_conclusion(list_of_rows, response)

                print("Updated Imaging Conclusion...")

            end_time = time.time()
            print(f"Total Time taken for OpenAI request: {end_time - start_time:.2f} seconds")

        except Exception as e:
            print(f"An error occurred during OpenAI request: {e}")

        return list_of_rows

    def get_imaging_exam_list(self, list_of_rows):
        procedures_list = []
        for row in list_of_rows:
            if not row.get('Exam'):
                continue
            procedures_list.append(row['Exam'][0]['text'])
        return list(set(procedures_list))

    def _update_procedures_with_impairment(self, list_of_rows, impairment_dict, procedure_impairment_mapping):
        for row in list_of_rows:
            if not row.get('Exam'):
                continue

            impairment = procedure_impairment_mapping.get(row['Exam'][0]['text'])
            if not impairment_dict.get(impairment):
                continue
            row['Impairment'] = [impairment_dict[impairment]]

    def get_impairment_list(self, edoc):
        impairment_dict = {}
        for page_of_lines in edoc:
            page_of_entities = flatten(page_of_lines)
            impairment_entity_list = lfilter(
                lambda entity_: entity_['type'] == 'cards_generated_impairment',
                page_of_entities
            )
            impairment_dict.update({ent_['text']: ent_ for ent_ in impairment_entity_list})
        return impairment_dict

    def _get_procedure_impairment_mapping_using_openai(self, procedures_list, impairments_list):

        input_prompt = f"""
        I have two lists:

        Procedures: A list of medical procedures. {procedures_list}

        Impairments: A list of medical impairments. {impairments_list}

        Your task is to associate one most likely impairment with each procedure based on medical knowledge, 
        common associations, and clinical relevance.

        Input:
        Procedures: [<list of procedures>]
        Impairments: [<list of impairments>]

        Output format:
        Return the result as a JSON object, where each key is a procedure name (exactly as provided) and the value is 
        the most likely impairment (exactly as provided).
        """

        try:
            start_time = time.time()

            print("Initializing OpenAI handler...")
            openai_handler = OpenAIRequestHandler()
            openai_handler.initialize()

            print("Sending request to OpenAI...")
            response = openai_handler.get_openai_response(input_prompt, encoded_image_list=[])

            if response:
                print("Processing response...")
                return response

            end_time = time.time()
            print(f"Total Time taken for OpenAI request: {end_time - start_time:.2f} seconds")

        except Exception as e:
            print(f"An error occurred during OpenAI request: {e}")

        return {}

    def build_study_card(self, edoc: EDocument, failed_cards_list):
        try:
            self._prepare_blueprint_data()
            empty_imaging_row = self.get_row_header_name()
            list_of_rows = self._extract_study_rows(edoc, empty_imaging_row)

            list_of_rows = self.misc.make_single_object_in_extended_columns(list_of_rows,
                                                                            self.blueprint_data["children_headers"])
            list_of_rows = self._remove_duplicates(list_of_rows)
            if Title.USE_OPEN_AI in self.retrieved_data.feature:
                list_of_rows = self._summarize_imaging_data_using_openai(list_of_rows)
            imaging_exam_list = self.get_imaging_exam_list(list_of_rows)
            impairment_dict = self.get_impairment_list(edoc)
            imaging_exam_impairment_mapping = self._get_procedure_impairment_mapping_using_openai(imaging_exam_list, list(
                impairment_dict.keys()))
            self._update_procedures_with_impairment(list_of_rows, impairment_dict, imaging_exam_impairment_mapping)
            ui_card = self._build_ui_card(list_of_rows, Title.IMAGING_STUDY)
            return ui_card
        except Exception as e:
            print(f'ERROR: Imaging Study Card Creation Failed: {e}')
            failed_cards_list.append({'card_name': 'Imaging Study', 'message': f'{traceback.format_exc()}'})
            return None

    # =======================================================================================================================

    def test(self):
        ix = 0
        with open(f"../logs/study-{ix}.json") as f:
            all = json.load(f)
        self.build_study_card(all['edoc'])

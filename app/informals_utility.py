from copy import deepcopy
from datetime import datetime
from typing import Optional, List, Dict
import traceback

from blueprints import BLUEPRINTS
from classification_utility import ClassificationUtility
from edoc_utility import get_new_entity
from elements.edocument import Entity
from friendlylib.iterators import lfilter
from medical_codes.card_reference import CodeEntities
from table_utils import rows_to_table, table_to_ui_card
import titles as Title
from utils import dates_utility


def parse_date(date_str: str):
    try:
        return datetime.fromisoformat(date_str)
    except ValueError:
        return datetime.min

def to_int(s: str) -> int | None:
        try:
            return int(float(s))
        except ValueError:
            return None

def to_float(entry: str):
    try:
        return float(entry)
    except:
        return None

class InformalsUtility:
    def __init__(self):
        self.card_name = Title.INFORMALS


    def set_dependencies(self, retrieved_data, misc, card_references):
        self.card_references = card_references
        self.misc = misc
        self.retrieved_data = retrieved_data
    
    def is_blood_pressure(self, blood_text: str):
        blood_list = blood_text.split('/')
        
        if len(blood_list) == 2:
            systolic_text = blood_list[0].strip()
            diastolic_text = blood_list[1].strip()
            
            if systolic_text.isdigit() and diastolic_text.isdigit():
                systolic_value = int(systolic_text)
                diastolic_value = int(diastolic_text)
                
                if systolic_value > diastolic_value:
                    return True
                else:
                    return False
            else:
                return False

        return False

    def average_blood_pressure(self, records: List[CodeEntities]) -> Optional[str]:
        total_systolic = 0
        total_diastolic = 0
        count = 0

        for record in records:
            bp_text: str = record.findings_cell['text']
            
            try:
                systolic, diastolic = map(int, bp_text.split('/'))
            except ValueError:
                continue  
            
            total_systolic += systolic
            total_diastolic += diastolic
            count += 1

        if count > 0:
            avg_systolic: float = total_systolic / count
            avg_diastolic: float = total_diastolic / count
            return f"{avg_systolic:.1f}/{avg_diastolic:.1f}"
        else:
            return None

    
    def max_blood_pressure(self, records: List[CodeEntities]) -> Optional[CodeEntities]:
        max_systolic = float('-inf')
        max_diastolic = float('-inf')
        max_record: Optional[CodeEntities] = None
        
        for record in records:
            bp_text: str = record.findings_cell['text']
            
            try:
                systolic, diastolic = map(int, bp_text.split('/'))
            except ValueError:
                continue  
            
            if systolic > max_systolic or (systolic == max_systolic and diastolic > max_diastolic):
                max_systolic = systolic
                max_diastolic = diastolic
                max_record = record
        
        return max_record
    
    def _get_blood_pressure(self, laboratory_tests: List[CodeEntities]) -> Optional[Entity]:
        valid_bps: List[CodeEntities] = lfilter(lambda lab_row: self.is_blood_pressure(lab_row.findings_cell.get('text', '')), laboratory_tests)
        latest_entries, latest_date = self._get_latest_date_entries(valid_bps)

        if not latest_entries:
            return None
        
        if latest_date:
            if len(latest_entries) == 1:
                return latest_entries[0].findings_cell
            blood_pressure_avg: str = self.average_blood_pressure(latest_entries)
            blood_pressure_entity: Entity = get_new_entity(deepcopy(latest_entries[0].findings_cell), 'patient.blood_pressure', blood_pressure_avg)
            return blood_pressure_entity

        bp_empty_dates = lfilter(lambda lab_row: not lab_row.date_cell, laboratory_tests)
        
        if not bp_empty_dates:
            return None

        max_bp_record: CodeEntities = self.max_blood_pressure(bp_empty_dates)

        if not max_bp_record:
            return None

        return max_bp_record.findings_cell
    
    def _map_blood_pressure(self, blood_pressure: Optional[Entity]) -> Dict[str, Optional[float]]:
        if not blood_pressure:
            return {"systolic": None, "diastolic": None}
        systolic, diastolic = map(float, blood_pressure.get('text','').split('/'))
        return {"systolic": systolic, "diastolic": diastolic}

    def _get_max_value(self, laboratory_tests: List[CodeEntities]):
        valid_labs = lfilter(lambda lab_row: self.misc.is_number(lab_row.findings_cell.get('text', '')), laboratory_tests)
        latest_entries, _ = self._get_latest_date_entries(valid_labs)
        
        max_value_entry = max(
            latest_entries,
            key=lambda x: float(x.findings_cell.get(
                'text', '-inf')) if self.misc.is_number(x.findings_cell.get('text', '')) else float('-inf')
        )

        return max_value_entry.findings_cell


    def _get_latest_date_entries(self, laboratory_tests: List[CodeEntities]):
        laboratory_tests = lfilter(lambda cell: cell.date_cell is not None, laboratory_tests)
        valid_dates = lfilter(
            lambda lab_row: dates_utility.check_date(lab_row.date_cell.get(
                'codified_datetime_obj') or lab_row.date_cell.get('text'), self.retrieved_data.date_format),
            laboratory_tests,
        )
        latest_date = ""
        if valid_dates:
            latest_entry = max(
                valid_dates,
                key=lambda lab_row: parse_date(lab_row.date_cell.get(
                    'codified_datetime_obj') or lab_row.date_cell.get('text'))
            )
            latest_date = latest_entry.date_cell.get(
                'codified_datetime_obj') or latest_entry.date_cell.get('text')

            latest_entries = lfilter(
                lambda x: (x.date_cell.get('codified_datetime_obj')
                           or x.date_cell.get('text')) == latest_date,
                valid_dates
            )

        else:
            latest_entries = laboratory_tests

        return latest_entries, latest_date


    def _get_value_by_code(self, medical_code: str, is_bp: bool = False):
        laboratory_tests: List[CodeEntities] = lfilter(
            lambda lab_row: lab_row.medical_code_text == medical_code,
            self.card_references.code_entities_list.loinc_entities,
        )
        if not laboratory_tests:
            return None

        max_value: Optional[CodeEntities] = None

        if not is_bp:
            max_value: Entity = self._get_max_value(laboratory_tests)
        else:
            max_value: Entity = self._get_blood_pressure(laboratory_tests)

        if not max_value:
            return None

        return max_value

    def _entity_to_number(self, entity: Entity, to_integer: bool = False)-> Optional[float]:
        if not entity or not entity.get('text', ''):
            return None
        entry: str = entity.get('text')
        
        if not to_integer:
            return to_float(entry)

        return to_int(entry)


    def _prepare_empty_table(self) -> dict:
        row = {}
        ner_keys = {}
        for cell in self.row_blueprint:
            display_key = cell['display_key']
            row[display_key] = {}
            for k in cell['ner.keys']:
                ner_keys[k] = display_key
        return row
    
    def _prepare_blueprint_data(self):
        self.row_blueprint = BLUEPRINTS[Title.INFORMALS]["row"]
    

    def _convert_dict_to_rows(self, sub_table: list):
        rows = []
        if not sub_table:
            return rows

        for entry in sub_table:
            row = []
            for display_key, cell in entry.items():
                if cell:
                    row.append(cell)
            rows.append(row)
        return rows


    def _fill_missing_dummy_cell(self, rows: Dict):
        new_rows = []
        for row in rows:
            new_row = deepcopy(self.row_blueprint)
            for i, bp in enumerate(self.row_blueprint):
                for j, cell in enumerate(row):
                    if cell['type'] in bp['ner.keys']:
                        new_row[i]['entity_object'] = deepcopy(cell)
                        del row[j]
                        break
            new_rows.append(new_row)
        return new_rows


    def _populate_all_data(self):
        bmi: Optional[Entity] = self._get_value_by_code("39156-5")
        cholesterol_labs: Optional[Entity] = self._get_value_by_code("2093-3")
        cholhdl_ratio_labs: Optional[Entity] = self._get_value_by_code("9830-1")
        blood_pressure: Optional[Entity] = self._get_value_by_code("18684-1", True)
        
        application_date = self.card_references.informals.application_date 
        last_smoked_date = self.card_references.informals.last_smoked_date 
        smoking_years = 100
        if self.card_references.informals.smoking_entity:
            if last_smoked_date and application_date:
                date_difference = dates_utility.date_diff(application_date, last_smoked_date)
                smoking_years = date_difference / 365.2
            else:
                smoking_text: str = self.card_references.informals.smoking_entity.get('text', '')
                if smoking_text in ['yes', 'former']:
                    smoking_years = 0
        
        informals_entity = {
            'Age': self.card_references.informals.age_entity,
            'Nicotine': self.card_references.informals.smoking_entity,
            'BMI': bmi,
            'Cholesterol': cholesterol_labs,
            'TC/HDL Ratio': cholhdl_ratio_labs,
            'Blood Pressure Maximum': blood_pressure
        }

        informals_data = {
            'Age': self._entity_to_number(self.card_references.informals.age_entity, True),
            'Nicotine': smoking_years,
            'BMI': self._entity_to_number(bmi),
            'Cholesterol': self._entity_to_number(cholesterol_labs),
            'TC/HDL Ratio': self._entity_to_number(cholhdl_ratio_labs),
            'Blood Pressure Maximum': self._map_blood_pressure(blood_pressure),
        }

        classification_utility = ClassificationUtility(informals_data)

        classification = classification_utility.determine_classification(
            age=informals_data['Age'],
            bmi=informals_data['BMI'],
            cholesterol=informals_data['Cholesterol'],
            nicotine_condition=informals_data['Nicotine'],
            tc_hdl_ratio=informals_data['TC/HDL Ratio'],
            systolic=informals_data['Blood Pressure Maximum']['systolic'],
            diastolic=informals_data['Blood Pressure Maximum']['diastolic']
        )
        
        informals_table = []
        for criteria, entry in informals_entity.items():
            if not entry:
                continue
            informal_values = self._prepare_empty_table()
            informal_values['Value'] = entry
            informal_values['Criteria'] = get_new_entity(deepcopy(entry), 'criteria', criteria)  

            if classification.get(criteria):
                informal_values['Classification'] = get_new_entity(deepcopy(entry), 'classification', classification.get(criteria))  
            informals_table.append(informal_values)


        informals_rows = self._convert_dict_to_rows(informals_table)
        informals_rows = self._fill_missing_dummy_cell(informals_rows)
        
        return informals_rows


    def build_informals_card(self, failed_cards_list):
        try:
            self._prepare_blueprint_data()
            rows = self._populate_all_data()
            table = rows_to_table(rows, Title.INFORMALS)
            card = table_to_ui_card(table)

            return card
        except Exception as e:
            print(f'ERROR: {Title.INFORMALS} Card Creation Failed: {e}')
            failed_cards_list.append({'card_name': 'Informals', 'message': f'{traceback.format_exc()}'})
            return None

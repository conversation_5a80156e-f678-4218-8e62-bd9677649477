import asyncio
import json
import logging
import os
import re
import time
from contextlib import contextmanager
from typing import Any, List, Optional

from google import genai
from google.api_core.exceptions import GoogleAP<PERSON>allError, RetryError
from google.genai import types
from google.oauth2 import service_account
from tenacity import (before_log, retry, stop_after_attempt,
                      wait_random_exponential)

PROJECT_ID = os.getenv("GOOGLE_PROJECT_ID")
LOCATION = os.getenv("GOOGLE_PROJECT_LOCATION")
GOOGLE_SERVICE_ACCOUNT = os.getenv("GOOGLE_SERVICE_ACCOUNT")
RETRIES = int(os.getenv("RETRIES", "4"))
DELAY = int(os.getenv("DELAY", "2"))

logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s %(levelname)-8s %(message)s",
    datefmt="%m-%d %H:%M",
)

logger = logging.getLogger(__name__)


class VertexAIConnectionError(Exception):
    """Custom exception for Vertex AI connection failures."""

    pass


class AsyncGemini:
    def __init__(
            self,
            content: List[Any],
            response_schema: Any,
            temperature: Optional[float] = 0,
            top_p: Optional[float] = 0.7,
            candidate_count: Optional[int] = 1,
            max_output_tokens: Optional[int] = 8192,
            response_mime_type: Optional[str] = "application/json",
    ):
        self.content = content  # Supports multiple prompts
        # self.model_name = "gemini-1.5-pro"
        self.model_name = "gemini-2.0-flash-001"
        # self.model_name = "gemini-2.0-pro-exp-02-05"
        self.generation_config = types.GenerateContentConfig(
            top_p=top_p,
            temperature=temperature,
            response_schema=response_schema,
            candidate_count=candidate_count,
            response_mime_type=response_mime_type,
            max_output_tokens=max_output_tokens,
            safety_settings=[
                types.SafetySetting(
                    category=types.HarmCategory.HARM_CATEGORY_HATE_SPEECH,
                    threshold=types.HarmBlockThreshold.OFF,
                ),
                types.SafetySetting(
                    category=types.HarmCategory.HARM_CATEGORY_DANGEROUS_CONTENT,
                    threshold=types.HarmBlockThreshold.OFF,
                ),
                types.SafetySetting(
                    category=types.HarmCategory.HARM_CATEGORY_SEXUALLY_EXPLICIT,
                    threshold=types.HarmBlockThreshold.OFF,
                ),
                types.SafetySetting(
                    category=types.HarmCategory.HARM_CATEGORY_HARASSMENT,
                    threshold=types.HarmBlockThreshold.OFF,
                ),
            ],
        )

    async def _get_client(self):
        """Get an authenticated Google GenAI client."""
        credentials = service_account.Credentials.from_service_account_file(
            GOOGLE_SERVICE_ACCOUNT,
            scopes=["https://www.googleapis.com/auth/cloud-platform"],
        )
        return genai.Client(
            vertexai=True,
            project=PROJECT_ID,
            location=LOCATION,
            credentials=credentials,
        )

    @retry(
        wait=wait_random_exponential(multiplier=1, max=20),
        stop=stop_after_attempt(3),
        before=before_log(logger, logging.INFO)
    )
    async def _send_request(self, client, prompt):
        """Send an async request to Gemini API for a single prompt."""
        for attempt in range(RETRIES):
            try:
                token_count_response = await client.aio.models.count_tokens(
                    model=self.model_name,
                    contents=[prompt]
                )
                logging.info(
                    f"Token Count: {token_count_response.total_tokens}")
                response = await client.aio.models.generate_content(
                    model=self.model_name,
                    contents=[prompt],
                    config=self.generation_config,
                )

                if response.text:
                    return response.text.strip()

                logging.warning("Received empty response.")
                break

            except Exception as e:
                logging.error(f"Error in attempt {attempt + 1}: {e}")
                await asyncio.sleep(DELAY * (2 ** attempt))

        return None

    async def fetch_responses(self):
        """Fetch responses for all prompts concurrently."""
        client = await self._get_client()
        tasks = [self._send_request(client, prompt) for prompt in self.content]
        responses = await asyncio.gather(*tasks, return_exceptions=True)

        results = []
        for response in responses:
            if isinstance(response, Exception):
                logging.error(f"Error in response: {response}")
            else:
                results.append(response)

        return results

    def __call__(self):
        """Run fetch_responses in an async-compatible way."""
        try:
            return asyncio.run(self.fetch_responses())  # Works in scripts
        except RuntimeError:
            return asyncio.create_task(
                self.fetch_responses()
            )  # Works in async environments (Jupyter, FastAPI)

    def __repr__(self):
        return f"{self.__class__.__name__}(num_prompts={len(self.content)})"


class Gemini:
    def __init__(
            self,
            content: List[Any] | str,
            response_schema: Any,
            temperature: Optional[float] = 0,
            top_p: Optional[float] = 0.7,
            candidate_count: Optional[int] = 1,
            max_output_tokens: Optional[int] = 8192,
            response_mime_type: Optional[str] = "application/json",
    ):
        self.content = content
        self.max_output_tokens = max_output_tokens
        self.generation_config = types.GenerateContentConfig(
            top_p=top_p,
            temperature=temperature,
            response_schema=response_schema,
            candidate_count=candidate_count,
            response_mime_type=response_mime_type,
            max_output_tokens=self.max_output_tokens,
            safety_settings=[
                types.SafetySetting(
                    category=types.HarmCategory.HARM_CATEGORY_HATE_SPEECH,
                    threshold=types.HarmBlockThreshold.OFF,
                ),
                types.SafetySetting(
                    category=types.HarmCategory.HARM_CATEGORY_DANGEROUS_CONTENT,
                    threshold=types.HarmBlockThreshold.OFF,
                ),
                types.SafetySetting(
                    category=types.HarmCategory.HARM_CATEGORY_SEXUALLY_EXPLICIT,
                    threshold=types.HarmBlockThreshold.OFF,
                ),
                types.SafetySetting(
                    category=types.HarmCategory.HARM_CATEGORY_HARASSMENT,
                    threshold=types.HarmBlockThreshold.OFF,
                ),
            ],
        )

    def clear(self):
        del self.content
        del self.generation_config
        del self.max_output_tokens

    @contextmanager
    def _connect(self):
        try:
            credentials = service_account.Credentials.from_service_account_file(
                GOOGLE_SERVICE_ACCOUNT,
                scopes=["https://www.googleapis.com/auth/cloud-platform"],
            )
            client = genai.Client(
                vertexai=True,
                project=PROJECT_ID,
                location=LOCATION,
                credentials=credentials,
            )

            yield client
        except (GoogleAPICallError, RetryError) as e:
            raise VertexAIConnectionError(
                f"Error connecting to Vertex AI: {e}") from e
        except Exception as e:
            raise VertexAIConnectionError(
                f"An unexpected error occurred while connecting to Vertex AI: {e}"
            ) from e

    def __call__(self, model_name: Optional[str] = "gemini-2.0-flash-001") -> str:
        with self._connect() as client:
            logging.info(
                f"Input Token Info: \n{client.models.count_tokens(model=model_name, contents=self.content)}"
            )

            # full_response = []
            # retry_count = 0
            # time_delay = int(DELAY)

            # while retry_count < int(RETRIES):
            response = client.models.generate_content(
                model=model_name,
                contents=self.content,
                config=self.generation_config,
            )
            output = response.text

            # if not response.text:
            #     logging.warning("Received empty response.")
            #     break

            # logging.info(
            #     f"Response Token Info: \n{client.models.count_tokens(model=model_name, contents=response.text)}"
            # )

            # raw_output = response.text.strip()
            # try:
            #     extracted_json = self._extract_and_merge_json(raw_output)
            #     full_response.extend(extracted_json)
            #     break
            # except json.JSONDecodeError as e:
            #     logging.error(
            #         f"JSON decoding error on attempt {retry_count + 1}/{RETRIES}: {e}"
            #     )
            #     logging.debug(f"Raw response received: {raw_output}")

            # self.content = "CONTINUE"
            # retry_count += 1

            # logging.info("Output truncated, retrying with CONTINUE...")
            # time.sleep(time_delay)
            # time_delay *= 2

        self.clear()
        return output

    @staticmethod
    def _extract_and_merge_json(raw_text: str) -> List[Any]:
        json_matches = re.findall(
            pattern=r"(\{.*?\}|\[.*?\])",
            string=raw_text,
            flags=re.DOTALL,
        )
        extracted_json_objects = []
        for json_str in json_matches:
            try:
                parsed_json = json.loads(json_str)
                if isinstance(parsed_json, list):
                    extracted_json_objects.extend(parsed_json)
                else:
                    extracted_json_objects.append(parsed_json)
            except json.JSONDecodeError:
                logging.warning(
                    f"Skipping invalid JSON fragment: {json_str[:50]}...")

        if not extracted_json_objects:
            raise json.JSONDecodeError(
                "No valid JSON found in response", raw_text, 0)

        return extracted_json_objects

    def __repr__(self):
        return self.__class__.__name__ + "content=(0), generation_config=(1)".format(
            self.content,
            self.generation_config,
        )

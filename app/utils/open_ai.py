import json
import os
import re
import time
from pprint import pprint
from typing import Dict, List

from azure.identity import get_bearer_token_provider, DefaultAzureCredential
from friendlylib import statsdmetrics
from openai import AzureOpenAI


class OpenAIRequestHandler:
    def __init__(self):
        self.endpoint = None
        self.api_version = None
        self.api_key = None
        self.deployment_name = None
        self.openai_timeout = None
        self.token_provider = None
        self.client = None

    def initialize(self):
        self.endpoint = os.getenv('OPEN_AI_ENDPOINT')
        self.api_version = os.getenv('OPEN_AI_API_VERSION')
        self.deployment_name = os.getenv('OPEN_AI_DEPLOYMENT_NAME')
        self.openai_timeout = os.getenv('OPEN_AI_TIMEOUT')
        self.token_provider = get_bearer_token_provider(
            DefaultAzureCredential(), "https://cognitiveservices.azure.com/.default"
        )
        self.client = AzureOpenAI(
            api_version=self.api_version,
            azure_endpoint=self.endpoint,
            azure_ad_token_provider=self.token_provider,
            timeout=int(self.openai_timeout) if self.openai_timeout else 300,
            max_retries=4
        )

    def _generate_payload_user_content(self, prompt: str, encoded_image_list: List[str]) -> List[
        Dict[str, Dict[str, str]]]:
        payload_user_content = [{"type": "image_url", "image_url": {"url": f"data:image/png;base64,{img}"}} for img in
                                encoded_image_list]
        payload_user_content.append({"type": "text", "text": prompt})
        return payload_user_content

    def _generate_payload(self, user_input_prompt: str, encoded_image_list: List[str]) -> Dict:
        return {
            "messages": [
                {
                    "role": "system",
                    "content": [
                        {"type": "text", "text": "You are an AI assistant that helps people find information."}
                    ]
                },
                {
                    "role": "user",
                    "content": self._generate_payload_user_content(user_input_prompt, encoded_image_list)
                }
            ],
            "temperature": 0,
            "top_p": 0.95,
            "max_tokens": 4000
        }

    def _print_headers(self, headers: Dict[str, str], title: str):
        print(f"==== {title} ====")
        for key, value in headers.items():
            print(f"{key}: {value}")

    def _print_usage(self, response_json) -> None:
        input_tokens = response_json["usage"]["prompt_tokens"]
        print(f"Input Tokens:       {input_tokens}")
        output_tokens = response_json["usage"]["completion_tokens"]
        print(f"Output Tokens:      {output_tokens}")
        cost_gpt4o = input_tokens * 0.005 / 1000 + output_tokens * 0.015 / 1000
        print(f"Cost GPT 4o (USD$): {cost_gpt4o}")
        statsdmetrics.gauge("openai.inputtokens", input_tokens)
        statsdmetrics.gauge("openai.outputtokens", output_tokens)

    def send_request(self, payload) -> Dict:
        start = time.time()
        try:
            response = self.client.chat.completions.create(
                model=self.deployment_name,
                messages=payload["messages"],
            )
        except Exception as e:
            print(f"Failed to make the request. Error: {e}")
            return {}
        end = time.time()
        total_time = round(end - start, 3)

        response_dict = response.to_dict()

        print("=" * 80)
        print("Full Response Json")
        print(response_dict)

        print("=" * 80)
        print("Time taken")
        print(f"Request Time (s): {total_time}")

        print("=" * 80)
        print("Usage")
        self._print_usage(response_dict)
        statsdmetrics.gauge("openai.processtime", total_time)

        return response_dict

    def extract_json_output_from_content(self, content_text: str) -> Dict:
        match = re.search(r'```json(.*?)```', content_text, re.DOTALL)
        if match:
            try:
                return json.loads(match.group(1))
            except json.JSONDecodeError as e:
                print(f"Error parsing JSON: {e}")
        print("Error! The number of 'max_tokens' may be less than the output tokens.")
        return {}

    def get_openai_response(self, user_input_prompt: str, encoded_image_list: List[str]) -> Dict:
        payload = self._generate_payload(user_input_prompt, encoded_image_list)
        response_json = self.send_request(payload)
        if not response_json:
            return {}
        content = response_json["choices"][0]["message"]["content"]
        return self.extract_json_output_from_content(content)


# Usage Example
if __name__ == "__main__":
    diag_list = ['episodes of feeling skipped a beat, dizziness, pain', 'ECG without significant abnormalities',
                 'muscle and bone disorder', 'Low T + sugar levels', 'High Blood Sugar', 'low testosterone',
                 'vit d deficiency', 'sinus rhythm', 'Cholesterol', 'Cholesterol', 'Follow up', 'Low Iron', 'Low Iron',
                 'Physical', 'Adult']
    output_format = {
        "invalid_diagnosis": "list of invalid diagnosis",
        "valid_diagnosis": {
            "diagnosis name": {
                "icd10": "icd10 code",
                "description": "original description of the icd10 code"
            }
        }
    }
    input_prompt = f"""
    Given list of diagnosis {diag_list}, return json of the format {output_format}. Do not modify name of diagnosis provided.
    """
    handler = OpenAIRequestHandler()
    handler.initialize()
    res = handler.get_openai_response(input_prompt, encoded_image_list=[])
    pprint(res)

import re
from dateutil.parser import parse
from datetime import datetime
from typing import Optional, List
from pydantic import BaseModel, Field, ValidationError
import pandas as pd
from utils.gemini import Gemini

import json
from typing import TypedDict
from dateutil import parser
import re
from collections import defaultdict


class TreeNode:
    def __init__(self, node_id):
        self.id = node_id
        self.children = []  # Links to child nodes

    def add_child(self, child_node):
        self.children.append(child_node)  # Linking child

    def print_tree(self, level=0):
        print(" " * (level * 2) + str(self.id))
        for child in self.children:
            child.print_tree(level + 1)

    def check_child(self, value):
        if value in self.children:
            return True


class ReasonDiagnosisPlan(BaseModel):
    id: Optional[int] = Field(None, description="Unique ID for the visit")
    connected_id: Optional[int] = Field(None, description="Connected ID")
    duplicate: Optional[int] = Field(
        None, description="ID the encounter duplicate to")
    invalid: Optional[bool] = Field(
        False, description="if its not a valid reason dx plan then set to true")
    date: Optional[str] = Field(None, description="Date of visit")
    reason_of_visit: Optional[str] = Field(
        None, description="Reason for the visit")
    diagnosis: Optional[str] = Field(None, description="Diagnosis details")
    plan: Optional[str] = Field(
        None, description="Treatment plan or follow-up actions")

    @classmethod
    def from_dataframe(cls, df: pd.DataFrame) -> List["ReasonDiagnosisPlan"]:
        records = []
        # Convert date column to datetime format
        print(df["date"])
        df['date'] = df['date'].astype(str).apply(robust_date_parse)
        print(df["date"])
        df = df.sort_values(by='date', ascending=False).reset_index(drop=True)
        for i, row in df.iterrows():
            try:
                print(i, row["date"])
                if pd.notna(row["date"]):
                    date = row["date"].strftime('%m/%d/%Y')
                elif row["date"] is pd.NaT or row["date"] is pd.NA:
                    date = None
                record = cls(
                    # Handle date conversion
                    id=i,
                    date=date,
                    reason_of_visit=row["reason_of_visit"] if pd.notna(
                        row["reason_of_visit"]) else None,
                    diagnosis=row["diagnosis"] if pd.notna(
                        row["diagnosis"]) else None,
                    plan=row["plan"] if pd.notna(row["plan"]) else None
                )
                records.append(record)
            except ValidationError as e:
                print(f"Validation Error for row {i}: {e}")
        return records

    @classmethod
    def from_data_table(cls, data) -> List["ReasonDiagnosisPlan"]:
        records = []

        # Generate records
        for i, row in enumerate(data):
            try:
                date = None
                if row.get("date") and pd.notna(row.get("date", {}).get("text")):
                    parsed_date = robust_date_parse(row["date"]["text"])
                    date = (
                        parsed_date.strftime("%m/%d/%Y")
                        if parsed_date not in [None, pd.NaT]
                        else None
                    )

                record = cls(
                    id=i,
                    date=date or row.get("date"),
                    reason_of_visit=row.get("reason_of_visit", {}).get("text"),
                    diagnosis=row.get("diagnoses/assessment", {}).get("text"),
                    plan=row.get("plan", {}).get("text"),
                )
                records.append(record)
            except ValidationError as e:
                print(f"Validation Error for row {i}: {e}")

        return records


class FinalSummaryOutput(BaseModel):
    id: int
    encounter_date: datetime
    reason_of_visit: str
    diagnosis: str
    plan: str
    FollowUp: Optional[ReasonDiagnosisPlan] = None
    duplicate_item: Optional[int] = None
    invalid_item: Optional[bool] = False


def robust_date_parse(date_str):
    """Parses mixed-format dates and correctly handles AM/PM issues."""

    if pd.isna(date_str) or not isinstance(date_str, str) or date_str.strip() == "":
        return pd.NaT

    try:
        # Normalize spacing issues
        date_str = date_str.replace(" : ", ":").strip()

        # Fix "08 52AM" issue → Convert to "08:52AM"
        date_str = re.sub(r"(\b\d{1,2})\s+(\d{2})(AM|PM)", r"\1:\2\3", date_str)

        # Ensure "11AM" without minutes → Convert to "11:00AM"
        date_str = re.sub(r"(\b\d{1,2})(AM|PM)\b", r"\1:00\2", date_str)

        # Fix cases like "08 :52:00AM" → "08:52:00AM"
        date_str = re.sub(r"(\d{1,2})\s*:\s*(\d{2}:\d{2})(AM|PM)", r"\1:\2\3", date_str)

        # Handle date ranges like "July 12, 2022 11:19AM -0400 - 1:11PM -0400"
        if ("AM" in date_str or "PM" in date_str) and " - " in date_str:
            date_str = date_str.split(" - ")[0].strip()

        # Parse the cleaned date string
        parsed_date = parse(date_str, fuzzy=True)

        # Remove timezone info if present
        if parsed_date.tzinfo:
            parsed_date = parsed_date.replace(tzinfo=None)

        return parsed_date
    except Exception as e:
        print(f"Error parsing date: {date_str} | {e}")
        return pd.NaT


def get_follow_up_date(followup):
    match = re.search(r"date:\s*\d{2}/\d{2}/\d{4}", followup)
    if match:
        extracted_date_line = match.group()
        return extracted_date_line.split(":")[1]
    else:
        return None


def group_connection(list_of_reason_diagnosis_plan):
    nodes = {}
    parentless = []

    for item in list_of_reason_diagnosis_plan:
        print(f"Processing item: id={item.id}, connected_id={item.connected_id}")

        parent_key = next(
            (key for key, value in nodes.items() if item.id in value.children),
            None
        )
        child_key = next(
            (key for key, value in nodes.items() if item.connected_id in value.children),
            None
        )

        got_parents = parent_key is not None

        if item.id not in nodes and not got_parents and child_key is None:
            if item.connected_id is not None:
                nodes[item.id] = TreeNode(item.id)
                nodes[item.id].add_child(item.connected_id)
            else:
                parentless.append([item.id])
        elif got_parents and child_key is None:
            if item.connected_id is not None:
                nodes[parent_key].add_child(item.connected_id)
        elif child_key is not None:
            nodes[child_key].add_child(item.id)

    keys_to_remove = []

    for node_key, node in list(nodes.items()):
        for node_key_2, node_2 in list(nodes.items()):
            intersection = set(node.children) & set(node_2.children)
            if (
                    intersection
                    and node_key != node_key_2
                    and node_key_2 not in keys_to_remove
            ):
                keys_list = list(nodes.keys())
                index_node_key = keys_list.index(node_key)
                index_node_key2 = keys_list.index(node_key_2)
                if index_node_key > index_node_key2:
                    continue

                node.children = list(set(node.children + node_2.children)) + [node_key_2]
                keys_to_remove.append(node_key_2)

    for key in keys_to_remove:
        del nodes[key]

    return [[key] + value.children for key, value in nodes.items()] + parentless


class Extraction(BaseModel):
    extraction: List[ReasonDiagnosisPlan]


def call_gemini(records):
    prompt = f"""
        <data>
        {records}
        </data>

        You are an intelligent and helpful analytical assistant tasked with producing clear, concise, and useful summaries of medical encounter data. Each encounter includes four fields: Encounter Date, Reason for Visit, Diagnosis, and Plan of Action.

        <Instructions>

        Summarize Clearly and Concisely:

        - For each of the fields—Reason, Diagnosis, and Plan—make summaries brief, readable, and easy to understand.
        - Preserve all crucial medical information such as symptoms, diagnosis details, medicine names, test names, treatments, ICD codes, and any dates explicitly mentioned.

        Reason for Visit:

        - Clearly list all reasons presented by the patient.
        - Summarize without omitting any medical details (symptoms, medicines, tests, diagnoses mentioned by the patient).

        Diagnosis:

        - Provide concise and readable summaries.
        - Retain all essential medical details, including symptoms, treatments, medicines, test names, dates, ICD codes, or other critical information explicitly mentioned.

        Plan of Action:

        - Summarize clearly and succinctly.
        - Keep all mentions of follow-ups, treatments, prescribed medicines, or critical medical instructions.

        Clean and Correct:

        - Remove unnecessary or junk information.
        - Correct spelling mistakes and clarify ambiguities.

        Maintain Accuracy and Integrity:

        - Preserve original encounter dates clearly and accurately formatted.
        - Ensure no critical medical information is lost during summarization.

        Establish Encounter Connections:

        - Assign a connected_id to encounters explicitly or implicitly related through follow-ups, treatments, similar diagnoses, or shared reasons, irrespective of their sequence in the dataset.
        - Identify connections based on explicit mentions of follow-up visits, similarity of diagnoses, reason overlaps, treatment continuity, or proximity of encounter dates.
        - Assign a connected_id to encounters encounters few days apart if they contain substantially similar information.
        - Assign id to a duplicate_id If its same date just hours are different, while reason, diagnosis or plan is same.

        ID Management:

        - Do not create new IDs; use only the supplied encounter IDs.
        - Return all the IDS, if something is duplicate assign the duplicate id to it
        - If diagnosis and plan are empty for a given encounter date and reason of visit is invalid or there is no date but there is plan and reason of visit which is not duplicate or connected to anything else (e.g., documented this, went out, etc.,) or all junk then set the invalid flag to True else leave it False

        </Instructions>


        Outcome:

        Your summaries and established connections should clearly reflect the continuity and relationship between encounters, allowing healthcare providers quick, accurate, and effective interpretation.
        """

    gemini = Gemini(
        content=[prompt],
        response_schema=Extraction
    )

    return gemini()


def connect_followup(groups, list_of_reason_diagnosis_plan: List[ReasonDiagnosisPlan]):
    final_output = []
    grouped_records = {}
    for item in list_of_reason_diagnosis_plan:
        for group in groups:
            group = tuple(group)
            if group not in grouped_records:
                grouped_records[group] = []
            if item.id in group:
                grouped_records[group].append(item)
    print(grouped_records.keys())
    for group, group_records in grouped_records.items():
        sorted_records = sorted(group_records, key=lambda x: parser.parse(x.date))
        initial_visit = sorted_records[0]
        connect_record_str = []
        for record in sorted_records[1:]:
            if parser.parse(record.date) != parser.parse(initial_visit.date):
                connect_record_str.append(record)
        connect_record_str = list(
            {
                (entry.date, entry.diagnosis): entry for entry in connect_record_str
            }.values()
        )
        final_output.append(
            FinalSummaryOutput(
                id=initial_visit.id,
                invalid_item=initial_visit.invalid,
                duplicate_item=initial_visit.duplicate,
                encounter_date=initial_visit.date,
                FollowUp=connect_record_str,
                reason_of_visit=initial_visit.reason_of_visit,
                diagnosis=initial_visit.diagnosis,
                plan=initial_visit.plan,
            )
        )

    unique_data = list({(entry['encounter_date'], len(
        entry['FollowUp'])): entry for entry in final_output}.values())
    return unique_data


def merge_followups(records):
    df = pd.DataFrame(records)
    grouped_df = df.groupby("id").agg(lambda x: list(filter(None, x))).reset_index()
    grouped_data = grouped_df.to_dict(orient="records")
    for record in grouped_data:
        if len(sum(record["FollowUp"], [])) > 1:
            followup_entries = []
            for followup in sum(record["FollowUp"], []):
                followup_entries.append((followup.date, followup))

            # Sort by date
            sorted_followups = [
                followup[1]
                for followup in sorted(
                    followup_entries, key=lambda x: parser.parse(x[0].strip())
                )
            ]

            # Join sorted followups
            record["FollowUp"] = sorted_followups
        for key, value in record.items():
            if "FollowUp" in key:
                if isinstance(record["FollowUp"], list) and all(
                        isinstance(item, list) for item in record["FollowUp"]
                ):
                    # It's a list of lists, you can safely use sum to flatten it
                    record[key] = sum(record["FollowUp"], [])
                continue
            if isinstance(value, list):
                unique = list(set(value))
                record[key] = (
                    unique[0] if len(unique) == 1 else ""
                )  # Keep as list if multiple unique values
            else:
                record[key] = value
    sorted_records = sorted(grouped_data, key=lambda x: parser.parse(x['encounter_date']))
    return sorted_records


def process_followup_data(grouped_data, table, data):
    followups_table = defaultdict(list)
    counter = 0
    new_data = {}
    new_page_mapping = {}
    new_urls = {}
    new_url_with_coords = {}
    new_cof = {}

    fields = [
        "Date",
        "Reason of Visit",
        "Diagnoses/Assessment",
        "Plan",
        "",
        "Page reference",
    ]

    def extract_values(table, rdx_id, key):
        return [table[key][str(rdx_id)].get(field, "") for field in fields]

    for rdx in grouped_data:
        if rdx["FollowUp"] != []:
            formatted_strings = [
                f"Date: {followup.date}\n"
                f"Reason of Visit:\n {followup.reason_of_visit}\n"
                f"Diagnoses/Assessment:\n {followup.diagnosis}\n"
                f"Plan:\n {followup.plan}\n"
                for followup in rdx["FollowUp"]
            ]

            formatted_string = "\n\n".join(formatted_strings)
        else:
            formatted_string = "not found"
        new_data.update(
            {
                f"{counter}_0": rdx["encounter_date"],
                f"{counter}_1": rdx["reason_of_visit"],
                f"{counter}_2": rdx["diagnosis"],
                f"{counter}_3": rdx["plan"],
                f"{counter}_4": formatted_string,
                f"{counter}_5": table["values"][str(rdx["id"])]["Page reference"],
            }
        )

        for idx, value in enumerate(extract_values(table, rdx["id"], "page_mappings")):
            new_page_mapping[f"{counter}_{idx}"] = value
        for idx, value in enumerate(extract_values(table, rdx["id"], "urls")):
            new_urls[f"{counter}_{idx}"] = value
        for idx, value in enumerate(
                extract_values(table, rdx["id"], "url_with_coords")
        ):
            new_url_with_coords[f"{counter}_{idx}"] = value
        for idx, value in enumerate(extract_values(table, rdx["id"], "confidences")):
            new_cof[f"{counter}_{idx}"] = value

        counter += 1

    data["header"]["4"] = "Follow up"
    data["header"]["5"] = "Page reference"
    data["values"] = new_data
    data["urls"] = new_urls
    data["url_with_coords"] = new_url_with_coords
    data["confidences"] = new_cof
    data["page_mappings"] = new_page_mapping
    data["num_rows"] = len(grouped_data)
    data["num_cols"] = len(data["header"])

    return data


def remove_duplicates(data: List[ReasonDiagnosisPlan]) -> List[ReasonDiagnosisPlan]:
    """
    Removes duplicate ReasonDiagnosisPlan objects based on date and the length
    of the reason_of_visit and diagnosis strings.

    Args:
        data: A list of ReasonDiagnosisPlan objects.

    Returns:
        A new list containing only the unique ReasonDiagnosisPlan objects.
    """
    seen = set()
    unique_data = []
    for record in data:
        reason_len = len(record.reason_of_visit) if record.reason_of_visit is not None else 0
        diagnosis_len = len(record.diagnosis) if record.diagnosis is not None else 0
        key = (record.date, reason_len, diagnosis_len)
        if key not in seen:
            seen.add(key)
            unique_data.append(record)
    return unique_data


def get_gemini_results_rx(data):
    filtered_rdx_data = []
    records = ReasonDiagnosisPlan.from_data_table(data)
    filtered_records = [
        record
        for record in records
        if not (record.reason_of_visit in [None, "Not Found", ""] and record.diagnosis in [None, "Not Found", ""])
    ]
    summary_records_to_dict = [
        record.model_dump()
        for record in filtered_records
    ]
    list_of_reason_diagnosis_plan = [call_gemini(summary_records_to_dict)]
    formatted_output = []
    for res in list_of_reason_diagnosis_plan:
        if res is None:
            res = "[]"
        extract = json.loads(res)
        if len(extract["extraction"]) > 0:
            formatted_output.extend(
                [
                    ReasonDiagnosisPlan(**val)
                    for val in extract["extraction"]
                ]
            )
    formatted_output_no_invalids = [record for record in formatted_output if record.invalid is not True]
    unique_records = remove_duplicates(formatted_output_no_invalids)
    groups = group_connection(unique_records)
    unique_records_to_dict = [
        record.model_dump()
        for record in unique_records
    ]
    preserved_records = {record['id']: record for record in unique_records_to_dict}
    for idx, rdx_item in enumerate(data):
        if idx not in preserved_records:
            continue
        for rdx_key, rdx_value in rdx_item.items():
            if rdx_key not in preserved_records[idx]:
                continue
            if rdx_key == 'diagnoses/assessment':
                rdx_key = 'diagnosis'
            rdx_value['text'] = preserved_records[idx][rdx_key]
        filtered_rdx_data.append(rdx_item)

    return filtered_rdx_data, groups

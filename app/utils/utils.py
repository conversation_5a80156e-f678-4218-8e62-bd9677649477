import re
from collections import Counter
from copy import deepcopy
from difflib import <PERSON>quence<PERSON><PERSON><PERSON>
from typing import List, Dict

from rapidfuzz import fuzz

import constants as const
from technical_dicts import product_type_abbr_mapping


def words_in_text(text, block_words):
    """
    Check if word exist in list of words
    :param text: Field name
    :param block_words: Blocked field names
    :return: Flag
    """
    text_lower = text.lower()
    for word in block_words:
        if str.lower(word) in text_lower:
            return True
    return False


def check_fieldname(field_name, indicators, counter_indicators):
    return words_in_text(field_name, indicators) and not words_in_text(field_name, counter_indicators)


def iterate_list_or_item(list_or_item):
    if type(list_or_item) == list:
        for value in list_or_item:
            yield value
    else:
        yield list_or_item


def check_document_type_is_aps(document_type):
    return document_type and const.APS in document_type.lower().split()


def get_card_limit(feature_list, card_name, default_value=1):
    for feature_name in feature_list:
        if card_name in feature_name:
            card_limit = feature_name.split('_')[-1]
            return int(card_limit) if card_limit.isdigit() else 1
    return default_value


def merge_values(value1, value2):
    if isinstance(value1, dict) and isinstance(value2, dict):
        merged_dict = {}
        for key in set(value1.keys()).union(value2.keys()):
            merged_dict[key] = merge_values(value1.get(key, {}), value2.get(key, {}))
        return merged_dict
    elif isinstance(value1, list) and isinstance(value2, list):
        return list(set(value1 + value2))
    elif value1 is None:
        return value2
    else:
        return value1


def get_merged_key_value_pair(key1, key2, value1, value2):
    merged_key = tuple(element2 if element1 == '' else element1 for element1, element2 in zip(key1, key2))
    merged_value = deepcopy(value1)
    for tech_key, metadata_value_dict in value2.get('item', {}).items():
        if metadata_value_dict.get('entity') and not merged_value['item'][tech_key]['entity']:
            merged_value['item'][tech_key]['entity'] = metadata_value_dict['entity']
    merged_value['record_locations'] = list(
        set(value1.get('record_locations', []) + value2.get('record_locations', [])))

    return merged_key, merged_value


def check_same_file(set1, set2):
    """
    Determine if two sets of strings represent the same file.

    Given two sets of strings, this function checks if any strings in the first set
    (set1) have the same base filename (i.e., the part before the '$' character) as
    any strings in the second set (set2). If there is any common base filename
    between the two sets, the function returns True; otherwise, it returns False.

    Args:
        set1 (set): The first set of strings to compare.
        set2 (set): The second set of strings to compare.

    Returns:
        bool: True if there is any common base filename between set1 and set2,
              False otherwise.

    Example:
        set1 = {"file1$1", "file2$2", "file3$3"}
        set2 = {"file4$2", "file2$4", "file5$7"}
        result = check_same_file(set1, set2)
        # result will be True since "file2$" is common between set1 and set2.
    """
    split_set1 = set(string.split('$')[0] for string in set1)
    split_set2 = set(string.split('$')[0] for string in set2)
    return len(split_set1.intersection(split_set2)) > 0


def extract_numbers_from_str(input_string: str) -> List[float]:
    numbers_list = re.findall(r'\d+\.\d+|\d+', input_string)
    numbers = [float(number) for number in numbers_list]
    return numbers


def check_zero_value(text: str) -> bool:
    if not text:
        return False
    numbers = extract_numbers_from_str(text)
    return len(numbers) == 1 and numbers[0] == 0


def check_less_than_one(text: str) -> bool:
    if not text:
        return False
    numbers = extract_numbers_from_str(text)
    return len(numbers) == 1 and numbers[0] < 1


def get_field_priority(priorities, field_id):
    for priority in priorities:
        if priority.field_id == field_id:
            return priority
    return None


def strip_non_alpha(input: str) -> str:
    if not input:
        return ""
    return re.sub(r'^[^a-zA-Z]+|[^a-zA-Z]+$', '', input)


def strip_non_alphanumeric(input_str: str) -> str:
    if not input_str:
        return ""

    return re.sub(r'^[^a-zA-Z0-9]+|[^a-zA-Z0-9]+$', '', input_str)


def remove_non_alpha(input_string: str) -> str:
    if not input_string:
        return ""
    return ''.join(char for char in input_string if char.isalpha())


def remove_non_alphanumeric(input_string: str) -> str:
    if not input_string:
        return ""
    return ''.join(char for char in input_string if char.isalnum())


def extract_non_numerical_prefix(input_string):
    # extract currency from sum insured -> ['$100', 'INR200', '300'] -> ['$', 'INR', '']
    match = re.match(r'^[^\d]*', input_string.strip())
    return match.group(0) if match else ''


def check_abbreviation(key1, key2):
    for abbr, full in product_type_abbr_mapping.items():
        if (key1 == abbr and full in key2) or (key2 == abbr and full in key1):
            return True

    return False


def filter_dict_with_similar_keys(input_dict):
    """
    Filter a dictionary by removing keys with matching non-empty values.

    This function takes a dictionary as input and removes keys that have matching
    non-empty values with any other key in the input dictionary. It retains keys
    that has the least number of empty values in the key tuple.

    Args:
        input_dict (dict): A dictionary containing keys and their associated values. key is a tuple

    Returns:
        dict: A new dictionary with keys that do not have matching non-empty values
        with any other key in the input dictionary.
    """
    output_dict = {}
    seen_pair = set()
    merged_keys = set()

    for key1, value1 in input_dict.items():
        matched = False
        new_key, new_value = None, None
        emptiness_count = 99
        for key2, value2 in input_dict.items():
            if key1 == key2 or tuple(sorted([key1, key2])) in seen_pair:
                continue
            primary_key1 = strip_non_alphanumeric(key1[0]).lower()
            primary_key2 = strip_non_alphanumeric(key2[0]).lower()
            # non-matching product type, others exactly match
            matching_values_1 = (fuzz.ratio(primary_key1, primary_key2) >= 0 and
                                 all(item1 == item2 for item1, item2 in zip(key1[1:], key2[1:])) and
                                 len(primary_key1) > 4 and len(primary_key2) > 4)
            # matching product type, partial matching (empty & non-empty)
            matching_values_2 = ((fuzz.ratio(primary_key1, primary_key2) >= 80 or check_abbreviation(primary_key1, primary_key2)) and
                                 all(item1 == item2 or (item1 == '' or item2 == '') for item1, item2 in
                                     zip(key1[1:], key2[1:])))
            if matching_values_1 or matching_values_2:
                empty_fields1 = sum(1 for value in key1 if value == '')
                empty_fields2 = sum(1 for value in key2 if value == '')
                if empty_fields2 < emptiness_count:
                    if empty_fields1 <= empty_fields2:
                        new_key, new_value = get_merged_key_value_pair(key1, key2, value1, value2)
                        emptiness_count = empty_fields1
                    else:
                        new_key, new_value = get_merged_key_value_pair(key2, key1, value2, value1)
                        emptiness_count = empty_fields2
                matched = True
                merged_keys.update([key1, key2])
            seen_pair.add(tuple(sorted([key1, key2])))
        if matched:
            output_dict[new_key] = new_value
        elif key1 in merged_keys:
            continue
        else:
            output_dict[key1] = value1

    return output_dict


def filter_dict_with_similar_keys_recursively(entities_dict: dict) -> dict:
    """
    Recursively filter a dictionary until the length of the filtered dictionary stops changing.

    Args:
    entities_dict (dict): Dictionary to be filtered.

    Returns:
    dict: Filtered dictionary.
    """
    initial_length = len(entities_dict)
    filtered_dict = filter_dict_with_similar_keys(entities_dict)

    if len(filtered_dict) == initial_length:
        return filtered_dict
    else:
        return filter_dict_with_similar_keys_recursively(filtered_dict)


def nearest_smaller(nums: List[int], target: int) -> int:
    nearest = None

    for num in nums:
        # Check if the current number is smaller than the target
        if num < target:
            # If it's the first smaller number found or closer than the previous nearest
            if nearest is None or target - num < target - nearest:
                nearest = num

    return nearest


def get_similarity_score(target_string: str, source_string: str) -> float:
    """
    Compute similarity scores between the target_string and each string in the string_list.

    Args:
    - target_string (str): The string to compare against.
    - source_string (str): The string to compute similarity with.

    Returns:
    - float: similarity score between 0 and 1.
    """
    score = SequenceMatcher(None, target_string, source_string).ratio()
    return round(score, 2)


def get_distribution_score(string_list: List[str]) -> Dict[str, float]:
    """
    Compute the distribution score for each unique string in the list.

    Args:
    - string_list (List[str]): The list of strings to compute distribution for.

    Returns:
    - Dict[str, float]: A dictionary with strings as keys and their distribution percentage as values.
    """
    count = Counter(string_list)
    total = len(string_list)
    distribution = {k: round((v / total), 2) for k, v in count.items()}
    return distribution


def load_dot_env():
    """
    Load environment variables from .env file in the app directory.
    
    This function looks for a .env file in the app directory and loads
    key-value pairs into the environment. If the file doesn't exist,
    it fails silently.
    
    Example .env file format:
    AWS_PROFILE=friendly
    DEBUG=true
    """
    import os
    from pathlib import Path
    
    # Get the app directory path
    app_dir = Path(__file__).parent.parent
    env_path = app_dir / '.env'
    
    if env_path.exists():
        try:
            with open(env_path, 'r') as f:
                for line in f:
                    line = line.strip()
                    # Skip empty lines and comments
                    if line and not line.startswith('#'):
                        if '=' in line:
                            key, value = line.split('=', 1)
                            os.environ[key.strip()] = value.strip()
        except Exception as e:
            print(f"Warning: Could not load .env file: {e}")


import time
import traceback
from uuid import uuid4
from collections import defaultdict
from typing import Any, Dict, List

import constants as const
import titles as Title
from blueprints import BLUEPRINTS
from copy import deepcopy
from medical_codes.coding import CodeOutput
from medical_codes.medical_coding_utility import MedicalCodingUtility
from friendlylib.iterators import lfilter, find, list_minus, flatten
import edoc_utility
from elements.edocument import EDocument
from utils.open_ai import OpenAIRequestHandler


# =================================================================================

class Headers:
    def __init__(self, title):
        self.list_of_headers = list(BLUEPRINTS[title][const.HEADERS].keys())
        
        self.PROCEDURE = self.list_of_headers[0]
        self.TYPE = self.list_of_headers[1]
        self.VISIT_TYPE = self.list_of_headers[2]
        self.SNOMED = self.list_of_headers[3]
        self.SNOMED_DESCRIPTION = self.list_of_headers[4]
        self.CPT = self.list_of_headers[5]
        self.CPT_DESCRIPTION = self.list_of_headers[6]
        self.VISIT_DATE = self.list_of_headers[7]
        self.FINDINGS = self.list_of_headers[8]


# =================================================================================

class ProceduresUtility:
    def __init__(self):
        self.misc = object
        self.retrieved_data = None

        # --------------------------------------------------------------------------------------------------------------

    def set_dependencies(self, retrieved_data, objMisc, medical_coding_service: MedicalCodingUtility):
        self.retrieved_data = retrieved_data
        self.misc = objMisc
        self.medical_coding_service: MedicalCodingUtility = medical_coding_service

    # ------------------------------------------------------------------------------------------------------------------

    def _prepare_blueprint_data(self, card_name: str):
        self.blueprint_groups = BLUEPRINTS[card_name][const.GROUPS]
        self.card_name = card_name
        self.Header = Headers(card_name)
        self.header_list = self.Header.list_of_headers
        procedure_types = BLUEPRINTS[card_name]["procedure_types"]
        self.procedure_types = {procedure: procedure_type for procedure_type, procedure_list in procedure_types.items() for procedure in procedure_list}
        diagnosis_types = BLUEPRINTS[Title.DIAGNOSIS]["diagnosis_types"]
        diagnosis_types_dict = {diagnosis: 'Current' if diagnosis_type == 'Pathology' else diagnosis_type 
                                for diagnosis_type, diagnosis_list in diagnosis_types.items() for diagnosis in diagnosis_list}
        self.procedure_types.update(diagnosis_types_dict)
        visit_types = BLUEPRINTS[card_name]["visit_types"]
        self.procedure_visit_types = {visit: visit_type for visit_type, procedure_list in visit_types.items() for visit in procedure_list}
        self.all_visit_types = {diagnosis_tag: "Office Visit" for diagnosis_tag in diagnosis_types_dict}
        self.all_visit_types.update(self.procedure_visit_types)
        self.all_positions = BLUEPRINTS[card_name][const.POSITIONS]
        self.page_classifiers = {file.file_id: {page.page_number : page.page_class for page in file.page_classes} 
                                  for file in self.retrieved_data.file_page_classes}
        self.procedure_to_visit_date = {}
        for group in self.blueprint_groups:
            procedures = group[self.Header.PROCEDURE]
            visit_dates = group[self.Header.VISIT_DATE]
            for procedure in procedures:
                self.procedure_to_visit_date[procedure] = visit_dates
            
    # ------------------------------------------------------------------------------------------------------------------
    
    def _make_list_of_vectors(self, edoc):
        pages = self.misc.edoc_to_page_wise(edoc, self.blueprint_groups)
        list_of_vectors = []
        procedure_dates = {}
        for _doc_page_num, page_data in pages.items():
            for this_group in self.blueprint_groups:
                vector = self.misc.get_element(page_data, this_group)
                if len(vector):
                    list_of_vectors.append(vector)
            all_dates_vector = self.misc.get_element(page_data, BLUEPRINTS[self.card_name][const.HEADERS])
            all_visit_dates = all_dates_vector.get(self.Header.VISIT_DATE) 
            current_document_num, current_page_num = map(int, _doc_page_num.split('-'))
            if all_visit_dates:
                procedure_dates.setdefault(current_document_num, {}).setdefault(current_page_num, [])
                procedure_dates[current_document_num][current_page_num] = all_visit_dates
                
        return list_of_vectors, procedure_dates

    # ------------------------------------------------------------------------------------------------------------------

    def _browse_in_rows_vectors(self, list_of_rows: list, vectors: list):
        for row in list_of_rows:
            for vector in vectors:
                for site_vector in vector[self.Header.PROCEDURE]:
                    if site_vector['id'] == row[self.Header.PROCEDURE][0]['id']:
                        yield row, vector.get(self.Header.VISIT_DATE, [])

    # -----------------------------------------------------------------------------------------------------------------------

    def _make_multiple_time_attachement(self, positions: list, candidate_cells: list, primary_cell: dict) -> dict:

        for position in positions:
            candidate_in_required_position = list(filter(
                lambda cell: self.misc.is_required_position(position, self.misc.get_point(cell),
                                                            self.misc.get_point(primary_cell)),
                candidate_cells
            ))

            if len(candidate_in_required_position) == 0:
                continue

            if len(candidate_in_required_position) == 1:
                return [candidate_in_required_position[0]]

            closest_cell = min(
                candidate_in_required_position,
                key=lambda cell: self.misc.rect_distance(self.misc.get_point(cell), self.misc.get_point(primary_cell))
            )
            return [closest_cell]
        return []

    # -----------------------------------------------------------------------------------------------------------------------

    def _make_multiple_candidates(self, positions: list, candidate_cells: list, primary_header: str, list_of_rows: list,
                                  procedure_objects) -> dict:
        for row in list_of_rows:
            primary_cell = row[primary_header][0]
            matched_candidates: list = []
            for position in positions:
                candidate_in_required_position = lfilter(
                    lambda cell: self.misc.is_required_position(position, self.misc.get_point(cell),
                                                                self.misc.get_point(primary_cell)),
                    candidate_cells
                )
                if position in [const.BELOW_LINE, const.ABOVE_LINE]:
                    procedures_in_between = lfilter(
                        lambda cell: self._is_in_between(procedure_objects, self.misc.get_point(cell), self.misc.get_point(primary_cell)),
                        candidate_cells
                    )
                    candidate_in_required_position = list_minus(candidate_in_required_position, procedures_in_between)
                if len(candidate_in_required_position) == 0:
                    continue
                
                unique_candidates = list_minus(candidate_in_required_position, matched_candidates)
                matched_candidates.extend(unique_candidates)
            row[self.Header.FINDINGS] = matched_candidates
        return

    # -----------------------------------------------------------------------------------------------------------------------

    def _is_in_between(self, candidates, bottom_obj_point, value_obj_point):
        for candidate in candidates:
            middle_point = self.misc.get_point(candidate)
            is_between = (middle_point[1] > value_obj_point[1]) and (bottom_obj_point[1] > middle_point[1])
            if is_between:
                return True
        return False

    # -----------------------------------------------------------------------------------------------------------------------
    
    def _build_vector_primary_and_extended_cells(self, list_of_vectors: list, primary_cell_name,
                                                 candidate_cell_name) -> list:
        return list(filter(
            lambda vector: len(vector.get(primary_cell_name, [])) and len(vector.get(candidate_cell_name, [])) >= 1,
            list_of_vectors
        ))

    # -----------------------------------------------------------------------------------------------------------------------

    def _attach_date_cell(self, list_of_vectors: list, list_of_rows: list):

        vectors = self._build_vector_primary_and_extended_cells(list_of_vectors, self.Header.PROCEDURE,
                                                                self.Header.VISIT_DATE)

        for row, list_of_date_cells in self._browse_in_rows_vectors(list_of_rows, vectors):
            visit_date = self._make_multiple_time_attachement(
                self.all_positions[self.Header.VISIT_DATE]['level'], list_of_date_cells, row[self.Header.PROCEDURE][0])
            row[self.Header.VISIT_DATE] = visit_date
            
    # -----------------------------------------------------------------------------------------------------------------------

    def _attach_date_cell_from_prev_page(self, list_of_rows: list, visit_dates: dict) -> None:
        if not visit_dates:
            return
        for procedure_row in list_of_rows:
            if len(procedure_row.get(self.Header.VISIT_DATE, [])) != 0:
                continue
            procedure_cell = procedure_row[self.Header.PROCEDURE][0]
            procedure_page_num = procedure_cell.get('abs_page', 0)
            procedure_doc_num = procedure_cell.get('doc_num', 0)
            
            last_page_by_page_class = procedure_page_num
            file_id = procedure_cell.get('claimid', '')
            current_page_class = self.page_classifiers[file_id][procedure_page_num]
            while last_page_by_page_class in self.page_classifiers[file_id] and self.page_classifiers[file_id][last_page_by_page_class] == current_page_class:
                last_page_by_page_class -= 1
            if last_page_by_page_class < 1 or procedure_doc_num not in visit_dates:
                continue
            filtered_children_candidates = {key: value for key, value in visit_dates[procedure_doc_num].items() if last_page_by_page_class <= key < procedure_page_num}
            match_date = self._get_date_from_previous_page(procedure_cell, filtered_children_candidates)
            if match_date:
                procedure_row[self.Header.VISIT_DATE] = [match_date]
            
    # -----------------------------------------------------------------------------------------------------------------------
    
    def _get_date_from_previous_page(self, procedure_cell: dict, prev_page_date_candidates: dict) -> dict:
        if len(prev_page_date_candidates) == 0:
            return {}
        procedure_tag = procedure_cell.get('type')
        
        for page_num, date_entries in reversed(prev_page_date_candidates.items()):
            for tag in self.procedure_to_visit_date[procedure_tag]:
                filtered_date_candidates = lfilter(
                    lambda child: child['type'] == tag,
                    date_entries)
                if len(filtered_date_candidates) != 0:
                    closest_date_prev_page = max(
                        filtered_date_candidates,
                        key=lambda child: (child['abs_page'], child['bottom']))
                    return closest_date_prev_page
        return {}

    # -----------------------------------------------------------------------------------------------------------------------

    def estimate_primary_cell_attachement(self, primary_objects: list, primary_cell_name: str) -> list:
        col_names = {}
        list_of_vectors = []
        for obj in primary_objects:
            vector = self.misc.prepare_vector(obj, col_names, primary_cell_name)
            procedure_type = self._insert_procedure_type(deepcopy(obj))
            procedure_visit_type = self._insert_procedure_visit_type(deepcopy(obj))
            if procedure_type is not None:
                vector['Type'] = [procedure_type]
            if procedure_visit_type is not None:
                vector['Visit Type'] = [procedure_visit_type]
            if Title.DISABLE_SNOMED not in self.retrieved_data.feature:
                snomed_object, snomed_code = self._generate_snomed_code(obj)
                vector['SNOMED'] = snomed_object
                vector['SNOMED Description'] = self._generate_code_description(obj, snomed_code, 'snomed.description')
            vector['Visit Date'] = []
            vector['Finding'] = []
            cpt_object, cpt_code = self._generate_cpt_code(obj)
            vector['CPT'] = cpt_object
            vector['CPT Description'] = self._generate_code_description(obj, cpt_code, 'cpt.description')
            list_of_vectors.append(vector)

        return list_of_vectors

    # -----------------------------------------------------------------------------------------------------------------------

    def _insert_procedure_type(self, procedure_dict: dict) -> None:
        procedure_type = self.procedure_types.get(procedure_dict['type'], '')
        if procedure_type == '':
            return None
        procedure_dict['text'] = procedure_type
        procedure_dict['codified_as'] = procedure_type
        procedure_dict['type'] = const.GENERATED_ENTITY + 'procedure.type'
        procedure_dict['id'] = str(uuid4())
        procedure_dict['confidence'] = 100

        return procedure_dict

    # -----------------------------------------------------------------------------------------------------------------------

    def _insert_procedure_visit_type(self, procedure_dict: dict) -> None:
        procedure_visit_type = self.all_visit_types.get(procedure_dict['type'], '')
        if procedure_visit_type == '':
            return None
        procedure_dict['text'] = procedure_visit_type
        procedure_dict['codified_as'] = procedure_visit_type
        procedure_dict['type'] = const.GENERATED_ENTITY + 'procedure.visit_type'
        procedure_dict['id'] = str(uuid4())
        procedure_dict['confidence'] = 100

        return procedure_dict

    # -----------------------------------------------------------------------------------------------------------------------
    def _generate_snomed_code(self, procedure_object):
        procedure_snomed = []
        procedure_result = deepcopy(procedure_object)
        procedure_text = procedure_object['text']
        code_output: CodeOutput = self.medical_coding_service._get_medical_code(procedure_text, "snomed_procedures")

        if code_output.medical_code:
            procedure_result['id'] = str(uuid4())
            procedure_result['text'] = code_output.medical_code
            procedure_result['type'] = const.GENERATED_ENTITY + "snomed"
            procedure_result['confidence'] = code_output.score
            if code_output.description:
                procedure_result['metadata'] = {"info": f"{code_output.description}", "type": "medical_code"}
            procedure_snomed.append(procedure_result)
        return procedure_snomed, code_output
    
    # -----------------------------------------------------------------------------------------------------------------------
    
    def _generate_cpt_code(self, procedure_object):
        procedure_snomed = []
        procedure_result = deepcopy(procedure_object)
        procedure_text = procedure_object['text']
        code_output: CodeOutput = self.medical_coding_service._get_medical_code(procedure_text, "cpt")

        if code_output.medical_code:
            procedure_result['id'] = str(uuid4())
            procedure_result['text'] = code_output.medical_code
            procedure_result['type'] = const.GENERATED_ENTITY + "cpt"
            procedure_result['confidence'] = code_output.score
            if code_output.description:
                procedure_result['metadata'] = {"info": f"{code_output.description}", "type": "medical_code"}
            procedure_snomed.append(procedure_result)
        return procedure_snomed, code_output
    
    # -----------------------------------------------------------------------------------------------------------------------
    
    def _generate_code_description(self, procedure_object, snomed_code: CodeOutput, type_desc: str):
        procedure_snomed = []
        procedure_result = deepcopy(procedure_object)

        if snomed_code.medical_code:
            procedure_result['id'] = str(uuid4())
            procedure_result['text'] = snomed_code.description
            procedure_result['type'] = type_desc
            procedure_result['metadata'] = {"card_generated": True}
            procedure_snomed.append(procedure_result)
        return procedure_snomed
    
    # -----------------------------------------------------------------------------------------------------------------------
    
    def _format_diagnosis_to_procedure_card(self, diagnosis_list):
        procedures_list = []
        if not diagnosis_list:
            return procedures_list
        for row in diagnosis_list:
            procedure = find(lambda cell: cell["display_key"] == "Diagnosis", row, {}).get('entity_object')
            visit_type = self._insert_procedure_type(deepcopy(procedure))
            procedure_visit_type = self._insert_procedure_visit_type(deepcopy(procedure))
            visit_date = find(lambda cell: cell["display_key"] == "Visit Date", row, {}).get('entity_object')
            snomed_object, snomed_code = self._generate_snomed_code(procedure) 
            snomed_description = self._generate_code_description(procedure, snomed_code, 'snomed.description')
            cpt_object, cpt_code = self._generate_cpt_code(procedure)
            cpt_description = self._generate_code_description(procedure, cpt_code, 'cpt.description')
            procedure_row = {
                    "Procedure": [] if not procedure else [procedure],
                    "Type": [] if not procedure else [visit_type],
                    "Visit Type": [] if not procedure else [procedure_visit_type],
                    "SNOMED": [] if not snomed_object else snomed_object,
                    "SNOMED Description": [] if not snomed_description else snomed_description,
                    "CPT": [] if not cpt_object else cpt_object,
                    "CPT Description": [] if not cpt_description else cpt_description,
                    "Visit Date": [] if not visit_date else [visit_date],
                    "Finding": []
                }
            procedures_list.append(procedure_row)
        return procedures_list

    # -----------------------------------------------------------------------------------------------------------------------

    def _make_list_of_rows(self, list_of_vectors):
        primary_cell_name = 'Procedure'
        list_of_rows = []
        for vectors in list_of_vectors:
            row = {}
            date_objects = vectors.get('Visit Date', [])
            finding_objects = vectors.get('Finding', [])
            procedure_objects = vectors.get(primary_cell_name, [])
            if len(procedure_objects) == 0:
                continue
            if (len(procedure_objects) < 2) and (len(date_objects) < 2) and (len(finding_objects) < 2):
                row[primary_cell_name] = procedure_objects
                procedure_type = self._insert_procedure_type(deepcopy(procedure_objects[0]))
                if procedure_type:
                    row['Type'] = [procedure_type]
                procedure_visit_type = self._insert_procedure_visit_type(deepcopy(procedure_objects[0]))
                if procedure_visit_type:
                    row['Visit Type'] = [procedure_visit_type]
                if Title.DISABLE_SNOMED not in self.retrieved_data.feature:
                    snomed_object, snomed_code = self._generate_snomed_code(procedure_objects[0])
                    row['SNOMED'] = snomed_object
                    row['SNOMED Description'] = self._generate_code_description(procedure_objects[0], snomed_code, 'snomed.description')
                    
                cpt_object, cpt_code = self._generate_cpt_code(procedure_objects[0])
                row['CPT'] = cpt_object
                row['CPT Description'] = self._generate_code_description(procedure_objects[0], cpt_code, 'cpt.description')
                row['Visit Date'] = date_objects
                row['Finding'] = finding_objects
                list_of_rows.append(row)
            else:
                page_list_of_rows = self.estimate_primary_cell_attachement(procedure_objects, primary_cell_name)
                if finding_objects:
                    self._make_multiple_candidates(self.all_positions[self.Header.FINDINGS]['level'], finding_objects, 'Procedure', page_list_of_rows,
                                                   procedure_objects)
                list_of_rows.extend(page_list_of_rows)
        return list_of_rows


    # ------------------------------------------------------------------------------------------------------------------

    def _deduplicate_procedures_by_snomed_and_date(self, list_of_rows):
        
        filtered_rows = []
        grouped_by_date = defaultdict(list)
        
        for row in list_of_rows:
            snomed_code = self.misc.get_display_key_text_other_format(row, const.SNOMED)
            visit_date = self.misc.get_display_key_text_other_format(row, const.VISIT_DATE)
            if snomed_code and visit_date:
                grouped_by_date[visit_date].append((snomed_code, row))
            else:
                filtered_rows.append(row)
        
        for date_key, icd_row_pairs in grouped_by_date.items():
            snomed_seen = set()
            for snomed_code, row in icd_row_pairs:
                if any(snomed_code in seen or seen in snomed_code for seen in snomed_seen):
                    continue
                snomed_seen.add(snomed_code)
                filtered_rows.append(row)
            
        return filtered_rows
    # ------------------------------------------------------------------------------------------------------------------

    def get_procedures_list(self, list_of_rows):
        procedures_list = []
        for row in list_of_rows:
            if not row.get('Procedure'):
                continue
            procedures_list.append(row['Procedure'][0]['text'])
        return list(set(procedures_list))

    def _update_procedures_with_impairment(self, list_of_rows, impairment_dict, procedure_impairment_mapping):
        for row in list_of_rows:
            if not row.get('Procedure'):
                continue

            impairment = procedure_impairment_mapping.get(row['Procedure'][0]['text'])
            if not impairment_dict.get(impairment):
                continue
            row['Impairment'] = [impairment_dict[impairment]]

    def get_impairment_list(self, edoc):
        impairment_dict = {}
        for page_of_lines in edoc:
            page_of_entities = flatten(page_of_lines)
            impairment_entity_list = lfilter(
                lambda entity_: entity_['type'] == 'cards_generated_impairment',
                page_of_entities
            )
            impairment_dict.update({ent_['text']: ent_ for ent_ in impairment_entity_list})
        return impairment_dict

    def _get_procedure_impairment_mapping_using_openai(self, procedures_list, impairments_list):

        input_prompt = f"""
        I have two lists:

        Procedures: A list of medical procedures. {procedures_list}
        Impairments: A list of medical impairments. {impairments_list}

        Your task is to associate the most likely impairment (if any) with each procedure based on medical knowledge,
        common associations, and clinical relevance.

        Only include procedures in the output if there is a clear and justifiable link to an impairment. 
        Do not force a match if no suitable impairment exists for a procedure, simply omit it from the output.

        Input:
        Procedures: [<list of procedures>]
        Impairments: [<list of impairments>]

        Output format:
        Return the result as a JSON object, where each key is a procedure name (exactly as provided) and the value is 
        the most likely impairment (exactly as provided). Do not include procedures with no relevant match.
        """

        try:
            start_time = time.time()

            print("Initializing OpenAI handler...")
            openai_handler = OpenAIRequestHandler()
            openai_handler.initialize()

            print("Sending request to OpenAI...")
            response = openai_handler.get_openai_response(input_prompt, encoded_image_list=[])

            if response:
                print("Processing response...")
                return response

            end_time = time.time()
            print(f"Total Time taken for OpenAI request: {end_time - start_time:.2f} seconds")

        except Exception as e:
            print(f"An error occurred during OpenAI request: {e}")

        return {}

    def build_card(
        self,
        edoc: EDocument,
        diagnosis_procedures,
        card_name: str,
        failed_cards_list: Dict[str, Any],
    ):
        try:
            self._prepare_blueprint_data(card_name)
            list_of_vectors, visit_dates = self._make_list_of_vectors(edoc)
            list_of_rows = self._make_list_of_rows(list_of_vectors)
            self._attach_date_cell(list_of_vectors, list_of_rows)
            self._attach_date_cell_from_prev_page(list_of_rows, visit_dates)
            
            if diagnosis_procedures:
                diagnosis_list = self._format_diagnosis_to_procedure_card(diagnosis_procedures)
                list_of_rows.extend(diagnosis_list)
                
            procedures_list = self.get_procedures_list(list_of_rows)
            impairment_dict = self.get_impairment_list(edoc)
            procedure_impairment_mapping = self._get_procedure_impairment_mapping_using_openai(procedures_list, list(
                impairment_dict.keys()))
            self._update_procedures_with_impairment(list_of_rows, impairment_dict, procedure_impairment_mapping)
            list_of_rows = self._deduplicate_procedures_by_snomed_and_date(list_of_rows)
            list_of_rows = self.misc.remove_duplicates_by_subtext_other_format(list_of_rows, "Procedure", {"SNOMED" : False, "CPT": False}, [])
            
            list_of_rows = self.misc.make_single_object_in_extended_columns(list_of_rows, [self.Header.FINDINGS])
            list_of_rows = self.misc.sort_rows_by_date_cells(list_of_rows,
                                                                self.Header.VISIT_DATE,
                                                                self.retrieved_data.date_format)

            ui_card = self.misc.build_smart_ui_card(list_of_rows, self.header_list, self.card_name, const.EMPTY_STRING)
            return ui_card
        except Exception as e:
            print(f'ERROR: Procedures Card Creation Failed: {e}')
            failed_cards_list.append({'card_name':'Procedures', 'message': f'{traceback.format_exc()}'})
            return None

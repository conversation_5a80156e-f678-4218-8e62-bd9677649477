#!/usr/bin/env python
# encoding: utf-8
"""
    This module contains all of the string literals which are 
    repeated across all of the classes. This ensures the 
    integrity and validity of the string literal 
    being used across the class

"""
# All constants

# ------ punctuation characters -------------
import titles as Title

bSlash = '\\'
fSlash = '/'
percentage = '%'
colon = ':'
comma = ','
point = '.'
hyphen = '-'
uscore = '_'
uscore2 = '__'
pipe = '|'
semiColon = ';'
dblQuote = '"'
open_parenthesis = '('
close_parenthesis = ')'
EMPTY_STRING = ''
space = ' '
space2 = '  '
smallAngle = '<'
largeAngle = '>'
question = '?'
dollar = '$'
cr = '\n'
cr2 = '\n\n'
tab = '\t'
tab2 = '\t\t'

HTML_SUPERSCRIPT_DAGGER = '<sup>&#8224;</sup>'

NOT_PROVIDED = 'not provided'

LOW = 'Low'
HIGH = 'High'
NEGATIVE = 'negative'
ABNORMAL = "Abnormal"

TIME_STAMP = 'time_stamp'
VALID = 'valid'
IN_VALID = 'invalid'
NOISE = 'noise'
VALUE = 'value'
STATUS = 'status'
POSSIBLE_DATE = 'possible_date'
US_DATE = 'us_date'
# DEFAULT_DATE_FORMAT = '%Y/%m/%d'
DEFAULT_DATE_FORMAT = '%d/%m/%Y'
DAYS_UL = 99999
NOT_FOUND = 'not found'
UNKNOWN = 'unknown'
UNKNOWN_VISIT_DATE = 'unknown visit date'
NO_DATE = 'No Date'
YEAR_LESS = 'year_less'
GROUPS = 'groups'
CM_TO_INCHES = 0.3937007874015
INCHES_TO_CM = 2.54
KG_TO_LBS = 2.20462
LBS_TO_KG = 0.453592

ASCENDING = 'ascending'
DESCENDING = 'descending'

SECTION_TAG = 'section_tag'
RESTRICTED_SECTIONS = 'restricted_sections'
HEADERS = 'headers'
EQUIPMENT = 'Equipment'
MAX_NUM = 65000

SAME_LINE = 'same line'
ABOVE_LINE = 'above line'
BELOW_LINE = 'below line'
SAME_LINE_RIGHT_SIDE = 'same line right side'
SAME_LINE_THEN_BELOW = 'same_line_then_below'
DEFAULT = 'default'
POSITIONS = 'positions'
FIELDS_LIST = 'fields_list'

# ------- UDC RULES --------
NEAREST = 'nearest'
NEAREST_IN_PAGE = 'nearest_in_page'
NEAREST_IN_FILE = 'nearest_in_file'
ANYWHERE = 'anywhere'
SAME_PAGE_OR_AFTER = 'same_page_or_after'
SAME_PAGE_OR_BEFORE = 'same_page_or_before'
SAME_LINE_OR_BELOW = 'same_line_or_below'
SAME_LINE_OR_ABOVE = 'same_line_or_above'
SAME_LINE_OR_AFTER = 'same_line_or_after'
SAME_LINE_OR_BEFORE = 'same_line_or_before'

# --------Cards Generated Tags-----------
GENERATED_ENTITY = "cards_generated_"
MERGED_ENTITY = "merged_"

# --------Medical Codes-----------
MEDICAL_CODE = "medical_code"
LOINC = "LOINC"
RXCODE = "RxCode"
CVXCODE = "CvxCode"

# -- Vital Signs ranges  ------
# https://pressbooks.library.ryerson.ca/vitalsign/chapter/blood-pressure-ranges/
SYSTOLIC_LOW = 40
SYSTOLIC_HIGH = 220
DIASTOLIC_LOW = 20
DIASTOLIC_HIGH = 160

BMI_LOW = 12.0
BMI_HIGH = 55.0

OXYGEN_LOW = 80
OXYGEN_HIGH = 100
# https://www.onhealth.com/content/1/normal_healthy_heart_rate_charts_health_heart
PULSE_LOW = 30
PULSE_HIGH = 180

TEMP_F_LOW = 95
TEMP_F_HIGH = 108

TEMP_C_LOW = 35
TEMP_C_HIGH = 42

RESP_LOW = 8
RESP_HIGH = 25

WEIGHT_LB_LOW = 40
WEIGHT_LB_HIGH = 440
WEIGHT_KG_LOW = round((0.4535 * WEIGHT_LB_LOW), 1)
WEIGHT_KG_HIGH = round((0.4535 * WEIGHT_LB_HIGH), 1)

WEIGHT_INSURED_LOW = 40
WEIGHT_INSURED_HIGH = 300

ENGLISH = 'English'
GERMAN = 'German'
FRENCH = 'French'

ADD = 'add'
DELETE = 'delete'
EDIT = 'edit'

EXTRA = 'extra'

DIAGNOSIS = 'Diagnosis'
ICD10 = 'ICD-10'
VISIT_DATE = 'Visit Date'
DIAGNOSIS_DATE = 'Diagnosis Date'
SNOMED = 'SNOMED'
DATE = 'Date'
CLAIMED_INJURY = 'Claimed Injury'

AGE_AT_DIAG = 'Age Diagnosed'
AGE_OF_DEATH = 'Age of Death'
TYPE = 'Type'

MIN_VITAL_POINTS = 3  # show the vital signs in table less than MIN_VITAL_POINTS for any vital sign  

CLASSIFIER_PAGES = {
    Title.LABORATORY_RESULTS: ['lab-report', 'templated_page'],
    Title.INSURED_DETAILS: ['proposal-form', 'medical-examination', 'templated_page']
}

ALERTS_REQUIRED_FIELDS = {
    Title.INSURED_DETAILS: [
        "Name",
        "Date of birth",
        "Gender",
        "Occupation",
        "Weight",
        "Height",
        "Smoking"
    ],
    Title.TECHNICAL_DETAILS: [
        "Product Type",
        "Annuity",
        "Sum Insured",
        "Duration",
        "Currency",
        "Application Date"
    ]
}

# ---- Cards output structure constants -----

FILE_PAGE_CLASSES = "file_page_classes"
WORDS = "words"
CLAIM_FILE_ID = "claim_file_id"
ALERTS = 'alerts'
PAGES = "pages"
PAGE = "page"
WORD_ID = "word_id"
TEXT = "text"
CONFIDENCE = "confidence"
OCR_CONFIDENCE = "ocr_confidence"
WORD_TYPE = "word_type"
BBOX = "bbox"
LEFT = "left"
TOP = "top"
RIGHT = "right"
BOTTOM = "bottom"
DATAVALUES = "datavalues"
DATAVALUE_ID = "data_value_id"
CODIFIED_AS = "codified_as"
CODIFICATION_CATEGORY = "codification_category"
FORMATTED_CODIFICATION = "fomatted_codification"
CODIFY = "codify"
DETECTED_LANG = "detected_lang"
ENTRY_ID = "entry_id"
SOURCE = "source"
LAYOUT_UUID = "layout_uuid"
CONTEXT = "context"
CODIFIED_DATETIME_OBJECT = "codified_datetime_obj"
SECTION_INFO = "section_info"
IS_SKIPPED = "is_skipped"
XPATH = "xpath"
UI_ID = "ui_id"
ENTITY_TYPE = "entity_type"
ENTITY_CONFIDENCE = "entity_confidence"
IS_USER_EDITED = "is_user_edited"
CARDS = "cards"
CARD_ID = "card_id"
DEFAULT_SCHEMA = "default_schema"
POSSIBLE_SCHEMAS = "possible_schemas"
RECORDS = "records"
RECORD_ID = "record_id"
RECORD_SCHEMA_ID = "record_schema_id"
ITEMS = "items"
FIELD_ID = "field_id"
CARD_CONFIDENCE = "card_confidence"
DATA_VALUES = "data_values"
SUMMARY_EVENTS = "summary_events"
CARD_INDEX = "card_index"
RELATIONSHIPS = "relationships"
RECORD_LOCATIONS = "record_locations"
SUMMARY_CUSTOM_DATA = "summary_custom_data"

GERMAN_MALE_PREFIXES = ["herr", "herrn"]

GERMAN_FEMALE_PREFIXES = ["frau", "fraulein"]

ENGLISH_MALE_PREFIXES = ["mr", "mr.", "mister", "sir"]

ENGLISH_FEMALE_PREFIXES = ["mrs", "mrs.", "madam", "miss", "ms", "ms."]

AVOCATION_SUFFIXES = ["highrisk", "midrisk", "lowrisk"]

TRAVEL_SUFFIXES = ["international", "domestic"]

EXTRA_SUBSTANCE_ABUSE_TAGS = ["smoking.device", "smoking.duration", "smoking.quantity", "tobacco.duration",
                              "tobacco.product", "tobacco.quantity", "alcohol.beverage", "alcohol.duration",
                              "alcohol.quantity", "drugs.duration", "drugs.product", "drugs.quantity"]

SUBSTANCE_ABUSE_TAGS = ["smoking", "tobacco", "alcohol", "drugs", "marijuana"]

SUBSTANCE_ABUSE_PRODUCT_TAGS = ["smoking.device", "tobacco.product", "alcohol.beverage", "drugs.product",
                                "marijuana.consumption_method"]

SUBSTANCE_ABUSE_DURATION_AND_QUANTITY_TAGS = ["smoking.duration", "smoking.quantity", "tobacco.duration",
                                              "tobacco.quantity", "alcohol.duration", "alcohol.quantity",
                                              "drugs.duration", "drugs.quantity", "marijuana.duration",
                                              "marijuana.quantity"]

US_NAMES = ["us", "usa", "united states", "united states of america", "u.s", "u.s.a", "u.s.a."]
US_STATES = ["alabama", "alaska", "arizona", "arkansas", "california", "colorado", "connecticut", "delaware", "florida",
             "georgia", "hawaii", "idaho", "illinois", "indiana", "iowa", "kansas", "kentucky", "louisiana", "maine",
             "maryland", "massachusetts", "michigan", "minnesota", "mississippi", "missouri", "montana", "nebraska",
             "nevada", "new hampshire", "new jersey", "new mexico", "new york", "north carolina", "north dakota",
             "ohio", "oklahoma", "oregon", "pennsylvania", "rhode island", "south carolina", "south dakota",
             "tennessee", "texas", "utah", "vermont", "virginia", "washington", "west virginia", "wisconsin", "wyoming"]

US_STATES_ABS = ["al", "ak", "az", "ar", "ca", "co", "ct", "de", "fl", "ga", "hi", "id", "il", "in", "ia", "ks", "ky",
                 "la", "me", "md", "ma", "mi", "mn", "ms", "mo", "mt", "ne", "nv", "nh", "nj", "nm", "ny", "nc", "nd",
                 "oh", "ok", "or", "pa", "ri", "sc", "sd", "tn", "tx", "ut", "vt", "va", "wa", "wv", "wi", "wy"]

TAGS_CREATED_BY_CARDS = ['insured_1.name', 'insured_2.name', 'patient.name', 'applicant.name', 'underwriter.name',
                         'insured_1.address', 'insured_2.address', 'patient.address', 'applicant.address',
                         'duration.type', 'annuity_duration.type', 'doctor.name', 'doctor.address', 'encounter.address']

IMPAIRMENT_SPECIFIC_TAGS = ["insured_1.heart.disorder.risk.yes", "insured_1.neoplasm.risk.yes",
                            "insured_1.mental.disorder.risk.yes", "insured_1.immune.disorder.risk.yes",
                            "insured_1.muscleandbone.disorder.risk.yes", "insured_1.endocrine.disorder.risk.yes",
                            "insured_1.circulatory.disorder.risk.yes", "insured_1.blood.disorder.risk.yes",
                            "insured_1.respiratory.disorder.risk.yes", "insured_1.ent_disorder.risk.yes",
                            "insured_1.lymphatic.disorder.risk.yes", "insured_1.digestive.disorder.risk.yes",
                            "insured_1.kidney.disorder.risk.yes", "insured_1.medical.test.yes",
                            "insured_1.female.reproductive.risk.yes", "insured_1.physical.disorder.risk.yes",
                            "insured_1.nervous.disorder.risk.yes", "insured_1.gastrointestinal.disorder.risk.yes",
                            "insured_1.endocrine_disorder.risk.yes", "insured_1.congenital.disorder.risk.yes",
                            "insured_1.hepatobiliary.risk.yes"]
# Document Types
APS = 'aps'

IMPAIRMENTS = [
    "anemia",
    "anxiety",
    "appendicitis",
    "asthma",
    "atrial fibrillation",
    "ADHD",
    "bacterial infections",
    "benign tumor",
    "bipolar disorder",
    "birth defects",
    "blood disorders",
    "bone disorders",
    "spinal disorders",
    "brain disorders",
    "cancer",
    "childbirth",
    "chronic obstructive pulmonary disease",
    "circulatory system disorders",
    "congestive heart failure",
    "connective tissue disorders",
    "covid",
    "cushing's syndrome",
    "dementia",
    "dental disorders",
    "depression",
    "developmental disorders",
    "diabetes",
    "digestive disorders",
    "drug resistance",
    "ear disorders",
    "eating and sleep disorders",
    "epilepsy",
    "eye disorders",
    "facial defects",
    "female reproductive health disorders",
    "fracture",
    "fungal infections",
    "gallbladder and pancreas disorders",
    "gastrointestinal infections",
    "genetic conditions",
    "genital defects",
    "heart defects",
    "heart disorders",
    "hemophilia",
    "hernia",
    "hormone gland disorders",
    "hypertension",
    "hypoglycemia",
    "immune deficiencies",
    "infectious disease",
    "injuries, poisoning, and burns",
    "intellectual disabilities",
    "joint disorders",
    "kidney disorders",
    "liver disorders",
    "lung defects",
    "lung infections",
    "male reproductive health disorders",
    "marfan syndrome",
    "mental disorders",
    "metabolic disorders",
    "migraine",
    "movement disorders",
    "multiple sclerosis",
    "muscle disorders",
    "nerve disorders",
    "newborn complications",
    "nutrient deficiency",
    "overweight and obesity",
    "parasitic infections",
    "peripheral artery disease",
    "personality disorders",
    "polycystic ovary syndrome",
    "post-traumatic stress disorder",
    "pregnancy-related complications",
    "psychotic disorders",
    "rheumatoid arthritis",
    "sclerosis",
    "sexually transmitted infections",
    "skin disorders",
    "sleep apnea",
    "spine disorders",
    "spleen complications",
    "stroke",
    "substance use disorders",
    "surgery complications",
    "throat infections",
    "thyroid disorders",
    "tuberculosis",
    "ulcer",
    "urinary tract disorders",
    "vein disorders"
]



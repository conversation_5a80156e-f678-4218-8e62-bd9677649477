import json
import sys
from typing_extensions import Dict, List, TypedDict

import field_id_entity_type_mapping
from friendlylib import storage


class EntityMappingFileCardField(TypedDict):
    card_field_id: str
    entity_ids: List[str]


class EntityMappingFileCard(TypedDict):
    card_id: str    
    card_fields: List[EntityMappingFileCardField]


class EntityMappingFile(TypedDict):
    cards: List[EntityMappingFileCard]


def _format_entity_mapping(cloud_folder_path: str) -> EntityMappingFile:
    card_ids_info = field_id_entity_type_mapping.FIELD_ID_BLUEPRINTS
    result = EntityMappingFile({"cards": []})

    for card_id, entities in card_ids_info.items():
        card_data: EntityMappingFileCard = {"card_id": card_id, "card_fields": []}

        # Create a dictionary to store entity_ids for each card_field_id
        card_fields_dict: Dict[str, EntityMappingFileCardField] = {}

        for entity_id, field_id in entities.items():
            card_fields_dict.setdefault(
                field_id, 
                {"card_field_id": field_id, "entity_ids": []}
            )
            card_fields_dict[field_id]["entity_ids"].append(entity_id)

        # Add the card_fields to the card_data
        card_data["card_fields"] = list(card_fields_dict.values())
        result["cards"].append(card_data)

    return result


if __name__ == "__main__":
    if len(sys.argv) == 1:
        print(f"pyhton3 {sys.argv[0]} <upload-path>")
        print(f"  ex: pyhton3 {sys.argv[0]} s3://bulk-test/cards/field_id_entity_id_mapping.json")
        print(f"use `-` to print results locally:")
        print(f"  pyhton3 {sys.argv[0]} -")
        exit(1)

    cloud_folder_path = sys.argv[1]

    result = _format_entity_mapping(cloud_folder_path)
    if cloud_folder_path == "-":
        print(json.dumps(result, indent = 4))

    else:
        storage.upload_json(cloud_folder_path, result)
        print("File Uploaded Successfully")

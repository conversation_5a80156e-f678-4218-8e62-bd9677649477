import json

from typing import Dict, Optional, Tuple

from pydantic import BaseModel, Field, ValidationError

import model_input_files_config as Model_Data


class NicotineRequirement(BaseModel):
    condition: int
    date_type_type: str = Field(alias="condition_date_type")


class BmiRange(BaseModel):
    min: float
    max: float


class CholesterolRange(BaseModel):
    min: Optional[float] = None
    max: Optional[float] = None


class BloodPressureLimit(BaseModel):
    systolic: int
    diastolic: int


class TcHdlRatio(BaseModel):
    min: Optional[float] = None
    max: Optional[float] = None


class RiskClassCriteria(BaseModel):
    nicotine: NicotineRequirement = Field(alias="Nicotine")
    body_mass_index: Dict[str, BmiRange] = Field(alias="Body Mass Index")
    cholesterol: Dict[str, CholesterolRange] = Field(alias="Cholesterol")
    tc_hdl_ratio: TcHdlRatio = Field(alias="TC/HDL Ratio")
    blood_pressure_maximum: Dict[str, BloodPressureLimit] = Field(
        alias="Blood Pressure Maximum")

    model_config = {
        "validate_by_name": True
    }


class ClassificationUtility:
    def __init__(self, informals_data: Dict[str, float]):
        self.informals_data = informals_data
        self.classification_json: Dict[str,
                                       Dict[str, dict]] = self._load_classifications()

    def _load_classifications(self):
        classification_file = Model_Data.CLASSIFICATIONS
        with open(classification_file) as f:
            return json.load(f)

    def get_age_group(self, age: int, category_name: str, criteria: str) -> str:
        if not age:
            return False
        for age_group in self.classification_json.get(category_name, {}).get(criteria, {}):
            min_age, max_age = self.parse_age_group(age_group)
            if min_age <= age <= max_age:
                return age_group
        return False

    def parse_age_group(self, age_group: str) -> tuple:
        if "+" in age_group:
            return int(age_group.split("+")[0]), float('inf')
        elif "-" in age_group:
            min_age, max_age = age_group.split("-")
            return int(min_age), int(max_age)
        return 0, 0

    def check_bmi(self, bmi: float, age: int, criteria: RiskClassCriteria, category_name: str) -> str:
        if not bmi:
            return False
        age_group = self.get_age_group(age, category_name, "Body Mass Index")
        if age_group:
            bmi_range = criteria.body_mass_index.get(age_group, None)
            if bmi_range and bmi_range.min <= bmi <= bmi_range.max:
                return True
        return False

    def check_cholesterol(self, cholesterol: float, age: int, criteria: RiskClassCriteria, category_name: str) -> str:
        if not cholesterol:
            return False
        age_group = self.get_age_group(age, category_name, "Cholesterol")
        if age_group:
            cholesterol_range = criteria.cholesterol.get(age_group, None)
            if cholesterol_range:
                if cholesterol_range.min <= cholesterol <= cholesterol_range.max:
                    return True
        return False

    def check_nicotine(self, last_nicotine_use_year: Optional[float], nicotine_threshold: Optional[int], criteria: RiskClassCriteria) -> Tuple[bool, Optional[int]]:
        if last_nicotine_use_year is None:
            return False, nicotine_threshold

        if nicotine_threshold:
            if criteria.nicotine.condition <= last_nicotine_use_year and criteria.nicotine.condition > nicotine_threshold:
                return True, criteria.nicotine.condition 
        elif criteria.nicotine.condition <= last_nicotine_use_year:
            return True, criteria.nicotine.condition

        return False, nicotine_threshold

    def check_tc_hdl_ratio(self, tc_hdl_ratio: float, criteria: RiskClassCriteria) -> str:
        if not criteria.tc_hdl_ratio.min and not criteria.tc_hdl_ratio.max:
            return False
        if not tc_hdl_ratio:
            return False
        if criteria.tc_hdl_ratio.min <= tc_hdl_ratio <= criteria.tc_hdl_ratio.max:
            return True
        return False

    def check_blood_pressure(self, systolic: int, diastolic: int, age: int, criteria: RiskClassCriteria, category_name: str) -> str:
        if not systolic and not diastolic:
            return False
        age_group = self.get_age_group(
            age, category_name, 'Blood Pressure Maximum')
        if age_group:
            blood_pressure_limits = criteria.blood_pressure_maximum.get(
                age_group, None)
            if blood_pressure_limits:
                limits = blood_pressure_limits
                if systolic <= limits.systolic and diastolic <= limits.diastolic:
                    return True
        return False

    def determine_classification(self, age: int, bmi: float, cholesterol: float, nicotine_condition: int, tc_hdl_ratio: float, systolic: int, diastolic: int) -> Dict[str, str]:
        classification = {}
        nicotine_threshold: Optional[int] = None
        for category_name, category_data in self.classification_json.items():
            try:
                criteria = RiskClassCriteria(**category_data)

                bmi_status = self.check_bmi(bmi, age, criteria, category_name)
                cholesterol_status = self.check_cholesterol(
                    cholesterol, age, criteria, category_name)
                nicotine_status, updated_threshold = self.check_nicotine(
                    nicotine_condition, nicotine_threshold, criteria)
                if updated_threshold is not None:
                    nicotine_threshold = updated_threshold
                tc_hdl_status = self.check_tc_hdl_ratio(tc_hdl_ratio, criteria)
                blood_pressure_status = self.check_blood_pressure(
                    systolic, diastolic, age, criteria, category_name)

                if bmi_status is True and 'BMI' not in classification:
                    classification["BMI"] = category_name
                if cholesterol_status is True and 'Cholesterol' not in classification:
                    classification["Cholesterol"] = category_name
                if nicotine_status is True:
                    classification["Nicotine"] = category_name
                if tc_hdl_status is True and 'TC/HDL Ratio' not in classification:
                    classification["TC/HDL Ratio"] = category_name
                if blood_pressure_status is True and 'Blood Pressure Maximum' not in classification:
                    classification["Blood Pressure Maximum"] = category_name

            except ValidationError as e:
                return {}

        return classification


if __name__ == "__main__":
    informals_data = {
        'Age': 38,
        'Nicotine': 100,
        'condition_date_type': 'year',
        'BMI': 22.04,
        'Cholesterol': 245,
        'TC/HDL Ratio': 2.42,
        'Blood Pressure Maximum': {"systolic": 132.7, "diastolic": 68.0},
    }
    utility = ClassificationUtility(informals_data)

    classification = utility.determine_classification(
        age=informals_data['Age'],
        bmi=informals_data['BMI'],
        cholesterol=informals_data['Cholesterol'],
        nicotine_condition=informals_data['Nicotine'],
        tc_hdl_ratio=informals_data['TC/HDL Ratio'],
        systolic=informals_data['Blood Pressure Maximum']['systolic'],
        diastolic=informals_data['Blood Pressure Maximum']['diastolic']
    )

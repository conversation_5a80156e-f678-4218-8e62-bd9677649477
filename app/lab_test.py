import uuid
from copy import deepcopy
from datetime import datetime, timedelta
from itertools import chain
from typing import Dict, List, Any

import constants as const
from medical_codes.coding import Laboratory, CodeOutput
from medical_codes.medical_coding_utility import MedicalCodingUtility
import table_dicts
import titles as Title
from blueprints import BLUEPRINTS, ENTITY_ROLE
from elements.edocument import EDocument, EPageLine, EPageData, Entity
from friendlylib.iterators import (
    find,
    get_first,
    lmap,
    lfilter,
    flatten,
)

class LabTest:
    def __init__(self):
        self.misc = object
        self.medical_coding_service: MedicalCodingUtility = object
        self.retrieved_data = None
        self.lab_date_ners = []
        self.page_classifiers = {}
        self.essential_items = []

    # ---------------------------------------------------------------------------------------------------------

    def set_dependencies(self, objMisc, medical_coding_service: MedicalCodingUtility, retrieved_data: dict):
        self.misc = objMisc
        self.medical_coding_service: MedicalCodingUtility = medical_coding_service
        self.retrieved_data = retrieved_data

    # ---------------------------------------------------------------------------------------------------------

    def build_lab_header_cells(self, sample):
        cells = []
        num_rows = 1
        num_cols = 0
        for item in sample:
            if not item["is_container"]:
                new_cell = deepcopy(table_dicts.empty_cell)
                new_cell["to_display"] = item["display_key"]
                new_cell["start_column"] = num_cols
                new_cell["entity_type"] = item["entity_type"]
                cells.append(new_cell)
                num_cols += 1

        return cells, num_rows, num_cols

    # ---------------------------------------------------------------------------------------------------------

    def lab_rows_to_table(self, list_of_rows, blueprint_name):
        row_blueprint = BLUEPRINTS[blueprint_name]["row"]
        mother = [e["entity_type"] for e in row_blueprint if e["role"] == ENTITY_ROLE.MOTHER]
        if len(mother):
            retain = mother[0]
            removeable = deepcopy(mother)
            removeable.remove(retain)
            row_blueprint = [x for x in row_blueprint if x['entity_type'] not in removeable]
        cells, num_header_rows, num_cols = self.build_lab_header_cells(row_blueprint)
        index = 0
        containers = None

        for index, observation in enumerate(list_of_rows):
            new_containers = list(filter(lambda x: x["is_container"], observation))
            if new_containers != containers:
                containers = new_containers
                col_num = 0
                for observation_cell in containers:

                    if col_num == 0:
                        num_header_rows += 1
                    if col_num + 2 > num_cols:
                        col_num = 0
                        num_header_rows += 1
                    cell = deepcopy(table_dicts.empty_cell)
                    cell["to_display"] = observation_cell["display_key"]
                    cell["type"] = "section_header"
                    cell["start_row"] = index + num_header_rows
                    cell["start_column"] = col_num
                    cells.append(cell)
                    col_num += 1
                    if observation_cell["entity_object"] is not None:
                        cell = deepcopy(table_dicts.empty_cell)
                        cell["to_display"] = "__auto__"
                        cell["type"] = "section_cell"
                        cell["start_row"] = index + num_header_rows
                        cell["start_column"] = col_num
                        cell["reference"] = observation_cell["entity_object"]
                        cells.append(cell)
                    col_num += 1
                num_header_rows += 1

            col_num = 0
            for observation_cell in observation:
                if observation_cell["entity_object"] is not None and not observation_cell["is_container"]:
                    cell = deepcopy(table_dicts.empty_cell)
                    cell["to_display"] = "__auto__"
                    cell["start_row"] = index + num_header_rows
                    cell["start_column"] = col_num
                    cell["reference"] = observation_cell["entity_object"]
                    cells.append(cell)
                col_num += 1
        table = deepcopy(table_dicts.empty_table)
        table["name"] = BLUEPRINTS[blueprint_name]["title"]
        table["row_count"] = index + num_header_rows + 1
        table["col_count"] = num_cols
        table["cells"] = cells
        return table

    # -----------------------------------------------------------------------------

    def _edoc_to_page_wise(self, edoc: EDocument, mother: list, children: list, priority_mapping: dict, card_id: str) -> dict:
        pages = {}
        document = {}
        for cell in self.misc.browse_edoc_pages(edoc):
            if not (self.misc.entity_with_valid_priority(cell, priority_mapping, card_id) or cell.get('source', '') in ['ccda', 'friendlyllm']):
                continue
            if (cell["type"] in mother) or (cell["type"] in children) or (cell["type"] in self.lab_date_ners):
                key = str(cell["doc_num"]) + const.hyphen + str(cell['abs_page'])
                pages.setdefault(key, {'mother': [], 'children': [], 'lab.dates': []})
                document.setdefault(cell['doc_num'], {'lab.dates': [], 'performer' : [], 'fasting' : [], 'panel': []})
                if cell['type'] in self.fasting_tags:
                    fasting_cell = self._format_codified_previous_pages(cell, True)
                    document[cell["doc_num"]]['fasting'].append(fasting_cell)
                    pages[key]['children'].append(fasting_cell)
                    continue
                if cell['type'] in ['lab.panel']:
                    substring_panel = self.medical_coding_service.get_panel_by_substring(cell['text'])
                    new_panel = self.generate_new_item(deepcopy(cell), 'lab.panel', substring_panel.title(), 100) if substring_panel else cell
                    document[cell["doc_num"]]['panel'].append(new_panel)
                    pages[key]['children'].append(new_panel)
                    continue
                if cell["type"] in mother:
                    pages[key]['mother'].append(cell)
                if cell["type"] in children:
                    pages[key]['children'].append(cell)
                if cell['type'] in self.lab_date_ners:
                    date_cell = self._format_codified_previous_pages(cell)
                    pages[key]['lab.dates'].append(date_cell)
                    document[cell["doc_num"]]['lab.dates'].append(date_cell)
                    continue
                if cell['type'] in ['lab.performer']:
                    document[cell["doc_num"]]['performer'].append(cell)
                
        return pages, document

    # -----------------------------------------------------------------------------------------------------------------------
    
    def _format_codified_previous_pages(self, cell: Entity, is_fasting = False):
        if Title.ENABLE_LAB_DATETIME_FORMAT not in self.retrieved_data.feature:
            return cell
        codified_datetime_obj = cell.get('codified_datetime_obj')
        if is_fasting and cell.get('codification_category','') == 'time':
            new_date_cell = deepcopy(cell)
            new_date_cell['id'] = str(uuid.uuid4())
            new_date_cell['type'] = const.GENERATED_ENTITY + new_date_cell['type']
            return new_date_cell
        if not codified_datetime_obj:
            return cell
        new_date_cell = deepcopy(cell)
        new_date_cell['id'] = str(uuid.uuid4())
        new_date_cell['type'] = const.GENERATED_ENTITY + new_date_cell['type']
        new_date_cell['text'] = codified_datetime_obj
        new_date_cell['codified_as'] = ""
        new_date_cell['codification_category'] = ""
        new_date_cell['codify'] = None
        return new_date_cell
                
    # -----------------------------------------------------------------------------------------------------------------------

    def _prepare_empty_table(self, row_blueprint: list):
        row = {}
        ner_keys = {}
        for cell in row_blueprint:
            display_key = cell['display_key']
            row[display_key] = []
            for k in cell['ner.keys']:
                ner_keys[k] = display_key
        empty_blueprint = deepcopy(row_blueprint)
        return row, ner_keys, empty_blueprint

    # -----------------------------------------------------------------------------------------------------------------------

    def _populate_with_mother_ners(self, page_data: dict, empty_row: dict) -> dict:
        sub_table = []
        unique_o_names = {}
        o_name_points = {}
        for x in page_data['mother']:
            id = x['id']
            point = self.misc.get_point(x)
            if id not in unique_o_names:
                o_name_points[id] = []
            o_name_points[id].append(point)

            if id in unique_o_names:
                continue
            unique_o_names[id] = ''
            row = deepcopy(empty_row)
            row['Name'] = x
            sub_table.append(row)

        if len(unique_o_names) == 0:
            row = deepcopy(empty_row)
            sub_table.append(row)

        row['o_name.points'] = o_name_points
        row['lab.dates'] = page_data['lab.dates']
        return sub_table

    # -----------------------------------------------------------------------------------------------------------------------

    def _convert_dict_to_rows(self, sub_table: list, value_dates: dict):
        rows = []
        for obj_sub_table in sub_table:
            sub_row = []
            value_list = obj_sub_table["Value"]
            rating_list = obj_sub_table["Rating"]
            first_row = {}
            for display_key, list_of_objects in obj_sub_table.items():
                if display_key == 'Name':
                    first_row[display_key] = list_of_objects
                else:
                    if len(list_of_objects):
                        first_row[display_key] = list_of_objects[0]

            if len(value_list):
                id = value_list[0]['id']
                if id in value_dates:
                    first_row['Date'] = value_dates[id]

            if len(rating_list) and first_row.get('Date') is None:
                id = rating_list[0]['id']
                if id in value_dates:
                    first_row['Date'] = value_dates[id]
            sub_row.append(first_row)
            for i, value_obj in enumerate(value_list):
                if i == 0:
                    continue
                row = deepcopy(first_row)
                row['Value'] = value_obj
                id = value_obj['id']
                if id in value_dates:
                    row['Date'] = value_dates[id]
                sub_row.append(row)
            self._assign_children_references(obj_sub_table, value_list, sub_row)
            if len(value_list) == 0 and len(rating_list) > 1:
                self._assign_children_references(obj_sub_table, rating_list, sub_row)
                
            rows.append(sub_row)
                        
        return flatten(rows)

    # -----------------------------------------------------------------------------------------------------------------------

    def filter_by_date(self, rows: List[Dict[str, Entity]]) -> List[Dict[str, Entity]]:
        unique_entries = {}
        
        for entry in rows:
            if not entry.get('Name', {}):
                continue
            name_text = entry.get('Name', {}).get('text', '').strip().lower()
            date_text = entry.get('Date', {}).get('text', '')
            lab_name = self.misc.get_point(entry.get('Name', {}))
            lab_value = entry.get('Value', {})
            lab_rating = entry.get('Rating', {})

            if not lab_value and not lab_rating:
                continue
            
            key = (name_text, date_text)

            if key not in unique_entries:
                unique_entries[key] = entry
            else:
                unique_lab_value = unique_entries[key].get('Value', {})
                unique_lab_rating = unique_entries[key].get('Rating', {})
                unique_input_position = self.misc.get_point(unique_lab_value) if unique_lab_value else self.misc.get_point(unique_lab_rating)
                current_input_position = self.misc.get_point(lab_value) if lab_value else self.misc.get_point(lab_rating)
                
                if self.misc.is_same_line(unique_input_position, lab_name):
                    if self.misc.is_same_line(current_input_position, lab_name):
                        if self.misc.rect_distance(lab_name, current_input_position) < self.misc.rect_distance(lab_name, unique_input_position):
                            unique_entries[key] = entry
                    continue
                elif self.misc.is_same_line(current_input_position, lab_name):
                    unique_entries[key] = entry
                    continue
                elif self.misc.is_above_line(unique_input_position, lab_name):
                    if self.misc.is_above_line(lab_name, current_input_position):
                        if self.misc.rect_distance(lab_name, current_input_position) < self.misc.rect_distance(lab_name, unique_input_position):
                            unique_entries[key] = entry
                    continue
                elif self.misc.is_above_line(current_input_position, lab_name):
                    unique_entries[key] = entry
                    continue
                else:
                    if self.misc.rect_distance(lab_name, current_input_position) < self.misc.rect_distance(lab_name, unique_input_position):
                        unique_entries[key] = entry
                        
        return list(unique_entries.values())
    
    # -----------------------------------------------------------------------------------------------------------------------
    
    def _assign_children_references(self, obj_sub_table: dict, value_list: list, sub_row: list) -> None:
        multiple_value_cols = [key for key, value in obj_sub_table.items() if len(value) > 1 and key not in ['Name', 'Rating', 'Value']]
        if len(value_list) > 1:
            for col_name in multiple_value_cols:
                for child_cell in obj_sub_table[col_name]:
                    children_closest_value = min(
                            value_list,
                            key=lambda cell: self.misc.rect_distance(self.misc.get_point(child_cell), self.misc.get_point(cell))
                        )
                    matched_row = get_first(lfilter(lambda x: x.get('Value', {}) == children_closest_value, sub_row), None)
                    if matched_row: 
                        matched_row[col_name] = child_cell
        
    # -----------------------------------------------------------------------------------------------------------------------

    def _get_entity_text(self, entity):
        if entity is None:
            return ""
        return entity.get("text", "")

    # -----------------------------------------------------------------------------------------------------------------------

    def generate_new_item(self, cell: Entity, type: str, text: str, loinc_score: int) -> Entity:
        cell.update({
            "type": type,
            "id": str(uuid.uuid4()),
            "text": text,
            "confidence": loinc_score,
            "confidence_threshold": 40
            })
        cell.setdefault("metadata", {}).update({"card_generated": True})

        return cell

    # ------------------------------------------------------
    
    def _fill_loinc_codes(self, laboratory_table):
        for laboratory_row in laboratory_table:
            laboratory_entry: Laboratory = Laboratory()
            laboratory_name_row = find(lambda cell: cell["key"] == "name", laboratory_row)["entity_object"]
            specimen_row = find(lambda cell: cell["key"] == "specimen", laboratory_row)
            laboratory_entry.name = self._get_entity_text(laboratory_name_row)
            laboratory_entry.unit = self._get_entity_text(find(lambda cell: cell["key"] == "unit", laboratory_row)["entity_object"])
            laboratory_entry.panel = self._get_entity_text(find(lambda cell: cell["key"] == "panel", laboratory_row)["entity_object"])
            laboratory_entry.specimen = self._get_entity_text(find(lambda cell: cell["key"] == "specimen", laboratory_row)["entity_object"])
            lab_result: CodeOutput = self.medical_coding_service._get_lab_loinc(laboratory_entry, "loinc")
            # lab_panel_loinc: CodeOutput = self.medical_coding_service._get_lab_panel_loinc(laboratory_entry.panel)
            if lab_result.medical_code:
                if laboratory_entry.specimen == "":
                    loinc_specimen: str = self.medical_coding_service.get_specimen_by_code(lab_result)
                    if loinc_specimen:
                        specimen_row["entity_object"] = self.generate_new_item(deepcopy(laboratory_name_row), "specimen", loinc_specimen, lab_result.score)
                        specimen_row["entity_object"]["confidence"] = lab_result.score
                loinc_row = find(lambda cell: cell["key"] == "loinc", laboratory_row)
                loinc_row["entity_object"] = self.generate_new_item(deepcopy(laboratory_name_row), "loinc", lab_result.medical_code, lab_result.score)
                loinc_row["entity_object"]['metadata'].update({"info": f"{lab_result.description}","type": "medical_code"})
            # if lab_panel_loinc.medical_code != "":
            #     panel_loinc_row = find(lambda cell: cell["key"] == "panel_loinc", laboratory_row)
            #     panel_loinc_row["entity_object"] = self.generate_new_item(deepcopy(laboratory_name_row), "panel_loinc", lab_panel_loinc.medical_code, lab_panel_loinc.score)
        return laboratory_table

    # -----------------------------------------------------------------------------------------------------------------------

    def _is_new_row_matches(self, new_rows, new_row, old_row, empty_blueprint):
        for i, bp in enumerate(empty_blueprint):
            display_key = bp['display_key']
            if display_key in old_row:
                cell = old_row[display_key]
                if len(cell):
                    new_row[i]['entity_object'] = deepcopy(cell)
                    new_row[i]['entity_type'] = new_row[i]['entity_object']['type']
                    if display_key == 'Name':
                        new_rows.append(new_row)

        return new_rows

    # -----------------------------------------------------------------------------------------------------------------------

    def _fill_missing_dummy_cell(self, rows, empty_blueprint: list):
        for x in empty_blueprint:
            x.pop('ner.keys', None)
        new_rows = []
        for old_row in rows:
            new_row = deepcopy(empty_blueprint)
            new_rows = self._is_new_row_matches(new_rows, new_row, old_row, empty_blueprint)
        return new_rows

    # -----------------------------------------------------------------------------------------------------------------------

    def _remove_duplicate_rows(self, selected_rows):
        list_of_rows = []
        unique = {}
        for row in selected_rows:
            row_text = const.EMPTY_STRING
            for cell in row:
                entity_object = cell.get('entity_object')
                if entity_object is not None:
                    text = entity_object['text'].strip().lower()
                    row_text += text
            if row_text in unique:
                continue
            unique[row_text] = const.EMPTY_STRING
            list_of_rows.append(row)
        return list_of_rows

    # -----------------------------------------------------------------------------------------------------------------------

    def _allot_child_to_mother(self, sub_table: list, ner_keys: dict, o_name_id: str, child: dict):
        display_key = ner_keys[child['type']]
        for obj in sub_table:
            mother_obj = obj['Name']
            if mother_obj['id'] == o_name_id:
                obj[display_key].append(child)
                return sub_table
        return sub_table

    # -----------------------------------------------------------------------------------------------------------------------

    def _get_common_front_face(self, left_obj, right_obj):
        top = max(left_obj[1], right_obj[1])
        bot = min(left_obj[3], right_obj[3])
        return bot - top

    # -----------------------------------------------------------------------------------------------------------------------

    def _is_other_o_name_between(self, all_o_points, left_obj_point, right_obj_point):
        for o_name_id, all_middle_points in all_o_points.items():
            for middle_point in all_middle_points:
                if (
                    self.misc.is_same_line(left_obj_point, middle_point)
                    and self.misc.is_same_line(middle_point, right_obj_point)
                    and not self.misc.is_same_column(left_obj_point, middle_point)
                ):
                    is_between_horizontal = (left_obj_point[2] < middle_point[2]) and (middle_point[2] < right_obj_point[2])
                    if is_between_horizontal:
                        return True
        return False
    
    # -----------------------------------------------------------------------------------------------------------------------

    def _is_other_o_name_above_value(self, all_name_points, bottom_obj_point, value_obj_point):
        for o_name_id, all_middle_points in all_name_points.items():
            for middle_point in all_middle_points:
                is_between = (middle_point[1] > value_obj_point[1]) and (bottom_obj_point[1] > middle_point[1])
                if is_between:
                    return True
        return False

    # -----------------------------------------------------------------------------------------------------------------------

    def _is_other_reference_above_value(self, all_name_points, bottom_obj_point, value_obj_point):
        for o_name_id, all_middle_points in all_name_points.items():
            for middle_point in all_middle_points:
                is_between = (middle_point[1] > value_obj_point[1]) and (bottom_obj_point[1] > middle_point[1])
                if is_between and not self.misc.is_same_line(middle_point,value_obj_point):
                    return True
        return False

    # -----------------------------------------------------------------------------------------------------------------------

    def _count_reference_in_between(self, all_name_points, bottom_obj_point, value_obj_point):
        counter = 0
        for o_name_id, all_middle_points in all_name_points.items():
            for middle_point in all_middle_points:
                is_between = (middle_point[1] > value_obj_point[1]) and (bottom_obj_point[1] > middle_point[1])
                if is_between and not self.misc.is_same_line(middle_point,value_obj_point):
                    counter +=1
        return counter
    
    # -----------------------------------------------------------------------------------------------------------------------

    def _is_other_o_name_below_value(self, all_name_points, bottom_obj_point, value_obj_point):
        for o_name_id, all_middle_points in all_name_points.items():
            for middle_point in all_middle_points:
                if (middle_point[1] > bottom_obj_point[1]) and self.misc.is_above_line(middle_point, value_obj_point):
                    return True
        return False

    # -----------------------------------------------------------------------------------------------------------------------

    def _is_same_line(self, left_obj, right_obj) -> bool:
        face_to_face = (left_obj[1] <= right_obj[1]) and (left_obj[3] >= right_obj[3])
        if face_to_face:
            return True
        top_margin = 20
        horizontal_match = abs(left_obj[1] - right_obj[1]) <= top_margin
        horizontal_match_by_left = (left_obj[1] <= right_obj[3]) and (left_obj[1] >= right_obj[1])
        horizontal_match_by_right = (left_obj[3] >= right_obj[1]) and (left_obj[3] <= right_obj[3])
        same_line_flag = horizontal_match or horizontal_match_by_left or horizontal_match_by_right
        return same_line_flag

    # -----------------------------------------------------------------------------------------------------------------------

    def _is_child_on_position(self, o_name_points, o_point, child_point, child_type):
        if self.misc.is_same_column(child_point, o_point) and not self._is_other_o_name_below_value(o_name_points, o_point, child_point):
            same_line_cells = lfilter(
                lambda o_name_cell: self._is_same_line(child_point, o_name_cell[0]),
                o_name_points.values()
            )
            if same_line_cells or not (child_point[1] - o_point[1] > 0):
                return False
            return True
        elif 'value' in child_type and not self._is_other_o_name_above_value(o_name_points, o_point, child_point) and (child_point[0] >= o_point[0]):
            if self.misc.is_above_line(child_point, o_point) and (o_point[1] - child_point[1] < 100):
                return True
            elif self.misc.is_below_line(child_point, o_point) and (child_point[1] - o_point[1] < 100):
                same_line_cells = lfilter(
                    lambda o_name_cell: self._is_same_line(child_point, o_name_cell[0]),
                    o_name_points.values()
                )
                if len(same_line_cells) >= 1:
                    if len(same_line_cells) == 1 and same_line_cells[0][0] == o_point:
                        return True
                    return False 
                return True
        elif child_type in ['o.ref','o.ref_low', 'o.ref_high', 'o.unit',
                            'lab.ref','lab.ref_low', 'lab.ref_high', 'lab.unit',]:
            if self.misc.is_above_line(o_point, child_point) and not self._is_other_o_name_below_value(o_name_points, o_point, child_point):
                return True
        return False
    # -----------------------------------------------------------------------------------------------------------------------

    def _populate_with_children_lab_test_data(self, sub_table: List, ner_keys: Dict[str, str], row_ners: List[str], 
                                              mother_entries: Dict[str, EPageLine], children_entries: Dict[str, EPageLine]) -> list:
        o_name_points = sub_table[-1]['o_name.points']
        lab_references: Dict[str, Any] = {}
        all_candidates = children_entries + mother_entries
        for child in children_entries:
            max_dist = 0
            current_o_name = []
            name_different_line_candidates = []
            if child['type'] not in row_ners:
                continue
            if child['type'] in ['o.ref_low', 'o.ref_high', 'o.ref', 'lab.ref_low', 'lab.ref_high', 'lab.ref']:
                lab_references[child['id']] = [self.misc.get_point(child)]
            if self.page_classifiers[child['claimid']][child['abs_page']] not in {'lab-report', 'crl-page'}:
                paragraph_format_candidate = self._get_paragraph_candidate(child, mother_entries, all_candidates)
                if paragraph_format_candidate:
                    sub_table = self._allot_child_to_mother(sub_table, ner_keys, paragraph_format_candidate.get('id',''), child)    
                    continue
            child_point = self.misc.get_point(child)
            for o_name_id, o_points in o_name_points.items():
                for o_point in o_points:
                    if 'value' in child['type'] and len(lab_references) != 0:
                        if self._is_other_reference_above_value(lab_references, child_point, o_point):
                            continue
                    if not self.misc.is_same_line_right_side(child_point, o_point):
                        if self._is_child_on_position(o_name_points, o_point, child_point, child['type']):
                            name_different_line_candidates.append(o_name_id)
                            continue
                        continue
                    if self._is_other_o_name_between(o_name_points, o_point, child_point):
                        continue
                    if self._is_child_on_position(o_name_points, o_point, child_point, child['type']):
                        name_different_line_candidates.append(o_name_id)
                        continue
                    face = self._get_common_front_face(o_point, child_point)
                    if face >= max_dist:
                        max_dist = face
                        current_o_name = o_name_id
            if len(current_o_name):
                sub_table = self._allot_child_to_mother(sub_table, ner_keys, current_o_name, child)
            elif len(name_different_line_candidates):
                best_candidate = min(lfilter(lambda x: x['id'] in name_different_line_candidates, mother_entries), 
                    key=lambda lab_candidate: lab_candidate['top']
                )['id']
                sub_table = self._allot_child_to_mother(sub_table, ner_keys, best_candidate, child)             
            if child.get("source", "") in ["ccda", "friendlyllm"]:
                child_entry_id = child.get("entry_id")
                same_mother_entries = lfilter(lambda cell: cell['entry_id'] == child_entry_id, mother_entries)
                for mother_entry in same_mother_entries:
                    sub_table = self._allot_child_to_mother(sub_table, ner_keys, mother_entry['id'], child)

        return sub_table

    # -----------------------------------------------------------------------------------------------------------------------
    
    def _get_paragraph_candidate(self, lab_details: Dict[str, Entity], name_objects: EPageData, all_candidates: EPageData) -> None:
        mother_point = self.misc.get_point(lab_details)
        if len(name_objects) == 0:
            return {}
        #Right side
        row_objects_in_required_position = lfilter(
            lambda child_object: self.misc.is_required_position(const.SAME_LINE_RIGHT_SIDE, mother_point,
                                                                self.misc.get_point(child_object)),
            name_objects
        )
        if row_objects_in_required_position:
            right_side_all_labs = lfilter(
                lambda child_object: self.misc.is_required_position(const.SAME_LINE_RIGHT_SIDE, self.misc.get_point(child_object),
                                                                    mother_point),
                all_candidates
            )
            last_right_side_candidate = max(
                right_side_all_labs, 
                key=lambda child_object: child_object.get('right', 0),
                default={}
            )
            check_below_line_first_entry = min(
                lfilter(
                    lambda candidate: (candidate['line_num'] == lab_details['line_num'] + 1), 
                    all_candidates
                    ),
                key=lambda child_object: child_object.get('left', 0),
                default={}
            )
            if 'name' in check_below_line_first_entry.get('type', '') and len(right_side_all_labs) == 1 and 'name' in last_right_side_candidate.get('type', ''):
                return last_right_side_candidate
            nearest_child_obj = min(
                row_objects_in_required_position,
                key=lambda child_object: self.misc.rect_distance(self.misc.get_point(child_object), mother_point)
            )
            if nearest_child_obj:
                return nearest_child_obj
        #Next line value
        next_line_entities = lfilter(lambda x: (x['line_num'] == lab_details['line_num'] - 1 ), name_objects)
        if next_line_entities:
            nearest_child_obj = max(
                next_line_entities,
                key=lambda child_object: child_object['left']
            )
            if nearest_child_obj:
                return nearest_child_obj
        return {}
        

    def _populate_with_children(self, sub_table: list, ner_keys: dict, mother_entries: list, children_entries: Dict[str, EPageLine]):
        row_ners = ['lab.value', 'lab.rating', 'lab.ref_low', 'lab.ref_high', 'lab.ref', 'lab.unit',
                    'o.value', 'o.rating', 'o.ref_low', 'o.ref_high', 'o.ref', 'o.unit']
        sub_table = self._populate_with_children_lab_test_data(sub_table, ner_keys, row_ners, mother_entries, children_entries)
        sub_table = lmap(
            lambda lab_row: self._correct_rating_references(lab_row),
            sub_table
        )
        row_ners = ['lab.panel', 'lab.performer',
                    'fasting.status.yes', 'fasting.in_hours', 'fasting.in_mins', 'fasting.date_time',
                    const.GENERATED_ENTITY + 'fasting.in_hours', const.GENERATED_ENTITY + 'fasting.in_mins', 
                    const.GENERATED_ENTITY + 'fasting.date_time', 'lab.specimen']
        sub_table = self._populate_with_children_lab_info(sub_table, ner_keys, row_ners, children_entries, mother_entries)
        value_dates = self._populate_with_children_dates(sub_table, ner_keys, children_entries, mother_entries)
        sub_table[-1].pop('o_name.points', None)
        return sub_table, value_dates

    # -----------------------------------------------------------------------------------------------------------------------
    
    def _correct_rating_references(self, lab_row):
        if not lab_row.get('Name'):
            return lab_row
        ref_low_cell = get_first(lab_row['Low'], {})
        ref_high_cell = get_first(lab_row['High'], {})
        value_cell = get_first(lab_row['Value'], {})
        
        ref_low = ref_low_cell.get('text', None)
        ref_high = ref_high_cell.get('text', None)
        lab_value = value_cell.get('text', None)
        if lab_value:
            lab_value = lab_value.replace("<", "").replace(">", "")

        if lab_row['Rating'] and not ref_high and not ref_low:
            return lab_row

        if lab_row['Rating']:
            if self.misc.is_number(ref_low) and self.misc.is_number(ref_high) and self.misc.is_number(lab_value):
                if float(ref_low) <= float(lab_value) <= float(ref_high):
                    lab_row['Rating'] = []
            elif self.misc.is_number(ref_low) and self.misc.is_number(lab_value) and ref_high is None:
                lab_row['Rating'][0]['text'] = 'Low'
                if float(ref_low) <= float(lab_value):
                    lab_row['Rating'] = []
            elif self.misc.is_number(ref_high) and self.misc.is_number(lab_value) and ref_low is None:
                lab_row['Rating'][0]['text'] = 'High'
                if float(ref_high) >= float(lab_value):
                    lab_row['Rating'] = []
        
        if lab_row["Low"] and lab_row["High"] and self.misc.is_number(ref_low) and self.misc.is_number(ref_high):
            if float(ref_high) < float(ref_low):
                lab_row["High"] = []
                    
        return lab_row

    # -----------------------------------------------------------------------------------------------------------------------

    def _populate_with_children_lab_info(
        self,
        sub_table: List[Dict[str, Entity]],
        ner_keys: Dict[str, str],
        row_ners: List[str],
        children_entries: EPageLine,
        mother_entries: EPageLine,
    ) -> list:
        o_name_points = sub_table[-1]['o_name.points']
        panel_references = {}
        same_line_specimens = []
        specimen_data = lfilter(lambda x: x['type'] == 'lab.specimen', children_entries)
        specimen_references = {entry['id']: [self.misc.get_point(entry)] for entry in specimen_data}
        for row_ner in row_ners:
            for o_name_id, o_name_point in o_name_points.items():
                for mother_point in o_name_point:
                    min_dist = const.MAX_NUM
                    current_o_name = []
                    selected_child = {}
                    for child in children_entries:
                        if child['type'] != row_ner:
                            continue
                        child_point = self.misc.get_point(child)
                        if child.get("source", "") in ["ccda", "friendlyllm"]:
                            child_entry_id = child.get("entry_id")
                            same_mother_entries = lfilter(lambda cell: cell['entry_id'] == child_entry_id, mother_entries)
                            for mother_entry in same_mother_entries:
                                sub_table = self._allot_child_to_mother(sub_table, ner_keys, mother_entry['id'], child)
                            continue
                        if child['type'] in ['lab.panel']:
                            panel_references[child['id']] = [self.misc.get_point(child)]
                        if child['type'] == 'lab.specimen':
                            if child['id'] in same_line_specimens:
                                continue
                            if self.misc.is_same_line(mother_point, child_point):
                                sub_table = self._allot_child_to_mother(sub_table, ner_keys, o_name_id, child)
                                same_line_specimens.append(child['id'])
                                continue
                            if self.misc.is_above_line(mother_point, child_point):
                                continue
                            check_same_line = self._get_closest_same_line(o_name_id, specimen_references, mother_point)
                            if check_same_line:
                                if check_same_line == child['id']:
                                    sub_table = self._allot_child_to_mother(sub_table, ner_keys, o_name_id, child)
                                    same_line_specimens.append(child['id'])
                                    continue
                                continue
                            specimen_inbetween = self._count_reference_in_between(specimen_references, mother_point, child_point)
                            if specimen_inbetween >= 1:
                                continue
                            panel_inbetween = self._count_reference_in_between(panel_references, mother_point, child_point)
                            if panel_inbetween <= 1:
                                sub_table = self._allot_child_to_mother(sub_table, ner_keys, o_name_id, child)
                            continue
                        if not self.misc.is_above_line(child_point, mother_point) and not self.misc.is_same_line(child_point, mother_point):
                            continue
                        cur_dist = self.misc.rect_distance(child_point, mother_point)
                        if cur_dist < min_dist:
                            min_dist = cur_dist
                            current_o_name = o_name_id
                            selected_child = child
                    if len(selected_child):
                        sub_table = self._allot_child_to_mother(sub_table, ner_keys, current_o_name, selected_child)
            
        return sub_table

    # -----------------------------------------------------------------------------------------------------------------------
    
    def _get_closest_same_line(self, o_name_id, o_name_points, child_point):
        same_line_data = {
            key: value
            for key, value in o_name_points.items()
            if self._is_same_line(child_point, value[0])
        }
        if same_line_data:
            closest_cell = min(
                same_line_data, 
                key=lambda o_id: self.misc.rect_distance(same_line_data[o_id][0], child_point)
            )
            return closest_cell
        return ""
            
    # -----------------------------------------------------------------------------------------------------------------------

    def _is_lab_date_upon_correct_position(self, value_point: list, date_point: list) -> bool:
        flag_position = False
        h_gap = value_point[1] - date_point[1]
        if h_gap > -2:
            return True
        top_margin = 20
        v_disp = abs(value_point[1] - date_point[1])
        if v_disp <= top_margin:
            return True
        return flag_position

    # -----------------------------------------------------------------------------------------------------------------------

    def _get_same_line_distance(self, first_point, second_point):
        first_on_left = abs(first_point[2] - second_point[0])
        second_on_left = abs(second_point[2] - first_point[0])
        return min(first_on_left, second_on_left)

    # -----------------------------------------------------------------------------------------------------------------------

    def _prepare_blue_print_data(self, card_name: str):
        row_blueprint = BLUEPRINTS[card_name]["row"]
        empty_row, ner_keys, empty_blueprint = self._prepare_empty_table(row_blueprint)
        mother = flatten([e["ner.keys"] for e in row_blueprint if e["role"] == ENTITY_ROLE.MOTHER]) 
        children = flatten([e["ner.keys"] for e in row_blueprint if
                    e["role"] in (ENTITY_ROLE.CHILD, ENTITY_ROLE.REQUIRED_CHILD)])
        self.lab_date_ners = [x['ner.keys'] for x in BLUEPRINTS[Title.LABORATORY_RESULTS]['row'] if x['key'] == 'date']
        self.lab_date_ners = list(chain(*self.lab_date_ners))
        self.date_order = BLUEPRINTS[card_name]["date-order"]
        self.fasting_tags = flatten([x['ner.keys'] for x in BLUEPRINTS[Title.LABORATORY_RESULTS]['row'] if x['key'] == "fasting"])
        return mother, children, empty_blueprint, empty_row, ner_keys

    # -----------------------------------------------------------------------------------------------------------------------

    def _populate_with_children_dates(
        self,
        sub_table: List[Dict[str, Entity]],
        ner_keys: Dict[str, str],
        children: EPageLine,
        mother_entries: EPageLine,
    ):
        lab_dates = lfilter(lambda cell: 'encounter.date' not in cell['type'], sub_table[-1]['lab.dates'])
        if len(lab_dates) == 0:
            return {}
        value_dates = {}
        for child in children:
            if child['type'] not in ['lab.value', 'o.value', 'o.rating', 'lab.rating']:
                continue
            o_value_point = self.misc.get_point(child)
            if child.get("source", "") in ["ccda", "friendlyllm"]:
                child_entry_id = child.get("entry_id")
                same_mother_entries = lfilter(lambda cell: cell['entry_id'] == child_entry_id, mother_entries)
                for mother_entry in same_mother_entries:
                    sub_table = self._allot_child_to_mother(sub_table, ner_keys, mother_entry['id'], child)
                continue
            candidate_in_required_position = list(filter(
                lambda lab_date: self._is_lab_date_upon_correct_position(o_value_point, self.misc.get_point(lab_date)),
                lab_dates
            ))
            if len(candidate_in_required_position) == 0:
                continue
            same_line_date_cells = list(filter(
                lambda lab_date: self._is_same_line(o_value_point, self.misc.get_point(lab_date)),
                candidate_in_required_position
            ))
            if len(same_line_date_cells):
                same_line_closest_date_cell = min(
                    same_line_date_cells,
                    key=lambda cell: self._get_same_line_distance(o_value_point, self.misc.get_point(cell))
                )
                value_dates[child['id']] = same_line_closest_date_cell
                continue

            closest_date = None
            for date_type in self.lab_date_ners:
                date_type_candidates = list(filter(
                    lambda cell: cell['type'] == date_type, candidate_in_required_position
                ))
                if len(date_type_candidates):
                    different_line_closest_date_cell = min(
                        date_type_candidates,
                        key=lambda cell: self.misc.rect_distance(o_value_point,
                                                                                 self.misc.get_point(cell))
                    )
                    if closest_date is None:
                        closest_date = different_line_closest_date_cell
                    if self.misc.is_same_column(self.misc.get_point(different_line_closest_date_cell), o_value_point):
                        value_dates[child['id']] = different_line_closest_date_cell
                        break
            if closest_date is not None and value_dates.get(child['id']) is None:
                value_dates[child['id']] = closest_date
                
        return value_dates

    # -----------------------------------------------------------------------------------------------------------------------

    def _fill_previous_page_data(self, lab_rows: list, column_children_candidates: list, column_name: str, tag_order: list, is_date = True) -> None:
        if len(column_children_candidates) == 0:
            return
        
        for row in lab_rows:
            if len(row.get(column_name, {})) != 0:
                continue
            if is_date is True:
                if row.get('Value', None) is None and row.get('Rating', None) is None:
                    continue
            for tag in tag_order:
                filtered_children_candidates = lfilter(
                    lambda child: child['type'] == tag,
                    column_children_candidates)
                if len(filtered_children_candidates) != 0:
                    bottom_child = max(
                        filtered_children_candidates,
                        key=lambda child: (child['abs_page'], child['bottom']))
                    row[column_name] = bottom_child
                    break
                
        return

    # -----------------------------------------------------------------------------------------------------------------------
    
    def _fill_previous_page_date(self, lab_rows: list, column_children_candidates: list, column_name: str, tag_order: list, same_page_included = False) -> None:
        if len(column_children_candidates) == 0:
            return
        for row in lab_rows:
            if (row.get('Value') is None and row.get('Rating') is None or \
                (row.get('Date') is not None)) and \
                ('collected' in row.get(column_name, {}).get('type', '')):
                continue
            for tag in tag_order:
                filtered_children_candidates = lfilter(
                    lambda child: child['type'] == tag,
                    column_children_candidates)
                if len(filtered_children_candidates) != 0:
                    if same_page_included is True:
                        children_closest_value = min(
                            filtered_children_candidates,
                            key=lambda cell: self.misc.rect_distance(self.misc.get_point(row.get('Name')), self.misc.get_point(cell))
                        )
                        if children_closest_value:
                            row[column_name] = children_closest_value
                            break
                    best_candidate = max(
                        filtered_children_candidates,
                        key=lambda child: (child['abs_page'], child['bottom']))
                    if len(row.get(column_name, {})) != 0 and 'collected' in tag:
                        row[column_name] = best_candidate
                        break
                    elif len(row.get(column_name, {})) == 0 and best_candidate:
                        row[column_name] = best_candidate
                        break

    # -----------------------------------------------------------------------------------------------------------------------

    def _get_prev_candidates_by_page_class(self, candidates: list, file_id: str, page_num: int, include_same_page = False, page_limit = 10) -> list:
        if len(candidates) == 0:
            return []
        last_page_by_page_class = page_num
        current_page_class = self.page_classifiers[file_id][last_page_by_page_class]
        while (
            last_page_by_page_class in self.page_classifiers[file_id]
            and self.page_classifiers[file_id][last_page_by_page_class]
            == current_page_class
            and (page_num - last_page_by_page_class) <= page_limit
        ):
            last_page_by_page_class -= 1

        if include_same_page:
            filtered_children_candidates = lfilter(
                lambda child: child['abs_page'] > last_page_by_page_class and child['abs_page'] <= page_num,
                candidates)
            return filtered_children_candidates
        
        filtered_children_candidates = lfilter(
            lambda child: child['abs_page'] > last_page_by_page_class and child['abs_page'] < page_num,
            candidates)

        return filtered_children_candidates        

    # -----------------------------------------------------------------------------------------------------------------------
    
    def is_datetime_convertible(self, date_time_str):
        try:
            dt = datetime.fromisoformat(date_time_str)
            return True, dt
        except ValueError:
            return False, None
        
    # -----------------------------------------------------------------------------------------------------------------------
    
    def _format_fasting_data(self, rows: EPageLine, formatted_fasting_ids: List[str]) -> None:
        for lab_row in rows:
            fasting_cell = lab_row.get('fasting', None)
            date_cell = lab_row.get('Date', None)
            if not fasting_cell or not date_cell:
                continue
            if fasting_cell.get('id') in formatted_fasting_ids:
                continue
            date_value = date_cell.get('text', '')
            is_valid, formatted_datetime = self.is_datetime_convertible(date_value)
            new_dt = {}
            if is_valid:
                fasting_codification_dict = fasting_cell.get('codify')
                if fasting_codification_dict:
                    codified_value = fasting_codification_dict.get("codified_as")
                    codified_unit = fasting_codification_dict.get("unit")
                    if codified_unit == 'hours' or codified_unit == 'minutes':
                        codified_value = float(codified_value)
                        new_dt = formatted_datetime - timedelta(hours=codified_value)
                    elif fasting_cell.get('codification_category', '') == 'date':
                        is_fasting_valid, fasting_dt = self.is_datetime_convertible(fasting_cell.get('text',''))
                        if is_fasting_valid:
                            new_dt = formatted_datetime - (formatted_datetime - fasting_dt)
            if new_dt:
                new_dt_str = new_dt.isoformat()
                fasting_cell['text'] = new_dt_str
                fasting_cell['codification_category'] = ''
                fasting_cell['type'] = const.GENERATED_ENTITY + 'fasting.date_time'
                fasting_cell['codified_as'] = ""
                fasting_cell['codify'] = None
                fasting_cell['codified_datetime_obj'] = new_dt_str

                formatted_fasting_ids.append(fasting_cell['id'])
        return

    # -----------------------------------------------------------------------------------------------------------------------
    
    def _fill_previous_lab_data(self, rows, document_tags, current_document_num, current_page_num, page_data):
        if current_document_num not in document_tags or not page_data.get("mother"):
            return

        file_id = get_first(page_data["mother"], {}).get("claimid", "")

        def _get_candidates(field, include_same_page=False, page_limit=10):
            return self._get_prev_candidates_by_page_class(
                document_tags[current_document_num].get(field, []), file_id, current_page_num, include_same_page, page_limit
            )

        default_dates = lfilter(lambda cell: 'encounter.date' not in cell['type'], document_tags[current_document_num].get("lab.dates", []))
        lab_dates_candidates = self._get_prev_candidates_by_page_class(default_dates, file_id, current_page_num, False)
        performer_candidates = _get_candidates("performer")
        fasting_candidates = _get_candidates("fasting")
        panel_candidates = _get_candidates("panel", False, 2)

        self._fill_previous_page_data(rows, performer_candidates, "Laboratory", ['lab.performer'], False)
        self._fill_previous_page_data(rows, fasting_candidates, "fasting", self.fasting_tags, False)
        self._fill_previous_page_data(rows, panel_candidates, "Panel", ['lab.panel'], False)
        self._fill_previous_page_date(rows, lab_dates_candidates, "Date", self.date_order)

        alternative_dates = lfilter(lambda cell: 'encounter.date' in cell['type'], document_tags[current_document_num].get("lab.dates", []))
        lab_dates_candidates = self._get_prev_candidates_by_page_class(alternative_dates, file_id, current_page_num, True)
        self._fill_previous_page_date(rows, lab_dates_candidates, "Date", self.date_order, True)

    # -----------------------------------------------------------------------------------------------------------------------

    def extract_lab_test_rows(self, edoc: EDocument, card_name: str):
        mother, children, empty_blueprint, empty_row, ner_keys = self._prepare_blue_print_data(card_name)
        selected_rows = []
        
        self.page_classifiers = {file.file_id: {page.page_number : page.page_class for page in file.page_classes} 
                             for file in self.retrieved_data.file_page_classes}
        
        priority_mapping = self.misc.get_field_wise_priority_mapping(self.retrieved_data.priorities, "laboratory_results", self.retrieved_data.page_classification_display_name_mapping)
        pages, document_tags = self._edoc_to_page_wise(edoc, mother, children, priority_mapping, "laboratory_results")
            
        for doc_page_num, page_data in pages.items():
            if len(page_data['children']) == 0:
                continue
            sub_table = self._populate_with_mother_ners(page_data, empty_row)
            sub_table, value_dates = self._populate_with_children(
                sub_table, ner_keys, page_data.get("mother", []), page_data.get("children", [])
            )
            sub_table = self.misc.make_single_object_in_extended_columns(
                sub_table, ["fasting"]
            )
            rows = self._convert_dict_to_rows(sub_table, value_dates)
            
            current_document_num, current_page_num = map(int, doc_page_num.split('-'))
            self._fill_previous_lab_data(rows, document_tags, current_document_num, current_page_num, page_data)
            rows = self.filter_by_date(rows)

            rows = self._fill_missing_dummy_cell(rows, empty_blueprint)
            if Title.DISABLE_LOINC not in self.retrieved_data.feature:
                rows = self._fill_loinc_codes(rows)
                
            selected_rows.extend(rows)
            
        selected_rows = self._remove_duplicate_rows(selected_rows)
        
        return selected_rows

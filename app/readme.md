#Naming conventions

### Case, File and Page:
A Case is a collection of Files and a file is a collection of Pages. 
Avoid using other namings like claim or document
Each of these objects should have ids, and in the code we use folder_id, file_id, and page_id

### Number and index
A number starts with 1, and index starts with 0.

A number is generally use to infer the order of the object in its parent. For example page number 3 will be the 3rd page
in the parent file.

### Portfolio
The V2 json used, it has the words, entities, and cards

### Immediate json
The first json that reaches the cards endpoint
import json
import sys
from typing import List

import pydantic
from pydantic import RootModel, TypeAdapter


class DocumentTypeMappingMapping(pydantic.BaseModel):
    class_name: str
    display_name: str


class DocumentTypeMappingDocumentType(pydantic.BaseModel):
    document_type: str
    classification_mapping: List[DocumentTypeMappingMapping]
    
    def classes_for_display_name(self, display_name: str) -> List[str]:
        matching_items = list(filter(
            lambda mapping: mapping.display_name == display_name,
            self.classification_mapping
        ))
        if len(matching_items) == 0:
            return [display_name]
        class_names = list(map(
            lambda mapping: mapping.display_name.lower(),
            matching_items
        ))
        return class_names


class DocumentTypeMappingFile(RootModel[List[DocumentTypeMappingDocumentType]]):

    @staticmethod
    def from_file(filename: str) -> "DocumentTypeMappingFile":
        with open(filename, "r", encoding="utf-8") as f:
            data = json.load(f)
        return TypeAdapter(DocumentTypeMappingFile).validate_python(data)

    def get_document_types(self) -> List[str]:
        return [doc.document_type for doc in self.root]

    def get_document_type(self, document_type: str) -> DocumentTypeMappingDocumentType:
        return next(
            doc for doc in self.root if doc.document_type == document_type
        )


if __name__ == "__main__":

    filename = sys.argv[1]
    # document_type_mapping = pydantic.parse_file_as(path = filename, type_=DocumentTypeMappingFile)
    document_type_mapping = DocumentTypeMappingFile.from_file(filename)

    print("DOCUMENT TYPES")
    document_type_names = document_type_mapping.get_document_types()
    for document_type_name in document_type_names:
        print(document_type_name)

    underwriting = document_type_mapping.get_document_type("Underwriting")
    print("==== Underwriting")
    for item in underwriting.classification_mapping:
        print(f"{item.display_name:40} => {item.class_name}")

    display_name = "Doctor's report"
    print(f"==== {display_name}")
    classes = ", ".join(underwriting.classes_for_display_name(display_name))
    print(f"{display_name} => [{classes}]")

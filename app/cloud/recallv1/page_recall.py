from typing import List, Optional

from pydantic import BaseModel, Field


class UnstructuredPageRecall(BaseModel):
    template_field_matching: List
    template_table_matching: List
    MedicalNLP: List
    dates: List
    claim_processing: dict  # TODO
    claim_processing_table: List
    field_names: object
    structured_claim_processing: object
    structured_claim_processing_table: object
    number: int = Field(alias="pageNo")
    classified_as_: str = Field(alias="PageClassifier")
    words: List
    # image_size: dict
    file_id: Optional[str] = None
    file_ui_url: Optional[str] = None

    @property
    def classified_as(self):
        return self.classified_as_.lower()

    @property
    def fields(self):
        return self.claim_processing["fields_list"]

    def get_legacy_json(self):
        return {
            "claim_processing": {
                "fields_list": self.fields,
                "sections": []
            },
            "PageNo": self.number,
            "page_classifier": self.classified_as,
            "words": self.words
        }

    def update_objects_indexes(self, file_id, file_ui_url):
        self.file_id = file_id
        self.file_ui_url = file_ui_url
        for field in self.fields:
            field["doc_num"] = self.file_id
            field["value"][0]["abs_page"] = self.number
            field["value"][0]["claimid"] = self.file_id
            field["file_ui_url"] = self.file_ui_url
            field["page_class"] = self.classified_as

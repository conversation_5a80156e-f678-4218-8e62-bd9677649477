from typing import Optional, List

from friendlylib import storage
from pydantic import BaseModel
from pydantic.fields import Field

from cloud.recallv1.page_recall import UnstructuredPageRecall
from cloud.vanilla.page import Page
from cloud.vanilla.templated_page_wrapper import TemplatedPageWrapper


class File(BaseModel):
    file_id: str = Field(alias="ClaimFileId")
    file_name: str = Field(alias="FileName")
    JsonPath: str
    templated_page_wrappers: List[TemplatedPageWrapper] = Field(alias="templated_pages")
    DocumentClaimId: str
    unstructured_pages: Optional[List[UnstructuredPageRecall]] = None
    templated_pages_: Optional[List[Page]] = None
    file_ui_url: Optional[str] = Field(default=None, alias="DocumentClaimId")

    @property
    def templated_pages(self):
        if self.templated_pages_ is None:
            self.templated_pages_ = [page_wrapper.page for page_wrapper in self.templated_page_wrappers]
        return self.templated_pages_

    @property
    def pages(self):
        return self.templated_pages + self.unstructured_pages

    def __init__(self, **data):
        super().__init__(**data)
        pages_data = storage.download_json(self.JsonPath)
        self.unstructured_pages = [UnstructuredPageRecall(**page_data) for page_data in pages_data["result"]]

    @property
    def page_classes(self):
        return {page.number: page.classified_as for page in self.pages}

    @property
    def page_confidence(self):
        return {page.number: page.page_confidence for page in self.pages}

    @property
    def confidences_dict(self):
        return {page.number: page.confidences_dict for page in self.pages}

    def update_objects_indexes(self):
        for page in self.unstructured_pages:
            page.update_objects_indexes(self.file_id, self.file_ui_url)

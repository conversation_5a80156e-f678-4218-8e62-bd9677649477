import os.path
from typing import Optional, List

from friendlylib import storage
from pydantic import BaseModel

from cloud.vanilla.casev1 import CaseV1
from cloud.vanilla.file_page_classes import FilePageClasses
from cloud.vanilla.page_classes import PageClasses
from medical_codes.medical_coding import MedicalCode

class ImmediateJson(BaseModel):
    card_input_url: str
    document_type: Optional[str] = None
    language: Optional[str] = None
    case_: Optional[CaseV1] = None  # = Field(alias="card_input")
    file_page_classes: Optional[List[FilePageClasses]] = None
    userdefined_card_url: Optional[str] = None
    feature: Optional[str] = None
    field_validations_url: Optional[str] = None
    medical_code_: Optional[MedicalCode] = None

    @property
    def medical_codes(self) -> MedicalCode:
        if self.medical_code_ is None:
            self.medical_code_ = MedicalCode()
        return self.medical_code_
    
    @property
    def case(self) -> CaseV1:
        if self.case_ is None:
            folder_data = storage.download_json(self.card_input_url)
            self.case_ = CaseV1(**folder_data)
        return self.case_

    @property
    def cards_validation(self) -> list:
        return []

    def update_objects_indexes(self):
        self.case.update_objects_indexes()

    @property
    def cloud_folder_url(self):
        return os.path.dirname(self.card_input_url)

    def generate_file_page_classes(self):
        if self.case.files is None:
            return
        file_page_classes = list()
        for file in self.case.files:
            page_class = self.generate_page_classes(file.page_classes, file)
            file_page_class = FilePageClasses(
                file_id=file.file_id,
                file_ui_url=file.file_ui_url,
                page_classes=page_class
            )
            file_page_classes.append(file_page_class)
        self.file_page_classes = file_page_classes

    def generate_page_classes(self, page_class_dictionary, file):
        page_classes = list()
        for page_class_number, page_class_name in page_class_dictionary.items():
            page_class = PageClasses(
                page_number=page_class_number,
                page_class=page_class_name,
                page_confidence=file.page_confidence.get(page_class_number),
                confidences_dict=file.confidences_dict.get(page_class_number)
            )
            page_classes.append(page_class)
        return page_classes

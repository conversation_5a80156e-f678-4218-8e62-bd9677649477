from typing import List, Optional

from pydantic import BaseModel, Field

from cloud.vanilla.filev1 import FileV1
from cloud.vanilla.user_config import UserConfig


class CaseV1(BaseModel):
    files: List[FileV1] = Field(alias="claims", min_items=1)
    ccda: List
    user_config: UserConfig
    language: Optional[str] = None

    def update_objects_indexes(self) -> None:
        offset = 0
        for file_number, file in enumerate(self.files, start=1):
            file.update_objects_indexes(file_number, offset)
            offset += len(file.pages)

import os
import time
from concurrent.futures import Thread<PERSON>oolExecutor
from typing import List, Optional, Union

from friendlylib import storage
from pydantic import BaseModel, Field

from cloud.document_type_mapping import DocumentTypeMappingFile
from cloud.vanilla.claim_priority import ClaimPriority
from cloud.vanilla.document import Document
from cloud.vanilla.page import Page
from cloud.vanilla.page_wrapper import PageWrapper
from cloud.vanilla.templated_page_wrapper import TemplatedPageWrapper


class FileV1(BaseModel):
    file_ui_url: str = Field(alias="DocumentClaimId")
    cards_extraction_priority_dictionary_url: str
    page_classifier_display_name_mapping_url: Optional[str] = None
    is_cards_recall: bool
    flayout_output_document_url: Optional[Union[str, dict]] = Field(default=None, alias="flayout_output_document")
    unstructured_page_wrappers: List[PageWrapper] = Field(alias="flayout_output_pages")
    templated_page_wrappers: List[TemplatedPageWrapper] = Field(alias="templated_pages")
    debug: bool
    language: str
    cards_extraction_priority_: Optional[List[ClaimPriority]] = None
    page_classification_display_name_mapping_: Optional[DocumentTypeMappingFile] = None
    file_number: Optional[int] = None
    flayout_output_document_: Optional[Document] = None
    page_wrappers_: Optional[List[PageWrapper]] = None

    @property
    def cards_extraction_priority(self):
        if self.cards_extraction_priority_ is not None:
            return self.cards_extraction_priority_
        extraction_data = storage.download_json(self.cards_extraction_priority_dictionary_url)
        self.cards_extraction_priority_ = [ClaimPriority(**claim_priority) for claim_priority in extraction_data]
        return self.cards_extraction_priority_

    @property
    def page_classification_display_name_mapping(self) -> Optional[DocumentTypeMappingFile]:
        if self.page_classification_display_name_mapping_ is not None:
            return self.page_classification_display_name_mapping_
        if self.page_classifier_display_name_mapping_url is None:
            print(f"[INFO] Downloading page classifier display name mapping URL is None -- not downloading")
            return None
        print(f"[INFO] Downloading page classifier display name mapping {self.page_classifier_display_name_mapping_url}")
        data = storage.download_json(self.page_classifier_display_name_mapping_url)
        self.page_classification_display_name_mapping_ = DocumentTypeMappingFile.parse_obj(data)
        return self.page_classification_display_name_mapping_

    @property
    def file_id(self):
        return self.file_ui_url.strip("/").split("/")[-1]

    @property
    def flayout_document(self):
        if not self.flayout_output_document_url:
            return None
        if self.flayout_output_document_ is not None:
            return self.flayout_output_document_
        document = storage.download_zjson(self.flayout_output_document_url)
        self.flayout_output_document_ = Document(**document)
        return self.flayout_output_document_

    @property
    def pages(self) -> List[Page]:
        return [page_wrapper.page for page_wrapper in self.page_wrappers]

    @property
    def page_wrappers(self):
        if self.page_wrappers_ is not None:
            return self.page_wrappers_
        if self.flayout_output_document_url:
            self.page_wrappers_ = [PageWrapper(**{'page_': page, 'PageNo': page.page_no, 'Classification': page.classified_as}) for page in self.flayout_document.pages_data]
            return self.page_wrappers_
        return self.unstructured_page_wrappers + self.templated_page_wrappers

    @property
    def page_classes(self):
        page_class_mapping = {page_wrapper.number: page_wrapper.page_class for page_wrapper in self.page_wrappers}
        return {page.number or page.page_no: page_class_mapping.get(page.number).lower() if page_class_mapping.get(page.number) else page.classified_as for page in self.pages}

    @property
    def page_confidence(self):
        return {page.number or page.page_no: page.page_confidence for page in self.pages}

    @property
    def confidences_dict(self):
        return {page.number or page.page_no: page.confidences_dict for page in self.pages}

    # def update_objects_indexes(self, file_number, offset):
    #     self.file_number = file_number
    #     t1 = time.time()
    #     for page_wrapper in self.page_wrappers:
    #         page_wrapper.update_objects_indexes(self.file_id, file_number, offset, self.file_ui_url)
    #     t2 = time.time()
    #     print(f"Time Taken: {t2 - t1}")

    def update_page_wrapper(self, page_wrapper, file_id, file_number, offset, file_ui_url):
        page_wrapper.update_objects_indexes(file_id, file_number, offset, file_ui_url)

    def update_objects_indexes(self, file_number, offset):
        self.file_number = file_number
        t1 = time.time()

        num_threads = int(os.getenv("PAGE_DOWNLOAD_THREADS", 5))
        print(f"File No: {file_number}, File ID: {self.file_id} :: Download Starts ...")
        print(f"Number of Threads to Download Pages: {num_threads}")
        with ThreadPoolExecutor(max_workers=num_threads) as executor:
            # Submit tasks to the thread pool to update page wrappers
            futures = [
                executor.submit(self.update_page_wrapper, page_wrapper, self.file_id, file_number, offset, self.file_ui_url)
                for page_wrapper in self.page_wrappers]

            # Wait for all tasks to complete
            for future in futures:
                future.result()

        t2 = time.time()
        print(f"Time Taken To Download File {self.file_id} having {len(self.pages)} Pages: {round(t2 - t1, 4)}s")

    def get_legacy_json(self) -> List:
        return [page.get_legacy_json() for page in self.pages]

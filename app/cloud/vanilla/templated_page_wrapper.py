import uuid
from copy import deepcopy
from typing import Optional

from friendlylib import storage
from pydantic import BaseModel, Field

from cloud.vanilla.page import Page


class TemplatedPageWrapper(BaseModel):
    number: int = Field(alias="PageNo")
    url: str
    page_: Optional[Page] = None
    file_id: Optional[str] = None
    file_number: Optional[int] = None
    file_ui_url: Optional[str] = None
    page_class: Optional[str] = Field(default=None, alias="Classification")

    @property
    def page(self) -> Page:
        if self.page_ is not None:
            return self.page_
        if self.url == "":
            self.page_ = Page.empty_page()
        else:
            page_data = storage.download_zjson(self.url)
            standardized_page_data = generate_unstructured_data(page_data, self.number)
            self.page_ = Page(**standardized_page_data)
        return self.page_

    def update_objects_indexes(self, file_id, file_number, offset, file_ui_url):
        self.file_id = file_id
        self.file_number = file_number
        self.file_ui_url = file_ui_url
        self.page.update_objects_indexes(self.number, self.file_id, file_number, offset, file_ui_url, self.page_class)


# TO BE CHANGED IN THE FUTURE TO MAKE TEMPLATE AND FULL EXTRACTION HAVE THE SAME FORMAT
def empty_ner_object():
    obj = {
        "word_ids": [],
        "field_name": "",
        "id": "",
        "type": "",
        "section-info": {
            "section_id": None,
            "section_name": None,
            "word_ids": [],
            "parent_section_id": None
        },
        "confidence": 17.26,
        "bbox": {},
        "value": [
            {
                "value_id": str(uuid.uuid4()),
                "word_ids": [],
                "post_check_type": "",
                "value_name": "Brooke",
                "confidence": 1.0,
                "confidence_threshold": 0.1,
                "id": "",
                "type": "",
                "bbox": {
                    "left": 1,
                    "top": 1,
                    "right": 1,
                    "bottom": 1,
                    "width": 1,
                    "height": 1
                }
            }
        ]
    }

    word = {
        "id": "",
        "page": -1,
        "text": "",
        "bbox": [
            960,
            133,
            1013,
            152
        ],
        "consumed_by": None,
        "left": 960,
        "right": 1013,
        "top": 133,
        "bottom": 152,
        "confidence": 84,
        "height": 19,
        "width": 53,
        "type": "O",
        "conf": 2.17
    }

    return obj, word


def convert_table_matching_to_fields_matching(fields_matching, table_matching):
    for table in table_matching:
        table_data = table.get('data', [])
        for cell in table_data:
            # ignoring the first row because it is for headers
            if cell["r"] == 0:
                continue
            obj = {'value': {}}
            obj['value']['value_name'] = cell['value']
            obj['value']['bbox'] = cell['bbox']
            obj['indicator_id'] = cell.get('indicator_id', "")
            obj['field_name'] = cell['field_name']
            obj['value']['confidence'] = cell['confidence']
            obj['codified_as'] = cell.get('codified_as', "")
            obj['codification_category'] = cell.get('codification_category', "")
            obj['codify'] = cell.get('codify')
            fields_matching.append(obj)


def generate_unstructured_data(templated_page_data, page_num):
    field_matching = templated_page_data.get('field_matching', [])
    table_matching = templated_page_data.get('table_matching', [])
    convert_table_matching_to_fields_matching(field_matching, table_matching)

    fields_list = []
    words_list = []
    word_id_counter = -1

    for field_obj in field_matching:
        ner_key = field_obj.get('indicator_id', "")
        if (ner_key is None) or (len(ner_key) == 0) or (ner_key == 'ignored'):
            ner_key = field_obj.get('id', "")
            if (ner_key is None) or (len(ner_key) == 0) or (ner_key == 'ignored'):
                continue

        value = field_obj['value']
        if type(value) == list:
            value = value[0]

        value_name = value['value_name'].strip()

        if len(value_name) == 0:
            continue

        empty_obj, empty_word = empty_ner_object()
        word_id_counter += 1
        wid = 't' + str(word_id_counter)

        confidence = (value['confidence'] * 100) if (value['confidence'] <= 1.0) else value['confidence']

        obj = deepcopy(empty_obj)
        obj["value"][0]["value_id"] = field_obj["value"].get("value_id", str(uuid.uuid4()))
        obj['field_name'] = field_obj['field_name']
        obj['confidence'] = confidence
        obj['value'][0]['value_name'] = value_name
        bbox = value['bbox']
        bbox['right'] = bbox['left'] + bbox['width']
        bbox['bottom'] = bbox['top'] + bbox['height']
        obj['type'] = ner_key
        obj['value'][0]['type'] = ner_key
        obj['value'][0]['bbox'] = bbox
        obj['word_ids'] = [wid]
        obj['value'][0]['word_ids'] = [wid]
        obj['value'][0]['confidence'] = confidence
        obj['codified_as'] = field_obj.get('codified_as', '')
        obj['codification_category'] = field_obj.get('codification_category', '')
        obj['codify'] = field_obj.get('codify')
        obj['metadata'] = {"page_type": "templated"}

        fields_list.append(obj)

        word = deepcopy(empty_word)
        word['id'] = wid
        word['page'] = str(page_num)
        word['text'] = value_name
        word['conf'] = confidence
        word['left'] = bbox['left']
        word['top'] = bbox['top']
        word['right'] = bbox['right']
        word['bottom'] = bbox['bottom']
        word['width'] = bbox['width']
        word['height'] = bbox['height']
        word['bbox'] = [bbox['left'], bbox['top'], bbox['right'], bbox['bottom']]
        words_list.append(word)

    page = {
        "field_matching": fields_list,
        "words": words_list,
        "page_classifier": templated_page_data["page_classifier"],
        "image_size": templated_page_data["image_size"],
        "page_confidence": templated_page_data["page_confidence"],
        "confidences_dict": templated_page_data.get("confidences_dict"),
        "medical_nlp": [],
        "number": page_num
    }
    return page

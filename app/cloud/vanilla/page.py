from typing import Optional, List

from pydantic import BaseModel, Field
from friendlylib.iterators import find, lmap, lfilter

class Page(BaseModel):
    fields: List[dict] = Field(alias="field_matching")
    words: List
    page_confidence: Optional[float] = None
    image_size: Optional[List] = None
    classified_as_: Optional[str] = Field(default="generated", alias="page_classifier")
    section: Optional[List] = Field(default=None, alias="sections")
    number: Optional[int] = None
    file_id: Optional[str] = None
    file_number: Optional[int] = None
    # this is temporary to align with the already used json
    # in the future, it is better to use (file_id, number) to uniquely find the page and also be able to sort them
    number_in_folder: Optional[int] = None
    file_ui_url: Optional[str] = None
    confidences_dict: Optional[dict] = None
    skipped_reason: Optional[str] = None
    page_no: Optional[int] = None

    @property
    def classified_as(self) -> str:
        return self.classified_as_.lower() if self.classified_as_ else 'generated'

    @classified_as.setter
    def classified_as(self, classified_as_):
        self.classified_as_ = classified_as_

    def get_legacy_json(self):
        return {
            "claim_processing": {
                "fields_list": self.fields,
                "sections": self.section
            },
            "PageNo": self.number_in_folder,
            "number": self.number,
            "page_classifier": self.classified_as,
            "words": self.words
        }

    def update_objects_indexes(self, page_number, file_id, file_number, offset, file_ui_url, page_class):
        self.number = page_number
        self.file_id = file_id
        self.file_number = file_number
        self.number_in_folder = page_number + offset
        self.file_ui_url = file_ui_url
        self.fields.extend(self.section or [])
        for word in self.words:
            word["page"] = self.number
            word['abs_page'] = self.number
            word['claimid'] = self.file_id

        for field in self.fields:
            field["doc_num"] = file_number
            field["value"][0]["abs_page"] = self.number
            field["value"][0]["claimid"] = self.file_id
            field["file_ui_url"] = self.file_ui_url
            field["page_class"] = page_class.lower() if page_class else self.classified_as
            
            words_of_data_value = lmap(self.find_word, field["value"][0]["word_ids"])
            words_of_data_value = lfilter(lambda word: word is not None, words_of_data_value)
            if len(words_of_data_value) and None not in words_of_data_value:
                field["value"][0]["bbox"]["left"] = words_of_data_value[0]['left']
                field["value"][0]["bbox"]["top"] = min([word["top"] for word in words_of_data_value])
                field["value"][0]["bbox"]["right"] = max([word["right"] for word in words_of_data_value])
                field["value"][0]["bbox"]["bottom"] = max([word["bottom"] for word in words_of_data_value])

    def find_word(self, word_id: str):
        words = find(
            lambda word_cell: word_cell["claimid"] == self.file_id and
            word_cell["page"] == self.number and
            word_cell["id"] == word_id,
            self.words
        )
        
        return words

    @classmethod
    def empty_page(cls):
        return cls(
            field_matching=[],
            words=[],
            page_confidence=-1.,
            image_size=[],
            page_classifier=""
        )

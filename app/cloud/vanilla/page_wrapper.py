from typing import Optional

from friendlylib import storage
from pydantic import BaseModel, Field

from cloud.vanilla.page import Page


class PageWrapper(BaseModel):
    number: int = Field(alias="PageNo")
    url: Optional[str] = None
    page_: Optional[Page] = None
    file_id: Optional[str] = None
    file_number: Optional[int] = None
    file_ui_url: Optional[str] = None
    page_class: Optional[str] = Field(default=None, alias="Classification")

    @property
    def page(self) -> Page:
        if self.page_ is not None:
            return self.page_
        if self.url == "":
            self.page_ = Page.empty_page()
        else:
            page_data = storage.download_zjson(self.url)
            self.page_ = Page(**page_data)
        return self.page_

    def update_objects_indexes(self, file_id, file_number, offset, file_ui_url):
        self.file_id = file_id
        self.file_number = file_number
        self.file_ui_url = file_ui_url
        self.page.update_objects_indexes(self.number, self.file_id, file_number, offset, file_ui_url, self.page_class)

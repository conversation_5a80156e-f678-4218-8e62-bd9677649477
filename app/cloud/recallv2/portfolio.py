from copy import deepcopy
from operator import methodcaller
from typing import List, Optional, Dict

from pydantic import BaseModel

from blueprints import CODIFICATION_INFO_MAPPING
from cloud.recallv2.data_value import DataValue
from cloud.recallv2.file_v2 import FileV2
from cloud.recallv2.word_location import WordLocation
from cloud.vanilla.page import Page
from cloud.vanilla.file_page_classes import FilePageClasses
from friendlylib.iterators import group_by_func, find, lmap, lfilter
from friendlylib.strings import get_suffix_by_delimiter
import constants as const
from cloud.vanilla.page_classes import PageClasses


class Portfolio(BaseModel):
    words: List
    data_values: List[DataValue]
    summary_events: List
    files: Optional[List[FileV2]] = None
    file_page_classes: Optional[List[FilePageClasses]] = None
    summary_custom_data: Optional[Dict] = None
    user_config: Optional[Dict] = None
    cards: Optional[List] = None
    input_data_values: Optional[List] = None

    def __init__(self, user_config=None, **data):
        super().__init__(**data)
        self.user_config = user_config
        self.remove_deleted_datavalues()
        self.remove_invalid_words()
        self.input_data_values = deepcopy(self.data_values)
        self.remove_data_values_without_words()
        self.codify_edited_fields()
        self.infer_data_value_bbox_from_words()
        self.generate_pages()

    def generate_pages(self):
        words_v1 = self.generate_words_v1()
        fields_v1 = self.generate_fields()
        files = []
        for file_id, words_in_file in words_v1.items():
            pages = []
            for page_number, words_in_page in words_in_file.items():
                page_classes_obj = self.get_page_class_obj(file_id, page_number)
                page_class = page_classes_obj.page_class if page_classes_obj else ""
                page_skipped_reason = page_classes_obj.skipped_reason if page_classes_obj.skipped_reason else None
                page = Page(
                    words=words_in_page,
                    field_matching=fields_v1.get(file_id, {}).get(page_number, []),
                    number=page_number,
                    page_classifier=page_class,
                    skipped_reason=page_skipped_reason
                )
                pages.append(page)
            file = FileV2(
                file_id=file_id,
                pages=pages
            )
            files.append(file)
        self.files = files

    def generate_words_v1(self):
        words_v1 = dict()
        for file_of_words in self.words:
            file_id = file_of_words["claim_file_id"]
            words_v1[file_id] = dict()
            for page_of_words in file_of_words["pages"]:
                page_number = page_of_words["page"]
                words_v1[file_id][page_number] = []
                for word in page_of_words["words"]:
                    word_v1 = deepcopy(word)
                    word_v1["id"] = word_v1["word_id"]
                    word_v1["page"] = page_number
                    word_v1["left"] = word_v1["bbox"]["left"]
                    word_v1["top"] = word_v1["bbox"]["top"]
                    word_v1["right"] = word_v1["bbox"]["right"]
                    word_v1["bottom"] = word_v1["bbox"]["bottom"]
                    word_v1["claimid"] = file_id
                    words_v1[file_id][page_number].append(word_v1)
        return words_v1

    def generate_fields(self):
        data_values = filter(lambda data_value: len(data_value.words) > 0, self.data_values)
        fields = map(methodcaller("to_field"), data_values)
        fields_in_files = group_by_func(fields, lambda field: field["value"][0]["claimid"])
        fields_v1 = dict()
        for file_id, field_list in fields_in_files.items():
            fields_v1[file_id] = group_by_func(field_list, lambda field: field["value"][0]["page_number"])
        return fields_v1

    def remove_deleted_datavalues(self):
        self.data_values = lfilter(
            lambda data_value: data_value.update_action != "Deleted",
            self.data_values
        )
    
    def remove_invalid_words(self):
        self.words = [
            {
                'claim_file_id': entry['claim_file_id'],
                'pages': [
                    {
                        'page': page['page'],
                        'words': [word for word in page['words'] if 'aw' not in word['word_id']]
                    }
                    for page in entry['pages']
                ]
            }
            for entry in self.words
        ]
        
    def remove_data_values_without_words(self):
        self.data_values = lfilter(
            lambda data_value: len(data_value.words) > 0,
            self.data_values
        )
        self.data_values = lfilter(
            lambda data_value: data_value.words and self.find_word(data_value.words[0]) is not None,
            self.data_values
        )
        self.data_values = lfilter(
            lambda data_value: const.MERGED_ENTITY not in data_value.entity_type and
                               const.GENERATED_ENTITY not in data_value.entity_type and
                               'us_citizen' != data_value.entity_type.split('.')[-1] and
                               data_value.entity_type not in const.TAGS_CREATED_BY_CARDS and
                               not (data_value.metadata and data_value.metadata.get('card_generated')),
            self.data_values
        )

    def infer_data_value_bbox_from_words(self):
        for data_value in self.data_values:
            words_of_data_value = lmap(self.find_word, data_value.words)
            words_of_data_value = lfilter(lambda word: word is not None, words_of_data_value)
            if len(words_of_data_value) and None not in words_of_data_value:
                data_value.left = words_of_data_value[0]["bbox"]["left"]
                data_value.top = min([word["bbox"]["top"] for word in words_of_data_value])
                data_value.right = max([word["bbox"]["right"] for word in words_of_data_value])
                data_value.bottom = max([word["bbox"]["bottom"] for word in words_of_data_value])

    def find_words(self, word_locations: List[WordLocation]):
        return [self.find_word(word_location) for word_location in word_locations]

    def find_word(self, word_location: WordLocation):
        file_of_words = find(
            lambda _file_of_words: _file_of_words["claim_file_id"] == word_location.file_id,
            self.words
        )
        page_of_words = find(
            lambda _page_of_words: _page_of_words["page"] == word_location.page_number,
            file_of_words["pages"]
        )
        word = find(
            lambda _word: _word["word_id"] == word_location.word_id,
            page_of_words["words"]
        )
        return word

    def update_objects_indexes(self) -> None:
        offset = 0
        for file_number, file in enumerate(self.files, start=1):
            file.update_objects_indexes(file_number, offset)
            offset += len(file.pages)

    def get_page_class_obj(self, file_id, page_number, default=None) -> PageClasses | None:
        if self.file_page_classes is None:
            return default
        file_page_class = next((file for file in self.file_page_classes if file.file_id == file_id), None)
        matching_page_class = next(
            (page_class for page_class in file_page_class.page_classes if page_class.page_number == page_number), None)
        if matching_page_class is None:
            return default
        return matching_page_class

    def codify_edited_fields(self):
        for data_value in self.data_values:
            try:
                if data_value.update_action not in ['Edited', 'Added']:
                    continue

                entity_type_suffix = get_suffix_by_delimiter(data_value.entity_type)
                if 'duration' in data_value.entity_type:
                    entity_type_suffix = 'duration'
                if CODIFICATION_INFO_MAPPING.get(entity_type_suffix) is None:
                    continue

                unit = CODIFICATION_INFO_MAPPING[entity_type_suffix]['category_unit']
                if unit == 'date' or 'date' in data_value.entity_type:
                    entity_type_suffix = 'date'
                    if self.user_config and self.user_config.get('input_date_format'):
                        unit = self.user_config.get('input_date_format')
                    else:
                        unit = "MM-DD-YYYY"
                validate_function = CODIFICATION_INFO_MAPPING[entity_type_suffix]['validate_function']
                if unit == 'duration':
                    codification_obj = validate_function(data_value.text)
                else:
                    codification_obj = validate_function(data_value.text, unit)

                data_value.codified_as = codification_obj.value
                data_value.codification_category = codification_obj.category
                data_value.codify = {
                    "codified_as": codification_obj.value,
                    "unit": codification_obj.units,
                    "codification_category": codification_obj.category
                }
            except Exception as e:
                print(f"ERROR: Recall Codification Failed: {e}")
                continue

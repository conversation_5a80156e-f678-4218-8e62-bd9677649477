from typing import List, Optional, Union, Dict

from pydantic import BaseModel, Field

from cloud.recallv2.word_location import WordLocation
from entity import Entity


class DataValue(BaseModel):
    data_value_id: str
    entity_type: str
    entity_confidence: float
    text: Optional[str] = Field(default='')
    is_user_edited: bool
    words: List[WordLocation]
    section_tag: Union[List[str], str, None] = None
    left: Optional[int] = None
    top: Optional[int] = None
    right: Optional[int] = None
    bottom: Optional[int] = None
    codified_as: Optional[str] = None
    codification_category: Optional[str] = None
    codify: Optional[Dict] = None
    codified_datetime_obj: Optional[str] = None
    update_action: Optional[str] = None
    detected_lang: Optional[Dict] = None
    metadata: Optional[Dict] = None
    word_type: Optional[str] = None
    entry_id: Optional[str] = None
    source: Optional[str] = None
    layout_uuid: Optional[str] = None
    ui_id: Optional[str] = None
    xpath: Optional[str] = None
    context: Optional[Dict] = None
    section_info: Optional[Dict] = None
    is_skipped: Optional[bool] = None

    model_config = {
        "validate_by_name": True
    }
    @property
    def page_number(self):
        if len(self.words) == 0:
            return -1
        return self.words[0].page_number

    @property
    def file_id(self):
        return self.words[0].file_id

    def to_field(self):
        return {
            "doc_num": None,
            "value": [{
                "value_id": self.data_value_id,
                "word_ids": [word.word_id for word in self.words],
                "value_name": self.text,
                "confidence": self.entity_confidence,
                "type": self.entity_type,
                "page_number": self.page_number,
                "abs_page": self.page_number,
                "bbox": {
                    "left": self.left,
                    "top": self.top,
                    "right": self.right,
                    "bottom": self.bottom
                },
                "claimid": self.file_id,
            }],
            "codified_as": self.codified_as,
            "codification_category": self.codification_category,
            "codify": self.codify,
            "codified_datetime_obj": self.codified_datetime_obj,
            "detected_lang": self.detected_lang,
            "metadata": self.metadata,
            "word_type": self.word_type,
            "entry_id": self.entry_id,
            "source": self.source,
            "layout_uuid": self.layout_uuid,
            "ui_id": self.ui_id,
            "xpath": self.xpath,
            "context": self.context,
            "section_info": self.section_info,
            "is_skipped": self.is_skipped
        }

    def to_entity(self) -> Entity:
        return Entity(
            left=self.left,
            top=self.top,
            right=self.right,
            bottom=self.bottom,
            text=self.text,
            confidence=self.entity_confidence,
            page_num=self.page_number,
            entity_type=self.entity_type,
            uid=self.data_value_id,
            document_id="",
            claim_id=self.file_id,
            codified_as=self.codified_as,
            codification_category=self.codification_category,
            fomatted_codification=None,
            codify=self.codify,
            codified_datetime_obj=self.codified_datetime_obj,
            detected_lang=self.detected_lang,
            metadata=self.metadata,
            word_type=self.word_type,
            entry_id=self.entry_id,
            source=self.source,
            layout_uuid=self.layout_uuid,
            ui_id=self.ui_id,
            xpath=self.xpath,
            context=self.context,
            section_info=self.section_info,
            is_skipped=self.is_skipped
        )

    def to_dict(self):
        return {
            "data_value_id": self.data_value_id,
            "entity_type": self.entity_type,
            "entity_confidence": self.entity_confidence,
            "text": self.text,
            "is_user_edited": self.is_user_edited,
            "words": [word.to_dict() for word in self.words],
            "codified_as": self.codified_as,
            "codification_category": self.codification_category,
            "codify": self.codify,
            "codified_datetime_obj": self.codified_datetime_obj,
            "detected_lang": self.detected_lang,
            "entry_id": self.entry_id,
            "source": self.source,
            "layout_uuid": self.layout_uuid,
            "metadata": self.metadata,
            "word_type": self.word_type,
            "ui_id": self.ui_id,
            "xpath": self.xpath,
            "context": self.context,
            "section_tag": self.section_tag,
            "section_info": self.section_info,
            "is_skipped": self.is_skipped
        }

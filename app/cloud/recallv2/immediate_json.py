import os
from typing import List, Optional, Dict

from friendlylib import storage
from friendlylib.iterators import find
from pydantic import BaseModel
from pydantic.fields import Field

from cloud.document_type_mapping import DocumentTypeMappingFile
from cloud.recallv1.file import File
from cloud.recallv2.portfolio import Portfolio
from cloud.vanilla.claim_priority import ClaimPriority
from cloud.vanilla.file_page_classes import FilePageClasses
from cloud.vanilla.page_classes import PageClasses
from medical_codes.medical_coding import MedicalCode

class ImmediateJson(BaseModel):
    portfolio_url: str = Field(alias="cards_json_url")
    cards_extraction_priority_dictionary_url: str
    page_classifier_display_name_mapping_url: Optional[str] = None
    is_cards_recall: bool
    debug: bool
    language: str
    document_type: str
    full_extraction_data: List
    files: Optional[List[File]] = None
    portfolio_: Optional[Portfolio] = None
    extraction_priorities_: Optional[List[ClaimPriority]] = None
    page_classification_display_name_mapping_: Optional[DocumentTypeMappingFile] = None
    user_config: Optional[Dict[str, str]] = None
    userdefined_card_url: Optional[str] = None
    feature: Optional[str] = None
    field_validations_url: Optional[str] = None
    recall_cards_list: Optional[List[str]] = None
    medical_code_url: Optional[str] = Field(alias="medical_code_url", default=None)
    medical_code_: Optional[MedicalCode] = None

    @property
    def portfolio(self) -> Portfolio:
        if self.portfolio_ is None:
            cards_data = storage.download_json(self.portfolio_url)
            self.portfolio_ = Portfolio(self.user_config, **cards_data)
        return self.portfolio_

    @property
    def medical_codes(self) -> MedicalCode:
        if self.medical_code_ is None:
            if self.medical_code_url:
                try:
                    medical_code_data = storage.download_json(self.medical_code_url)
                    self.medical_code_ = MedicalCode(**medical_code_data)
                except Exception as e:
                    error_message = "Could not download"
                    if error_message in str(e):
                        print(f"Handled specific exception: {error_message}")
                        self.medical_code_ = MedicalCode()
            else:
                self.medical_code_ = MedicalCode()
        return self.medical_code_

    @property
    def extraction_priorities(self):
        if self.extraction_priorities_ is not None:
            return self.extraction_priorities_
        extraction_data = storage.download_json(self.cards_extraction_priority_dictionary_url)
        self.extraction_priorities_ = [ClaimPriority(**claim_priority) for claim_priority in extraction_data]
        return self.extraction_priorities_

    @property
    def page_classification_display_name_mapping(self) -> Optional[DocumentTypeMappingFile]:
        if self.page_classifier_display_name_mapping_url is None:
            return None
        if self.page_classification_display_name_mapping_ is not None:
            return self.page_classification_display_name_mapping_
        print(
            f"[INFO] Downloading page classifier display name mapping {self.page_classifier_display_name_mapping_url}")
        data = storage.download_json(self.page_classifier_display_name_mapping_url)
        self.page_classification_display_name_mapping_ = DocumentTypeMappingFile.parse_obj(data)
        return self.page_classification_display_name_mapping_

    @property
    def cloud_folder_url(self):
        return os.path.dirname(self.portfolio_url)

    def update_objects_indexes(self):
        self.portfolio.update_objects_indexes()

    def generate_file_page_classes(self) -> None:
        if self.portfolio_.file_page_classes is not None:
            return
        self.files = [File(**file) for file in self.full_extraction_data]
        file_page_classes = []
        for file in self.files:
            page_class = self.generate_page_classes(file.page_classes, file)
            self._update_page_class(file.file_id, page_class)
            file_page_class = FilePageClasses(
                file_id=file.file_id,
                file_ui_url=file.file_ui_url,
                page_classes=page_class
            )
            file_page_classes.append(file_page_class)
        self.portfolio_.file_page_classes = file_page_classes

    def generate_page_classes(self, page_class_dictionary, file):
        page_classes = list()
        for page_class_number, page_class_name in page_class_dictionary.items():
            page_class = PageClasses(
                page_number=page_class_number,
                page_class=page_class_name,
                page_confidence=file.page_confidence.get(page_class_number),
                confidences_dict=file.confidences_dict.get(page_class_number)
            )
            page_classes.append(page_class)
        return page_classes

    def _update_page_class(self, file_id, page_classes):
        file = find(
            lambda _file: _file.file_id == file_id,
            self.portfolio_.files
        )
        for page in page_classes:
            page_document = find(
                lambda _page: _page.number == page.page_number,
                file.pages
            )
            if page_document is not None:
                page_document.classified_as_ = page.page_class

    def generate_file_ui_url(self) -> None:
        if self.files is None:
            self.files = [File(**file) for file in self.full_extraction_data]
        for portfolio_file in self.portfolio_.files:
            file = find(
                lambda _file: _file.file_id == portfolio_file.file_id,
                self.files
            )
            portfolio_file.file_ui_url = file.file_ui_url

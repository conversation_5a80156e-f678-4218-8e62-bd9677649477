from typing import List, Optional

from pydantic.main import BaseModel

from cloud.vanilla.page import Page


class FileV2(BaseModel):
    file_id: str
    pages: List[Page]
    file_ui_url: Optional[str] = None
    file_number: Optional[int] = None

    @property
    def claim_id(self):
        return self.file_id

    @property
    def page_classes(self):
        return {page.number: page.classified_as for page in self.pages}

    @property
    def page_confidence(self):
        return {page.number: page.page_confidence for page in self.pages}

    @property
    def confidences_dict(self):
        return {page.number: page.confidences_dict for page in self.pages}

    def update_objects_indexes(self, file_number, offset):
        self.file_number = file_number
        for page in self.pages:
            page.update_objects_indexes(page.number, self.file_id, file_number, offset, self.file_ui_url, self.page_classes.get(page.number))
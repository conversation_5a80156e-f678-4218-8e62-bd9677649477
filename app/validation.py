import json
import re
import string
import warnings
from copy import deepcopy
from uuid import uuid4
from typing import List
import constants as const
from medication_conditions import MedicationConditions
from friendlylib import iterators


# ------------------------------------------------------

class Validations:

    def __init__(self):
        warnings.warn("This service will be removed in the future", DeprecationWarning)
        self.misc = object
        self.medication_conditions_service = object
        self.page_seq = {}
        self.all_urls = {}
        self.cid_filename = {}
        self.reg_expressions = {}  # this should be removed after swap  types implementation
        self.swap_types = []
        self.doc_languages = {}
        self.icd_codes_unwanted = []
        self.study = {}
        self.test_names = {}
        self.local_testing = False
        self.unwanted_character_in_names = []
        self.corrigible_units = {}

    # ------------------------------------------------------

    def set_dependencies(self, objMisc, retrieved_data_feature: List[str] = []):
        self.misc = objMisc
        self.medication_conditions_service = MedicationConditions()
        self.retrieved_data_feature = retrieved_data_feature

    # ------------------------------------------------------

    def initialize_states(self):

        self.icd_codes_unwanted = ["(", ")", ";", "[", "]", ":", ".", "-", "#", "_", "=", "+", "!", " "]

        self.reg_expressions = {
            'kg': r"(\d*[.,]?\d+)\s*(kg|kilos|gram|g)",
            'pounds': r"(\d*\.?\d+)\s*(lbs?|1b|pound)",
            'fahrenheit': r"(\d*\.?\d+)\s*(°f|°\s*f|degree\S?\s*[.]?f|\s*[\*]f|deg\s*f)",
            'celsius': r"(\d*\.?\d+)\s*(°c|℃ |\s*c|degree\S?\s*[.]?c|\s*[\*]c|deg\s*c)",
            'height_cm': r"(\d*\.?\d+)\s*[^mm](cm|m|met|centi)",
            'height_in': r"(\d*\.?\d+)\s*(in|inch)"
        }

        self.swap_types = [
            {"regex": r"(\d*[.,]?\d+)\s*(kg|kilos|kilo|gram|g)", "from_type": "vital.weight_lbs",
             "to_type": "vital.weight_kg"},
            {"regex": r"(\d*\.?\d+)\s*(lbs?|1b|pound)", "from_type": "vital.weight_kg", "to_type": "vital.weight_lbs"},
            {"regex": r"(\d*[.,]?\d+)\s*(kg|kilos|kilo|gram|g)", "from_type": "patient.weight_lbs",
             "to_type": "patient.weight_kg"},
            {"regex": r"(\d*\.?\d+)\s*(lbs?|1b|pound)", "from_type": "patient.weight_kg", "to_type": "patient.weight_lbs"},
            {"regex": r"(\d*\.?\d+)\s*(°f|°\s*f|degree\S?\s*[.]?f|\s*[\*]f|deg\s*f)",
             "from_type": "vital.temperature_c", "to_type": "vital.temperature_f"},
            {"regex": r"(\d*\.?\d+)\s*(°f|°\s*f|degree\S?\s*[.]?f|\s*[\*]f|deg\s*f)",
             "from_type": "patient.temperature_c", "to_type": "patient.temperature_f"},
            {"regex": r"(\d*\.?\d+)\s*(°c|℃ |\s*c|degree\S?\s*[.]?c|\s*[\*]c|deg\s*c)",
             "from_type": "vital.temperature_f", "to_type": "vital.temperature_c"},
            {"regex": r"(\d*\.?\d+)\s*(°c|℃ |\s*c|degree\S?\s*[.]?c|\s*[\*]c|deg\s*c)",
             "from_type": "patient.temperature_f", "to_type": "patient.temperature_c"},
            {"regex": r"(\d*\.?\d+)\s*[^mm](cm|m|met|centi)", "from_type": "vital.height_in",
             "to_type": "vital.height_cm"},
            {"regex": r"(\d*\.?\d+)\s*(in|inch)", "from_type": "vital.height_cm", "to_type": "vital.height_in"},
            {"regex": r"(\d*\.?\d+)\s*[^mm](cm|m|met|centi)", "from_type": "patient.height_in",
             "to_type": "patient.height_cm"},
            {"regex": r"(\d*\.?\d+)\s*(in|inch)", "from_type": "patient.height_cm", "to_type": "patient.height_in"}
        ]
        self.unwanted_character_in_names = [',', '.', '-', '@']

        self._get_corrigible_units()

    # ------------------------------------------------------

    def _do_examin_test(self, examin, ner, bf, af):
        if ner not in examin:
            examin[ner] = {bf: af}
        else:
            if bf not in examin[ner]:
                examin[ner][bf] = af
        return examin

    # ------------------------------------------------------

    def _update_word_ids(self, field, page_data, before):
        word_ids = field['value'][0]["word_ids"]
        if before == field["value"][0]["value_name"] or len(word_ids) == 0:
            return
        wid = str(word_ids[0])
        list_of_words = [w for w in page_data.words if w['id'] in word_ids]
        if len(list_of_words):
            new_word = deepcopy(list_of_words[0])
            new_word['text'] = field["value"][0]["value_name"]
            new_word['id'] = wid
            field['value'][0]["word_ids"] = [wid]
            if len(list_of_words) > 1:
                ids = [x['id'] for x in list_of_words]
                field['value'][0]["word_ids"].extend(ids[1:])
            page_data.words.append(new_word)

    # ------------------------------------------------------

    def _right_strip(self, text):
        if text.endswith('.'):
            text = text[:-1]
        return text

    # ------------------------------------------------------

    def _clean_reason_value(self, text: str):
        text = text.strip(",;:-_~()[] ")
        # hack only
        unwanted = ['frequent', 'follow up', 'reason for visit history', 'back']
        for w in unwanted:
            if text.lower().strip() == w:
                return const.EMPTY_STRING

        return text.strip()

    # ------------------------------------------------------

    def _clean_lab_data(self, text):
        text = text.strip("¢^₱;!,:~ |*><")
        if (text.startswith('[') or text.startswith(']')) or \
                (('[' in text) and (']' not in text)) or \
                ((']' in text) and ('[' not in text)):
            text = text.strip("[]")

        if (text.startswith('(') or text.startswith(')')) or \
                (('(' in text) and (')' not in text)) or \
                ((')' in text) and ('(' not in text)):
            text = text.strip("()")

        return text

    def _clean_lab_o_value(self, text):
        text = text.strip("¢^₱;!,:~ |*%")
        if (text.startswith('[') and text.startswith(']')) or \
                (('[' in text) and (']' not in text)) or \
                ((']' in text) and ('[' not in text)):
            text = text.strip("[]")

        if (text.startswith('(') and text.startswith(')')) or \
                (('(' in text) and (')' not in text)) or \
                ((')' in text) and ('(' not in text)):
            text = text.strip("()")
        return text
    # ------------------------------------------------------

    def _strip_non_alphabet(self, text):
        pattern = r'[^\u4E00-\u9FFF\u0000-\u05C0\u2100-\u214F]|(?:\([^a-zA-Z\u4E00-\u9FFF]*?\))|\s*/\s*(?![A-Za-z])'
        stripped_text = re.sub(pattern, '', text)
        return stripped_text.strip()

    # ------------------------------------------------------

    def _format_icd_10(self, text):
        text = text.strip().upper()
        start_character_pattern = r'^[a-zA-Z].*$'
        text = re.sub(r'[^a-zA-Z0-9]', '', text)
        if len(text) > 3 and re.match(start_character_pattern, text):
            text = text.strip('.')
            modified_string = text[:3] + '.' + text[3:]
            return modified_string
        else:
            return text

    # ------------------------------------------------------

    def _get_clean_text(self, field):
        this_type = field['value'][0]['type']
        if this_type is None:
            return

        this_type = this_type.lower()
        text = field['value'][0]['value_name']

        # if this_type in ['vital.weight_kg', 'patient.weight_kg']:
        #     matches = re.findall(self.reg_expressions['kg'], text)
        #     if len(matches):
        #         text = matches[0][0]
        #         if (const.point not in text) and (const.comma in text):
        #             text = text.replace(',', '.')
        #
        #         unit = matches[0][1]
        #         if ('gram' in unit) and text.isnumeric():
        #             v = float(text)
        #             text = str(v / 1000)
        #
        #     else:
        #         weight_splitter = ['lb', '[b', '1b', 'pounds', 'pounde']
        #         for s in weight_splitter:
        #             text = text.split(s)[0]
        #         text = re.sub("[^0-9.]", "", text)  # keep digits and dot
        #         text = self._right_strip(text)
        #
        # elif this_type in ['vital.weight_lbs', 'patient.weight_lbs']:
        #     matches = re.findall(self.reg_expressions['pounds'], text)
        #     if len(matches):
        #         text = matches[0][0]
        #     else:
        #         weight_splitter = ['lb', '[b', '1b', 'pounds', 'pounde']
        #         for s in weight_splitter:
        #             text = text.split(s)[0]
        #         text = re.sub("[^0-9.]", "", text)  # keep digits and dot
        #         text = self._right_strip(text)
        #
        # elif this_type in ['vital.height_cm', 'patient.height_cm']:
        #     matches = re.findall(self.reg_expressions['height_cm'], text)
        #     if len(matches):
        #         text = matches[0][0]
        #         if (const.point not in text) and (const.comma in text):
        #             text = re.sub("[^0-9.]", "", text)
        #     else:
        #         text = text.strip("|();[]:, ")
        #
        # elif this_type in ['vital.height_in', 'patient.height_in']:
        #     matches = re.findall(self.reg_expressions['height_in'], text)
        #     if len(matches):
        #         text = matches[0][0]
        #         if (const.point not in text) and (const.comma in text):
        #             text = re.sub("[^0-9.]", "", text)
        #     else:
        #         text = text.strip("|();[]:, ")

        if this_type in ["vital.blood", "patient.blood"]:
            text = text.strip("();[]- ")
            if len(text) and (const.fSlash not in text):
                if ('i' in text) and ('i' != text[0]):
                    text = text.replace('i', const.fSlash)
                elif (const.comma in text) and (const.comma != text[0]):
                    text = text.replace(const.comma, const.fSlash)
                elif (const.space in text) and (len(text) >= 5):
                    arr = text.strip().split(const.space)
                    if len(arr) > 1:
                        text = str(arr[0]) + '/' + str(arr[1])

            text = re.sub("[^0-9/]", "", text)  # keep digits and /

        elif this_type in ["vital.bmi", "patient.bmi"]:
            arr = text.split('kg')
            if len(arr):
                text = arr[0]
            if (const.point not in text) and (const.comma in text):
                text = text.replace(const.comma, const.point)
            text = re.sub("[^0-9.]", "", text)  # keep digits and dot
            text = self._right_strip(text)

        elif this_type in ["vital.heart_rate", "patient.heart_rate"]:
            text = re.sub("[^0-9.]", "", text)  # keep digits and dot
            text = self._right_strip(text)


        # elif this_type in ["vital.temperature_f", "patient.temperature_f"]:
        #     matches = re.findall(self.reg_expressions['fahrenheit'], text)
        #     if len(matches):
        #         text = matches[0][0]
        #     else:
        #         text = re.sub("[^0-9.]", "", text)  # keep digits and dot
        #         text = self._right_strip(text)
        #
        # elif this_type in ["vital.temperature_c", "patient.temperature_c"]:
        #     matches = re.findall(self.reg_expressions['celsius'], text)
        #     if len(matches):
        #         text = matches[0][0]
        #     else:
        #         text = re.sub("[^0-9.]", "", text)  # keep digits and dot
        #         text = self._right_strip(text)

        elif this_type in ["vital.oxygen", "patient.oxygen"]:
            text = re.sub("[^0-9.]", "", text)
            text = self._right_strip(text)
        elif this_type in ["vital.respiration", "patient.respiration"]:
            text = re.sub("[^0-9.]", "", text)
            text = self._right_strip(text)

        elif "reason" in this_type:
            text = self._clean_reason_value(text)
        elif this_type in ['icd10']:
            text = text.replace('$', 'S')
            for x in self.icd_codes_unwanted:
                text = text.replace(x, const.EMPTY_STRING)
            text = re.sub(' +', ' ', text)
            text = text.strip()
            text = self._format_icd_10(text)

        elif "patient.gender" in this_type:
            text = text.strip("();[]-:,}{ ")
            text = text.strip(string.digits)


        elif "patient.ssn" in this_type:
            text = text.strip("();[]:, ")


        elif "reason" in this_type:
            text = text.strip("();[]-:,}{ ").strip()

        elif this_type in ['o.name']:
            text = text.strip("'\";,^₱. |/#?$%")
            # text = self._strip_non_alphabet(text)
            
        elif this_type in ['lab.panel']:
            text = text.lower()
            # text = self._strip_non_alphabet(text)
        
        elif this_type in ['o.value', 'o.ref_low', 'o.ref_high', 'o.unit']:
            if this_type == 'o.value' and 'enable_lab_val_inequalities' in self.retrieved_data_feature:
                text = self._clean_lab_o_value(text)
            else:
                text = self._clean_lab_data(text)
            text = text.lstrip('+')
            val = text.replace(const.comma, const.point)
            if self.misc.is_number(val):
                text = val
                
        elif this_type in ['o.rating', 'o.ref']:
            text = self._clean_lab_data(text)

        elif this_type in ['imaging.exam', 'imaging.finding', 'imaging.impression', 'imaging.procedure', 'cpt']:
            text = text.strip('.,!-()[]:;')

        elif 'family.' in this_type:
            text = text.strip()
            text = text.strip("'.,!-()[]:;")


        elif this_type in ['current.medication', 'plan.medication', 'history.medication', 'medication.name']:
            text = self.medication_conditions_service.remove_uwanted_words(text)
            text = text.strip('.:;-!')

        elif this_type in ['assessment.diag', 'history.diag', 'diagnosis.principal', 'diagnosis.primary', 'pathology.diagnosis', 'diagnosis.history']:
            text = text.strip("\";'’,:()[]-_.|*%#$? ")
            # text = text.strip(string.digits)

        elif 'performer' in this_type:
            text = text.strip("\";'’,:()[]-_.|*%#$ ")
            text = text.strip(string.digits)

        elif this_type in ['patient.name', 'insured_1.name', 'insured_2.name']:
            text = re.sub('\d+', const.space, text)
            for ch in self.unwanted_character_in_names:
                text = text.replace(ch, const.space)
            text = text.strip(string.digits)
            text = text.strip(".();[]:, ")
            text = re.sub('\s+', const.space, text)

        elif this_type in ['product_type']:
            text = text.strip(".();[]:, ")
            text = re.sub('\s+', const.space, text)

        elif any(word in this_type for word in ['alcohol', 'drugs', 'smoking', 'tobacco', 'marijuana']):
            text = text.strip(".();[]:, ")

        elif this_type in ['pe.name', 'pe.body']:
            text = text.strip("|();[]:, ")

        field['value'][0]['value_name'] = text

    # ------------------------------------------------------

    def _is_recall_data(self, this_type, field):
        if this_type is None:
            field['type'] = field['value'][0]['type']
            field['value'][0]['value_id'] = str(uuid4())
            field['id'] = field['value'][0]['id']
            # obj['value'][0]['word_ids'] = ['new000']
            return True, field

        return False, field

    # ------------------------------------------------------

    def _swap_ner_keys(self, this_type, text, field):

        if '\u00b0' in text:  # degree
            text = text.replace('\u00b0', '°')

        for swap_type in self.swap_types:
            if this_type == swap_type["from_type"]:
                matches = re.findall(swap_type["regex"], text)
                if len(matches):
                    field['type'] = swap_type["to_type"]
                    print(text, '-->', matches, this_type, '--->', field['type'])
                    return field, True

        return field, False

    # ------------------------------------------------------

    def _correct_ner_key(self, field):
        this_type = field['value'][0]['type']
        flag_recall, field = self._is_recall_data(this_type, field)
        if flag_recall:
            return field

        text = field['value'][0]['value_name'].lower()

        field, is_swap = self._swap_ner_keys(this_type, text, field)
        if is_swap:
            return field

        if this_type in ['assessment.diag', 'history.diag', 'diagnosis.history']:
            if text.isnumeric():
                field['type'] = 'ignored'
                field['value'][0]['type'] = field['type']
                return field

        if 'history.medication' in this_type:
            field['type'] = 'medication.name'
            field['value'][0]['type'] = field['type']
            return field

        return field

    # ------------------------------------------------------

    def _get_doc_language(self, obj):
        cid = obj['value'][0]['claimid']
        return self.doc_languages.get(cid, const.EMPTY_STRING)

    # ------------------------------------------------------

    def _validate_input_json(self, retrieved_data):
        for page_data in retrieved_data.pages:
            for field in page_data.fields:
                self._correct_ner_key(field)
                before = field['value'][0]['value_name']
                self._get_clean_text(field)
                self._update_word_ids(field, page_data, before)

    # ------------------------------------------------------

    def _normalize(self, field, system_unit):
        if system_unit.lower() == 'metric':
            self._normalize_metric(system_unit, field)
        elif system_unit.lower() == 'english':
            self._normalize_english(system_unit, field)

    # ------------------------------------------------------

    def _normalize_metric(self, system_unit, field):
        text = field['value'][0]['value_name'].lower()
        this_type = field['value'][0]['type']
        if this_type == 'vital.weight_lbs':
            text = self._remove_height_units(system_unit, 'weight', text)
            if self.misc.is_number(text):
                wt = float(text)
                wt *= const.LBS_TO_KG
                wt = str(round(wt, 2))
                field['value'][0]['value_name'] = wt
                field['type'] = 'vital.weight_kg'
                field['value'][0]['type'] = field['type']
        elif this_type == 'patient.weight_lbs':
            text = self._remove_height_units(system_unit, 'weight', text)
            if self.misc.is_number(text):
                wt = float(text)
                wt *= const.LBS_TO_KG
                wt = str(round(wt, 2))
                field['value'][0]['value_name'] = wt
                field['type'] = 'patient.weight_kg'
                field['value'][0]['type'] = field['type']
        # celsius to fahrenheit
        elif 'vital.temperature_f' in this_type:
            if self.misc.is_number(text):
                temperature = float(text)
                if (len(str(temperature)) > 2) and (temperature > 950) and (temperature < 1000):
                    temperature = str(temperature)[0:3]
                    temperature = float(temperature) / 100

                temperature = (temperature - 32) * (5 / 9)
                temperature = str(round(temperature, 2))
                field['value'][0]['value_name'] = temperature
                field['type'] = 'vital.temperature_c'
                field['value'][0]['type'] = field['type']
        elif 'patient.temperature_f' in this_type:
            if self.misc.is_number(text):
                temperature = float(text)
                if (len(str(temperature)) > 2) and (temperature > 950) and (temperature < 1000):
                    temperature = str(temperature)[0:3]
                    temperature = float(temperature) / 100

                temperature = (temperature - 32) * (5 / 9)
                temperature = str(round(temperature, 2))
                field['value'][0]['value_name'] = temperature
                field['type'] = 'patient.temperature_c'
                field['value'][0]['type'] = field['type']
        # feet --> meters
        elif 'vital.height_in' in this_type:
            text = self._remove_height_units(system_unit, 'height', text)
            if self.misc.is_number(text):
                height = float(text)
                height = height * const.INCHES_TO_CM
                height = str(round(height, 2))
                field['value'][0]['value_name'] = height + ' cm'
                field['type'] = 'vital.height_cm'
                field['value'][0]['type'] = field['type']
                
        elif 'patient.height_in' in this_type:
            text = self._remove_height_units(system_unit, 'height', text)
            if self.misc.is_number(text):
                height = float(text)
                height = height * const.INCHES_TO_CM
                height = str(round(height, 2))
                field['value'][0]['value_name'] = height + ' cm'
                field['type'] = 'patient.height_cm'
                field['value'][0]['type'] = field['type']

    # ------------------------------------------------------

    def _normalize_english(self, system_unit, field):
        text = field['value'][0]['value_name'].lower()
        this_type = field['value'][0]['type']
        if this_type == 'vital.weight_kg':
            text = self._remove_height_units(system_unit, 'weight', text)
            if self.misc.is_number(text):
                wt = float(text)
                wt *= const.KG_TO_LBS
                wt = str(round(wt, 2))
                field['value'][0]['value_name'] = wt
                field['type'] = 'vital.weight_lbs'
                field['value'][0]['type'] = field['type']
        elif this_type == 'patient.weight_kg':
            text = self._remove_height_units(system_unit, 'weight', text)
            if self.misc.is_number(text):
                wt = float(text)
                wt *= const.KG_TO_LBS
                wt = str(round(wt, 2))
                field['value'][0]['value_name'] = wt
                field['type'] = 'patient.weight_lbs'
                field['value'][0]['type'] = field['type']
        # celsius --> fahrenheit
        elif 'vital.temperature_c' in this_type:
            if self.misc.is_number(text):
                temperature = float(text)
                if (len(str(temperature)) > 2) and (temperature > 360) and (temperature < 386):
                    temperature = str(temperature)[0:3]
                    temperature = float(temperature) / 100

                temperature = (temperature * (9 / 5)) + 32
                temperature = str(round(temperature, 2))
                field['value'][0]['value_name'] = temperature
                field['type'] = 'vital.temperature_f'
                field['value'][0]['type'] = field['type']
        elif 'patient.temperature_c' in this_type:
            if self.misc.is_number(text):
                temperature = float(text)
                if (len(str(temperature)) > 2) and (temperature > 360) and (temperature < 386):
                    temperature = str(temperature)[0:3]
                    temperature = float(temperature) / 100

                temperature = (temperature * (9 / 5)) + 32
                temperature = str(round(temperature, 2))
                field['value'][0]['value_name'] = temperature
                field['type'] = 'patient.temperature_f'
                field['value'][0]['type'] = field['type']
        # meters --> feet
        elif 'vital.height_cm' in this_type:
            text = self._remove_height_units(system_unit, 'height', text)
            if self.misc.is_number(text):
                height = float(text)
                height = height * const.CM_TO_INCHES
                height = str(round(height, 2))
                field['value'][0]['value_name'] = height + ' in'
                field['type'] = 'vital.height_in'
                field['value'][0]['type'] = field['type']
        
        elif 'patient.height_cm' in this_type:
            text = self._remove_height_units(system_unit, 'height', text)
            if self.misc.is_number(text):
                height = float(text)
                height = height * const.CM_TO_INCHES
                height = str(round(height, 2))
                field['value'][0]['value_name'] = height + ' in'
                field['type'] = 'patient.height_in'
                field['value'][0]['type'] = field['type']

    # ------------------------------------------------------

    def _remove_height_units(self, system_unit: str, vital_type: str, text: str) -> str:
        lst = self.corrigible_units[system_unit.lower()][vital_type]
        text = text.lower()
        for x in lst:
            text = text.replace(x, '')
        text = re.sub(' +', ' ', text)
        return text.strip()

    # ------------------------------------------------------

    def _get_corrigible_units(self):
        self.corrigible_units = {
            'english': {
                "height": ['cm', 'cms'],
                "weight": ['kilograms', 'kilogram', 'kg', 'grams', 'gram', 'gm', 'g']
            },
            'metric': {
                "height": ['inches', 'inche', 'inch', 'in'],
                "weight": ['pounds', 'pound', 'lbs', 'lb']
            }
        }

    # ------------------------------------------------------

    def clean_and_validate_date_call(self, retrieved_data):
        self.initialize_states()
        self._validate_input_json(retrieved_data)

    # ------------------------------------------------------

    def _load_json(self, input_dir, filename):
        # noinspection PyBroadException
        try:
            with open(input_dir + filename) as f:
                data = json.load(f)
                if data is None:
                    return {}
                return data
        except Exception as _ex:
            return {}

            # ------------------------------------------------------

    def _collect_all_schedules(self):
        input_dir = '../input/'
        schedules = []
        with open(input_dir + 'inputs.json') as f:
            all_files = json.load(f)
        for _, these_files in all_files.items():
            claims = these_files.get('claims', {})
            for claim in claims:
                scp = claim['flayout_output_document'].get('structured_claim_processing', '')
                arr = scp.split(const.fSlash)
                if len(arr) < 2:
                    continue
                schedules.append(arr[-1])

        schedules = iterators.remove_redundant_items_from_ordered_list(schedules)
        all_objects = []
        for schedule in schedules:
            data = self._load_json(input_dir, schedule)
            results = data.get('result', [])
            for result in results:
                all_objects.append(result)
        return {"result": all_objects}

    # ------------------------------------------------------
    
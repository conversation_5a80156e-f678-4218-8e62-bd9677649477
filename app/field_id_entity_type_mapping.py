import titles as Title
import constants as const

class ENTITY_ROLE:
    MOTHER = 1
    CHILD = 2
    REQUIRED_CHILD = 3
    SHARED = 4


# ----------------------------------------------------------------------------------------------------------------------
def get_schema_id(card_name: str):
    return card_name.lower().replace(' ', '_')


FIELD_ID_BLUEPRINTS = {

    "laboratory_results": {
        "o.name": get_schema_id(Title.LABORATORY_RESULTS) + "." + "name",
        "o.value": get_schema_id(Title.LABORATORY_RESULTS) + "." + "value",
        "o.rating": get_schema_id(Title.LABORATORY_RESULTS) + "." + "rating",
        "o.ref_low": get_schema_id(Title.LABORATORY_RESULTS) + "." + "low",
        "o.ref_high": get_schema_id(Title.LABORATORY_RESULTS) + "." + "high",
        "o.ref": get_schema_id(Title.LABORATORY_RESULTS) + "." + "descriptive_reference",
        "o.unit": get_schema_id(Title.LABORATORY_RESULTS) + "." + "units",
        "lab.name": get_schema_id(Title.LABORATORY_RESULTS) + "." + "name",
        "lab.value": get_schema_id(Title.LABORATORY_RESULTS) + "." + "value",
        "lab.rating": get_schema_id(Title.LABORATORY_RESULTS) + "." + "rating",
        "lab.ref_low": get_schema_id(Title.LABORATORY_RESULTS) + "." + "low",
        "lab.ref_high": get_schema_id(Title.LABORATORY_RESULTS) + "." + "high",
        "lab.ref": get_schema_id(Title.LABORATORY_RESULTS) + "." + "descriptive_reference",
        "lab.unit": get_schema_id(Title.LABORATORY_RESULTS) + "." + "units",
        "lab.panel": get_schema_id(Title.LABORATORY_RESULTS) + "." + "panel",
        "lab.specimen": get_schema_id(Title.LABORATORY_RESULTS) + "." + "specimen",
        "specimen": get_schema_id(Title.LABORATORY_RESULTS) + "." + "specimen",
        const.GENERATED_ENTITY + "specimen": get_schema_id(Title.LABORATORY_RESULTS) + "." + "specimen",
        "o.issued": get_schema_id(Title.LABORATORY_RESULTS) + "." + "date",
        "lab.collected": get_schema_id(Title.LABORATORY_RESULTS) + "." + "date",
        "lab.received": get_schema_id(Title.LABORATORY_RESULTS) + "." + "date",
        "lab.issued": get_schema_id(Title.LABORATORY_RESULTS) + "." + "date",
        "pathology.collected": get_schema_id(Title.LABORATORY_RESULTS) + "." + "date",
        "pathology.received": get_schema_id(Title.LABORATORY_RESULTS) + "." + "date",
        "pathology.issued": get_schema_id(Title.LABORATORY_RESULTS) + "." + "date",
        "encounter.date": get_schema_id(Title.LABORATORY_RESULTS) + "." + "date",
        const.GENERATED_ENTITY + "o.issued": get_schema_id(Title.LABORATORY_RESULTS) + "." + "date",
        const.GENERATED_ENTITY + "lab.collected": get_schema_id(Title.LABORATORY_RESULTS) + "." + "date",
        const.GENERATED_ENTITY + "lab.received": get_schema_id(Title.LABORATORY_RESULTS) + "." + "date",
        const.GENERATED_ENTITY + "lab.issued": get_schema_id(Title.LABORATORY_RESULTS) + "." + "date",
        const.GENERATED_ENTITY + "pathology.collected": get_schema_id(Title.LABORATORY_RESULTS) + "." + "date",
        const.GENERATED_ENTITY + "pathology.received": get_schema_id(Title.LABORATORY_RESULTS) + "." + "date",
        const.GENERATED_ENTITY + "pathology.issued": get_schema_id(Title.LABORATORY_RESULTS) + "." + "date",
        const.GENERATED_ENTITY + "encounter.date": get_schema_id(Title.LABORATORY_RESULTS) + "." + "date",
        "lab.performer": get_schema_id(Title.LABORATORY_RESULTS) + "." + "laboratory",
        "loinc": get_schema_id(Title.LABORATORY_RESULTS) + "." + "loinc",
        const.GENERATED_ENTITY + "loinc": get_schema_id(Title.LABORATORY_RESULTS) + "." + "loinc",
        "fasting.date_time": get_schema_id(Title.LABORATORY_RESULTS) + "." + "fasting",
        const.GENERATED_ENTITY + "fasting.date_time": get_schema_id(Title.LABORATORY_RESULTS) + "." + "fasting",
        "fasting.in_mins": get_schema_id(Title.LABORATORY_RESULTS) + "." + "fasting",
        const.GENERATED_ENTITY + "fasting.in_mins": get_schema_id(Title.LABORATORY_RESULTS) + "." + "fasting",
        "fasting.in_hours": get_schema_id(Title.LABORATORY_RESULTS) + "." + "fasting",
        const.GENERATED_ENTITY + "fasting.in_hours": get_schema_id(Title.LABORATORY_RESULTS) + "." + "fasting",
        "fasting.status.yes": get_schema_id(Title.LABORATORY_RESULTS) + "." + "fasting",
        "merged_fasting": get_schema_id(Title.LABORATORY_RESULTS) + "." + "fasting",
        const.GENERATED_ENTITY + "panel_loinc": get_schema_id(Title.LABORATORY_RESULTS) + "." + "panel_loinc",
        const.GENERATED_ENTITY + "impairment": get_schema_id(Title.LABORATORY_RESULTS) + "." + "impairment"
        },

    "abnormal_observations": {
        "o.name": get_schema_id(Title.ABNORMAL_OBSERVATIONS) + "." + "name",
        "o.value": get_schema_id(Title.ABNORMAL_OBSERVATIONS) + "." + "value",
        "o.rating": get_schema_id(Title.ABNORMAL_OBSERVATIONS) + "." + "rating",
        "o.ref_low": get_schema_id(Title.ABNORMAL_OBSERVATIONS) + "." + "low",
        "o.ref_high": get_schema_id(Title.ABNORMAL_OBSERVATIONS) + "." + "high",
        "o.ref": get_schema_id(Title.ABNORMAL_OBSERVATIONS) + "." + "descriptive_reference",
        "o.unit": get_schema_id(Title.ABNORMAL_OBSERVATIONS) + "." + "units",
        "lab.name": get_schema_id(Title.ABNORMAL_OBSERVATIONS) + "." + "name",
        "lab.value": get_schema_id(Title.ABNORMAL_OBSERVATIONS) + "." + "value",
        "lab.rating": get_schema_id(Title.ABNORMAL_OBSERVATIONS) + "." + "rating",
        "lab.ref_low": get_schema_id(Title.ABNORMAL_OBSERVATIONS) + "." + "low",
        "lab.ref_high": get_schema_id(Title.ABNORMAL_OBSERVATIONS) + "." + "high",
        "lab.ref": get_schema_id(Title.ABNORMAL_OBSERVATIONS) + "." + "descriptive_reference",
        "lab.unit": get_schema_id(Title.ABNORMAL_OBSERVATIONS) + "." + "units",
        "lab.panel": get_schema_id(Title.ABNORMAL_OBSERVATIONS) + "." + "panel",
        "lab.specimen": get_schema_id(Title.ABNORMAL_OBSERVATIONS) + "." + "specimen",
        "specimen": get_schema_id(Title.ABNORMAL_OBSERVATIONS) + "." + "specimen",
        const.GENERATED_ENTITY + "specimen": get_schema_id(Title.ABNORMAL_OBSERVATIONS) + "." + "specimen",
        "o.issued": get_schema_id(Title.ABNORMAL_OBSERVATIONS) + "." + "date",
        "lab.performer": get_schema_id(Title.ABNORMAL_OBSERVATIONS) + "." + "laboratory",
        "loinc": get_schema_id(Title.ABNORMAL_OBSERVATIONS) + "." + "loinc",
        const.GENERATED_ENTITY + "loinc": get_schema_id(Title.ABNORMAL_OBSERVATIONS) + "." + "loinc"
        },

    "physical_exams": {
        "pe.name": get_schema_id(Title.PHYSICAL_EXAMS) + "." + "site",
        "pe.body": get_schema_id(Title.PHYSICAL_EXAMS) + "." + "site",
        "ros.system": get_schema_id(Title.PHYSICAL_EXAMS) + "." + "site",
        "pe.patient.value.positive": get_schema_id(Title.PHYSICAL_EXAMS) + "." + "observation",
        "pe.patient.value.abnormal": get_schema_id(Title.PHYSICAL_EXAMS) + "." + "observation",
        "pe.value.positive": get_schema_id(Title.PHYSICAL_EXAMS) + "." + "observation",
        "pe.value.abnormal": get_schema_id(Title.PHYSICAL_EXAMS) + "." + "observation",
        "ros.value.positive": get_schema_id(Title.PHYSICAL_EXAMS) + "." + "observation",
        "ros.value.abnormal": get_schema_id(Title.PHYSICAL_EXAMS) + "." + "observation",
        "encounter.date": get_schema_id(Title.PHYSICAL_EXAMS) + "." + "date"},

    "vital_signs": {
        "vital.date": get_schema_id(Title.VITAL_SIGNS) + "." + "date",
        "patient.vital.date": get_schema_id(Title.VITAL_SIGNS) + "." + "date",
        "patient.date": get_schema_id(Title.VITAL_SIGNS) + "." + "date",
        "encounter.date": get_schema_id(Title.VITAL_SIGNS) + "." + "date",
        "imaging.date": get_schema_id(Title.VITAL_SIGNS) + "." + "date",
        "lab.issued": get_schema_id(Title.VITAL_SIGNS) + "." + "date",
        "lab.collected": get_schema_id(Title.VITAL_SIGNS) + "." + "date",
        "lab.received": get_schema_id(Title.VITAL_SIGNS) + "." + "date",
        "vital.blood": get_schema_id(Title.VITAL_SIGNS) + "." + "blood_pressure",
        "patient.blood": get_schema_id(Title.VITAL_SIGNS) + "." + "blood_pressure",
        "insured_1.blood": get_schema_id(Title.VITAL_SIGNS) + "." + "blood_pressure",
        "insured_2.blood": get_schema_id(Title.VITAL_SIGNS) + "." + "blood_pressure",
        const.GENERATED_ENTITY + "patient.blood": get_schema_id(Title.VITAL_SIGNS) + "." + "blood_pressure",
        "patient.blood_pressure": get_schema_id(Title.VITAL_SIGNS) + "." + "blood_pressure",
        "insured_1.blood_pressure": get_schema_id(Title.VITAL_SIGNS) + "." + "blood_pressure",
        "insured_2.blood_pressure": get_schema_id(Title.VITAL_SIGNS) + "." + "blood_pressure",
        "vital.weight_lbs": get_schema_id(Title.VITAL_SIGNS) + "." + "weight",
        "patient.weight_lbs": get_schema_id(Title.VITAL_SIGNS) + "." + "weight",
        "insured_1.weight_lbs": get_schema_id(Title.VITAL_SIGNS) + "." + "weight",
        "vital.weight_kg": get_schema_id(Title.VITAL_SIGNS) + "." + "weight",
        "patient.weight_kg": get_schema_id(Title.VITAL_SIGNS) + "." + "weight",
        "insured_1.weight_kg": get_schema_id(Title.VITAL_SIGNS) + "." + "weight",
        "vital.height_in": get_schema_id(Title.VITAL_SIGNS) + "." + "height",
        "patient.height_in": get_schema_id(Title.VITAL_SIGNS) + "." + "height",
        "insured_1.height_in": get_schema_id(Title.VITAL_SIGNS) + "." + "height",
        "vital.height_cm": get_schema_id(Title.VITAL_SIGNS) + "." + "height",
        "patient.height_cm": get_schema_id(Title.VITAL_SIGNS) + "." + "height",
        "insured_1.height_cm": get_schema_id(Title.VITAL_SIGNS) + "." + "height",
        "vital.bmi": get_schema_id(Title.VITAL_SIGNS) + "." + "bmi",
        "patient.bmi": get_schema_id(Title.VITAL_SIGNS) + "." + "bmi",
        "vital.heart_rate": get_schema_id(Title.VITAL_SIGNS) + "." + "pulse",
        "patient.heart_rate": get_schema_id(Title.VITAL_SIGNS) + "." + "pulse",
        "insured_1.heart_rate": get_schema_id(Title.VITAL_SIGNS) + "." + "pulse",
        "insured_2.heart_rate": get_schema_id(Title.VITAL_SIGNS) + "." + "pulse",
        "vital.oxygen": get_schema_id(Title.VITAL_SIGNS) + "." + "oxygen",
        "patient.oxygen": get_schema_id(Title.VITAL_SIGNS) + "." + "oxygen",
        "vital.respiration": get_schema_id(Title.VITAL_SIGNS) + "." + "respiration",
        "patient.respiration": get_schema_id(Title.VITAL_SIGNS) + "." + "respiration",
        "vital.temperature_f": get_schema_id(Title.VITAL_SIGNS) + "." + "temperature",
        "patient.temperature_f": get_schema_id(Title.VITAL_SIGNS) + "." + "temperature",
        "vital.temperature_c": get_schema_id(Title.VITAL_SIGNS) + "." + "temperature",
        "patient.temperature_c": get_schema_id(Title.VITAL_SIGNS) + "." + "temperature"},

    "ccda_vitals": {
        "vital.type": get_schema_id(Title.CCDA_VITALS) + "." + "type",
        const.GENERATED_ENTITY + "vital.unit": get_schema_id(Title.CCDA_VITALS) + "." + "unit",
        const.GENERATED_ENTITY + "vital.loinc": get_schema_id(Title.CCDA_VITALS) + "." + "loinc",
        "vital.date": get_schema_id(Title.CCDA_VITALS) + "." + "date",
        "patient.vital.date": get_schema_id(Title.CCDA_VITALS) + "." + "date",
        "patient.date": get_schema_id(Title.CCDA_VITALS) + "." + "date",
        "encounter.date": get_schema_id(Title.CCDA_VITALS) + "." + "date",
        "imaging.date": get_schema_id(Title.CCDA_VITALS) + "." + "date",
        "lab.issued": get_schema_id(Title.CCDA_VITALS) + "." + "date",
        "lab.collected": get_schema_id(Title.CCDA_VITALS) + "." + "date",
        "lab.received": get_schema_id(Title.CCDA_VITALS) + "." + "date",
        "vital.blood": get_schema_id(Title.CCDA_VITALS) + "." + "value",
        "patient.blood": get_schema_id(Title.CCDA_VITALS) + "." + "value",
        "vital.systolic": get_schema_id(Title.CCDA_VITALS) + "." + "value",
        "patient.systolic": get_schema_id(Title.CCDA_VITALS) + "." + "value",
        "insured_1.systolic": get_schema_id(Title.CCDA_VITALS) + "." + "value",
        "insured_2.systolic": get_schema_id(Title.CCDA_VITALS) + "." + "value",
        const.GENERATED_ENTITY + "vital.systolic": get_schema_id(Title.CCDA_VITALS) + "." + "value",
        const.GENERATED_ENTITY + "patient.systolic": get_schema_id(Title.CCDA_VITALS) + "." + "value",
        const.GENERATED_ENTITY + "insured_1.systolic": get_schema_id(Title.CCDA_VITALS) + "." + "value",
        const.GENERATED_ENTITY + "insured_2.systolic": get_schema_id(Title.CCDA_VITALS) + "." + "value",
        "vital.diastolic": get_schema_id(Title.CCDA_VITALS) + "." + "value",
        "patient.diastolic": get_schema_id(Title.CCDA_VITALS) + "." + "value",
        "insured_1.diastolic": get_schema_id(Title.CCDA_VITALS) + "." + "value",
        "insured_2.diastolic": get_schema_id(Title.CCDA_VITALS) + "." + "value",
        const.GENERATED_ENTITY + "vital.diastolic": get_schema_id(Title.CCDA_VITALS) + "." + "value",
        const.GENERATED_ENTITY + "patient.diastolic": get_schema_id(Title.CCDA_VITALS) + "." + "value",
        const.GENERATED_ENTITY + "insured_1.diastolic": get_schema_id(Title.CCDA_VITALS) + "." + "value",
        const.GENERATED_ENTITY + "insured_2.diastolic": get_schema_id(Title.CCDA_VITALS) + "." + "value",
        "vital.weight_lbs": get_schema_id(Title.CCDA_VITALS) + "." + "value",
        "patient.weight_lbs": get_schema_id(Title.CCDA_VITALS) + "." + "value",
        "insured_1.weight_lbs": get_schema_id(Title.CCDA_VITALS) + "." + "value",
        "insured_2.weight_lbs": get_schema_id(Title.CCDA_VITALS) + "." + "value",
        "vital.weight_kg": get_schema_id(Title.CCDA_VITALS) + "." + "value",
        "patient.weight_kg": get_schema_id(Title.CCDA_VITALS) + "." + "value",
        "insured_1.weight_kg": get_schema_id(Title.CCDA_VITALS) + "." + "value",
        "insured_2.weight_kg": get_schema_id(Title.CCDA_VITALS) + "." + "value",
        "vital.height_in": get_schema_id(Title.CCDA_VITALS) + "." + "value",
        "patient.height_in": get_schema_id(Title.CCDA_VITALS) + "." + "value",
        "insured_1.height_in": get_schema_id(Title.CCDA_VITALS) + "." + "value",
        "insured_2.height_in": get_schema_id(Title.CCDA_VITALS) + "." + "value",
        "vital.height_cm": get_schema_id(Title.CCDA_VITALS) + "." + "value",
        "patient.height_cm": get_schema_id(Title.CCDA_VITALS) + "." + "value",
        "insured_1.height_cm": get_schema_id(Title.CCDA_VITALS) + "." + "value",
        "insured_2.height_cm": get_schema_id(Title.CCDA_VITALS) + "." + "value",
        "vital.bmi": get_schema_id(Title.CCDA_VITALS) + "." + "value",
        "patient.bmi": get_schema_id(Title.CCDA_VITALS) + "." + "value",
        "insured_1.bmi": get_schema_id(Title.CCDA_VITALS) + "." + "value",
        "insured_2.bmi": get_schema_id(Title.CCDA_VITALS) + "." + "value",
        "vital.heart_rate": get_schema_id(Title.CCDA_VITALS) + "." + "value",
        "patient.heart_rate": get_schema_id(Title.CCDA_VITALS) + "." + "value",
        "insured_1.heart_rate": get_schema_id(Title.CCDA_VITALS) + "." + "value",
        "insured_2.heart_rate": get_schema_id(Title.CCDA_VITALS) + "." + "value",
        "vital.oxygen": get_schema_id(Title.CCDA_VITALS) + "." + "value",
        "patient.oxygen": get_schema_id(Title.CCDA_VITALS) + "." + "value",
        "vital.respiration": get_schema_id(Title.CCDA_VITALS) + "." + "value",
        "patient.respiration": get_schema_id(Title.CCDA_VITALS) + "." + "value",
        "vital.temperature_f": get_schema_id(Title.CCDA_VITALS) + "." + "value",
        "patient.temperature_f": get_schema_id(Title.CCDA_VITALS) + "." + "value",
        "vital.temperature_c": get_schema_id(Title.CCDA_VITALS) + "." + "value",
        "patient.temperature_c": get_schema_id(Title.CCDA_VITALS) + "." + "value"},

    "diagnoses": {
        const.GENERATED_ENTITY + "diagnosis_condition": get_schema_id(Title.DIAGNOSIS) + "." + "condition",
        const.GENERATED_ENTITY + "icd_description": get_schema_id(Title.DIAGNOSIS) + "." + "description",
        "icd_description": get_schema_id(Title.DIAGNOSIS) + "." + "description",
        "diagnosis.principal": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "diagnosis.primary": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "diagnosis.secondary": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "assessment.diag": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "death.cause": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "medical.condition": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "history.diag": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "diagnosis.history": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "pathology.diagnosis": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "patient.covid.positive": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "insured_1.covid.positive": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "insured_2.covid.positive": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "patient.medical_disorder.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "insured_1.medical_disorder.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "insured_2.medical_disorder.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "patient.heart.disorder.risk.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "insured_1.heart.disorder.risk.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "insured_2.heart.disorder.risk.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "patient.neoplasm.risk.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "insured_1.neoplasm.risk.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "insured_2.neoplasm.risk.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "patient.mental.disorder.risk.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "insured_1.mental.disorder.risk.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "insured_2.mental.disorder.risk.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "patient.brain_hemorrhage.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "insured_1.brain_hemorrhage.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "insured_2.brain_hemorrhage.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "patient.neurological_disorder.risk.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "insured_1.neurological_disorder.risk.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "insured_2.neurological_disorder.risk.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "patient.immune.disorder.risk.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "insured_1.immune.disorder.risk.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "insured_2.immune.disorder.risk.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "patient.std.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "insured_1.std.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "insured_2.std.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "patient.muscleandbone.disorder.risk.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "insured_1.muscleandbone.disorder.risk.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "insured_2.muscleandbone.disorder.risk.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "patient.hepatitis.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "insured_1.hepatitis.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "insured_2.hepatitis.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "patient.endocrine.disorder.risk.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "insured_1.endocrine.disorder.risk.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "insured_2.endocrine.disorder.risk.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "patient.circulatory.disorder.risk.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "insured_1.circulatory.disorder.risk.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "insured_2.circulatory.disorder.risk.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "patient.blood.disorder.risk.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "insured_1.blood.disorder.risk.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "insured_2.blood.disorder.risk.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "patient.respiratory.disorder.risk.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "insured_1.respiratory.disorder.risk.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "insured_2.respiratory.disorder.risk.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "patient.ent_disorder.risk.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "insured_1.ent_disorder.risk.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "insured_2.ent_disorder.risk.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "patient.ent.nose_disorder.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "insured_1.ent.nose_disorder.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "insured_2.ent.nose_disorder.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "patient.ent.ear_disorder.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "insured_1.ent.ear_disorder.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "insured_2.ent.ear_disorder.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "patient.ent.throat_disorder.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "insured_1.ent.throat_disorder.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "insured_2.ent.throat_disorder.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "patient.ent.thyroid_disorder.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "insured_1.ent.thyroid_disorder.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "insured_2.ent.thyroid_disorder.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "patient.lymphatic.disorder.risk.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "insured_1.lymphatic.disorder.risk.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "insured_2.lymphatic.disorder.risk.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "patient.oedema_disease.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "insured_1.oedema_disease.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "insured_2.oedema_disease.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "patient.digestive.disorder.risk.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "insured_1.digestive.disorder.risk.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "insured_2.digestive.disorder.risk.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "patient.liver_disorder.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "insured_1.liver_disorder.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "insured_2.liver_disorder.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "patient.kidney.disorder.risk.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "insured_1.kidney.disorder.risk.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "insured_2.kidney.disorder.risk.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "patient.breast_disorder.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "insured_1.breast_disorder.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "insured_2.breast_disorder.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "patient.cyst.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "insured_1.cyst.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "insured_2.cyst.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "patient.skin_diseases.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "insured_1.skin_diseases.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "insured_2.skin_diseases.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "patient.hormonal_disorder.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "insured_1.hormonal_disorder.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "insured_2.hormonal_disorder.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "patient.female.reproductive.risk.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "insured_1.female.reproductive.risk.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "insured_2.female.reproductive.risk.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "patient.fever.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "insured_1.fever.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "insured_2.fever.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "patient.tuberculosis.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "insured_1.tuberculosis.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "insured_2.tuberculosis.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "patient.physical.disorder.risk.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "insured_1.physical.disorder.risk.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "insured_2.physical.disorder.risk.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "patient.allergies.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "insured_1.allergies.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "insured_2.allergies.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "patient.endocrine_disorder.risk.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "insured_1.endocrine_disorder.risk.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "insured_2.endocrine_disorder.risk.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "patient.congenital.disorder.risk.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "insured_1.congenital.disorder.risk.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "insured_2.congenital.disorder.risk.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "patient.nervous.disorder.risk.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "insured_1.nervous.disorder.risk.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "insured_2.nervous.disorder.risk.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "patient.gastrointestinal.disorder.risk.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "insured_1.gastrointestinal.disorder.risk.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "insured_2.gastrointestinal.disorder.risk.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "patient.chronic_disease.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "insured_1.chronic_disease.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "insured_2.chronic_disease.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "patient.urinary_disorder.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "insured_1.urinary_disorder.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "insured_2.urinary_disorder.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "patient.heart_disease.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "insured_1.heart_disease.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "insured_2.heart_disease.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "patient.cancer_disease.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "insured_1.cancer_disease.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "insured_2.cancer_disease.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "patient.mental_disorder.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "insured_1.mental_disorder.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "insured_2.mental_disorder.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "patient.hiv.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "insured_1.hiv.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "insured_2.hiv.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "patient.muscular_pain.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "insured_1.muscular_pain.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "insured_2.muscular_pain.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "patient.diabetes.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "insured_1.diabetes.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "insured_2.diabetes.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "patient.high_blood_pressure.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "insured_1.high_blood_pressure.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "insured_2.high_blood_pressure.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "patient.blood_disorder.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "insured_1.blood_disorder.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "insured_2.blood_disorder.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "patient.breathing_disorder.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "insured_1.breathing_disorder.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "insured_2.breathing_disorder.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "patient.ent.eyes_disorder.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "insured_1.ent.eyes_disorder.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "insured_2.ent.eyes_disorder.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "patient.lymphatic_system_disease.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "insured_1.lymphatic_system_disease.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "insured_2.lymphatic_system_disease.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "patient.stomach_disorder.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "insured_1.stomach_disorder.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "insured_2.stomach_disorder.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "patient.kidney_disorder.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "insured_1.kidney_disorder.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "insured_2.kidney_disorder.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "patient.reproductive_organs_diseases.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "insured_1.reproductive_organs_diseases.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "insured_2.reproductive_organs_diseases.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "patient.physical_disorder.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "insured_1.physical_disorder.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "insured_2.physical_disorder.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "patient.endocrine_glands_disorder.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "insured_1.endocrine_glands_disorder.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "insured_2.endocrine_glands_disorder.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "patient.nervous_system_disorder.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "insured_1.nervous_system_disorder.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "insured_2.nervous_system_disorder.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "patient.gastrointestinal_disorder.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "insured_1.gastrointestinal_disorder.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "insured_2.gastrointestinal_disorder.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "insured_1.heart_disorder.risk.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "insured_2.heart_disorder.risk.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "patient.heart_disorder.risk.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "insured_1.neoplasm_risk.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "insured_2.neoplasm_risk.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "patient.neoplasm_risk.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "insured_1.mental_disorder_risk.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "insured_2.mental_disorder_risk.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "patient.mental_disorder_risk.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "insured_1.immune_disorder_risk.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "insured_2.immune_disorder_risk.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "patient.immune_disorder_risk.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "insured_1.muscle_and_bone.disorder.risk.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "insured_2.muscle_and_bone.disorder.risk.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "patient.muscle_and_bone.disorder.risk.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "insured_1.circulatory_disorder_risk.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "insured_2.circulatory_disorder_risk.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "patient.circulatory_disorder_risk.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "insured_1.blood_disorder.risk.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "insured_2.blood_disorder.risk.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "patient.blood_disorder.risk.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "insured_1.respiratory_disorder_risk.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "insured_2.respiratory_disorder_risk.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "patient.respiratory_disorder_risk.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "insured_1.ent_disorder_risk.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "insured_2.ent_disorder_risk.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "patient.ent_disorder_risk.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "insured_1.lymphatic_disorder_risk.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "insured_2.lymphatic_disorder_risk.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "patient.lymphatic_disorder_risk.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "insured_1.digestive_disorder_risk.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "insured_2.digestive_disorder_risk.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "patient.digestive_disorder_risk.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "insured_1.kidney_disorder.risk.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "insured_2.kidney_disorder.risk.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "patient.kidney_disorder.risk.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "insured_1.medical_test.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "insured_2.medical_test.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "patient.medical_test.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "insured_1.medication.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "insured_2.medication.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "patient.medication.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "insured_1.hospitalization.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "insured_2.hospitalization.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "patient.hospitalization.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "insured_1.recent.hospitalization.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "insured_2.recent.hospitalization.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "patient.recent.hospitalization.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "insured_1.pregnant.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "insured_2.pregnant.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "patient.pregnant.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "insured_1.female_reproductive_risk.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "insured_2.female_reproductive_risk.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "patient.female_reproductive_risk.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "insured_1.physical_disability.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "insured_2.physical_disability.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "patient.physical_disability.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "insured_1.nervous_disorder_risk.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "insured_2.nervous_disorder_risk.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "patient.nervous_disorder_risk.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "insured_1.gastrointestinal_disorder.risk.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "insured_2.gastrointestinal_disorder.risk.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "patient.gastrointestinal_disorder.risk.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "insured_1.endocrine_disorder_risk.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "insured_2.endocrine_disorder_risk.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "patient.endocrine_disorder_risk.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "insured_1.congenital_disorder_risk.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "insured_2.congenital_disorder_risk.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "patient.congenital_disorder_risk.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "insured_1.other_symptoms.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "insured_2.other_symptoms.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "patient.other_symptoms.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "insured_1.current.treatment.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "insured_2.current.treatment.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "patient.current.treatment.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "insured_1.history.treatment.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "insured_2.history.treatment.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "patient.history.treatment.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "insured_1.consultation.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "insured_2.consultation.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "patient.consultation.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "insured_1.plan.consultation.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "insured_2.plan.consultation.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "patient.plan.consultation.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "insured_1.hepatobiliary_risk.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "insured_2.hepatobiliary_risk.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "patient.hepatobiliary_risk.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "insured_1.gastrointestinal_disorder_risk.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "insured_2.gastrointestinal_disorder_risk.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "patient.gastrointestinal_disorder_risk.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "patient.respiratory_disorder_risk.details": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "insured_1.respiratory_disorder_risk.details": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "insured_2.respiratory_disorder_risk.details": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "patient.neoplasm_risk.details": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "insured_1.neoplasm_risk.details": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "insured_2.neoplasm_risk.details": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "patient.mental_disorder_risk.details": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "insured_1.mental_disorder_risk.details": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "insured_2.mental_disorder_risk.details": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "patient.blood_pressure_disorder.risk.details": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "insured_1.blood_pressure_disorder.risk.details": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "insured_2.blood_pressure_disorder.risk.details": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "patient.muscle_and_bone_disorder_risk.details": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "insured_1.muscle_and_bone_disorder_risk.details": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "insured_2.muscle_and_bone_disorder_risk.details": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "patient.digestive_disorder_risk.details": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "insured_1.digestive_disorder_risk.details": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "insured_2.digestive_disorder_risk.details": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "patient.muscle_and_bone_disorder_risk.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "insured_1.muscle_and_bone_disorder_risk.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "insured_2.muscle_and_bone_disorder_risk.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "patient.blood_pressure_disorder.risk.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "insured_1.blood_pressure_disorder.risk.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "insured_2.blood_pressure_disorder.risk.yes": get_schema_id(Title.DIAGNOSIS) + "." + "diagnosis",
        "diagnoses.type": get_schema_id(Title.DIAGNOSIS) + "." + "type",
        const.GENERATED_ENTITY + "diagnoses.type": get_schema_id(Title.DIAGNOSIS) + "." + "type",
        "icd10": get_schema_id(Title.DIAGNOSIS) + "." + "icd_10",
        const.GENERATED_ENTITY + "icd10": get_schema_id(Title.DIAGNOSIS) + "." + "icd_10",
        "icd9": get_schema_id(Title.DIAGNOSIS) + "." + "icd_10",
        "diag.date": get_schema_id(Title.DIAGNOSIS) + "." + "diag_date",
        "diagnosis.date": get_schema_id(Title.DIAGNOSIS) + "." + "diag_date",
        "diagnosis.history.date": get_schema_id(Title.DIAGNOSIS) + "." + "diag_date",
        "assessment.date": get_schema_id(Title.DIAGNOSIS) + "." + "date",
        "vital.date": get_schema_id(Title.DIAGNOSIS) + "." + "date",
        "patient.date": get_schema_id(Title.DIAGNOSIS) + "." + "date",
        "encounter.date": get_schema_id(Title.DIAGNOSIS) + "." + "date",
        "pathology.date": get_schema_id(Title.DIAGNOSIS) + "." + "date",
        "imaging.date": get_schema_id(Title.DIAGNOSIS) + "." + "date",
        "patient.vital.date": get_schema_id(Title.DIAGNOSIS) + "." + "date",
        const.GENERATED_ENTITY + "icd_chapter": get_schema_id(Title.DIAGNOSIS) + "." + "chapter",
        const.GENERATED_ENTITY + "icd_section": get_schema_id(Title.DIAGNOSIS) + "." + "section",
        const.GENERATED_ENTITY + "impairment": get_schema_id(Title.DIAGNOSIS) + "." + "impairment",
        const.GENERATED_ENTITY + "snomed": get_schema_id(Title.DIAGNOSIS) + "." + "snomed",
        },

    "claimed_injury_details": {
        "injury.type": get_schema_id(Title.CLAIMED_INJURY_DETAILS) + "." + "claimed_injury",
        "injury.cause": get_schema_id(Title.CLAIMED_INJURY_DETAILS) + "." + "claimed_injury",
        "history.injury.type": get_schema_id(Title.CLAIMED_INJURY_DETAILS) + "." + "claimed_injury",
        "injury.history.type": get_schema_id(Title.CLAIMED_INJURY_DETAILS) + "." + "claimed_injury",
        "history.injury.cause": get_schema_id(Title.CLAIMED_INJURY_DETAILS) + "." + "claimed_injury",
        "injury.history.cause": get_schema_id(Title.CLAIMED_INJURY_DETAILS) + "." + "claimed_injury",
        "icd10": get_schema_id(Title.CLAIMED_INJURY_DETAILS) + "." + "icd_10",
        const.GENERATED_ENTITY + "icd10": get_schema_id(Title.CLAIMED_INJURY_DETAILS) + "." + "icd_10",
        "icd9": get_schema_id(Title.CLAIMED_INJURY_DETAILS) + "." + "icd_10",
        "injury.date": get_schema_id(Title.CLAIMED_INJURY_DETAILS) + "." + "date",
        "history.injury.date": get_schema_id(Title.CLAIMED_INJURY_DETAILS) + "." + "date",
        "injury.history.date": get_schema_id(Title.CLAIMED_INJURY_DETAILS) + "." + "date",
        const.GENERATED_ENTITY + "icd_chapter": get_schema_id(Title.DIAGNOSIS) + "." + "chapter",
        const.SNOMED: get_schema_id(Title.DIAGNOSIS) + "." + const.SNOMED,
        "snomed": get_schema_id(Title.DIAGNOSIS) + "." + "snomed",
        },

    "reason_of_visit": {
        "encounter.date": get_schema_id(Title.REASON_OF_VISIT) + "." + "date",
        "assessment.plan": get_schema_id(Title.REASON_OF_VISIT) + "." + "plan",
        "history.surgery": get_schema_id(Title.REASON_OF_VISIT) + "." + "reason",
        "surgery.history": get_schema_id(Title.REASON_OF_VISIT) + "." + "reason",
        "hpi.symptom.positive": get_schema_id(Title.REASON_OF_VISIT) + "." + "reason",
        "hpi.patient.symptom.positive": get_schema_id(Title.REASON_OF_VISIT) + "." + "reason",
        "merged_reason": get_schema_id(Title.REASON_OF_VISIT) + "." + "reason",
        "history.diag": get_schema_id(Title.REASON_OF_VISIT) + "." + "reason",
        "diagnosis.history": get_schema_id(Title.REASON_OF_VISIT) + "." + "reason",
        "assessment.diag": get_schema_id(Title.REASON_OF_VISIT) + "." + "reason",
        "assessment": get_schema_id(Title.REASON_OF_VISIT) + "." + "reason",
        "imaging.exam": get_schema_id(Title.REASON_OF_VISIT) + "." + "reason",
        "imaging.finding": get_schema_id(Title.REASON_OF_VISIT) + "." + "reason",
        "imaging.impression": get_schema_id(Title.REASON_OF_VISIT) + "." + "reason",
        "surgery.procedure": get_schema_id(Title.REASON_OF_VISIT) + "." + "reason",
        "imaging.procedure": get_schema_id(Title.REASON_OF_VISIT) + "." + "reason",
        "assessment.phytherapy": get_schema_id(Title.REASON_OF_VISIT) + "." + "reason",
        "pathology.diagnosis": get_schema_id(Title.REASON_OF_VISIT) + "." + "reason",
        "pathology.finding": get_schema_id(Title.REASON_OF_VISIT) + "." + "reason",
        "pathology.procedure": get_schema_id(Title.REASON_OF_VISIT) + "." + "reason",
        "encounter.conclusion": get_schema_id(Title.REASON_OF_VISIT) + "." + "reason",
        "reason": get_schema_id(Title.REASON_OF_VISIT) + "." + "reason",
        "lab.collected": get_schema_id(Title.REASON_OF_VISIT) + "." + "date",
        "o.collected": get_schema_id(Title.REASON_OF_VISIT) + "." + "date",
        "lab.received": get_schema_id(Title.REASON_OF_VISIT) + "." + "date",
        "o.received": get_schema_id(Title.REASON_OF_VISIT) + "." + "date",
        "o.issued": get_schema_id(Title.REASON_OF_VISIT) + "." + "date",
        "lab.issued": get_schema_id(Title.REASON_OF_VISIT) + "." + "date",
		"diag.date": get_schema_id(Title.REASON_OF_VISIT) + "." + "date",
		"diagnosis.date": get_schema_id(Title.REASON_OF_VISIT) + "." + "date",
		"assessment.date": get_schema_id(Title.REASON_OF_VISIT) + "." + "date",
		"diagnosis.history.date": get_schema_id(Title.REASON_OF_VISIT) + "." + "date",
		"pathology.date": get_schema_id(Title.REASON_OF_VISIT) + "." + "date",
		"surgery.date": get_schema_id(Title.REASON_OF_VISIT) + "." + "date",
		"imaging.date": get_schema_id(Title.REASON_OF_VISIT) + "." + "date",
        "hpi.reason": get_schema_id(Title.REASON_OF_VISIT) + "." + "reason",
        "plan.lab": get_schema_id(Title.REASON_OF_VISIT) + "." + "plan",
        "plan.medication": get_schema_id(Title.REASON_OF_VISIT) + "." + "plan",
        "plan.radiology": get_schema_id(Title.REASON_OF_VISIT) + "." + "plan",
        "plan.evaluation": get_schema_id(Title.REASON_OF_VISIT) + "." + "plan",
        "plan.surgery": get_schema_id(Title.REASON_OF_VISIT) + "." + "plan",
		"plan.phytherapy": get_schema_id(Title.REASON_OF_VISIT) + "." + "plan",
		"plan.physiotherapy": get_schema_id(Title.REASON_OF_VISIT) + "." + "plan",
        "merged_plan": get_schema_id(Title.REASON_OF_VISIT) + "." + "plan"},

    "medication_current": {
        "illness.condition": get_schema_id(Title.MEDICATION_CURRENT) + "." + "condition",
        "current.medication": get_schema_id(Title.MEDICATION_CURRENT) + "." + "medication",
        "hpi.medication_current": get_schema_id(Title.MEDICATION_CURRENT) + "." + "medication",
        "medication.instruction": get_schema_id(Title.MEDICATION_CURRENT) + "." + "instruction",
        "medication.duration": get_schema_id(Title.MEDICATION_CURRENT) + "." + "duration",
        "medication.dosage": get_schema_id(Title.MEDICATION_CURRENT) + "." + "dosage",
        "medication.dosage.quantity": get_schema_id(Title.MEDICATION_CURRENT) + "." + "dosage",
        "medication.dosage.unit": get_schema_id(Title.MEDICATION_CURRENT) + "." + "unit",
        "medication.start": get_schema_id(Title.MEDICATION_CURRENT) + "." + "start",
        "medication.end": get_schema_id(Title.MEDICATION_CURRENT) + "." + "end",
        "medication.rx_code": get_schema_id(Title.MEDICATION_CURRENT) + "." + "rx_code",
        "snomed": get_schema_id(Title.MEDICATION_CURRENT) + "." + "snomed"},

    "medications": {
        "illness.condition": get_schema_id(Title.MEDICATIONS) + "." + "condition",
        "reason": get_schema_id(Title.MEDICATIONS) + "." + "condition",
        "reason_of_visit": get_schema_id(Title.MEDICATIONS) + "." + "condition",
        "medication.disease.prescription": get_schema_id(Title.MEDICATIONS) + "." + "condition",
        const.MERGED_ENTITY + "condition": get_schema_id(Title.MEDICATIONS) + "." + "condition",
        "medication.reason": get_schema_id(Title.MEDICATIONS) + "." + "condition",
        const.GENERATED_ENTITY + "medication_type": get_schema_id(Title.MEDICATIONS) + "." + "type",
        "current.medication": get_schema_id(Title.MEDICATIONS) + "." + "medication",
        "medication.name": get_schema_id(Title.MEDICATIONS) + "." + "medication",
        "hpi.medication_current": get_schema_id(Title.MEDICATIONS) + "." + "medication",
        "history.medication": get_schema_id(Title.MEDICATIONS) + "." + "medication",
        "medication.history": get_schema_id(Title.MEDICATIONS) + "." + "medication",
        "plan.medication": get_schema_id(Title.MEDICATIONS) + "." + "medication",
        "hpi.medication_discontinued": get_schema_id(Title.MEDICATIONS) + "." + "medication",
        "medication.instruction": get_schema_id(Title.MEDICATIONS) + "." + "instruction",
        "medication.duration": get_schema_id(Title.MEDICATIONS) + "." + "duration",
        "medication.quantity": get_schema_id(Title.MEDICATIONS) + "." + "quantity",
        "medication.dosage": get_schema_id(Title.MEDICATIONS) + "." + "dosage",
        "medication.dosage.quantity": get_schema_id(Title.MEDICATIONS) + "." + "dosage",
        "medication.strength.quantity": get_schema_id(Title.MEDICATIONS) + "." + "dosage",
        "medication.dosage.unit": get_schema_id(Title.MEDICATIONS) + "." + "unit",
        "medication.strength.unit": get_schema_id(Title.MEDICATIONS) + "." + "unit",
        "medication.start": get_schema_id(Title.MEDICATIONS) + "." + "start",
        "medication.end": get_schema_id(Title.MEDICATIONS) + "." + "end",
        "encounter.date": get_schema_id(Title.MEDICATIONS) + "." + "date",
        const.GENERATED_ENTITY + "medication.rx_code": get_schema_id(Title.MEDICATIONS) + "." + "rx_code",
        "rx.number": get_schema_id(Title.MEDICATIONS) + "." + "rx_code",
        const.GENERATED_ENTITY + "snomed": get_schema_id(Title.MEDICATIONS) + "." + "snomed",
        const.GENERATED_ENTITY + "category": get_schema_id(Title.MEDICATIONS) + "." + "category",
        "doctor.speciality": get_schema_id(Title.MEDICATIONS) + "." + "provider"
        },

    "imaging_study": {
        "imaging.date": get_schema_id(Title.IMAGING_STUDY) + "." + "date",
        "hospitalization.date": get_schema_id(Title.IMAGING_STUDY) + "." + "date",
        "imaging.exam": get_schema_id(Title.IMAGING_STUDY) + "." + "exam",
        "diagnostic_tests.name": get_schema_id(Title.IMAGING_STUDY) + "." + "exam",
        "cardio.exam": get_schema_id(Title.IMAGING_STUDY) + "." + "exam",
        "imaging.finding": get_schema_id(Title.IMAGING_STUDY) + "." + "finding",
        "diagnostic_tests.finding": get_schema_id(Title.IMAGING_STUDY) + "." + "finding",
        const.MERGED_ENTITY + "Finding": get_schema_id(Title.IMAGING_STUDY) + "." + "finding",
        "imaging.impression": get_schema_id(Title.IMAGING_STUDY) + "." + "impression",
        "diagnostic_tests.impression": get_schema_id(Title.IMAGING_STUDY) + "." + "impression",
        const.MERGED_ENTITY + "Impression": get_schema_id(Title.IMAGING_STUDY) + "." + "impression",
        "imaging.conclusion": get_schema_id(Title.IMAGING_STUDY) + "." + "conclusion",
        "diagnostic_tests.conclusion": get_schema_id(Title.IMAGING_STUDY) + "." + "conclusion",
        const.MERGED_ENTITY + "Conclusion": get_schema_id(Title.IMAGING_STUDY) + "." + "conclusion",
        "reason": get_schema_id(Title.IMAGING_STUDY) + "." + "reason",
        const.MERGED_ENTITY + "Reason": get_schema_id(Title.IMAGING_STUDY) + "." + "reason",
        "reason_of_visit": get_schema_id(Title.IMAGING_STUDY) + "." + "reason",
        const.MERGED_ENTITY + "Reason_of_visit": get_schema_id(Title.IMAGING_STUDY) + "." + "reason",
        "encounter.date": get_schema_id(Title.IMAGING_STUDY) + "." + "date",
        const.GENERATED_ENTITY + "impairment": get_schema_id(Title.IMAGING_STUDY) + "." + "impairment"
    },

    "list_of_events": {
        "lab.collected": get_schema_id(Title.LIST_OF_EVENTS) + "." + "date",
        "o.collected": get_schema_id(Title.LIST_OF_EVENTS) + "." + "date",
        "lab.received": get_schema_id(Title.LIST_OF_EVENTS) + "." + "date",
        "o.received": get_schema_id(Title.LIST_OF_EVENTS) + "." + "date",
        "o.issued": get_schema_id(Title.LIST_OF_EVENTS) + "." + "date",
        "lab.issued": get_schema_id(Title.LIST_OF_EVENTS) + "." + "date",
        "encounter.date": get_schema_id(Title.LIST_OF_EVENTS) + "." + "date",
        "vital.date": get_schema_id(Title.LIST_OF_EVENTS) + "." + "date",
        "imaging.date": get_schema_id(Title.LIST_OF_EVENTS) + "." + "date",
        "procedure.date": get_schema_id(Title.LIST_OF_EVENTS) + "." + "date",
        "surgery.date": get_schema_id(Title.LIST_OF_EVENTS) + "." + "date",
        "pathology.date": get_schema_id(Title.LIST_OF_EVENTS) + "." + "date",
        "phystherapy.date": get_schema_id(Title.LIST_OF_EVENTS) + "." + "date",
        "lab.performer": get_schema_id(Title.LIST_OF_EVENTS) + "." + "date",
        "encounter.performer": get_schema_id(Title.LIST_OF_EVENTS) + "." + "performer",
        "imaging.performer": get_schema_id(Title.LIST_OF_EVENTS) + "." + "performer",
        "procedure.performer": get_schema_id(Title.LIST_OF_EVENTS) + "." + "performer",
        "surgery.performer": get_schema_id(Title.LIST_OF_EVENTS) + "." + "performer",
        "pathology.performer": get_schema_id(Title.LIST_OF_EVENTS) + "." + "performer",
        "phystherapy.perfomer": get_schema_id(Title.LIST_OF_EVENTS) + "." + "performer"},

    "family_history": {
        const.GENERATED_ENTITY + "family.relation.all": get_schema_id(Title.FAMILY_HISTORY) + "." + "family",
        const.GENERATED_ENTITY + "family.relation.father": get_schema_id(Title.FAMILY_HISTORY) + "." + "family",
        const.GENERATED_ENTITY + "family.relation.mother": get_schema_id(Title.FAMILY_HISTORY) + "." + "family",
        const.GENERATED_ENTITY + "family.relation.siblings": get_schema_id(Title.FAMILY_HISTORY) + "." + "family",
        const.GENERATED_ENTITY + "family.relation.parents": get_schema_id(Title.FAMILY_HISTORY) + "." + "family",
        const.GENERATED_ENTITY + "family.relation.children": get_schema_id(Title.FAMILY_HISTORY) + "." + "family",
        "family.relation.all": get_schema_id(Title.FAMILY_HISTORY) + "." + "family",
        "family.relation.father": get_schema_id(Title.FAMILY_HISTORY) + "." + "family",
        "family.relation.mother": get_schema_id(Title.FAMILY_HISTORY) + "." + "family",
        "family.relation.siblings": get_schema_id(Title.FAMILY_HISTORY) + "." + "family",
        "family.relation.parents": get_schema_id(Title.FAMILY_HISTORY) + "." + "family",
        "family.relation.children": get_schema_id(Title.FAMILY_HISTORY) + "." + "family",
        "family.all.disease": get_schema_id(Title.FAMILY_HISTORY) + "." + "diagnosis",
        "family.father.disease": get_schema_id(Title.FAMILY_HISTORY) + "." + "diagnosis",
        "family.mother.disease": get_schema_id(Title.FAMILY_HISTORY) + "." + "diagnosis",
        "family.siblings.disease": get_schema_id(Title.FAMILY_HISTORY) + "." + "diagnosis",
        "family.children.disease": get_schema_id(Title.FAMILY_HISTORY) + "." + "diagnosis",
        "family.parents.disease": get_schema_id(Title.FAMILY_HISTORY) + "." + "diagnosis",
        "family.all": get_schema_id(Title.FAMILY_HISTORY) + "." + "diagnosis",
        "family.father": get_schema_id(Title.FAMILY_HISTORY) + "." + "diagnosis",
        "family.mother": get_schema_id(Title.FAMILY_HISTORY) + "." + "diagnosis",
        "family.sibling": get_schema_id(Title.FAMILY_HISTORY) + "." + "diagnosis",
        "family.children": get_schema_id(Title.FAMILY_HISTORY) + "." + "diagnosis",
        "family.parents": get_schema_id(Title.FAMILY_HISTORY) + "." + "diagnosis",
        "age_diagnosed" : get_schema_id(Title.FAMILY_HISTORY) + "." + "age_diagnosed",
        "family.all.age" : get_schema_id(Title.FAMILY_HISTORY) + "." + "age_diagnosed",
        "family.parents.age" : get_schema_id(Title.FAMILY_HISTORY) + "." + "age_diagnosed",
        "family.father.age" : get_schema_id(Title.FAMILY_HISTORY) + "." + "age_diagnosed",
        "family.mother.age" : get_schema_id(Title.FAMILY_HISTORY) + "." + "age_diagnosed",
        "family.sibling.age" : get_schema_id(Title.FAMILY_HISTORY) + "." + "age_diagnosed",
        "family.children.age" : get_schema_id(Title.FAMILY_HISTORY) + "." + "age_diagnosed",
        "family.all.disease.age" : get_schema_id(Title.FAMILY_HISTORY) + "." + "age_diagnosed",
        "family.parents.disease.age" : get_schema_id(Title.FAMILY_HISTORY) + "." + "age_diagnosed",
        "family.father.disease.age" : get_schema_id(Title.FAMILY_HISTORY) + "." + "age_diagnosed",
        "family.mother.disease.age" : get_schema_id(Title.FAMILY_HISTORY) + "." + "age_diagnosed",
        "family.siblings.disease.age" : get_schema_id(Title.FAMILY_HISTORY) + "." + "age_diagnosed",
        "family.children.disease.age" : get_schema_id(Title.FAMILY_HISTORY) + "." + "age_diagnosed",
        "family.all.death.age" : get_schema_id(Title.FAMILY_HISTORY) + "." + "death_age",
        "family.parents.death.age" : get_schema_id(Title.FAMILY_HISTORY) + "." + "death_age",
        "family.father.death.age" : get_schema_id(Title.FAMILY_HISTORY) + "." + "death_age",
        "family.mother.death.age" : get_schema_id(Title.FAMILY_HISTORY) + "." + "death_age",
        "family.sibling.death.age" : get_schema_id(Title.FAMILY_HISTORY) + "." + "death_age",
        "family.siblings.death.age" : get_schema_id(Title.FAMILY_HISTORY) + "." + "death_age",
        "family.children.death.age" : get_schema_id(Title.FAMILY_HISTORY) + "." + "death_age",
        const.GENERATED_ENTITY + "snomed": get_schema_id(Title.FAMILY_HISTORY) + "." + "snomed"},

    "encounter_details": {
        "lab.collected": get_schema_id(Title.ENCOUNTER_DETAILS) + "." + "date",
        "o.collected": get_schema_id(Title.ENCOUNTER_DETAILS) + "." + "date",
        "lab.received": get_schema_id(Title.ENCOUNTER_DETAILS) + "." + "date",
        "o.received": get_schema_id(Title.ENCOUNTER_DETAILS) + "." + "date",
        "o.issued": get_schema_id(Title.ENCOUNTER_DETAILS) + "." + "date",
        "lab.issued": get_schema_id(Title.ENCOUNTER_DETAILS) + "." + "date",
        "encounter.date": get_schema_id(Title.ENCOUNTER_DETAILS) + "." + "date",
        "vital.date": get_schema_id(Title.ENCOUNTER_DETAILS) + "." + "date",
        "patient.vital.date": get_schema_id(Title.ENCOUNTER_DETAILS) + "." + "date",
        "imaging.date": get_schema_id(Title.ENCOUNTER_DETAILS) + "." + "date",
        "procedure.date": get_schema_id(Title.ENCOUNTER_DETAILS) + "." + "date",
        "surgery.date": get_schema_id(Title.ENCOUNTER_DETAILS) + "." + "date",
        "pathology.date": get_schema_id(Title.ENCOUNTER_DETAILS) + "." + "date",
        "phystherapy.date": get_schema_id(Title.ENCOUNTER_DETAILS) + "." + "date",
        "encounter.type": get_schema_id(Title.ENCOUNTER_DETAILS) + "." + "type",
        "encounter.performer": get_schema_id(Title.ENCOUNTER_DETAILS) + "." + "department",
        "merged_" + Title.PERFORMER : get_schema_id(Title.ENCOUNTER_DETAILS) + "." + "department",
        "encounter.finding": get_schema_id(Title.ENCOUNTER_DETAILS) + "." + "description",
        "hpi.reason": get_schema_id(Title.ENCOUNTER_DETAILS) + "." + "description",
        "reason": get_schema_id(Title.ENCOUNTER_DETAILS) + "." + "description",
        "merged_" + Title.DESCRIPTION: get_schema_id(Title.ENCOUNTER_DETAILS) + "." + "description",
        "assessment.diag": get_schema_id(Title.ENCOUNTER_DETAILS) + "." + "description",
        "assessment": get_schema_id(Title.ENCOUNTER_DETAILS) + "." + "description",
        "encounter.conclusion": get_schema_id(Title.ENCOUNTER_DETAILS) + "." + "description"},

    "medical_equipment": {
        "medical.equipment": get_schema_id(Title.MEDICAL_EQUIPMENT) + "." + "equipment",
        "medical.device": get_schema_id(Title.MEDICAL_EQUIPMENT) + "." + "equipment",
        "merged_" + const.EQUIPMENT: get_schema_id(Title.MEDICAL_EQUIPMENT) + "." + "equipment",
        "patient.medical.equipment": get_schema_id(Title.MEDICAL_EQUIPMENT) + "." + "equipment",
        "equipment.date": get_schema_id(Title.MEDICAL_EQUIPMENT) + "." + "date",
        "encounter.date": get_schema_id(Title.MEDICAL_EQUIPMENT) + "." + "date"},

    "social_history": {
        "encounter.date": get_schema_id(Title.SOCIAL_HISTORY) + "." + "date",
        "smoking": get_schema_id(Title.SOCIAL_HISTORY) + "." + "smoking_status",
        "alcohol": get_schema_id(Title.SOCIAL_HISTORY) + "." + "alcohol_status",
        "drugs": get_schema_id(Title.SOCIAL_HISTORY) + "." + "drugs_status",
        "social.smoking": get_schema_id(Title.SOCIAL_HISTORY) + "." + "description",
        "insured_1.smoking": get_schema_id(Title.SOCIAL_HISTORY) + "." + "description",
        "insured_2.smoking": get_schema_id(Title.SOCIAL_HISTORY) + "." + "description",
        "social.alcohol": get_schema_id(Title.SOCIAL_HISTORY) + "." + "description",
        "insured_1.alcohol": get_schema_id(Title.SOCIAL_HISTORY) + "." + "description",
        "insured_2.alcohol": get_schema_id(Title.SOCIAL_HISTORY) + "." + "description",
        "social.drugs": get_schema_id(Title.SOCIAL_HISTORY) + "." + "description",
        "insured_1.drugs": get_schema_id(Title.SOCIAL_HISTORY) + "." + "description",
        "insured_2.drugs": get_schema_id(Title.SOCIAL_HISTORY) + "." + "description",
        "smoking.status": get_schema_id(Title.SOCIAL_HISTORY) + "." + "smoking_status",
        "tobacco.status": get_schema_id(Title.SOCIAL_HISTORY) + "." + "tobacco_status",
        "alcohol.status": get_schema_id(Title.SOCIAL_HISTORY) + "." + "alcohol_status",
        "drugs.status": get_schema_id(Title.SOCIAL_HISTORY) + "." + "drugs_status",
        "marijuana.status": get_schema_id(Title.SOCIAL_HISTORY) + "." + "marijuana_status",
        "patient.smoking.status.yes": get_schema_id(Title.SOCIAL_HISTORY) + "." + "description",
        "patient.smoking.status.no": get_schema_id(Title.SOCIAL_HISTORY) + "." + "description",
        "patient.smoking.status.former": get_schema_id(Title.SOCIAL_HISTORY) + "." + "description",
        "patient.smoking.status.passive": get_schema_id(Title.SOCIAL_HISTORY) + "." + "description",
        "insured_1.smoking.status.yes": get_schema_id(Title.SOCIAL_HISTORY) + "." + "description",
        "insured_1.smoking.status.no": get_schema_id(Title.SOCIAL_HISTORY) + "." + "description",
        "insured_1.smoking.status.former": get_schema_id(Title.SOCIAL_HISTORY) + "." + "description",
        "insured_1.smoking.status.passive": get_schema_id(Title.SOCIAL_HISTORY) + "." + "description",
        "insured_2.smoking.status.yes": get_schema_id(Title.SOCIAL_HISTORY) + "." + "description",
        "insured_2.smoking.status.no": get_schema_id(Title.SOCIAL_HISTORY) + "." + "description",
        "insured_2.smoking.status.former": get_schema_id(Title.SOCIAL_HISTORY) + "." + "description",
        "insured_2.smoking.status.passive": get_schema_id(Title.SOCIAL_HISTORY) + "." + "description",
        "applicant.smoking.status.yes": get_schema_id(Title.SOCIAL_HISTORY) + "." + "description",
        "applicant.smoking.status.no": get_schema_id(Title.SOCIAL_HISTORY) + "." + "description",
        "applicant.smoking.status.former": get_schema_id(Title.SOCIAL_HISTORY) + "." + "description",
        "applicant.smoking.status.passive": get_schema_id(Title.SOCIAL_HISTORY) + "." + "description",
        "patient.smoking.device": get_schema_id(Title.SOCIAL_HISTORY) + "." + "device",
        "insured_1.smoking.device": get_schema_id(Title.SOCIAL_HISTORY) + "." + "device",
        "insured_2.smoking.device": get_schema_id(Title.SOCIAL_HISTORY) + "." + "device",
        "applicant.smoking.device": get_schema_id(Title.SOCIAL_HISTORY) + "." + "device",
        "patient.tobacco.status.yes": get_schema_id(Title.SOCIAL_HISTORY) + "." + "description",
        "patient.tobacco.status.no": get_schema_id(Title.SOCIAL_HISTORY) + "." + "description",
        "patient.tobacco.status.former": get_schema_id(Title.SOCIAL_HISTORY) + "." + "description",
        "insured_1.tobacco.status.yes": get_schema_id(Title.SOCIAL_HISTORY) + "." + "description",
        "insured_1.tobacco.status.no": get_schema_id(Title.SOCIAL_HISTORY) + "." + "description",
        "insured_1.tobacco.status.former": get_schema_id(Title.SOCIAL_HISTORY) + "." + "description",
        "insured_2.tobacco.status.yes": get_schema_id(Title.SOCIAL_HISTORY) + "." + "description",
        "insured_2.tobacco.status.no": get_schema_id(Title.SOCIAL_HISTORY) + "." + "description",
        "insured_2.tobacco.status.former": get_schema_id(Title.SOCIAL_HISTORY) + "." + "description",
        "applicant.tobacco.status.yes": get_schema_id(Title.SOCIAL_HISTORY) + "." + "description",
        "applicant.tobacco.status.no": get_schema_id(Title.SOCIAL_HISTORY) + "." + "description",
        "applicant.tobacco.status.former": get_schema_id(Title.SOCIAL_HISTORY) + "." + "description",
        "patient.tobacco.product": get_schema_id(Title.SOCIAL_HISTORY) + "." + "device",
        "insured_1.tobacco.product": get_schema_id(Title.SOCIAL_HISTORY) + "." + "device",
        "insured_2.tobacco.product": get_schema_id(Title.SOCIAL_HISTORY) + "." + "device",
        "applicant.tobacco.product": get_schema_id(Title.SOCIAL_HISTORY) + "." + "device",
        "patient.alcohol.status.yes": get_schema_id(Title.SOCIAL_HISTORY) + "." + "description",
        "patient.alcohol.status.no": get_schema_id(Title.SOCIAL_HISTORY) + "." + "description",
        "patient.alcohol.status.former": get_schema_id(Title.SOCIAL_HISTORY) + "." + "description",
        "insured_1.alcohol.status.yes": get_schema_id(Title.SOCIAL_HISTORY) + "." + "description",
        "insured_1.alcohol.status.no": get_schema_id(Title.SOCIAL_HISTORY) + "." + "description",
        "insured_1.alcohol.status.former": get_schema_id(Title.SOCIAL_HISTORY) + "." + "description",
        "insured_2.alcohol.status.yes": get_schema_id(Title.SOCIAL_HISTORY) + "." + "description",
        "insured_2.alcohol.status.no": get_schema_id(Title.SOCIAL_HISTORY) + "." + "description",
        "insured_2.alcohol.status.former": get_schema_id(Title.SOCIAL_HISTORY) + "." + "description",
        "applicant.alcohol.status.yes": get_schema_id(Title.SOCIAL_HISTORY) + "." + "description",
        "applicant.alcohol.status.no": get_schema_id(Title.SOCIAL_HISTORY) + "." + "description",
        "applicant.alcohol.status.former": get_schema_id(Title.SOCIAL_HISTORY) + "." + "description",
        "patient.alcohol.beverage": get_schema_id(Title.SOCIAL_HISTORY) + "." + "device",
        "insured_1.alcohol.beverage": get_schema_id(Title.SOCIAL_HISTORY) + "." + "device",
        "insured_2.alcohol.beverage": get_schema_id(Title.SOCIAL_HISTORY) + "." + "device",
        "applicant.alcohol.beverage": get_schema_id(Title.SOCIAL_HISTORY) + "." + "device",
        "patient.drugs.status.yes": get_schema_id(Title.SOCIAL_HISTORY) + "." + "description",
        "patient.drugs.status.no": get_schema_id(Title.SOCIAL_HISTORY) + "." + "description",
        "patient.drugs.status.former": get_schema_id(Title.SOCIAL_HISTORY) + "." + "description",
        "insured_1.drugs.status.yes": get_schema_id(Title.SOCIAL_HISTORY) + "." + "description",
        "insured_1.drugs.status.no": get_schema_id(Title.SOCIAL_HISTORY) + "." + "description",
        "insured_1.drugs.status.former": get_schema_id(Title.SOCIAL_HISTORY) + "." + "description",
        "insured_2.drugs.status.yes": get_schema_id(Title.SOCIAL_HISTORY) + "." + "description",
        "insured_2.drugs.status.no": get_schema_id(Title.SOCIAL_HISTORY) + "." + "description",
        "insured_2.drugs.status.former": get_schema_id(Title.SOCIAL_HISTORY) + "." + "description",
        "applicant.drugs.status.yes": get_schema_id(Title.SOCIAL_HISTORY) + "." + "description",
        "applicant.drugs.status.no": get_schema_id(Title.SOCIAL_HISTORY) + "." + "description",
        "applicant.drugs.status.former": get_schema_id(Title.SOCIAL_HISTORY) + "." + "description",
        "patient.drugs.product": get_schema_id(Title.SOCIAL_HISTORY) + "." + "device",
        "insured_1.drugs.product": get_schema_id(Title.SOCIAL_HISTORY) + "." + "device",
        "insured_2.drugs.product": get_schema_id(Title.SOCIAL_HISTORY) + "." + "device",
        "applicant.drugs.product": get_schema_id(Title.SOCIAL_HISTORY) + "." + "device",
        "patient.marijuana.status.yes": get_schema_id(Title.SOCIAL_HISTORY) + "." + "description",
        "patient.marijuana.status.no": get_schema_id(Title.SOCIAL_HISTORY) + "." + "description",
        "patient.marijuana.status.details": get_schema_id(Title.SOCIAL_HISTORY) + "." + "description",
        "applicant.marijuana.status.yes": get_schema_id(Title.SOCIAL_HISTORY) + "." + "description",
        "applicant.marijuana.status.no": get_schema_id(Title.SOCIAL_HISTORY) + "." + "description",
        "applicant.marijuana.status.details": get_schema_id(Title.SOCIAL_HISTORY) + "." + "description",
        "insured_1.marijuana.status.yes": get_schema_id(Title.SOCIAL_HISTORY) + "." + "description",
        "insured_1.marijuana.status.no": get_schema_id(Title.SOCIAL_HISTORY) + "." + "description",
        "insured_1.marijuana.status.details": get_schema_id(Title.SOCIAL_HISTORY) + "." + "description",
        "insured_2.marijuana.status.yes": get_schema_id(Title.SOCIAL_HISTORY) + "." + "description",
        "insured_2.marijuana.status.no": get_schema_id(Title.SOCIAL_HISTORY) + "." + "description",
        "insured_2.marijuana.status.details": get_schema_id(Title.SOCIAL_HISTORY) + "." + "description",
        "patient.marijuana.consumption_method.smoking": get_schema_id(Title.SOCIAL_HISTORY) + "." + "device",
        "patient.marijuana.consumption_method.vaporizing": get_schema_id(Title.SOCIAL_HISTORY) + "." + "device",
        "patient.marijuana.consumption_method.edible": get_schema_id(Title.SOCIAL_HISTORY) + "." + "device",
        "patient.marijuana.consumption_method.other": get_schema_id(Title.SOCIAL_HISTORY) + "." + "device",
        "applicant.marijuana.consumption_method.smoking": get_schema_id(Title.SOCIAL_HISTORY) + "." + "device",
        "applicant.marijuana.consumption_method.vaporizing": get_schema_id(Title.SOCIAL_HISTORY) + "." + "device",
        "applicant.marijuana.consumption_method.edible": get_schema_id(Title.SOCIAL_HISTORY) + "." + "device",
        "applicant.marijuana.consumption_method.other": get_schema_id(Title.SOCIAL_HISTORY) + "." + "device",
        "insured_1.marijuana.consumption_method.smoking": get_schema_id(Title.SOCIAL_HISTORY) + "." + "device",
        "insured_1.marijuana.consumption_method.vaporizing": get_schema_id(Title.SOCIAL_HISTORY) + "." + "device",
        "insured_1.marijuana.consumption_method.edible": get_schema_id(Title.SOCIAL_HISTORY) + "." + "device",
        "insured_1.marijuana.consumption_method.other": get_schema_id(Title.SOCIAL_HISTORY) + "." + "device",
        "insured_2.marijuana.consumption_method.smoking": get_schema_id(Title.SOCIAL_HISTORY) + "." + "device",
        "insured_2.marijuana.consumption_method.vaporizing": get_schema_id(Title.SOCIAL_HISTORY) + "." + "device",
        "insured_2.marijuana.consumption_method.edible": get_schema_id(Title.SOCIAL_HISTORY) + "." + "device",
        "insured_2.marijuana.consumption_method.other": get_schema_id(Title.SOCIAL_HISTORY) + "." + "device",
        "patient.smoking.quantity": get_schema_id(Title.SOCIAL_HISTORY) + "." + "quantity",
        "insured_1.smoking.quantity": get_schema_id(Title.SOCIAL_HISTORY) + "." + "quantity",
        "insured_2.smoking.quantity": get_schema_id(Title.SOCIAL_HISTORY) + "." + "quantity",
        "applicant.smoking.quantity": get_schema_id(Title.SOCIAL_HISTORY) + "." + "quantity",
        "patient.tobacco.quantity": get_schema_id(Title.SOCIAL_HISTORY) + "." + "quantity",
        "insured_1.tobacco.quantity": get_schema_id(Title.SOCIAL_HISTORY) + "." + "quantity",
        "insured_2.tobacco.quantity": get_schema_id(Title.SOCIAL_HISTORY) + "." + "quantity",
        "applicant.tobacco.quantity": get_schema_id(Title.SOCIAL_HISTORY) + "." + "quantity",
        "patient.alcohol.quantity": get_schema_id(Title.SOCIAL_HISTORY) + "." + "quantity",
        "insured_1.alcohol.quantity": get_schema_id(Title.SOCIAL_HISTORY) + "." + "quantity",
        "insured_2.alcohol.quantity": get_schema_id(Title.SOCIAL_HISTORY) + "." + "quantity",
        "applicant.alcohol.quantity": get_schema_id(Title.SOCIAL_HISTORY) + "." + "quantity",
        "patient.drugs.quantity": get_schema_id(Title.SOCIAL_HISTORY) + "." + "quantity",
        "insured_1.drugs.quantity": get_schema_id(Title.SOCIAL_HISTORY) + "." + "quantity",
        "insured_2.drugs.quantity": get_schema_id(Title.SOCIAL_HISTORY) + "." + "quantity",
        "applicant.drugs.quantity": get_schema_id(Title.SOCIAL_HISTORY) + "." + "quantity",
        "patient.marijuana.quantity": get_schema_id(Title.SOCIAL_HISTORY) + "." + "quantity",
        "applicant.marijuana.quantity": get_schema_id(Title.SOCIAL_HISTORY) + "." + "quantity",
        "insured_1.marijuana.quantity": get_schema_id(Title.SOCIAL_HISTORY) + "." + "quantity",
        "insured_2.marijuana.quantity": get_schema_id(Title.SOCIAL_HISTORY) + "." + "quantity",
        'patient.alcohol.quantity.unit': get_schema_id(Title.SOCIAL_HISTORY) + "." + "unit",
        'insured_1.alcohol.quantity.unit': get_schema_id(Title.SOCIAL_HISTORY) + "." + "unit",
        'insured_2.alcohol.quantity.unit': get_schema_id(Title.SOCIAL_HISTORY) + "." + "unit",
        'applicant.alcohol.quantity.unit': get_schema_id(Title.SOCIAL_HISTORY) + "." + "unit",
        "patient.marijuana.quantity.unit": get_schema_id(Title.SOCIAL_HISTORY) + "." + "unit",
        "applicant.marijuana.quantity.unit": get_schema_id(Title.SOCIAL_HISTORY) + "." + "unit",
        "insured_1.marijuana.quantity.unit": get_schema_id(Title.SOCIAL_HISTORY) + "." + "unit",
        "insured_2.marijuana.quantity.unit": get_schema_id(Title.SOCIAL_HISTORY) + "." + "unit",
        "patient.smoking.start_date": get_schema_id(Title.SOCIAL_HISTORY) + "." + "start_date",
        "insured_1.smoking.start_date": get_schema_id(Title.SOCIAL_HISTORY) + "." + "start_date",
        "insured_2.smoking.start_date": get_schema_id(Title.SOCIAL_HISTORY) + "." + "start_date",
        "applicant.smoking.start_date": get_schema_id(Title.SOCIAL_HISTORY) + "." + "start_date",
        "patient.tobacco.start_date": get_schema_id(Title.SOCIAL_HISTORY) + "." + "start_date",
        "insured_1.tobacco.start_date": get_schema_id(Title.SOCIAL_HISTORY) + "." + "start_date",
        "insured_2.tobacco.start_date": get_schema_id(Title.SOCIAL_HISTORY) + "." + "start_date",
        "applicant.tobacco.start_date": get_schema_id(Title.SOCIAL_HISTORY) + "." + "start_date",
        "patient.alcohol.start_date": get_schema_id(Title.SOCIAL_HISTORY) + "." + "start_date",
        "insured_1.alcohol.start_date": get_schema_id(Title.SOCIAL_HISTORY) + "." + "start_date",
        "insured_2.alcohol.start_date": get_schema_id(Title.SOCIAL_HISTORY) + "." + "start_date",
        "applicant.alcohol.start_date": get_schema_id(Title.SOCIAL_HISTORY) + "." + "start_date",
        "patient.drugs.start_date": get_schema_id(Title.SOCIAL_HISTORY) + "." + "start_date",
        "insured_1.drugs.start_date": get_schema_id(Title.SOCIAL_HISTORY) + "." + "start_date",
        "insured_2.drugs.start_date": get_schema_id(Title.SOCIAL_HISTORY) + "." + "start_date",
        "applicant.drugs.start_date": get_schema_id(Title.SOCIAL_HISTORY) + "." + "start_date",
        "patient.marijuana.start_date": get_schema_id(Title.SOCIAL_HISTORY) + "." + "start_date",
        "applicant.marijuana.start_date": get_schema_id(Title.SOCIAL_HISTORY) + "." + "start_date",
        "insured_1.marijuana.start_date": get_schema_id(Title.SOCIAL_HISTORY) + "." + "start_date",
        "insured_2.marijuana.start_date": get_schema_id(Title.SOCIAL_HISTORY) + "." + "start_date",
        "patient.smoking.end_date": get_schema_id(Title.SOCIAL_HISTORY) + "." + "end_date",
        "insured_1.smoking.end_date": get_schema_id(Title.SOCIAL_HISTORY) + "." + "end_date",
        "insured_2.smoking.end_date": get_schema_id(Title.SOCIAL_HISTORY) + "." + "end_date",
        "applicant.smoking.end_date": get_schema_id(Title.SOCIAL_HISTORY) + "." + "end_date",
        "patient.tobacco.end_date": get_schema_id(Title.SOCIAL_HISTORY) + "." + "end_date",
        "insured_1.tobacco.end_date": get_schema_id(Title.SOCIAL_HISTORY) + "." + "end_date",
        "insured_2.tobacco.end_date": get_schema_id(Title.SOCIAL_HISTORY) + "." + "end_date",
        "applicant.tobacco.end_date": get_schema_id(Title.SOCIAL_HISTORY) + "." + "end_date",
        "patient.alcohol.end_date": get_schema_id(Title.SOCIAL_HISTORY) + "." + "end_date",
        "insured_1.alcohol.end_date": get_schema_id(Title.SOCIAL_HISTORY) + "." + "end_date",
        "insured_2.alcohol.end_date": get_schema_id(Title.SOCIAL_HISTORY) + "." + "end_date",
        "applicant.alcohol.end_date": get_schema_id(Title.SOCIAL_HISTORY) + "." + "end_date",
        "patient.drugs.end_date": get_schema_id(Title.SOCIAL_HISTORY) + "." + "end_date",
        "insured_1.drugs.end_date": get_schema_id(Title.SOCIAL_HISTORY) + "." + "end_date",
        "insured_2.drugs.end_date": get_schema_id(Title.SOCIAL_HISTORY) + "." + "end_date",
        "applicant.drugs.end_date": get_schema_id(Title.SOCIAL_HISTORY) + "." + "end_date",
        "patient.marijuana.end_date": get_schema_id(Title.SOCIAL_HISTORY) + "." + "end_date",
        "applicant.marijuana.end_date": get_schema_id(Title.SOCIAL_HISTORY) + "." + "end_date",
        "insured_1.marijuana.end_date": get_schema_id(Title.SOCIAL_HISTORY) + "." + "end_date",
        "insured_2.marijuana.end_date": get_schema_id(Title.SOCIAL_HISTORY) + "." + "end_date",
        "patient.smoking.duration": get_schema_id(Title.SOCIAL_HISTORY) + "." + "duration",
        "insured_1.smoking.duration": get_schema_id(Title.SOCIAL_HISTORY) + "." + "duration",
        "insured_2.smoking.duration": get_schema_id(Title.SOCIAL_HISTORY) + "." + "duration",
        "applicant.smoking.duration": get_schema_id(Title.SOCIAL_HISTORY) + "." + "duration",
        "patient.tobacco.duration": get_schema_id(Title.SOCIAL_HISTORY) + "." + "duration",
        "insured_1.tobacco.duration": get_schema_id(Title.SOCIAL_HISTORY) + "." + "duration",
        "insured_2.tobacco.duration": get_schema_id(Title.SOCIAL_HISTORY) + "." + "duration",
        "applicant.tobacco.duration": get_schema_id(Title.SOCIAL_HISTORY) + "." + "duration",
        "patient.alcohol.duration": get_schema_id(Title.SOCIAL_HISTORY) + "." + "duration",
        "insured_1.alcohol.duration": get_schema_id(Title.SOCIAL_HISTORY) + "." + "duration",
        "insured_2.alcohol.duration": get_schema_id(Title.SOCIAL_HISTORY) + "." + "duration",
        "applicant.alcohol.duration": get_schema_id(Title.SOCIAL_HISTORY) + "." + "duration",
        "patient.drugs.duration": get_schema_id(Title.SOCIAL_HISTORY) + "." + "duration",
        "insured_1.drugs.duration": get_schema_id(Title.SOCIAL_HISTORY) + "." + "duration",
        "insured_2.drugs.duration": get_schema_id(Title.SOCIAL_HISTORY) + "." + "duration",
        "applicant.drugs.duration": get_schema_id(Title.SOCIAL_HISTORY) + "." + "duration",
        "patient.marijuana.duration": get_schema_id(Title.SOCIAL_HISTORY) + "." + "duration",
        "applicant.marijuana.duration": get_schema_id(Title.SOCIAL_HISTORY) + "." + "duration",
        "insured_1.marijuana.duration": get_schema_id(Title.SOCIAL_HISTORY) + "." + "duration",
        "insured_2.marijuana.duration": get_schema_id(Title.SOCIAL_HISTORY) + "." + "duration",
        "patient.marijuana.frequency": get_schema_id(Title.SOCIAL_HISTORY) + "." + "frequency",
        "applicant.marijuana.frequency": get_schema_id(Title.SOCIAL_HISTORY) + "." + "frequency",
        "insured_1.marijuana.frequency": get_schema_id(Title.SOCIAL_HISTORY) + "." + "frequency",
        "insured_2.marijuana.frequency": get_schema_id(Title.SOCIAL_HISTORY) + "." + "frequency",
        "snomed": get_schema_id(Title.SOCIAL_HISTORY) + "." + "snomed",
        const.GENERATED_ENTITY + "snomed": get_schema_id(Title.SOCIAL_HISTORY) + "." + "snomed",
    },

    "dental_details": {
        "type_of_service": get_schema_id(Title.DENTAL_DETAILS) + "." + "type_of_service",
        "encounter.date": get_schema_id(Title.DENTAL_DETAILS) + "." + "date_of_service",
        "tooth_code": get_schema_id(Title.DENTAL_DETAILS) + "." + "tooth_code",
        "tooth_surfaces": get_schema_id(Title.DENTAL_DETAILS) + "." + "tooth_surfaces",
        "cob.amount": get_schema_id(Title.DENTAL_DETAILS) + "." + "cob_amount",
        "quantity": get_schema_id(Title.DENTAL_DETAILS) + "." + "quantity",
        "lab_charge": get_schema_id(Title.DENTAL_DETAILS) + "." + "lab_charge",
        "total_amount": get_schema_id(Title.DENTAL_DETAILS) + "." + "total_amount",
        "procedure.code": get_schema_id(Title.DENTAL_DETAILS) + "." + "procedure_code",
        "tax": get_schema_id(Title.DENTAL_DETAILS) + "." + "tax",
        "units": get_schema_id(Title.DENTAL_DETAILS) + "." + "units",
        "patient.name": get_schema_id(Title.DENTAL_DETAILS) + "." + "patient_name"},

    "claims": {
        "type_of_service": get_schema_id(Title.CLAIMS) + "." + "type_of_service",
        "encounter.date": get_schema_id(Title.CLAIMS) + "." + "date_of_service",
        "cob.amount": get_schema_id(Title.CLAIMS) + "." + "cob_amount",
        "quantity": get_schema_id(Title.CLAIMS) + "." + "quantity",
        "total_amount": get_schema_id(Title.CLAIMS) + "." + "total_amount",
        "tax": get_schema_id(Title.CLAIMS) + "." + "tax",
        "units": get_schema_id(Title.CLAIMS) + "." + "units",
        "patient.name": get_schema_id(Title.CLAIMS) + "." + "patient_name",
        "doctor.name": get_schema_id(Title.CLAIMS) + "." + "doctor_name",
        "ordering_doctor.name": get_schema_id(Title.CLAIMS) + "." + "doctor_name"},

    "rx_details": {
        "type_of_service": get_schema_id(Title.RX_DETAILS) + "." + "type_of_service",
        "encounter.date": get_schema_id(Title.RX_DETAILS) + "." + "date_of_service",
        "cob.amount": get_schema_id(Title.RX_DETAILS) + "." + "cob_amount",
        "quantity": get_schema_id(Title.RX_DETAILS) + "." + "quantity",
        "total_amount": get_schema_id(Title.RX_DETAILS) + "." + "total_amount",
        "tax": get_schema_id(Title.RX_DETAILS) + "." + "tax",
        "units": get_schema_id(Title.RX_DETAILS) + "." + "units",
        "patient.name": get_schema_id(Title.RX_DETAILS) + "." + "patient_name",
        "doctor.name": get_schema_id(Title.RX_DETAILS) + "." + "doctor_name",
        "ordering_doctor.name": get_schema_id(Title.RX_DETAILS) + "." + "doctor_name",
        "rx.cost": get_schema_id(Title.RX_DETAILS) + "." + "cost",
        "rx.fee": get_schema_id(Title.RX_DETAILS) + "." + "fee",
        "rx.din": get_schema_id(Title.RX_DETAILS) + "." + "din_number"
    },

    "provider_details": {
        "doctor.name": get_schema_id(Title.PROVIDER_DETAILS) + "." + "doctor_name",
        "ordering_doctor.name": get_schema_id(Title.PROVIDER_DETAILS) + "." + "doctor_name",
        "doctor.licence_number": get_schema_id(Title.PROVIDER_DETAILS) + "." + "doctor_registration_number",
        "encounter.performer": get_schema_id(Title.PROVIDER_DETAILS) + "." + "facility_name",
        "lab.performer": get_schema_id(Title.PROVIDER_DETAILS) + "." + "facility_name",
        "doctor.address": get_schema_id(Title.PROVIDER_DETAILS) + "." + "facility_address",
        "ordering_doctor.address": get_schema_id(Title.PROVIDER_DETAILS) + "." + "facility_address",
        "encounter.address": get_schema_id(Title.PROVIDER_DETAILS) + "." + "facility_address",
        "lab.address": get_schema_id(Title.PROVIDER_DETAILS) + "." + "facility_address"},

    "claim_details": {
        "policy_number": get_schema_id(Title.CLAIM_DETAIL) + "." + "policy_number",
        "claim_number": get_schema_id(Title.CLAIM_DETAIL) + "." + "claim_number",
        "company.claim_number": get_schema_id(Title.CLAIM_DETAIL) + "." + "claim_number",
        "policy.effect_date": get_schema_id(Title.CLAIM_DETAIL) + "." + "policy_effective_date",
        "policy.start_date": get_schema_id(Title.CLAIM_DETAIL) + "." + "policy_effective_date",
        "injury.date": get_schema_id(Title.CLAIM_DETAIL) + "." + "date_of_loss",
        "application.date": get_schema_id(Title.CLAIM_DETAIL) + "." + "date_of_loss",
        "diagnosis.primary": get_schema_id(Title.CLAIM_DETAIL) + "." + "primary_diagnosis",
        "assessment.diag": get_schema_id(Title.CLAIM_DETAIL) + "." + "primary_diagnosis",
        "autopsy.performed": get_schema_id(Title.CLAIM_DETAIL) + "." + "autopsy_performed",
    },
    "insured_details": {
        "insured_1.zipcode": get_schema_id(Title.INSURED_DETAILS) + "." + "zip_code",
        "insured_2.zipcode": get_schema_id(Title.INSURED_DETAILS) + "." + "zip_code",
        "patient.zipcode": get_schema_id(Title.INSURED_DETAILS) + "." + "zip_code",
        "applicant.zipcode": get_schema_id(Title.INSURED_DETAILS) + "." + "zip_code",
        "insured_1.postal_code": get_schema_id(Title.INSURED_DETAILS) + "." + "zip_code",
        "insured_2.postal_code": get_schema_id(Title.INSURED_DETAILS) + "." + "zip_code",
        "patient.postal_code": get_schema_id(Title.INSURED_DETAILS) + "." + "zip_code",
        "applicant.postal_code": get_schema_id(Title.INSURED_DETAILS) + "." + "zip_code",
        "insured_1.address": get_schema_id(Title.INSURED_DETAILS) + "." + "address",
        "insured_2.address": get_schema_id(Title.INSURED_DETAILS) + "." + "address",
        "patient.address": get_schema_id(Title.INSURED_DETAILS) + "." + "address",
        "applicant.address": get_schema_id(Title.INSURED_DETAILS) + "." + "address",
        "insured_1.address.line": get_schema_id(Title.INSURED_DETAILS) + "." + "address.line",
        "insured_2.address.line": get_schema_id(Title.INSURED_DETAILS) + "." + "address.line",
        "patient.address.line": get_schema_id(Title.INSURED_DETAILS) + "." + "address.line",
        "applicant.address.line": get_schema_id(Title.INSURED_DETAILS) + "." + "address.line",
        "insured_1.address.city": get_schema_id(Title.INSURED_DETAILS) + "." + "address.city",
        "insured_2.address.city": get_schema_id(Title.INSURED_DETAILS) + "." + "address.city",
        "patient.address.city": get_schema_id(Title.INSURED_DETAILS) + "." + "address.city",
        "applicant.address.city": get_schema_id(Title.INSURED_DETAILS) + "." + "address.city",
        "insured_1.address.state": get_schema_id(Title.INSURED_DETAILS) + "." + "address.state",
        "insured_2.address.state": get_schema_id(Title.INSURED_DETAILS) + "." + "address.state",
        "patient.address.state": get_schema_id(Title.INSURED_DETAILS) + "." + "address.state",
        "applicant.address.state": get_schema_id(Title.INSURED_DETAILS) + "." + "address.state",
        "insured_1.address.zipcode": get_schema_id(Title.INSURED_DETAILS) + "." + "address.zipcode",
        "insured_2.address.zipcode": get_schema_id(Title.INSURED_DETAILS) + "." + "address.zipcode",
        "patient.address.zipcode": get_schema_id(Title.INSURED_DETAILS) + "." + "address.zipcode",
        "applicant.address.zipcode": get_schema_id(Title.INSURED_DETAILS) + "." + "address.zipcode",
        "insured_1.address.postal_code": get_schema_id(Title.INSURED_DETAILS) + "." + "address.zipcode",
        "insured_2.address.postal_code": get_schema_id(Title.INSURED_DETAILS) + "." + "address.zipcode",
        "patient.address.postal_code": get_schema_id(Title.INSURED_DETAILS) + "." + "address.zipcode",
        "applicant.address.postal_code": get_schema_id(Title.INSURED_DETAILS) + "." + "address.zipcode",
        "insured_1.address.country": get_schema_id(Title.INSURED_DETAILS) + "." + "address.country",
        "insured_2.address.country": get_schema_id(Title.INSURED_DETAILS) + "." + "address.country",
        "patient.address.country": get_schema_id(Title.INSURED_DETAILS) + "." + "address.country",
        "applicant.address.country": get_schema_id(Title.INSURED_DETAILS) + "." + "address.country",
        "insured_1.occupation": get_schema_id(Title.INSURED_DETAILS) + "." + "occupation",
        "insured_2.occupation": get_schema_id(Title.INSURED_DETAILS) + "." + "occupation",
        "patient.occupation": get_schema_id(Title.INSURED_DETAILS) + "." + "occupation",
        "applicant.occupation": get_schema_id(Title.INSURED_DETAILS) + "." + "occupation",
        "patient.occupation.duty": get_schema_id(Title.INSURED_DETAILS) + "." + "occupation_duty",
        "insured_1.occupation.duty": get_schema_id(Title.INSURED_DETAILS) + "." + "occupation_duty",
        "insured_2.occupation.duty": get_schema_id(Title.INSURED_DETAILS) + "." + "occupation_duty",
        "applicant.occupation.duty": get_schema_id(Title.INSURED_DETAILS) + "." + "occupation_duty",
        "patient.occupation.risk.yes": get_schema_id(Title.INSURED_DETAILS) + "." + "occupation_risk",
        "insured_1.occupation.risk.yes": get_schema_id(Title.INSURED_DETAILS) + "." + "occupation_risk",
        "insured_2.occupation.risk.yes": get_schema_id(Title.INSURED_DETAILS) + "." + "occupation_risk",
        "applicant.occupation.risk.yes": get_schema_id(Title.INSURED_DETAILS) + "." + "occupation_risk",
        "patient.occupation.risk.no": get_schema_id(Title.INSURED_DETAILS) + "." + "occupation_risk",
        "insured_1.occupation.risk.no": get_schema_id(Title.INSURED_DETAILS) + "." + "occupation_risk",
        "insured_2.occupation.risk.no": get_schema_id(Title.INSURED_DETAILS) + "." + "occupation_risk",
        "applicant.occupation.risk.no": get_schema_id(Title.INSURED_DETAILS) + "." + "occupation_risk",
        "insured_1.ssn": get_schema_id(Title.INSURED_DETAILS) + "." + "ssn",
        "insured_2.ssn": get_schema_id(Title.INSURED_DETAILS) + "." + "ssn",
        "patient.ssn": get_schema_id(Title.INSURED_DETAILS) + "." + "ssn",
        "applicant.ssn": get_schema_id(Title.INSURED_DETAILS) + "." + "ssn",
        "insured_1.tax.number": get_schema_id(Title.INSURED_DETAILS) + "." + "ssn",
        "insured_2.tax.number": get_schema_id(Title.INSURED_DETAILS) + "." + "ssn",
        "patient.tax.number": get_schema_id(Title.INSURED_DETAILS) + "." + "ssn",
        "applicant.tax.number": get_schema_id(Title.INSURED_DETAILS) + "." + "ssn",
        "insured_1.id_number": get_schema_id(Title.INSURED_DETAILS) + "." + "ssn",
        "insured_2.id_number": get_schema_id(Title.INSURED_DETAILS) + "." + "ssn",
        "patient.id_number": get_schema_id(Title.INSURED_DETAILS) + "." + "ssn",
        "applicant.id_number": get_schema_id(Title.INSURED_DETAILS) + "." + "ssn",
        "insured_1.birth": get_schema_id(Title.INSURED_DETAILS) + "." + "date_of_birth",
        "patient.birth": get_schema_id(Title.INSURED_DETAILS) + "." + "date_of_birth",
        "insured_2.birth": get_schema_id(Title.INSURED_DETAILS) + "." + "date_of_birth",
        "applicant.birth": get_schema_id(Title.INSURED_DETAILS) + "." + "date_of_birth",
        "insured_1.birth.date": get_schema_id(Title.INSURED_DETAILS) + "." + "date_of_birth",
        "patient.birth.date": get_schema_id(Title.INSURED_DETAILS) + "." + "date_of_birth",
        "insured_2.birth.date": get_schema_id(Title.INSURED_DETAILS) + "." + "date_of_birth",
        "applicant.birth.date": get_schema_id(Title.INSURED_DETAILS) + "." + "date_of_birth",
        "insured_1.place_of_birth": get_schema_id(Title.INSURED_DETAILS) + "." + "birth_country",
        "patient.place_of_birth": get_schema_id(Title.INSURED_DETAILS) + "." + "birth_country",
        "insured_2.place_of_birth": get_schema_id(Title.INSURED_DETAILS) + "." + "birth_country",
        "applicant.place_of_birth": get_schema_id(Title.INSURED_DETAILS) + "." + "birth_country",
        "insured_1.birth.country": get_schema_id(Title.INSURED_DETAILS) + "." + "birth_country",
        "patient.birth.country": get_schema_id(Title.INSURED_DETAILS) + "." + "birth_country",
        "insured_2.birth.country": get_schema_id(Title.INSURED_DETAILS) + "." + "birth_country",
        "applicant.birth.country": get_schema_id(Title.INSURED_DETAILS) + "." + "birth_country",
        "insured_1.birth.state": get_schema_id(Title.INSURED_DETAILS) + "." + "birth_state",
        "patient.birth.state": get_schema_id(Title.INSURED_DETAILS) + "." + "birth_state",
        "insured_2.birth.state": get_schema_id(Title.INSURED_DETAILS) + "." + "birth_state",
        "applicant.birth.state": get_schema_id(Title.INSURED_DETAILS) + "." + "birth_state",
        # "insured_1.birth.city": get_schema_id(Title.INSURED_DETAILS) + "." + "birth_country",
        # "patient.birth.city": get_schema_id(Title.INSURED_DETAILS) + "." + "birth_country",
        # "insured_2.birth.city": get_schema_id(Title.INSURED_DETAILS) + "." + "birth_country",
        # "applicant.birth.city": get_schema_id(Title.INSURED_DETAILS) + "." + "birth_country",
        "insured_1.name": get_schema_id(Title.INSURED_DETAILS) + "." + "name",
        "patient.name": get_schema_id(Title.INSURED_DETAILS) + "." + "name",
        "insured_2.name": get_schema_id(Title.INSURED_DETAILS) + "." + "name",
        "applicant.name": get_schema_id(Title.INSURED_DETAILS) + "." + "name",
        "insured_1.name.prefix": get_schema_id(Title.INSURED_DETAILS) + "." + "name.prefix",
        "patient.name.prefix": get_schema_id(Title.INSURED_DETAILS) + "." + "name.prefix",
        "insured_2.name.prefix": get_schema_id(Title.INSURED_DETAILS) + "." + "name.prefix",
        "applicant.name.prefix": get_schema_id(Title.INSURED_DETAILS) + "." + "name.prefix",
        "insured_1.name.family_name": get_schema_id(Title.INSURED_DETAILS) + "." + "name.family_name",
        "patient.name.family_name": get_schema_id(Title.INSURED_DETAILS) + "." + "name.family_name",
        "insured_2.name.family_name": get_schema_id(Title.INSURED_DETAILS) + "." + "name.family_name",
        "applicant.name.family_name": get_schema_id(Title.INSURED_DETAILS) + "." + "name.family_name",
        "insured_1.name.suffix": get_schema_id(Title.INSURED_DETAILS) + "." + "name.suffix",
        "patient.name.suffix": get_schema_id(Title.INSURED_DETAILS) + "." + "name.suffix",
        "insured_2.name.suffix": get_schema_id(Title.INSURED_DETAILS) + "." + "name.suffix",
        "applicant.name.suffix": get_schema_id(Title.INSURED_DETAILS) + "." + "name.suffix",
        "insured_1.name.given_name": get_schema_id(Title.INSURED_DETAILS) + "." + "name.given_name",
        "patient.name.given_name": get_schema_id(Title.INSURED_DETAILS) + "." + "name.given_name",
        "insured_2.name.given_name": get_schema_id(Title.INSURED_DETAILS) + "." + "name.given_name",
        "applicant.name.given_name": get_schema_id(Title.INSURED_DETAILS) + "." + "name.given_name",
        "patient.gender": get_schema_id(Title.INSURED_DETAILS) + "." + "gender",
        "insured_1.gender": get_schema_id(Title.INSURED_DETAILS) + "." + "gender",
        "insured_2.gender": get_schema_id(Title.INSURED_DETAILS) + "." + "gender",
        "applicant.gender": get_schema_id(Title.INSURED_DETAILS) + "." + "gender",
        "social.marital": get_schema_id(Title.INSURED_DETAILS) + "." + "marital_status",
        "patient.marital": get_schema_id(Title.INSURED_DETAILS) + "." + "marital_status",
        "insured_1.marital": get_schema_id(Title.INSURED_DETAILS) + "." + "marital_status",
        "insured_2.marital": get_schema_id(Title.INSURED_DETAILS) + "." + "marital_status",
        "applicant.marital": get_schema_id(Title.INSURED_DETAILS) + "." + "marital_status",
        "social.occupation": get_schema_id(Title.INSURED_DETAILS) + "." + "occupation",
        "insured_1.weight": get_schema_id(Title.INSURED_DETAILS) + "." + "weight",
        "insured_2.weight": get_schema_id(Title.INSURED_DETAILS) + "." + "weight",
        "patient.weight": get_schema_id(Title.INSURED_DETAILS) + "." + "weight",
        "insured_1.height": get_schema_id(Title.INSURED_DETAILS) + "." + "height",
        "insured_2.height": get_schema_id(Title.INSURED_DETAILS) + "." + "height",
        "patient.height": get_schema_id(Title.INSURED_DETAILS) + "." + "height",
        "social.alcohol": get_schema_id(Title.INSURED_DETAILS) + "." + "alcohol",
        "insured_1.alcohol": get_schema_id(Title.INSURED_DETAILS) + "." + "alcohol",
        "insured_2.alcohol": get_schema_id(Title.INSURED_DETAILS) + "." + "alcohol",
        "patient.alcohol": get_schema_id(Title.INSURED_DETAILS) + "." + "alcohol",
        "social.smoking": get_schema_id(Title.INSURED_DETAILS) + "." + "smoking",
        "insured_1.smoking": get_schema_id(Title.INSURED_DETAILS) + "." + "smoking",
        "insured_2.smoking": get_schema_id(Title.INSURED_DETAILS) + "." + "smoking",
        "patient.smoking": get_schema_id(Title.INSURED_DETAILS) + "." + "smoking",
        "social.drug": get_schema_id(Title.INSURED_DETAILS) + "." + "drugs",
        "insured_1.drug": get_schema_id(Title.INSURED_DETAILS) + "." + "drugs",
        "insured_2.drug": get_schema_id(Title.INSURED_DETAILS) + "." + "drugs",
        "patient.drug": get_schema_id(Title.INSURED_DETAILS) + "." + "drugs",
        "vital.weight_lbs": get_schema_id(Title.INSURED_DETAILS) + "." + "weight",
        "patient.weight_lbs": get_schema_id(Title.INSURED_DETAILS) + "." + "weight",
        "vital.weight_kg": get_schema_id(Title.INSURED_DETAILS) + "." + "weight",
        "patient.weight_kg": get_schema_id(Title.INSURED_DETAILS) + "." + "weight",
        "vital.height_in": get_schema_id(Title.INSURED_DETAILS) + "." + "height",
        "patient.height_in": get_schema_id(Title.INSURED_DETAILS) + "." + "height",
        "vital.height_cm": get_schema_id(Title.INSURED_DETAILS) + "." + "height",
        "patient.height_cm": get_schema_id(Title.INSURED_DETAILS) + "." + "height",
        "insured_1.weight_lbs": get_schema_id(Title.INSURED_DETAILS) + "." + "weight",
        "insured_1.weight_kg": get_schema_id(Title.INSURED_DETAILS) + "." + "weight",
        "insured_1.height_in": get_schema_id(Title.INSURED_DETAILS) + "." + "height",
        "insured_1.height_cm": get_schema_id(Title.INSURED_DETAILS) + "." + "height",
        "insured_2.weight_lbs": get_schema_id(Title.INSURED_DETAILS) + "." + "weight",
        "insured_2.weight_kg": get_schema_id(Title.INSURED_DETAILS) + "." + "weight",
        "insured_2.height_in": get_schema_id(Title.INSURED_DETAILS) + "." + "height",
        "insured_2.height_cm": get_schema_id(Title.INSURED_DETAILS) + "." + "height",
        "patient.smoking.status.yes": get_schema_id(Title.INSURED_DETAILS) + "." + "smoking",
        "patient.smoking.status.no": get_schema_id(Title.INSURED_DETAILS) + "." + "smoking",
        "patient.smoking.status.former": get_schema_id(Title.INSURED_DETAILS) + "." + "smoking",
        "patient.smoking.status.passive": get_schema_id(Title.INSURED_DETAILS) + "." + "smoking",
        "insured_1.smoking.status.yes": get_schema_id(Title.INSURED_DETAILS) + "." + "smoking",
        "insured_1.smoking.status.no": get_schema_id(Title.INSURED_DETAILS) + "." + "smoking",
        "insured_1.smoking.status.former": get_schema_id(Title.INSURED_DETAILS) + "." + "smoking",
        "insured_1.smoking.status.passive": get_schema_id(Title.INSURED_DETAILS) + "." + "smoking",
        "insured_2.smoking.status.yes": get_schema_id(Title.INSURED_DETAILS) + "." + "smoking",
        "insured_2.smoking.status.no": get_schema_id(Title.INSURED_DETAILS) + "." + "smoking",
        "insured_2.smoking.status.former": get_schema_id(Title.INSURED_DETAILS) + "." + "smoking",
        "insured_2.smoking.status.passive": get_schema_id(Title.INSURED_DETAILS) + "." + "smoking",
        "applicant.smoking.status.yes": get_schema_id(Title.INSURED_DETAILS) + "." + "smoking",
        "applicant.smoking.status.no": get_schema_id(Title.INSURED_DETAILS) + "." + "smoking",
        "applicant.smoking.status.former": get_schema_id(Title.INSURED_DETAILS) + "." + "smoking",
        "applicant.smoking.status.passive": get_schema_id(Title.INSURED_DETAILS) + "." + "smoking",
        "applicant.smoking.quantity": get_schema_id(Title.INSURED_DETAILS) + "." + "smoking",
        "applicant.smoking.device": get_schema_id(Title.INSURED_DETAILS) + "." + "smoking",
        "applicant.smoking.duration": get_schema_id(Title.INSURED_DETAILS) + "." + "smoking",
        "patient.smoking.quantity": get_schema_id(Title.INSURED_DETAILS) + "." + "smoking",
        "patient.smoking.device": get_schema_id(Title.INSURED_DETAILS) + "." + "smoking",
        "patient.smoking.duration": get_schema_id(Title.INSURED_DETAILS) + "." + "smoking",
        "insured_1.smoking.quantity": get_schema_id(Title.INSURED_DETAILS) + "." + "smoking",
        "insured_1.smoking.device": get_schema_id(Title.INSURED_DETAILS) + "." + "smoking",
        "insured_1.smoking.duration": get_schema_id(Title.INSURED_DETAILS) + "." + "smoking",
        "insured_2.smoking.quantity": get_schema_id(Title.INSURED_DETAILS) + "." + "smoking",
        "insured_2.smoking.device": get_schema_id(Title.INSURED_DETAILS) + "." + "smoking",
        "insured_2.smoking.duration": get_schema_id(Title.INSURED_DETAILS) + "." + "smoking",
        "patient.tobacco.status.yes": get_schema_id(Title.INSURED_DETAILS) + "." + "smoking",
        "patient.tobacco.status.no": get_schema_id(Title.INSURED_DETAILS) + "." + "smoking",
        "patient.tobacco.status.former": get_schema_id(Title.INSURED_DETAILS) + "." + "smoking",
        "insured_1.tobacco.status.yes": get_schema_id(Title.INSURED_DETAILS) + "." + "smoking",
        "insured_1.tobacco.status.no": get_schema_id(Title.INSURED_DETAILS) + "." + "smoking",
        "insured_1.tobacco.status.former": get_schema_id(Title.INSURED_DETAILS) + "." + "smoking",
        "insured_2.tobacco.status.yes": get_schema_id(Title.INSURED_DETAILS) + "." + "smoking",
        "insured_2.tobacco.status.no": get_schema_id(Title.INSURED_DETAILS) + "." + "smoking",
        "insured_2.tobacco.status.former": get_schema_id(Title.INSURED_DETAILS) + "." + "smoking",
        "applicant.tobacco.status.yes": get_schema_id(Title.INSURED_DETAILS) + "." + "smoking",
        "applicant.tobacco.status.no": get_schema_id(Title.INSURED_DETAILS) + "." + "smoking",
        "applicant.tobacco.status.former": get_schema_id(Title.INSURED_DETAILS) + "." + "smoking",
        "patient.tobacco.quantity": get_schema_id(Title.INSURED_DETAILS) + "." + "smoking",
        "patient.tobacco.duration": get_schema_id(Title.INSURED_DETAILS) + "." + "smoking",
        "patient.tobacco.product": get_schema_id(Title.INSURED_DETAILS) + "." + "smoking",
        "insured_1.tobacco.quantity": get_schema_id(Title.INSURED_DETAILS) + "." + "smoking",
        "insured_1.tobacco.duration": get_schema_id(Title.INSURED_DETAILS) + "." + "smoking",
        "insured_1.tobacco.product": get_schema_id(Title.INSURED_DETAILS) + "." + "smoking",
        "insured_2.tobacco.quantity": get_schema_id(Title.INSURED_DETAILS) + "." + "smoking",
        "insured_2.tobacco.duration": get_schema_id(Title.INSURED_DETAILS) + "." + "smoking",
        "insured_2.tobacco.product": get_schema_id(Title.INSURED_DETAILS) + "." + "smoking",
        "applicant.tobacco.quantity": get_schema_id(Title.INSURED_DETAILS) + "." + "smoking",
        "applicant.tobacco.duration": get_schema_id(Title.INSURED_DETAILS) + "." + "smoking",
        "applicant.tobacco.product": get_schema_id(Title.INSURED_DETAILS) + "." + "smoking",
        "patient.alcohol.status.yes": get_schema_id(Title.INSURED_DETAILS) + "." + "alcohol",
        "patient.alcohol.status.no": get_schema_id(Title.INSURED_DETAILS) + "." + "alcohol",
        "patient.alcohol.status.former": get_schema_id(Title.INSURED_DETAILS) + "." + "alcohol",
        "insured_1.alcohol.status.yes": get_schema_id(Title.INSURED_DETAILS) + "." + "alcohol",
        "insured_1.alcohol.status.no": get_schema_id(Title.INSURED_DETAILS) + "." + "alcohol",
        "insured_1.alcohol.status.former": get_schema_id(Title.INSURED_DETAILS) + "." + "alcohol",
        "insured_2.alcohol.status.yes": get_schema_id(Title.INSURED_DETAILS) + "." + "alcohol",
        "insured_2.alcohol.status.no": get_schema_id(Title.INSURED_DETAILS) + "." + "alcohol",
        "insured_2.alcohol.status.former": get_schema_id(Title.INSURED_DETAILS) + "." + "alcohol",
        "applicant.alcohol.status.yes": get_schema_id(Title.INSURED_DETAILS) + "." + "alcohol",
        "applicant.alcohol.status.no": get_schema_id(Title.INSURED_DETAILS) + "." + "alcohol",
        "applicant.alcohol.status.former": get_schema_id(Title.INSURED_DETAILS) + "." + "alcohol",
        "patient.alcohol.quantity": get_schema_id(Title.INSURED_DETAILS) + "." + "alcohol",
        "patient.alcohol.duration": get_schema_id(Title.INSURED_DETAILS) + "." + "alcohol",
        "patient.alcohol.beverage": get_schema_id(Title.INSURED_DETAILS) + "." + "alcohol",
        "insured_1.alcohol.quantity": get_schema_id(Title.INSURED_DETAILS) + "." + "alcohol",
        "insured_1.alcohol.duration": get_schema_id(Title.INSURED_DETAILS) + "." + "alcohol",
        "insured_1.alcohol.beverage": get_schema_id(Title.INSURED_DETAILS) + "." + "alcohol",
        "insured_2.alcohol.quantity": get_schema_id(Title.INSURED_DETAILS) + "." + "alcohol",
        "insured_2.alcohol.duration": get_schema_id(Title.INSURED_DETAILS) + "." + "alcohol",
        "insured_2.alcohol.beverage": get_schema_id(Title.INSURED_DETAILS) + "." + "alcohol",
        "patient.drugs.status.yes": get_schema_id(Title.INSURED_DETAILS) + "." + "drugs",
        "patient.drugs.status.no": get_schema_id(Title.INSURED_DETAILS) + "." + "drugs",
        "patient.drugs.status.former": get_schema_id(Title.INSURED_DETAILS) + "." + "drugs",
        "insured_1.drugs.status.yes": get_schema_id(Title.INSURED_DETAILS) + "." + "drugs",
        "insured_1.drugs.status.no": get_schema_id(Title.INSURED_DETAILS) + "." + "drugs",
        "insured_1.drugs.status.former": get_schema_id(Title.INSURED_DETAILS) + "." + "drugs",
        "insured_2.drugs.status.yes": get_schema_id(Title.INSURED_DETAILS) + "." + "drugs",
        "insured_2.drugs.status.no": get_schema_id(Title.INSURED_DETAILS) + "." + "drugs",
        "insured_2.drugs.status.former": get_schema_id(Title.INSURED_DETAILS) + "." + "drugs",
        "applicant.drugs.status.yes": get_schema_id(Title.INSURED_DETAILS) + "." + "drugs",
        "applicant.drugs.status.no": get_schema_id(Title.INSURED_DETAILS) + "." + "drugs",
        "applicant.drugs.status.former": get_schema_id(Title.INSURED_DETAILS) + "." + "drugs",
        "patient.drugs.quantity": get_schema_id(Title.INSURED_DETAILS) + "." + "drugs",
        "patient.drugs.duration": get_schema_id(Title.INSURED_DETAILS) + "." + "drugs",
        "patient.drugs.product": get_schema_id(Title.INSURED_DETAILS) + "." + "drugs",
        "insured_1.drugs.quantity": get_schema_id(Title.INSURED_DETAILS) + "." + "drugs",
        "insured_1.drugs.duration": get_schema_id(Title.INSURED_DETAILS) + "." + "drugs",
        "insured_1.drugs.product": get_schema_id(Title.INSURED_DETAILS) + "." + "drugs",
        "insured_2.drugs.quantity": get_schema_id(Title.INSURED_DETAILS) + "." + "drugs",
        "insured_2.drugs.duration": get_schema_id(Title.INSURED_DETAILS) + "." + "drugs",
        "insured_2.drugs.product": get_schema_id(Title.INSURED_DETAILS) + "." + "drugs",
        "applicant.drugs.quantity": get_schema_id(Title.INSURED_DETAILS) + "." + "drugs",
        "applicant.drugs.duration": get_schema_id(Title.INSURED_DETAILS) + "." + "drugs",
        "applicant.drugs.product": get_schema_id(Title.INSURED_DETAILS) + "." + "drugs",
        "patient.marijuana.status.yes": get_schema_id(Title.INSURED_DETAILS) + "." + "marijuana",
        "patient.marijuana.status.no": get_schema_id(Title.INSURED_DETAILS) + "." + "marijuana",
        "patient.marijuana.status.details": get_schema_id(Title.INSURED_DETAILS) + "." + "marijuana",
        "patient.marijuana.frequency": get_schema_id(Title.INSURED_DETAILS) + "." + "marijuana",
        "patient.marijuana.quantity": get_schema_id(Title.INSURED_DETAILS) + "." + "marijuana",
        "patient.marijuana.quantity.unit": get_schema_id(Title.INSURED_DETAILS) + "." + "marijuana",
        "patient.marijuana.end_date": get_schema_id(Title.INSURED_DETAILS) + "." + "marijuana",
        "patient.marijuana.start_date": get_schema_id(Title.INSURED_DETAILS) + "." + "marijuana",
        "patient.marijuana.duration": get_schema_id(Title.INSURED_DETAILS) + "." + "marijuana",
        "patient.marijuana.reason.recreational": get_schema_id(Title.INSURED_DETAILS) + "." + "marijuana",
        "patient.marijuana.reason.medical": get_schema_id(Title.INSURED_DETAILS) + "." + "marijuana",
        "patient.marijuana.reason.other": get_schema_id(Title.INSURED_DETAILS) + "." + "marijuana",
        "patient.marijuana.reason.details": get_schema_id(Title.INSURED_DETAILS) + "." + "marijuana",
        "patient.marijuana.consumption_method.smoking": get_schema_id(Title.INSURED_DETAILS) + "." + "marijuana",
        "patient.marijuana.consumption_method.vaporizing": get_schema_id(Title.INSURED_DETAILS) + "." + "marijuana",
        "patient.marijuana.consumption_method.edible": get_schema_id(Title.INSURED_DETAILS) + "." + "marijuana",
        "patient.marijuana.consumption_method.other": get_schema_id(Title.INSURED_DETAILS) + "." + "marijuana",
        "applicant.marijuana.status.yes": get_schema_id(Title.INSURED_DETAILS) + "." + "marijuana",
        "applicant.marijuana.status.no": get_schema_id(Title.INSURED_DETAILS) + "." + "marijuana",
        "applicant.marijuana.status.details": get_schema_id(Title.INSURED_DETAILS) + "." + "marijuana",
        "applicant.marijuana.frequency": get_schema_id(Title.INSURED_DETAILS) + "." + "marijuana",
        "applicant.marijuana.quantity": get_schema_id(Title.INSURED_DETAILS) + "." + "marijuana",
        "applicant.marijuana.quantity.unit": get_schema_id(Title.INSURED_DETAILS) + "." + "marijuana",
        "applicant.marijuana.end_date": get_schema_id(Title.INSURED_DETAILS) + "." + "marijuana",
        "applicant.marijuana.start_date": get_schema_id(Title.INSURED_DETAILS) + "." + "marijuana",
        "applicant.marijuana.duration": get_schema_id(Title.INSURED_DETAILS) + "." + "marijuana",
        "applicant.marijuana.reason.recreational": get_schema_id(Title.INSURED_DETAILS) + "." + "marijuana",
        "applicant.marijuana.reason.medical": get_schema_id(Title.INSURED_DETAILS) + "." + "marijuana",
        "applicant.marijuana.reason.other": get_schema_id(Title.INSURED_DETAILS) + "." + "marijuana",
        "applicant.marijuana.reason.details": get_schema_id(Title.INSURED_DETAILS) + "." + "marijuana",
        "applicant.marijuana.consumption_method.smoking": get_schema_id(Title.INSURED_DETAILS) + "." + "marijuana",
        "applicant.marijuana.consumption_method.vaporizing": get_schema_id(Title.INSURED_DETAILS) + "." + "marijuana",
        "applicant.marijuana.consumption_method.edible": get_schema_id(Title.INSURED_DETAILS) + "." + "marijuana",
        "applicant.marijuana.consumption_method.other": get_schema_id(Title.INSURED_DETAILS) + "." + "marijuana",
        "insured_1.marijuana.status.yes": get_schema_id(Title.INSURED_DETAILS) + "." + "marijuana",
        "insured_1.marijuana.status.no": get_schema_id(Title.INSURED_DETAILS) + "." + "marijuana",
        "insured_1.marijuana.status.details": get_schema_id(Title.INSURED_DETAILS) + "." + "marijuana",
        "insured_1.marijuana.frequency": get_schema_id(Title.INSURED_DETAILS) + "." + "marijuana",
        "insured_1.marijuana.quantity": get_schema_id(Title.INSURED_DETAILS) + "." + "marijuana",
        "insured_1.marijuana.quantity.unit": get_schema_id(Title.INSURED_DETAILS) + "." + "marijuana",
        "insured_1.marijuana.end_date": get_schema_id(Title.INSURED_DETAILS) + "." + "marijuana",
        "insured_1.marijuana.start_date": get_schema_id(Title.INSURED_DETAILS) + "." + "marijuana",
        "insured_1.marijuana.duration": get_schema_id(Title.INSURED_DETAILS) + "." + "marijuana",
        "insured_1.marijuana.reason.recreational": get_schema_id(Title.INSURED_DETAILS) + "." + "marijuana",
        "insured_1.marijuana.reason.medical": get_schema_id(Title.INSURED_DETAILS) + "." + "marijuana",
        "insured_1.marijuana.reason.other": get_schema_id(Title.INSURED_DETAILS) + "." + "marijuana",
        "insured_1.marijuana.reason.details": get_schema_id(Title.INSURED_DETAILS) + "." + "marijuana",
        "insured_1.marijuana.consumption_method.smoking": get_schema_id(Title.INSURED_DETAILS) + "." + "marijuana",
        "insured_1.marijuana.consumption_method.vaporizing": get_schema_id(Title.INSURED_DETAILS) + "." + "marijuana",
        "insured_1.marijuana.consumption_method.edible": get_schema_id(Title.INSURED_DETAILS) + "." + "marijuana",
        "insured_1.marijuana.consumption_method.other": get_schema_id(Title.INSURED_DETAILS) + "." + "marijuana",
        "insured_2.marijuana.status.yes": get_schema_id(Title.INSURED_DETAILS) + "." + "marijuana",
        "insured_2.marijuana.status.no": get_schema_id(Title.INSURED_DETAILS) + "." + "marijuana",
        "insured_2.marijuana.status.details": get_schema_id(Title.INSURED_DETAILS) + "." + "marijuana",
        "insured_2.marijuana.frequency": get_schema_id(Title.INSURED_DETAILS) + "." + "marijuana",
        "insured_2.marijuana.quantity": get_schema_id(Title.INSURED_DETAILS) + "." + "marijuana",
        "insured_2.marijuana.quantity.unit": get_schema_id(Title.INSURED_DETAILS) + "." + "marijuana",
        "insured_2.marijuana.end_date": get_schema_id(Title.INSURED_DETAILS) + "." + "marijuana",
        "insured_2.marijuana.start_date": get_schema_id(Title.INSURED_DETAILS) + "." + "marijuana",
        "insured_2.marijuana.duration": get_schema_id(Title.INSURED_DETAILS) + "." + "marijuana",
        "insured_2.marijuana.reason.recreational": get_schema_id(Title.INSURED_DETAILS) + "." + "marijuana",
        "insured_2.marijuana.reason.medical": get_schema_id(Title.INSURED_DETAILS) + "." + "marijuana",
        "insured_2.marijuana.reason.other": get_schema_id(Title.INSURED_DETAILS) + "." + "marijuana",
        "insured_2.marijuana.reason.details": get_schema_id(Title.INSURED_DETAILS) + "." + "marijuana",
        "insured_2.marijuana.consumption_method.smoking": get_schema_id(Title.INSURED_DETAILS) + "." + "marijuana",
        "insured_2.marijuana.consumption_method.vaporizing": get_schema_id(Title.INSURED_DETAILS) + "." + "marijuana",
        "insured_2.marijuana.consumption_method.edible": get_schema_id(Title.INSURED_DETAILS) + "." + "marijuana",
        "insured_2.marijuana.consumption_method.other": get_schema_id(Title.INSURED_DETAILS) + "." + "marijuana",
        "insured_1.avocation.activity.risk_level.high": get_schema_id(Title.INSURED_DETAILS) + "." + "avocation",
        "insured_1.avocation.activity.risk_level.medium": get_schema_id(Title.INSURED_DETAILS) + "." + "avocation",
        "insured_1.avocation.activity.risk_level.low": get_schema_id(Title.INSURED_DETAILS) + "." + "avocation",
        "insured_2.avocation.activity.risk_level.high": get_schema_id(Title.INSURED_DETAILS) + "." + "avocation",
        "insured_2.avocation.activity.risk_level.medium": get_schema_id(Title.INSURED_DETAILS) + "." + "avocation",
        "insured_2.avocation.activity.risk_level.low": get_schema_id(Title.INSURED_DETAILS) + "." + "avocation",
        "patient.avocation.activity.risk_level.high": get_schema_id(Title.INSURED_DETAILS) + "." + "avocation",
        "patient.avocation.activity.risk_level.medium": get_schema_id(Title.INSURED_DETAILS) + "." + "avocation",
        "patient.avocation.activity.risk_level.low": get_schema_id(Title.INSURED_DETAILS) + "." + "avocation",
        "applicant.avocation.activity.risk_level.high": get_schema_id(Title.INSURED_DETAILS) + "." + "avocation",
        "applicant.avocation.activity.risk_level.medium": get_schema_id(Title.INSURED_DETAILS) + "." + "avocation",
        "applicant.avocation.activity.risk_level.low": get_schema_id(Title.INSURED_DETAILS) + "." + "avocation",
        "insured_1.travel.international": get_schema_id(Title.INSURED_DETAILS) + "." + "travel",
        "insured_1.travel.domestic": get_schema_id(Title.INSURED_DETAILS) + "." + "travel",
        "insured_2.travel.international": get_schema_id(Title.INSURED_DETAILS) + "." + "travel",
        "insured_2.travel.domestic": get_schema_id(Title.INSURED_DETAILS) + "." + "travel",
        "patient.travel.international": get_schema_id(Title.INSURED_DETAILS) + "." + "travel",
        "patient.travel.domestic": get_schema_id(Title.INSURED_DETAILS) + "." + "travel",
        "applicant.travel.international": get_schema_id(Title.INSURED_DETAILS) + "." + "travel",
        "applicant.travel.domestic": get_schema_id(Title.INSURED_DETAILS) + "." + "travel",
        "insured_1.lifestyle": get_schema_id(Title.INSURED_DETAILS) + "." + "behavior",
        "insured_1.driving.record": get_schema_id(Title.INSURED_DETAILS) + "." + "behavior",
        "insured_1.criminal.record": get_schema_id(Title.INSURED_DETAILS) + "." + "behavior",
        "insured_2.lifestyle": get_schema_id(Title.INSURED_DETAILS) + "." + "behavior",
        "insured_2.driving.record": get_schema_id(Title.INSURED_DETAILS) + "." + "behavior",
        "insured_2.criminal.record": get_schema_id(Title.INSURED_DETAILS) + "." + "behavior",
        "patient.lifestyle": get_schema_id(Title.INSURED_DETAILS) + "." + "behavior",
        "patient.driving.record": get_schema_id(Title.INSURED_DETAILS) + "." + "behavior",
        "patient.criminal.record": get_schema_id(Title.INSURED_DETAILS) + "." + "behavior",
        "applicant.lifestyle": get_schema_id(Title.INSURED_DETAILS) + "." + "behavior",
        "applicant.driving.record": get_schema_id(Title.INSURED_DETAILS) + "." + "behavior",
        "applicant.criminal.record": get_schema_id(Title.INSURED_DETAILS) + "." + "behavior",
        "beneficiary.relationship_with_insured_1": get_schema_id(Title.INSURED_DETAILS) + "." + "beneficiary_relationship",
        "beneficiary.relationship_with_insured_2": get_schema_id(Title.INSURED_DETAILS) + "." + "beneficiary_relationship",
        "policy_holder.relationship_with_insured_1": get_schema_id(Title.INSURED_DETAILS) + "." + "owner_relationship",
        "policy_holder.relationship_with_insured_2": get_schema_id(Title.INSURED_DETAILS) + "." + "owner_relationship",
        "insured_1.citizenship": get_schema_id(Title.INSURED_DETAILS) + "." + "citizenship",
        "insured_2.citizenship": get_schema_id(Title.INSURED_DETAILS) + "." + "citizenship",
        "patient.citizenship": get_schema_id(Title.INSURED_DETAILS) + "." + "citizenship",
        "applicant.citizenship": get_schema_id(Title.INSURED_DETAILS) + "." + "citizenship",
        "insured_1.residentship": get_schema_id(Title.INSURED_DETAILS) + "." + "residentship",
        "insured_2.residentship": get_schema_id(Title.INSURED_DETAILS) + "." + "residentship",
        "patient.residentship": get_schema_id(Title.INSURED_DETAILS) + "." + "residentship",
        "applicant.residentship": get_schema_id(Title.INSURED_DETAILS) + "." + "residentship",
        "insured_1.cards_generated_residentship": get_schema_id(Title.INSURED_DETAILS) + "." + "residentship",
        "insured_2.cards_generated_residentship": get_schema_id(Title.INSURED_DETAILS) + "." + "residentship",
        "patient.cards_generated_residentship": get_schema_id(Title.INSURED_DETAILS) + "." + "residentship",
        "applicant.cards_generated_residentship": get_schema_id(Title.INSURED_DETAILS) + "." + "residentship",
        "insured_1.license.number": get_schema_id(Title.INSURED_DETAILS) + "." + "license_number",
        "insured_2.license.number": get_schema_id(Title.INSURED_DETAILS) + "." + "license_number",
        "patient.license.number": get_schema_id(Title.INSURED_DETAILS) + "." + "license_number",
        "applicant.license.number": get_schema_id(Title.INSURED_DETAILS) + "." + "license_number",
        "insured_1.license.state": get_schema_id(Title.INSURED_DETAILS) + "." + "license_state",
        "insured_2.license.state": get_schema_id(Title.INSURED_DETAILS) + "." + "license_state",
        "patient.license.state": get_schema_id(Title.INSURED_DETAILS) + "." + "license_state",
        "applicant.license.state": get_schema_id(Title.INSURED_DETAILS) + "." + "license_state",
        "insured_1.income": get_schema_id(Title.INSURED_DETAILS) + "." + "income",
        "insured_2.income": get_schema_id(Title.INSURED_DETAILS) + "." + "income",
        "patient.income": get_schema_id(Title.INSURED_DETAILS) + "." + "income",
        "applicant.income": get_schema_id(Title.INSURED_DETAILS) + "." + "income",
        "insured_1.us_citizen": get_schema_id(Title.INSURED_DETAILS) + "." + "us_citizen",
        "insured_2.us_citizen": get_schema_id(Title.INSURED_DETAILS) + "." + "us_citizen",
        "patient.us_citizen": get_schema_id(Title.INSURED_DETAILS) + "." + "us_citizen",
        "applicant.us_citizen": get_schema_id(Title.INSURED_DETAILS) + "." + "us_citizen",
        "insured_1.us_citizen.details": get_schema_id(Title.INSURED_DETAILS) + "." + "us_citizen",
        "insured_2.us_citizen.details": get_schema_id(Title.INSURED_DETAILS) + "." + "us_citizen",
        "patient.us_citizen.details": get_schema_id(Title.INSURED_DETAILS) + "." + "us_citizen",
        "applicant.us_citizen.details": get_schema_id(Title.INSURED_DETAILS) + "." + "us_citizen",
        "insured_1.us_citizen.yes": get_schema_id(Title.INSURED_DETAILS) + "." + "us_citizen",
        "insured_2.us_citizen.yes": get_schema_id(Title.INSURED_DETAILS) + "." + "us_citizen",
        "patient.us_citizen.yes": get_schema_id(Title.INSURED_DETAILS) + "." + "us_citizen",
        "applicant.us_citizen.yes": get_schema_id(Title.INSURED_DETAILS) + "." + "us_citizen",
        "insured_1.us_citizen.no": get_schema_id(Title.INSURED_DETAILS) + "." + "us_citizen",
        "insured_2.us_citizen.no": get_schema_id(Title.INSURED_DETAILS) + "." + "us_citizen",
        "patient.us_citizen.no": get_schema_id(Title.INSURED_DETAILS) + "." + "us_citizen",
        "applicant.us_citizen.no": get_schema_id(Title.INSURED_DETAILS) + "." + "us_citizen",
        "insured_1.sign.date": get_schema_id(Title.INSURED_DETAILS) + "." + "sign_date",
        "insured_2.sign.date": get_schema_id(Title.INSURED_DETAILS) + "." + "sign_date",
        "patient.sign.date": get_schema_id(Title.INSURED_DETAILS) + "." + "sign_date",
        "applicant.sign.date": get_schema_id(Title.INSURED_DETAILS) + "." + "sign_date",
        "insured_1." + const.GENERATED_ENTITY + "insured_type": get_schema_id(Title.INSURED_DETAILS) + "." + "insured_type",
        "insured_2." + const.GENERATED_ENTITY + "insured_type": get_schema_id(Title.INSURED_DETAILS) + "." + "insured_type",
        "patient." + const.GENERATED_ENTITY + "insured_type": get_schema_id(Title.INSURED_DETAILS) + "." + "insured_type",
        "applicant." + const.GENERATED_ENTITY + "insured_type": get_schema_id(Title.INSURED_DETAILS) + "." + "insured_type",
        "insured_1.sign.location.state": get_schema_id(Title.INSURED_DETAILS) + "." + "sign_state",
        "insured_1.sign.location.country": get_schema_id(Title.INSURED_DETAILS) + "." + "sign_state",
        "insured_2.sign.location.state": get_schema_id(Title.INSURED_DETAILS) + "." + "sign_state",
        "insured_2.sign.location.country": get_schema_id(Title.INSURED_DETAILS) + "." + "sign_state",
        "applicant.sign.location.state": get_schema_id(Title.INSURED_DETAILS) + "." + "sign_state",
        "applicant.sign.location.country": get_schema_id(Title.INSURED_DETAILS) + "." + "sign_state",
        "insured_1.policy.decision": get_schema_id(Title.INSURED_DETAILS) + "." + "client_company_assessment",
        "insured_2.policy.decision": get_schema_id(Title.INSURED_DETAILS) + "." + "client_company_assessment",
        "applicant.policy.decision": get_schema_id(Title.INSURED_DETAILS) + "." + "client_company_assessment",
        "insured_1.policy.assessment": get_schema_id(Title.INSURED_DETAILS) + "." + "client_company_assessment",
        "insured_2.policy.assessment": get_schema_id(Title.INSURED_DETAILS) + "." + "client_company_assessment",
        "applicant.policy.assessment": get_schema_id(Title.INSURED_DETAILS) + "." + "client_company_assessment",
        "insured_1.policy.mortality": get_schema_id(Title.INSURED_DETAILS) + "." + "client_company_assessment",
        "insured_2.policy.mortality": get_schema_id(Title.INSURED_DETAILS) + "." + "client_company_assessment",
        "applicant.policy.mortality": get_schema_id(Title.INSURED_DETAILS) + "." + "client_company_assessment",
        "insured_1.policy.reason": get_schema_id(Title.INSURED_DETAILS) + "." + "policy_reason",
        "insured_2.policy.reason": get_schema_id(Title.INSURED_DETAILS) + "." + "policy_reason",
        "applicant.policy.reason": get_schema_id(Title.INSURED_DETAILS) + "." + "policy_reason",
        "insured_1." + const.GENERATED_ENTITY + "us_resident": get_schema_id(Title.INSURED_DETAILS) + "." + "us_resident",
        "insured_2." + const.GENERATED_ENTITY + "us_resident": get_schema_id(Title.INSURED_DETAILS) + "." + "us_resident",
        "patient." + const.GENERATED_ENTITY + "us_resident": get_schema_id(Title.INSURED_DETAILS) + "." + "us_resident",
        "applicant." + const.GENERATED_ENTITY + "us_resident": get_schema_id(Title.INSURED_DETAILS) + "." + "us_resident",
        "insured_1.net_worth": get_schema_id(Title.INSURED_DETAILS) + "." + "net_worth",
        "insured_2.net_worth": get_schema_id(Title.INSURED_DETAILS) + "." + "net_worth",
        "patient.net_worth": get_schema_id(Title.INSURED_DETAILS) + "." + "net_worth",
        "applicant.net_worth": get_schema_id(Title.INSURED_DETAILS) + "." + "net_worth",
        "applicant.insurance_rated_differentialy_or_declined.yes": get_schema_id(Title.INSURED_DETAILS) + "." + "insurance_rated_differentialy_or_declined",
        "insured_1.insurance_rated_differentialy_or_declined.yes": get_schema_id(Title.INSURED_DETAILS) + "." + "insurance_rated_differentialy_or_declined",
        "insured_2.insurance_rated_differentialy_or_declined.yes": get_schema_id(Title.INSURED_DETAILS) + "." + "insurance_rated_differentialy_or_declined",
        "patient.insurance_rated_differentialy_or_declined.yes": get_schema_id(Title.INSURED_DETAILS) + "." + "insurance_rated_differentialy_or_declined",
        "applicant.insurance_rated_differentialy_or_declined.no": get_schema_id(Title.INSURED_DETAILS) + "." + "insurance_rated_differentialy_or_declined",
        "insured_1.insurance_rated_differentialy_or_declined.no": get_schema_id(Title.INSURED_DETAILS) + "." + "insurance_rated_differentialy_or_declined",
        "insured_2.insurance_rated_differentialy_or_declined.no": get_schema_id(Title.INSURED_DETAILS) + "." + "insurance_rated_differentialy_or_declined",
        "patient.insurance_rated_differentialy_or_declined.no": get_schema_id(Title.INSURED_DETAILS) + "." + "insurance_rated_differentialy_or_declined",
        "applicant.insurance_rated_differentialy_or_declined.details": get_schema_id(Title.INSURED_DETAILS) + "." + "insurance_rated_differentialy_or_declined",
        "insured_1.insurance_rated_differentialy_or_declined.details": get_schema_id(Title.INSURED_DETAILS) + "." + "insurance_rated_differentialy_or_declined",
        "insured_2.insurance_rated_differentialy_or_declined.details": get_schema_id(Title.INSURED_DETAILS) + "." + "insurance_rated_differentialy_or_declined",
        "patient.insurance_rated_differentialy_or_declined.details": get_schema_id(Title.INSURED_DETAILS) + "." + "insurance_rated_differentialy_or_declined",
        "applicant.filed_bankruptcy.yes": get_schema_id(Title.INSURED_DETAILS) + "." + "filed_bankruptcy",
        "insured_1.filed_bankruptcy.yes": get_schema_id(Title.INSURED_DETAILS) + "." + "filed_bankruptcy",
        "insured_2.filed_bankruptcy.yes": get_schema_id(Title.INSURED_DETAILS) + "." + "filed_bankruptcy",
        "patient.filed_bankruptcy.yes": get_schema_id(Title.INSURED_DETAILS) + "." + "filed_bankruptcy",
        "applicant.filed_bankruptcy.no": get_schema_id(Title.INSURED_DETAILS) + "." + "filed_bankruptcy",
        "insured_1.filed_bankruptcy.no": get_schema_id(Title.INSURED_DETAILS) + "." + "filed_bankruptcy",
        "insured_2.filed_bankruptcy.no": get_schema_id(Title.INSURED_DETAILS) + "." + "filed_bankruptcy",
        "patient.filed_bankruptcy.no": get_schema_id(Title.INSURED_DETAILS) + "." + "filed_bankruptcy",
        "applicant.filed_bankruptcy.details": get_schema_id(Title.INSURED_DETAILS) + "." + "filed_bankruptcy",
        "insured_1.filed_bankruptcy.details": get_schema_id(Title.INSURED_DETAILS) + "." + "filed_bankruptcy",
        "insured_2.filed_bankruptcy.details": get_schema_id(Title.INSURED_DETAILS) + "." + "filed_bankruptcy",
        "patient.filed_bankruptcy.details": get_schema_id(Title.INSURED_DETAILS) + "." + "filed_bankruptcy",
        "insured_1.age": get_schema_id(Title.INSURED_DETAILS) + "." + "age",
        "insured_2.age": get_schema_id(Title.INSURED_DETAILS) + "." + "age",
        "patient.age": get_schema_id(Title.INSURED_DETAILS) + "." + "age",
        "applicant.age": get_schema_id(Title.INSURED_DETAILS) + "." + "age"
    },

    "technical_details": {
        "sum_insured": get_schema_id(Title.TECHNICAL_DETAILS) + "." + "sum_insured",
        "policy.product.sum_insured": get_schema_id(Title.TECHNICAL_DETAILS) + "." + "sum_insured",
        "annuity": get_schema_id(Title.TECHNICAL_DETAILS) + "." + "annuity",
        "policy.annuity": get_schema_id(Title.TECHNICAL_DETAILS) + "." + "annuity",
        "policy.annuity.amount": get_schema_id(Title.TECHNICAL_DETAILS) + "." + "annuity",
        "policy.product.premium.amount": get_schema_id(Title.TECHNICAL_DETAILS) + "." + "premium_amount",
        "policy.product.premium.payment_period.age": get_schema_id(Title.TECHNICAL_DETAILS) + "." + "payment_period",
        "policy.product.premium.payment_period.in_months": get_schema_id(Title.TECHNICAL_DETAILS) + "." + "payment_period",
        "policy.product.premium.payment_period.in_years": get_schema_id(Title.TECHNICAL_DETAILS) + "." + "payment_period",
        "policy.product.premium.payment_period.end.date": get_schema_id(Title.TECHNICAL_DETAILS) + "." + "payment_period",
        "policy.annuity.duration.payment_period.end.age": get_schema_id(Title.TECHNICAL_DETAILS) + "." + "annuity_duration",
        "policy.annuity.duration.payment_period.end.in_months": get_schema_id(Title.TECHNICAL_DETAILS) + "." + "annuity_duration",
        "policy.annuity.duration.payment_period.end.in_years": get_schema_id(Title.TECHNICAL_DETAILS) + "." + "annuity_duration",
        "annuity_duration.type": get_schema_id(Title.TECHNICAL_DETAILS) + "." + "annuity_duration_type",
        "policy.annuity.duration.payment_period.end.date": get_schema_id(Title.TECHNICAL_DETAILS) + "." + "annuity_end_date",
        "policy.product.name": get_schema_id(Title.TECHNICAL_DETAILS) + "." + "plan_type",
        "duration": get_schema_id(Title.TECHNICAL_DETAILS) + "." + "duration",
        "policy.product.duration.end.age": get_schema_id(Title.TECHNICAL_DETAILS) + "." + "duration",
        "policy.product.duration.in_years": get_schema_id(Title.TECHNICAL_DETAILS) + "." + "duration",
        "policy.product.duration.in_months": get_schema_id(Title.TECHNICAL_DETAILS) + "." + "duration",
        "policy.product.duration.end.date": get_schema_id(Title.TECHNICAL_DETAILS) + "." + "duration",
        "policy.duration.end_date": get_schema_id(Title.TECHNICAL_DETAILS) + "." + "duration",
        "policy.product.duration.whole_of_life": get_schema_id(Title.TECHNICAL_DETAILS) + "." + "duration",
        "duration.type": get_schema_id(Title.TECHNICAL_DETAILS) + "." + "duration_type",
        "currency": get_schema_id(Title.TECHNICAL_DETAILS) + "." + "currency",
        "product_type": get_schema_id(Title.TECHNICAL_DETAILS) + "." + "product_type",
        "policy.product.type": get_schema_id(Title.TECHNICAL_DETAILS) + "." + "product_type",
        "policy.product.cover_type": get_schema_id(Title.TECHNICAL_DETAILS) + "." + "product_type",
        "policy.start_date": get_schema_id(Title.TECHNICAL_DETAILS) + "." + "policy_start_date",
        "reinsurance.requested_amount": get_schema_id(Title.TECHNICAL_DETAILS) + "." + "reinsurance_amount",
        "reinsurance.total_amount": get_schema_id(Title.TECHNICAL_DETAILS) + "." + "risk_amount",
        "policy.indexation.value": get_schema_id(Title.TECHNICAL_DETAILS) + "." + "indexation",
        "policy.indexation.yes": get_schema_id(Title.TECHNICAL_DETAILS) + "." + "indexation",
        "policy.indexation.no": get_schema_id(Title.TECHNICAL_DETAILS) + "." + "indexation",
        "benefit.deferment": get_schema_id(Title.TECHNICAL_DETAILS) + "." + "deferment_period",
        "policy.purpose": get_schema_id(Title.TECHNICAL_DETAILS) + "." + "policy_purpose",
        "merged_policy.purpose": get_schema_id(Title.TECHNICAL_DETAILS) + "." + "policy_purpose",
    },
    "subjective_details": {
        "subjective.findings": get_schema_id(Title.SUBJECTIVE_DETAILS) + "." + "subjective_findings",
        "encounter.date": get_schema_id(Title.SUBJECTIVE_DETAILS) + "." + "date",
        "hpi.reason": get_schema_id(Title.SUBJECTIVE_DETAILS) + "." + "findings",
        "reason": get_schema_id(Title.SUBJECTIVE_DETAILS) + "." + "findings",
        "hpi.patient.symptom.positive": get_schema_id(Title.SUBJECTIVE_DETAILS) + "." + "findings",
        "hpi.patient.diagnosis": get_schema_id(Title.SUBJECTIVE_DETAILS) + "." + "findings",
        "pe.value.positive": get_schema_id(Title.SUBJECTIVE_DETAILS) + "." + "findings",
        "pe.value.abnormal": get_schema_id(Title.SUBJECTIVE_DETAILS) + "." + "findings",
        "pe.patient.value.positive": get_schema_id(Title.SUBJECTIVE_DETAILS) + "." + "findings",
        "pe.patient.value.abnormal": get_schema_id(Title.SUBJECTIVE_DETAILS) + "." + "findings",
        "patient.covid.positive": get_schema_id(Title.SUBJECTIVE_DETAILS) + "." + "findings",
        "insured_1.covid.positive": get_schema_id(Title.SUBJECTIVE_DETAILS) + "." + "findings",
        "insured_2.covid.positive": get_schema_id(Title.SUBJECTIVE_DETAILS) + "." + "findings",
        "patient.medical_disorder.yes": get_schema_id(Title.SUBJECTIVE_DETAILS) + "." + "findings",
        "insured_1.medical_disorder.yes": get_schema_id(Title.SUBJECTIVE_DETAILS) + "." + "findings",
        "insured_2.medical_disorder.yes": get_schema_id(Title.SUBJECTIVE_DETAILS) + "." + "findings",
        "patient.heart.disorder.risk.yes": get_schema_id(Title.SUBJECTIVE_DETAILS) + "." + "findings",
        "insured_1.heart.disorder.risk.yes": get_schema_id(Title.SUBJECTIVE_DETAILS) + "." + "findings",
        "insured_2.heart.disorder.risk.yes": get_schema_id(Title.SUBJECTIVE_DETAILS) + "." + "findings",
        "patient.neoplasm.risk.yes": get_schema_id(Title.SUBJECTIVE_DETAILS) + "." + "findings",
        "insured_1.neoplasm.risk.yes": get_schema_id(Title.SUBJECTIVE_DETAILS) + "." + "findings",
        "insured_2.neoplasm.risk.yes": get_schema_id(Title.SUBJECTIVE_DETAILS) + "." + "findings",
        "patient.mental.disorder.risk.yes": get_schema_id(Title.SUBJECTIVE_DETAILS) + "." + "findings",
        "insured_1.mental.disorder.risk.yes": get_schema_id(Title.SUBJECTIVE_DETAILS) + "." + "findings",
        "insured_2.mental.disorder.risk.yes": get_schema_id(Title.SUBJECTIVE_DETAILS) + "." + "findings",
        "patient.brain_hemorrhage.yes": get_schema_id(Title.SUBJECTIVE_DETAILS) + "." + "findings",
        "insured_1.brain_hemorrhage.yes": get_schema_id(Title.SUBJECTIVE_DETAILS) + "." + "findings",
        "insured_2.brain_hemorrhage.yes": get_schema_id(Title.SUBJECTIVE_DETAILS) + "." + "findings",
        "patient.neurological_disorder.risk.yes": get_schema_id(Title.SUBJECTIVE_DETAILS) + "." + "findings",
        "insured_1.neurological_disorder.risk.yes": get_schema_id(Title.SUBJECTIVE_DETAILS) + "." + "findings",
        "insured_2.neurological_disorder.risk.yes": get_schema_id(Title.SUBJECTIVE_DETAILS) + "." + "findings",
        "patient.immune.disorder.risk.yes": get_schema_id(Title.SUBJECTIVE_DETAILS) + "." + "findings",
        "insured_1.immune.disorder.risk.yes": get_schema_id(Title.SUBJECTIVE_DETAILS) + "." + "findings",
        "insured_2.immune.disorder.risk.yes": get_schema_id(Title.SUBJECTIVE_DETAILS) + "." + "findings",
        "patient.std.yes": get_schema_id(Title.SUBJECTIVE_DETAILS) + "." + "findings",
        "insured_1.std.yes": get_schema_id(Title.SUBJECTIVE_DETAILS) + "." + "findings",
        "insured_2.std.yes": get_schema_id(Title.SUBJECTIVE_DETAILS) + "." + "findings",
        "patient.muscleandbone.disorder.risk.yes": get_schema_id(Title.SUBJECTIVE_DETAILS) + "." + "findings",
        "insured_1.muscleandbone.disorder.risk.yes": get_schema_id(Title.SUBJECTIVE_DETAILS) + "." + "findings",
        "insured_2.muscleandbone.disorder.risk.yes": get_schema_id(Title.SUBJECTIVE_DETAILS) + "." + "findings",
        "patient.hepatitis.yes": get_schema_id(Title.SUBJECTIVE_DETAILS) + "." + "findings",
        "insured_1.hepatitis.yes": get_schema_id(Title.SUBJECTIVE_DETAILS) + "." + "findings",
        "insured_2.hepatitis.yes": get_schema_id(Title.SUBJECTIVE_DETAILS) + "." + "findings",
        "patient.endocrine.disorder.risk.yes": get_schema_id(Title.SUBJECTIVE_DETAILS) + "." + "findings",
        "insured_1.endocrine.disorder.risk.yes": get_schema_id(Title.SUBJECTIVE_DETAILS) + "." + "findings",
        "insured_2.endocrine.disorder.risk.yes": get_schema_id(Title.SUBJECTIVE_DETAILS) + "." + "findings",
        "patient.circulatory.disorder.risk.yes": get_schema_id(Title.SUBJECTIVE_DETAILS) + "." + "findings",
        "insured_1.circulatory.disorder.risk.yes": get_schema_id(Title.SUBJECTIVE_DETAILS) + "." + "findings",
        "insured_2.circulatory.disorder.risk.yes": get_schema_id(Title.SUBJECTIVE_DETAILS) + "." + "findings",
        "patient.blood.disorder.risk.yes": get_schema_id(Title.SUBJECTIVE_DETAILS) + "." + "findings",
        "insured_1.blood.disorder.risk.yes": get_schema_id(Title.SUBJECTIVE_DETAILS) + "." + "findings",
        "insured_2.blood.disorder.risk.yes": get_schema_id(Title.SUBJECTIVE_DETAILS) + "." + "findings",
        "patient.respiratory.disorder.risk.yes": get_schema_id(Title.SUBJECTIVE_DETAILS) + "." + "findings",
        "insured_1.respiratory.disorder.risk.yes": get_schema_id(Title.SUBJECTIVE_DETAILS) + "." + "findings",
        "insured_2.respiratory.disorder.risk.yes": get_schema_id(Title.SUBJECTIVE_DETAILS) + "." + "findings",
        "patient.ent_disorder.risk.yes": get_schema_id(Title.SUBJECTIVE_DETAILS) + "." + "findings",
        "insured_1.ent_disorder.risk.yes": get_schema_id(Title.SUBJECTIVE_DETAILS) + "." + "findings",
        "insured_2.ent_disorder.risk.yes": get_schema_id(Title.SUBJECTIVE_DETAILS) + "." + "findings",
        "patient.ent.nose_disorder.yes": get_schema_id(Title.SUBJECTIVE_DETAILS) + "." + "findings",
        "insured_1.ent.nose_disorder.yes": get_schema_id(Title.SUBJECTIVE_DETAILS) + "." + "findings",
        "insured_2.ent.nose_disorder.yes": get_schema_id(Title.SUBJECTIVE_DETAILS) + "." + "findings",
        "patient.ent.ear_disorder.yes": get_schema_id(Title.SUBJECTIVE_DETAILS) + "." + "findings",
        "insured_1.ent.ear_disorder.yes": get_schema_id(Title.SUBJECTIVE_DETAILS) + "." + "findings",
        "insured_2.ent.ear_disorder.yes": get_schema_id(Title.SUBJECTIVE_DETAILS) + "." + "findings",
        "patient.ent.throat_disorder.yes": get_schema_id(Title.SUBJECTIVE_DETAILS) + "." + "findings",
        "insured_1.ent.throat_disorder.yes": get_schema_id(Title.SUBJECTIVE_DETAILS) + "." + "findings",
        "insured_2.ent.throat_disorder.yes": get_schema_id(Title.SUBJECTIVE_DETAILS) + "." + "findings",
        "patient.ent.thyroid_disorder.yes": get_schema_id(Title.SUBJECTIVE_DETAILS) + "." + "findings",
        "insured_1.ent.thyroid_disorder.yes": get_schema_id(Title.SUBJECTIVE_DETAILS) + "." + "findings",
        "insured_2.ent.thyroid_disorder.yes": get_schema_id(Title.SUBJECTIVE_DETAILS) + "." + "findings",
        "patient.lymphatic.disorder.risk.yes": get_schema_id(Title.SUBJECTIVE_DETAILS) + "." + "findings",
        "insured_1.lymphatic.disorder.risk.yes": get_schema_id(Title.SUBJECTIVE_DETAILS) + "." + "findings",
        "insured_2.lymphatic.disorder.risk.yes": get_schema_id(Title.SUBJECTIVE_DETAILS) + "." + "findings",
        "patient.oedema_disease.yes": get_schema_id(Title.SUBJECTIVE_DETAILS) + "." + "findings",
        "insured_1.oedema_disease.yes": get_schema_id(Title.SUBJECTIVE_DETAILS) + "." + "findings",
        "insured_2.oedema_disease.yes": get_schema_id(Title.SUBJECTIVE_DETAILS) + "." + "findings",
        "patient.digestive.disorder.risk.yes": get_schema_id(Title.SUBJECTIVE_DETAILS) + "." + "findings",
        "insured_1.digestive.disorder.risk.yes": get_schema_id(Title.SUBJECTIVE_DETAILS) + "." + "findings",
        "insured_2.digestive.disorder.risk.yes": get_schema_id(Title.SUBJECTIVE_DETAILS) + "." + "findings",
        "patient.liver_disorder.yes": get_schema_id(Title.SUBJECTIVE_DETAILS) + "." + "findings",
        "insured_1.liver_disorder.yes": get_schema_id(Title.SUBJECTIVE_DETAILS) + "." + "findings",
        "insured_2.liver_disorder.yes": get_schema_id(Title.SUBJECTIVE_DETAILS) + "." + "findings",
        "patient.kidney.disorder.risk.yes": get_schema_id(Title.SUBJECTIVE_DETAILS) + "." + "findings",
        "insured_1.kidney.disorder.risk.yes": get_schema_id(Title.SUBJECTIVE_DETAILS) + "." + "findings",
        "insured_2.kidney.disorder.risk.yes": get_schema_id(Title.SUBJECTIVE_DETAILS) + "." + "findings",
        "patient.breast_disorder.yes": get_schema_id(Title.SUBJECTIVE_DETAILS) + "." + "findings",
        "insured_1.breast_disorder.yes": get_schema_id(Title.SUBJECTIVE_DETAILS) + "." + "findings",
        "insured_2.breast_disorder.yes": get_schema_id(Title.SUBJECTIVE_DETAILS) + "." + "findings",
        "patient.cyst.yes": get_schema_id(Title.SUBJECTIVE_DETAILS) + "." + "findings",
        "insured_1.cyst.yes": get_schema_id(Title.SUBJECTIVE_DETAILS) + "." + "findings",
        "insured_2.cyst.yes": get_schema_id(Title.SUBJECTIVE_DETAILS) + "." + "findings",
        "patient.skin_diseases.yes": get_schema_id(Title.SUBJECTIVE_DETAILS) + "." + "findings",
        "insured_1.skin_diseases.yes": get_schema_id(Title.SUBJECTIVE_DETAILS) + "." + "findings",
        "insured_2.skin_diseases.yes": get_schema_id(Title.SUBJECTIVE_DETAILS) + "." + "findings",
        "patient.hormonal_disorder.yes": get_schema_id(Title.SUBJECTIVE_DETAILS) + "." + "findings",
        "insured_1.hormonal_disorder.yes": get_schema_id(Title.SUBJECTIVE_DETAILS) + "." + "findings",
        "insured_2.hormonal_disorder.yes": get_schema_id(Title.SUBJECTIVE_DETAILS) + "." + "findings",
        "patient.female.reproductive.risk.yes": get_schema_id(Title.SUBJECTIVE_DETAILS) + "." + "findings",
        "insured_1.female.reproductive.risk.yes": get_schema_id(
            Title.SUBJECTIVE_DETAILS) + "." + "findings",
        "insured_2.female.reproductive.risk.yes": get_schema_id(
            Title.SUBJECTIVE_DETAILS) + "." + "findings",
        "patient.fever.yes": get_schema_id(Title.SUBJECTIVE_DETAILS) + "." + "findings",
        "insured_1.fever.yes": get_schema_id(Title.SUBJECTIVE_DETAILS) + "." + "findings",
        "insured_2.fever.yes": get_schema_id(Title.SUBJECTIVE_DETAILS) + "." + "findings",
        "patient.tuberculosis.yes": get_schema_id(Title.SUBJECTIVE_DETAILS) + "." + "findings",
        "insured_1.tuberculosis.yes": get_schema_id(Title.SUBJECTIVE_DETAILS) + "." + "findings",
        "insured_2.tuberculosis.yes": get_schema_id(Title.SUBJECTIVE_DETAILS) + "." + "findings",
        "patient.physical.disorder.risk.yes": get_schema_id(Title.SUBJECTIVE_DETAILS) + "." + "findings",
        "insured_1.physical.disorder.risk.yes": get_schema_id(Title.SUBJECTIVE_DETAILS) + "." + "findings",
        "insured_2.physical.disorder.risk.yes": get_schema_id(Title.SUBJECTIVE_DETAILS) + "." + "findings",
        "patient.allergies.yes": get_schema_id(Title.SUBJECTIVE_DETAILS) + "." + "findings",
        "insured_1.allergies.yes": get_schema_id(Title.SUBJECTIVE_DETAILS) + "." + "findings",
        "insured_2.allergies.yes": get_schema_id(Title.SUBJECTIVE_DETAILS) + "." + "findings",
        "patient.endocrine_disorder.risk.yes": get_schema_id(Title.SUBJECTIVE_DETAILS) + "." + "findings",
        "insured_1.endocrine_disorder.risk.yes": get_schema_id(Title.SUBJECTIVE_DETAILS) + "." + "findings",
        "insured_2.endocrine_disorder.risk.yes": get_schema_id(Title.SUBJECTIVE_DETAILS) + "." + "findings",
        "patient.nervous.disorder.risk.yes": get_schema_id(Title.SUBJECTIVE_DETAILS) + "." + "findings",
        "insured_1.nervous.disorder.risk.yes": get_schema_id(Title.SUBJECTIVE_DETAILS) + "." + "findings",
        "insured_2.nervous.disorder.risk.yes": get_schema_id(Title.SUBJECTIVE_DETAILS) + "." + "findings",
        "patient.gastrointestinal.disorder.risk.yes": get_schema_id(Title.SUBJECTIVE_DETAILS) + "." + "findings",
        "insured_1.gastrointestinal.disorder.risk.yes": get_schema_id(Title.SUBJECTIVE_DETAILS) + "." + "findings",
        "insured_2.gastrointestinal.disorder.risk.yes": get_schema_id(Title.SUBJECTIVE_DETAILS) + "." + "findings",
        "patient.chronic_disease.yes": get_schema_id(Title.SUBJECTIVE_DETAILS) + "." + "findings",
        "insured_1.chronic_disease.yes": get_schema_id(Title.SUBJECTIVE_DETAILS) + "." + "findings",
        "insured_2.chronic_disease.yes": get_schema_id(Title.SUBJECTIVE_DETAILS) + "." + "findings",
        "patient.urinary_disorder.yes": get_schema_id(Title.SUBJECTIVE_DETAILS) + "." + "findings",
        "insured_1.urinary_disorder.yes": get_schema_id(Title.SUBJECTIVE_DETAILS) + "." + "findings",
        "insured_2.urinary_disorder.yes": get_schema_id(Title.SUBJECTIVE_DETAILS) + "." + "findings",
        "patient.heart_disease.yes": get_schema_id(Title.SUBJECTIVE_DETAILS) + "." + "findings",
        "insured_1.heart_disease.yes": get_schema_id(Title.SUBJECTIVE_DETAILS) + "." + "findings",
        "insured_2.heart_disease.yes": get_schema_id(Title.SUBJECTIVE_DETAILS) + "." + "findings",
        "patient.cancer_disease.yes": get_schema_id(Title.SUBJECTIVE_DETAILS) + "." + "findings",
        "insured_1.cancer_disease.yes": get_schema_id(Title.SUBJECTIVE_DETAILS) + "." + "findings",
        "insured_2.cancer_disease.yes": get_schema_id(Title.SUBJECTIVE_DETAILS) + "." + "findings",
        "patient.mental_disorder.yes": get_schema_id(Title.SUBJECTIVE_DETAILS) + "." + "findings",
        "insured_1.mental_disorder.yes": get_schema_id(Title.SUBJECTIVE_DETAILS) + "." + "findings",
        "insured_2.mental_disorder.yes": get_schema_id(Title.SUBJECTIVE_DETAILS) + "." + "findings",
        "patient.hiv.yes": get_schema_id(Title.SUBJECTIVE_DETAILS) + "." + "findings",
        "insured_1.hiv.yes": get_schema_id(Title.SUBJECTIVE_DETAILS) + "." + "findings",
        "insured_2.hiv.yes": get_schema_id(Title.SUBJECTIVE_DETAILS) + "." + "findings",
        "patient.muscular_pain.yes": get_schema_id(Title.SUBJECTIVE_DETAILS) + "." + "findings",
        "insured_1.muscular_pain.yes": get_schema_id(Title.SUBJECTIVE_DETAILS) + "." + "findings",
        "insured_2.muscular_pain.yes": get_schema_id(Title.SUBJECTIVE_DETAILS) + "." + "findings",
        "patient.diabetes.yes": get_schema_id(Title.SUBJECTIVE_DETAILS) + "." + "findings",
        "insured_1.diabetes.yes": get_schema_id(Title.SUBJECTIVE_DETAILS) + "." + "findings",
        "insured_2.diabetes.yes": get_schema_id(Title.SUBJECTIVE_DETAILS) + "." + "findings",
        "patient.high_blood_pressure.yes": get_schema_id(Title.SUBJECTIVE_DETAILS) + "." + "findings",
        "insured_1.high_blood_pressure.yes": get_schema_id(Title.SUBJECTIVE_DETAILS) + "." + "findings",
        "insured_2.high_blood_pressure.yes": get_schema_id(Title.SUBJECTIVE_DETAILS) + "." + "findings",
        "patient.blood_disorder.yes": get_schema_id(Title.SUBJECTIVE_DETAILS) + "." + "findings",
        "insured_1.blood_disorder.yes": get_schema_id(Title.SUBJECTIVE_DETAILS) + "." + "findings",
        "insured_2.blood_disorder.yes": get_schema_id(Title.SUBJECTIVE_DETAILS) + "." + "findings",
        "patient.breathing_disorder.yes": get_schema_id(Title.SUBJECTIVE_DETAILS) + "." + "findings",
        "insured_1.breathing_disorder.yes": get_schema_id(Title.SUBJECTIVE_DETAILS) + "." + "findings",
        "insured_2.breathing_disorder.yes": get_schema_id(Title.SUBJECTIVE_DETAILS) + "." + "findings",
        "patient.ent.eyes_disorder.yes": get_schema_id(Title.SUBJECTIVE_DETAILS) + "." + "findings",
        "insured_1.ent.eyes_disorder.yes": get_schema_id(Title.SUBJECTIVE_DETAILS) + "." + "findings",
        "insured_2.ent.eyes_disorder.yes": get_schema_id(Title.SUBJECTIVE_DETAILS) + "." + "findings",
        "patient.lymphatic_system_disease.yes": get_schema_id(Title.SUBJECTIVE_DETAILS) + "." + "findings",
        "insured_1.lymphatic_system_disease.yes": get_schema_id(Title.SUBJECTIVE_DETAILS) + "." + "findings",
        "insured_2.lymphatic_system_disease.yes": get_schema_id(Title.SUBJECTIVE_DETAILS) + "." + "findings",
        "patient.stomach_disorder.yes": get_schema_id(Title.SUBJECTIVE_DETAILS) + "." + "findings",
        "insured_1.stomach_disorder.yes": get_schema_id(Title.SUBJECTIVE_DETAILS) + "." + "findings",
        "insured_2.stomach_disorder.yes": get_schema_id(Title.SUBJECTIVE_DETAILS) + "." + "findings",
        "patient.kidney_disorder.yes": get_schema_id(Title.SUBJECTIVE_DETAILS) + "." + "findings",
        "insured_1.kidney_disorder.yes": get_schema_id(Title.SUBJECTIVE_DETAILS) + "." + "findings",
        "insured_2.kidney_disorder.yes": get_schema_id(Title.SUBJECTIVE_DETAILS) + "." + "findings",
        "patient.reproductive_organs_diseases.yes": get_schema_id(Title.SUBJECTIVE_DETAILS) + "." + "findings",
        "insured_1.reproductive_organs_diseases.yes": get_schema_id(Title.SUBJECTIVE_DETAILS) + "." + "findings",
        "insured_2.reproductive_organs_diseases.yes": get_schema_id(Title.SUBJECTIVE_DETAILS) + "." + "findings",
        "patient.physical_disorder.yes": get_schema_id(Title.SUBJECTIVE_DETAILS) + "." + "findings",
        "insured_1.physical_disorder.yes": get_schema_id(Title.SUBJECTIVE_DETAILS) + "." + "findings",
        "insured_2.physical_disorder.yes": get_schema_id(Title.SUBJECTIVE_DETAILS) + "." + "findings",
        "patient.endocrine_glands_disorder.yes": get_schema_id(Title.SUBJECTIVE_DETAILS) + "." + "findings",
        "insured_1.endocrine_glands_disorder.yes": get_schema_id(Title.SUBJECTIVE_DETAILS) + "." + "findings",
        "insured_2.endocrine_glands_disorder.yes": get_schema_id(Title.SUBJECTIVE_DETAILS) + "." + "findings",
        "patient.nervous_system_disorder.yes": get_schema_id(Title.SUBJECTIVE_DETAILS) + "." + "findings",
        "insured_1.nervous_system_disorder.yes": get_schema_id(Title.SUBJECTIVE_DETAILS) + "." + "findings",
        "insured_2.nervous_system_disorder.yes": get_schema_id(Title.SUBJECTIVE_DETAILS) + "." + "findings",
        "patient.gastrointestinal_disorder.yes": get_schema_id(Title.SUBJECTIVE_DETAILS) + "." + "findings",
        "insured_1.gastrointestinal_disorder.yes": get_schema_id(Title.SUBJECTIVE_DETAILS) + "." + "findings",
        "insured_2.gastrointestinal_disorder.yes": get_schema_id(Title.SUBJECTIVE_DETAILS) + "." + "findings"
    },

    "application_details": {
        "application.number": get_schema_id(Title.APPLICATION_DETAILS) + "." + "application_number",
        "policy_number": get_schema_id(Title.APPLICATION_DETAILS) + "." + "application_id",
        "policy.number": get_schema_id(Title.APPLICATION_DETAILS) + "." + "application_id",
        "company.name": get_schema_id(Title.APPLICATION_DETAILS) + "." + "cedent_name",
        "application.date": get_schema_id(Title.APPLICATION_DETAILS) + "." + "date_received",
        "underwriter.name": get_schema_id(Title.APPLICATION_DETAILS) + "." + "company_underwriter",
        "email.from.name": get_schema_id(Title.APPLICATION_DETAILS) + "." + "company_underwriter",
    },
    "diagnostic_procedures": {
        "diagnostic_procedure.type": get_schema_id(Title.DIAGNOSTIC_PROCEDURES) + "." + "type",
        "encounter.date": get_schema_id(Title.DIAGNOSTIC_PROCEDURES) + "." + "date",
        "pathology.date": get_schema_id(Title.DIAGNOSTIC_PROCEDURES) + "." + "date",
        "pathology.collected": get_schema_id(Title.DIAGNOSTIC_PROCEDURES) + "." + "date",
        "pathology.received": get_schema_id(Title.DIAGNOSTIC_PROCEDURES) + "." + "date",
        "pathology.issued": get_schema_id(Title.DIAGNOSTIC_PROCEDURES) + "." + "date",
        "encounter.performer": get_schema_id(Title.DIAGNOSTIC_PROCEDURES) + "." + "department",
        "pathology.performer": get_schema_id(Title.DIAGNOSTIC_PROCEDURES) + "." + "department",
        "merged_" + Title.PERFORMER : get_schema_id(Title.DIAGNOSTIC_PROCEDURES) + "." + "department",
        "assessment": get_schema_id(Title.DIAGNOSTIC_PROCEDURES) + "." + "description",
        "encounter.conclusion": get_schema_id(Title.DIAGNOSTIC_PROCEDURES) + "." + "description",
        "pathology.finding": get_schema_id(Title.DIAGNOSTIC_PROCEDURES) + "." + "description",
        "merged_" + Title.DESCRIPTION : get_schema_id(Title.DIAGNOSTIC_PROCEDURES) + "." + "description"
    },

    "procedures": {
        "snomed.description": get_schema_id(Title.PROCEDURES) + "." + "description",
        "cpt.description": get_schema_id(Title.PROCEDURES) + "." + "cpt_description",
        "cpt": get_schema_id(Title.PROCEDURES) + "." + "cpt",
        const.GENERATED_ENTITY + "cpt": get_schema_id(Title.PROCEDURES) + "." + "cpt",
        "surgery.procedure": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "plan.anesthesia": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "plan.phytherapy": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "plan.physiotherapy": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "plan.surgery": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "history.surgery": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "surgery.history": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "plan.lab": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "encounter.procedure": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "pathology.procedure": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "phytherapy.procedure": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "physiotherapy.procedure": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "diagnosis.principal": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "diagnosis.primary": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "diagnosis.secondary": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "assessment.diag": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "death.cause": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "medical.condition": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "history.diag": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "diagnosis.history": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "pathology.diagnosis": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "patient.covid.positive": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "insured_1.covid.positive": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "insured_2.covid.positive": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "patient.medical_disorder.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "insured_1.medical_disorder.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "insured_2.medical_disorder.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "patient.heart.disorder.risk.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "insured_1.heart.disorder.risk.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "insured_2.heart.disorder.risk.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "patient.neoplasm.risk.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "insured_1.neoplasm.risk.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "insured_2.neoplasm.risk.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "patient.mental.disorder.risk.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "insured_1.mental.disorder.risk.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "insured_2.mental.disorder.risk.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "patient.brain_hemorrhage.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "insured_1.brain_hemorrhage.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "insured_2.brain_hemorrhage.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "patient.neurological_disorder.risk.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "insured_1.neurological_disorder.risk.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "insured_2.neurological_disorder.risk.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "patient.immune.disorder.risk.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "insured_1.immune.disorder.risk.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "insured_2.immune.disorder.risk.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "patient.std.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "insured_1.std.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "insured_2.std.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "patient.muscleandbone.disorder.risk.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "insured_1.muscleandbone.disorder.risk.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "insured_2.muscleandbone.disorder.risk.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "patient.hepatitis.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "insured_1.hepatitis.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "insured_2.hepatitis.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "patient.endocrine.disorder.risk.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "insured_1.endocrine.disorder.risk.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "insured_2.endocrine.disorder.risk.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "patient.circulatory.disorder.risk.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "insured_1.circulatory.disorder.risk.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "insured_2.circulatory.disorder.risk.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "patient.blood.disorder.risk.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "insured_1.blood.disorder.risk.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "insured_2.blood.disorder.risk.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "patient.respiratory.disorder.risk.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "insured_1.respiratory.disorder.risk.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "insured_2.respiratory.disorder.risk.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "patient.ent_disorder.risk.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "insured_1.ent_disorder.risk.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "insured_2.ent_disorder.risk.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "patient.ent.nose_disorder.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "insured_1.ent.nose_disorder.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "insured_2.ent.nose_disorder.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "patient.ent.ear_disorder.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "insured_1.ent.ear_disorder.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "insured_2.ent.ear_disorder.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "patient.ent.throat_disorder.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "insured_1.ent.throat_disorder.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "insured_2.ent.throat_disorder.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "patient.ent.thyroid_disorder.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "insured_1.ent.thyroid_disorder.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "insured_2.ent.thyroid_disorder.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "patient.lymphatic.disorder.risk.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "insured_1.lymphatic.disorder.risk.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "insured_2.lymphatic.disorder.risk.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "patient.oedema_disease.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "insured_1.oedema_disease.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "insured_2.oedema_disease.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "patient.digestive.disorder.risk.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "insured_1.digestive.disorder.risk.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "insured_2.digestive.disorder.risk.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "patient.liver_disorder.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "insured_1.liver_disorder.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "insured_2.liver_disorder.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "patient.kidney.disorder.risk.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "insured_1.kidney.disorder.risk.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "insured_2.kidney.disorder.risk.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "patient.breast_disorder.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "insured_1.breast_disorder.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "insured_2.breast_disorder.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "patient.cyst.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "insured_1.cyst.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "insured_2.cyst.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "patient.skin_diseases.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "insured_1.skin_diseases.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "insured_2.skin_diseases.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "patient.hormonal_disorder.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "insured_1.hormonal_disorder.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "insured_2.hormonal_disorder.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "patient.female.reproductive.risk.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "insured_1.female.reproductive.risk.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "insured_2.female.reproductive.risk.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "patient.fever.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "insured_1.fever.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "insured_2.fever.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "patient.tuberculosis.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "insured_1.tuberculosis.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "insured_2.tuberculosis.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "patient.physical.disorder.risk.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "insured_1.physical.disorder.risk.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "insured_2.physical.disorder.risk.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "patient.allergies.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "insured_1.allergies.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "insured_2.allergies.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "patient.endocrine_disorder.risk.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "insured_1.endocrine_disorder.risk.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "insured_2.endocrine_disorder.risk.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "patient.nervous.disorder.risk.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "insured_1.nervous.disorder.risk.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "insured_2.nervous.disorder.risk.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "patient.gastrointestinal.disorder.risk.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "insured_1.gastrointestinal.disorder.risk.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "insured_2.gastrointestinal.disorder.risk.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "patient.chronic_disease.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "insured_1.chronic_disease.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "insured_2.chronic_disease.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "patient.urinary_disorder.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "insured_1.urinary_disorder.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "insured_2.urinary_disorder.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "patient.heart_disease.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "insured_1.heart_disease.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "insured_2.heart_disease.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "patient.cancer_disease.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "insured_1.cancer_disease.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "insured_2.cancer_disease.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "patient.mental_disorder.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "insured_1.mental_disorder.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "insured_2.mental_disorder.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "patient.hiv.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "insured_1.hiv.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "insured_2.hiv.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "patient.muscular_pain.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "insured_1.muscular_pain.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "insured_2.muscular_pain.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "patient.diabetes.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "insured_1.diabetes.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "insured_2.diabetes.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "patient.high_blood_pressure.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "insured_1.high_blood_pressure.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "insured_2.high_blood_pressure.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "patient.blood_disorder.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "insured_1.blood_disorder.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "insured_2.blood_disorder.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "patient.breathing_disorder.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "insured_1.breathing_disorder.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "insured_2.breathing_disorder.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "patient.ent.eyes_disorder.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "insured_1.ent.eyes_disorder.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "insured_2.ent.eyes_disorder.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "patient.lymphatic_system_disease.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "insured_1.lymphatic_system_disease.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "insured_2.lymphatic_system_disease.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "patient.stomach_disorder.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "insured_1.stomach_disorder.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "insured_2.stomach_disorder.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "patient.kidney_disorder.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "insured_1.kidney_disorder.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "insured_2.kidney_disorder.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "patient.reproductive_organs_diseases.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "insured_1.reproductive_organs_diseases.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "insured_2.reproductive_organs_diseases.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "patient.physical_disorder.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "insured_1.physical_disorder.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "insured_2.physical_disorder.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "patient.endocrine_glands_disorder.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "insured_1.endocrine_glands_disorder.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "insured_2.endocrine_glands_disorder.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "patient.nervous_system_disorder.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "insured_1.nervous_system_disorder.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "insured_2.nervous_system_disorder.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "patient.gastrointestinal_disorder.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "insured_1.gastrointestinal_disorder.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "insured_2.gastrointestinal_disorder.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "insured_1.heart_disorder.risk.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "insured_2.heart_disorder.risk.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "patient.heart_disorder.risk.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "insured_1.neoplasm_risk.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "insured_2.neoplasm_risk.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "patient.neoplasm_risk.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "insured_1.mental_disorder_risk.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "insured_2.mental_disorder_risk.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "patient.mental_disorder_risk.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "insured_1.immune_disorder_risk.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "insured_2.immune_disorder_risk.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "patient.immune_disorder_risk.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "insured_1.muscle_and_bone.disorder.risk.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "insured_2.muscle_and_bone.disorder.risk.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "patient.muscle_and_bone.disorder.risk.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "insured_1.circulatory_disorder_risk.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "insured_2.circulatory_disorder_risk.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "patient.circulatory_disorder_risk.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "insured_1.blood_disorder.risk.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "insured_2.blood_disorder.risk.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "patient.blood_disorder.risk.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "insured_1.respiratory_disorder_risk.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "insured_2.respiratory_disorder_risk.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "patient.respiratory_disorder_risk.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "insured_1.ent_disorder_risk.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "insured_2.ent_disorder_risk.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "patient.ent_disorder_risk.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "insured_1.lymphatic_disorder_risk.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "insured_2.lymphatic_disorder_risk.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "patient.lymphatic_disorder_risk.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "insured_1.digestive_disorder_risk.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "insured_2.digestive_disorder_risk.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "patient.digestive_disorder_risk.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "insured_1.kidney_disorder.risk.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "insured_2.kidney_disorder.risk.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "patient.kidney_disorder.risk.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "insured_1.medical_test.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "insured_2.medical_test.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "patient.medical_test.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "insured_1.medication.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "insured_2.medication.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "patient.medication.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "insured_1.hospitalization.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "insured_2.hospitalization.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "patient.hospitalization.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "insured_1.recent.hospitalization.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "insured_2.recent.hospitalization.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "patient.recent.hospitalization.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "insured_1.pregnant.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "insured_2.pregnant.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "patient.pregnant.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "insured_1.female_reproductive_risk.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "insured_2.female_reproductive_risk.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "patient.female_reproductive_risk.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "insured_1.physical_disability.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "insured_2.physical_disability.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "patient.physical_disability.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "insured_1.nervous_disorder_risk.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "insured_2.nervous_disorder_risk.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "patient.nervous_disorder_risk.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "insured_1.gastrointestinal_disorder.risk.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "insured_2.gastrointestinal_disorder.risk.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "patient.gastrointestinal_disorder.risk.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "insured_1.endocrine_disorder_risk.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "insured_2.endocrine_disorder_risk.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "patient.endocrine_disorder_risk.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "insured_1.congenital_disorder_risk.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "insured_2.congenital_disorder_risk.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "patient.congenital_disorder_risk.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "insured_1.other_symptoms.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "insured_2.other_symptoms.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "patient.other_symptoms.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "insured_1.current.treatment.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "insured_2.current.treatment.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "patient.current.treatment.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "insured_1.history.treatment.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "insured_2.history.treatment.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "patient.history.treatment.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "insured_1.consultation.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "insured_2.consultation.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "patient.consultation.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "insured_1.plan.consultation.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "insured_2.plan.consultation.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "patient.plan.consultation.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "insured_1.hepatobiliary_risk.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "insured_2.hepatobiliary_risk.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "patient.hepatobiliary_risk.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "insured_1.gastrointestinal_disorder_risk.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "insured_2.gastrointestinal_disorder_risk.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "patient.gastrointestinal_disorder_risk.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "patient.respiratory_disorder_risk.details": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "insured_1.respiratory_disorder_risk.details": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "insured_2.respiratory_disorder_risk.details": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "patient.neoplasm_risk.details": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "insured_1.neoplasm_risk.details": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "insured_2.neoplasm_risk.details": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "patient.mental_disorder_risk.details": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "insured_1.mental_disorder_risk.details": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "insured_2.mental_disorder_risk.details": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "patient.blood_pressure_disorder.risk.details": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "insured_1.blood_pressure_disorder.risk.details": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "insured_2.blood_pressure_disorder.risk.details": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "patient.muscle_and_bone_disorder_risk.details": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "insured_1.muscle_and_bone_disorder_risk.details": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "insured_2.muscle_and_bone_disorder_risk.details": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "patient.digestive_disorder_risk.details": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "insured_1.digestive_disorder_risk.details": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "insured_2.digestive_disorder_risk.details": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "patient.muscle_and_bone_disorder_risk.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "insured_1.muscle_and_bone_disorder_risk.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "insured_2.muscle_and_bone_disorder_risk.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "patient.blood_pressure_disorder.risk.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "insured_1.blood_pressure_disorder.risk.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "insured_2.blood_pressure_disorder.risk.yes": get_schema_id(Title.PROCEDURES) + "." + "procedure",
        "encounter.date": get_schema_id(Title.PROCEDURES) + "." + "date",
        "diag.date": get_schema_id(Title.PROCEDURES) + "." + "date",
        "pathology.date": get_schema_id(Title.PROCEDURES) + "." + "date",
        "pathology.issued": get_schema_id(Title.PROCEDURES) + "." + "date",
        "pathology.collected": get_schema_id(Title.PROCEDURES) + "." + "date",
        "surgery.date": get_schema_id(Title.PROCEDURES) + "." + "date",
        "phytherapy.date": get_schema_id(Title.PROCEDURES) + "." + "date",
        "physiotherapy.date": get_schema_id(Title.PROCEDURES) + "." + "date",
        const.GENERATED_ENTITY + "procedure.type": get_schema_id(Title.PROCEDURES) + "." + "type",
        const.GENERATED_ENTITY + "procedure.visit_type": get_schema_id(Title.PROCEDURES) + "." + "visit_type",
        "snomed": get_schema_id(Title.PROCEDURES) + "." + "snomed",
        const.GENERATED_ENTITY + "snomed": get_schema_id(Title.PROCEDURES) + "." + "snomed",
        const.GENERATED_ENTITY + "SNOMED": get_schema_id(Title.PROCEDURES) + "." + "snomed",
        "encounter.finding": get_schema_id(Title.PROCEDURES) + "." + "finding",
        "surgery.finding": get_schema_id(Title.PROCEDURES) + "." + "finding",
        "pathology.finding": get_schema_id(Title.PROCEDURES) + "." + "finding",
        "assessment.phytherapy": get_schema_id(Title.PROCEDURES) + "." + "finding",
        "merged_Finding": get_schema_id(Title.PROCEDURES) + "." + "finding",
        const.GENERATED_ENTITY + "impairment": get_schema_id(Title.PROCEDURES) + "." + "impairment",
    },
    "treatments": {
        "snomed.description": get_schema_id(Title.TREATMENTS) + "." + "description",
        "cpt.description": get_schema_id(Title.TREATMENTS) + "." + "cpt_description",
        "cpt": get_schema_id(Title.TREATMENTS) + "." + "cpt",
        const.GENERATED_ENTITY + "cpt": get_schema_id(Title.TREATMENTS) + "." + "cpt",
        "surgery.procedure": get_schema_id(Title.TREATMENTS) + "." + "procedure",
        "plan.anesthesia": get_schema_id(Title.TREATMENTS) + "." + "procedure",
        "plan.phytherapy": get_schema_id(Title.TREATMENTS) + "." + "procedure",
        "plan.physiotherapy": get_schema_id(Title.TREATMENTS) + "." + "procedure",
        "plan.surgery": get_schema_id(Title.TREATMENTS) + "." + "procedure",
        "history.surgery": get_schema_id(Title.TREATMENTS) + "." + "procedure",
        "surgery.history": get_schema_id(Title.TREATMENTS) + "." + "procedure",
        "pathology.procedure": get_schema_id(Title.TREATMENTS) + "." + "procedure",
        "phytherapy.procedure": get_schema_id(Title.TREATMENTS) + "." + "procedure",
        "physiotherapy.procedure": get_schema_id(Title.TREATMENTS) + "." + "procedure",
        "encounter.date": get_schema_id(Title.TREATMENTS) + "." + "date",
        "diag.date": get_schema_id(Title.TREATMENTS) + "." + "date",
        "pathology.date": get_schema_id(Title.TREATMENTS) + "." + "date",
        "pathology.issued": get_schema_id(Title.TREATMENTS) + "." + "date",
        "pathology.collected": get_schema_id(Title.TREATMENTS) + "." + "date",
        "surgery.date": get_schema_id(Title.TREATMENTS) + "." + "date",
        "phytherapy.date": get_schema_id(Title.TREATMENTS) + "." + "date",
        "physiotherapy.date": get_schema_id(Title.TREATMENTS) + "." + "date",
        const.GENERATED_ENTITY + "procedure.type": get_schema_id(Title.TREATMENTS) + "." + "type",
        const.GENERATED_ENTITY + "procedure.visit_type": get_schema_id(Title.TREATMENTS) + "." + "visit_type",
        "snomed": get_schema_id(Title.TREATMENTS) + "." + "snomed",
        const.GENERATED_ENTITY + "snomed": get_schema_id(Title.TREATMENTS) + "." + "snomed",
        const.GENERATED_ENTITY + "SNOMED": get_schema_id(Title.TREATMENTS) + "." + "snomed",
        "surgery.finding": get_schema_id(Title.TREATMENTS) + "." + "finding",
        "pathology.finding": get_schema_id(Title.TREATMENTS) + "." + "finding",
        "assessment.phytherapy": get_schema_id(Title.TREATMENTS) + "." + "finding",
        "merged_Finding": get_schema_id(Title.TREATMENTS) + "." + "finding",
        const.GENERATED_ENTITY + "impairment": get_schema_id(Title.TREATMENTS) + "." + "impairment",
    },
    "allergens": {
        "allergen": get_schema_id(Title.ALLERGENS) + "." + "allergen",
        "allergy.medication": get_schema_id(Title.ALLERGENS) + "." + "allergen",
        const.GENERATED_ENTITY + "allergen.type": get_schema_id(Title.ALLERGENS) + "." + "type",
        const.GENERATED_ENTITY + "snomed": get_schema_id(Title.ALLERGENS) + "." + "snomed"
    },
    "ccda_immunizations": {
        "immunization.name": get_schema_id(Title.IMMUNIZATIONS) + "." + "name",
        "immunization.date": get_schema_id(Title.IMMUNIZATIONS) + "." + "date",
        const.GENERATED_ENTITY + "rx_code": get_schema_id(Title.IMMUNIZATIONS) + "." + "rx_code",
        const.GENERATED_ENTITY + "cvx_code": get_schema_id(Title.IMMUNIZATIONS) + "." + "cvx_code",
    },
    "encounter_dx_details": {
        "diagnosis.principal": get_schema_id(Title.ENCOUNTER_DX_DETAILS) + "." + "diagnosis",
        "diagnosis.primary": get_schema_id(Title.ENCOUNTER_DX_DETAILS) + "." + "diagnosis",
        "diagnosis.secondary": get_schema_id(Title.ENCOUNTER_DX_DETAILS) + "." + "diagnosis",
        "diagnosis.history": get_schema_id(Title.ENCOUNTER_DX_DETAILS) + "." + "diagnosis",
        "pathology.diagnosis": get_schema_id(Title.ENCOUNTER_DX_DETAILS) + "." + "diagnosis",
        "diagnoses.type": get_schema_id(Title.ENCOUNTER_DX_DETAILS) + "." + "type",
        const.GENERATED_ENTITY + "diagnoses.type": get_schema_id(Title.ENCOUNTER_DX_DETAILS) + "." + "type",
        "icd10": get_schema_id(Title.ENCOUNTER_DX_DETAILS) + "." + "icd_10",
        const.GENERATED_ENTITY + "icd10": get_schema_id(Title.ENCOUNTER_DX_DETAILS) + "." + "icd_10",
        "icd9": get_schema_id(Title.ENCOUNTER_DX_DETAILS) + "." + "icd_10",
        "diag.date": get_schema_id(Title.ENCOUNTER_DX_DETAILS) + "." + "diag_date",
        "diagnosis.date": get_schema_id(Title.ENCOUNTER_DX_DETAILS) + "." + "diag_date",
        "diagnosis.history.date": get_schema_id(Title.ENCOUNTER_DX_DETAILS) + "." + "diag_date",
        "assessment.date": get_schema_id(Title.ENCOUNTER_DX_DETAILS) + "." + "date",
        "vital.date": get_schema_id(Title.ENCOUNTER_DX_DETAILS) + "." + "date",
        "patient.date": get_schema_id(Title.ENCOUNTER_DX_DETAILS) + "." + "date",
        "encounter.date": get_schema_id(Title.ENCOUNTER_DX_DETAILS) + "." + "date",
        "pathology.date": get_schema_id(Title.ENCOUNTER_DX_DETAILS) + "." + "date",
        "imaging.date": get_schema_id(Title.ENCOUNTER_DX_DETAILS) + "." + "date",
        const.GENERATED_ENTITY + "icd_chapter": get_schema_id(Title.ENCOUNTER_DX_DETAILS) + "." + "chapter",
        const.GENERATED_ENTITY + "dx_reason_of_visit": get_schema_id(Title.ENCOUNTER_DX_DETAILS) + "." + "reason_of_visit",
        "doctor.name": get_schema_id(Title.ENCOUNTER_DX_DETAILS) + "." + "performer_name",
        "doctor.address": get_schema_id(Title.ENCOUNTER_DX_DETAILS) + "." + "performer_address",
        const.GENERATED_ENTITY + const.SNOMED : get_schema_id(Title.ENCOUNTER_DX_DETAILS) + "." + const.SNOMED,
    },
    "diabetes": {
        "o.name": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "lab.name": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "diagnosis.principal": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "diagnosis.primary": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "diagnosis.secondary": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "diagnosis.history": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "assessment.diag": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "death.cause": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "medical.condition": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "history.diag": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "pathology.diagnosis": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "patient.covid.positive": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "insured_1.covid.positive": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "insured_2.covid.positive": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "patient.medical_disorder.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "insured_1.medical_disorder.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "insured_2.medical_disorder.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "patient.heart.disorder.risk.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "insured_1.heart.disorder.risk.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "insured_2.heart.disorder.risk.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "patient.neoplasm.risk.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "insured_1.neoplasm.risk.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "insured_2.neoplasm.risk.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "patient.mental.disorder.risk.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "insured_1.mental.disorder.risk.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "insured_2.mental.disorder.risk.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "patient.brain_hemorrhage.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "insured_1.brain_hemorrhage.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "insured_2.brain_hemorrhage.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "patient.neurological_disorder.risk.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "insured_1.neurological_disorder.risk.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "insured_2.neurological_disorder.risk.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "patient.immune.disorder.risk.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "insured_1.immune.disorder.risk.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "insured_2.immune.disorder.risk.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "patient.std.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "insured_1.std.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "insured_2.std.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "patient.muscleandbone.disorder.risk.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "insured_1.muscleandbone.disorder.risk.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "insured_2.muscleandbone.disorder.risk.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "patient.hepatitis.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "insured_1.hepatitis.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "insured_2.hepatitis.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "patient.endocrine.disorder.risk.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "insured_1.endocrine.disorder.risk.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "insured_2.endocrine.disorder.risk.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "patient.circulatory.disorder.risk.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "insured_1.circulatory.disorder.risk.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "insured_2.circulatory.disorder.risk.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "patient.blood.disorder.risk.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "insured_1.blood.disorder.risk.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "insured_2.blood.disorder.risk.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "patient.respiratory.disorder.risk.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "insured_1.respiratory.disorder.risk.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "insured_2.respiratory.disorder.risk.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "patient.ent_disorder.risk.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "insured_1.ent_disorder.risk.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "insured_2.ent_disorder.risk.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "patient.ent.nose_disorder.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "insured_1.ent.nose_disorder.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "insured_2.ent.nose_disorder.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "patient.ent.ear_disorder.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "insured_1.ent.ear_disorder.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "insured_2.ent.ear_disorder.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "patient.ent.throat_disorder.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "insured_1.ent.throat_disorder.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "insured_2.ent.throat_disorder.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "patient.ent.thyroid_disorder.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "insured_1.ent.thyroid_disorder.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "insured_2.ent.thyroid_disorder.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "patient.lymphatic.disorder.risk.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "insured_1.lymphatic.disorder.risk.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "insured_2.lymphatic.disorder.risk.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "patient.oedema_disease.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "insured_1.oedema_disease.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "insured_2.oedema_disease.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "patient.digestive.disorder.risk.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "insured_1.digestive.disorder.risk.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "insured_2.digestive.disorder.risk.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "patient.liver_disorder.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "insured_1.liver_disorder.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "insured_2.liver_disorder.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "patient.kidney.disorder.risk.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "insured_1.kidney.disorder.risk.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "insured_2.kidney.disorder.risk.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "patient.breast_disorder.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "insured_1.breast_disorder.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "insured_2.breast_disorder.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "patient.cyst.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "insured_1.cyst.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "insured_2.cyst.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "patient.skin_diseases.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "insured_1.skin_diseases.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "insured_2.skin_diseases.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "patient.hormonal_disorder.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "insured_1.hormonal_disorder.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "insured_2.hormonal_disorder.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "patient.female.reproductive.risk.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "insured_1.female.reproductive.risk.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "insured_2.female.reproductive.risk.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "patient.fever.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "insured_1.fever.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "insured_2.fever.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "patient.tuberculosis.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "insured_1.tuberculosis.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "insured_2.tuberculosis.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "patient.physical.disorder.risk.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "insured_1.physical.disorder.risk.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "insured_2.physical.disorder.risk.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "patient.allergies.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "insured_1.allergies.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "insured_2.allergies.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "patient.endocrine_disorder.risk.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "insured_1.endocrine_disorder.risk.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "insured_2.endocrine_disorder.risk.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "patient.congenital.disorder.risk.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "insured_1.congenital.disorder.risk.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "insured_2.congenital.disorder.risk.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "patient.nervous.disorder.risk.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "insured_1.nervous.disorder.risk.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "insured_2.nervous.disorder.risk.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "patient.gastrointestinal.disorder.risk.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "insured_1.gastrointestinal.disorder.risk.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "insured_2.gastrointestinal.disorder.risk.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "patient.chronic_disease.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "insured_1.chronic_disease.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "insured_2.chronic_disease.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "patient.urinary_disorder.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "insured_1.urinary_disorder.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "insured_2.urinary_disorder.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "patient.heart_disease.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "insured_1.heart_disease.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "insured_2.heart_disease.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "patient.cancer_disease.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "insured_1.cancer_disease.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "insured_2.cancer_disease.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "patient.mental_disorder.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "insured_1.mental_disorder.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "insured_2.mental_disorder.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "patient.hiv.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "insured_1.hiv.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "insured_2.hiv.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "patient.muscular_pain.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "insured_1.muscular_pain.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "insured_2.muscular_pain.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "patient.diabetes.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "insured_1.diabetes.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "insured_2.diabetes.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "patient.high_blood_pressure.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "insured_1.high_blood_pressure.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "insured_2.high_blood_pressure.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "patient.blood_disorder.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "insured_1.blood_disorder.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "insured_2.blood_disorder.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "patient.breathing_disorder.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "insured_1.breathing_disorder.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "insured_2.breathing_disorder.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "patient.ent.eyes_disorder.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "insured_1.ent.eyes_disorder.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "insured_2.ent.eyes_disorder.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "patient.lymphatic_system_disease.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "insured_1.lymphatic_system_disease.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "insured_2.lymphatic_system_disease.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "patient.stomach_disorder.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "insured_1.stomach_disorder.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "insured_2.stomach_disorder.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "patient.kidney_disorder.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "insured_1.kidney_disorder.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "insured_2.kidney_disorder.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "patient.reproductive_organs_diseases.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "insured_1.reproductive_organs_diseases.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "insured_2.reproductive_organs_diseases.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "patient.physical_disorder.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "insured_1.physical_disorder.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "insured_2.physical_disorder.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "patient.endocrine_glands_disorder.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "insured_1.endocrine_glands_disorder.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "insured_2.endocrine_glands_disorder.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "patient.nervous_system_disorder.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "insured_1.nervous_system_disorder.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "insured_2.nervous_system_disorder.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "patient.gastrointestinal_disorder.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "insured_1.gastrointestinal_disorder.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "insured_2.gastrointestinal_disorder.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "insured_1.heart_disorder.risk.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "insured_2.heart_disorder.risk.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "patient.heart_disorder.risk.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "insured_1.neoplasm_risk.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "insured_2.neoplasm_risk.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "patient.neoplasm_risk.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "insured_1.mental_disorder_risk.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "insured_2.mental_disorder_risk.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "patient.mental_disorder_risk.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "insured_1.immune_disorder_risk.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "insured_2.immune_disorder_risk.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "patient.immune_disorder_risk.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "insured_1.muscle_and_bone.disorder.risk.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "insured_2.muscle_and_bone.disorder.risk.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "patient.muscle_and_bone.disorder.risk.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "insured_1.circulatory_disorder_risk.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "insured_2.circulatory_disorder_risk.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "patient.circulatory_disorder_risk.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "insured_1.blood_disorder.risk.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "insured_2.blood_disorder.risk.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "patient.blood_disorder.risk.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "insured_1.respiratory_disorder_risk.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "insured_2.respiratory_disorder_risk.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "patient.respiratory_disorder_risk.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "insured_1.ent_disorder_risk.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "insured_2.ent_disorder_risk.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "patient.ent_disorder_risk.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "insured_1.lymphatic_disorder_risk.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "insured_2.lymphatic_disorder_risk.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "patient.lymphatic_disorder_risk.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "insured_1.digestive_disorder_risk.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "insured_2.digestive_disorder_risk.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "patient.digestive_disorder_risk.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "insured_1.kidney_disorder.risk.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "insured_2.kidney_disorder.risk.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "patient.kidney_disorder.risk.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "insured_1.medical_test.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "insured_2.medical_test.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "patient.medical_test.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "insured_1.medication.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "insured_2.medication.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "patient.medication.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "insured_1.hospitalization.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "insured_2.hospitalization.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "patient.hospitalization.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "insured_1.recent.hospitalization.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "insured_2.recent.hospitalization.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "patient.recent.hospitalization.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "insured_1.pregnant.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "insured_2.pregnant.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "patient.pregnant.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "insured_1.female_reproductive_risk.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "insured_2.female_reproductive_risk.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "patient.female_reproductive_risk.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "insured_1.physical_disability.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "insured_2.physical_disability.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "patient.physical_disability.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "insured_1.nervous_disorder_risk.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "insured_2.nervous_disorder_risk.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "patient.nervous_disorder_risk.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "insured_1.gastrointestinal_disorder.risk.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "insured_2.gastrointestinal_disorder.risk.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "patient.gastrointestinal_disorder.risk.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "insured_1.endocrine_disorder_risk.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "insured_2.endocrine_disorder_risk.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "patient.endocrine_disorder_risk.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "insured_1.congenital_disorder_risk.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "insured_2.congenital_disorder_risk.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "patient.congenital_disorder_risk.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "insured_1.other_symptoms.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "insured_2.other_symptoms.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "patient.other_symptoms.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "insured_1.current.treatment.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "insured_2.current.treatment.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "patient.current.treatment.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "insured_1.history.treatment.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "insured_2.history.treatment.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "patient.history.treatment.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "insured_1.consultation.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "insured_2.consultation.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "patient.consultation.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "insured_1.plan.consultation.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "insured_2.plan.consultation.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "patient.plan.consultation.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "insured_1.hepatobiliary_risk.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "insured_2.hepatobiliary_risk.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "patient.hepatobiliary_risk.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "insured_1.gastrointestinal_disorder_risk.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "insured_2.gastrointestinal_disorder_risk.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "patient.gastrointestinal_disorder_risk.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "patient.respiratory_disorder_risk.details": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "insured_1.respiratory_disorder_risk.details": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "insured_2.respiratory_disorder_risk.details": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "patient.neoplasm_risk.details": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "insured_1.neoplasm_risk.details": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "insured_2.neoplasm_risk.details": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "patient.mental_disorder_risk.details": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "insured_1.mental_disorder_risk.details": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "insured_2.mental_disorder_risk.details": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "patient.blood_pressure_disorder.risk.details": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "insured_1.blood_pressure_disorder.risk.details": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "insured_2.blood_pressure_disorder.risk.details": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "patient.muscle_and_bone_disorder_risk.details": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "insured_1.muscle_and_bone_disorder_risk.details": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "insured_2.muscle_and_bone_disorder_risk.details": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "patient.digestive_disorder_risk.details": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "insured_1.digestive_disorder_risk.details": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "insured_2.digestive_disorder_risk.details": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "patient.muscle_and_bone_disorder_risk.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "insured_1.muscle_and_bone_disorder_risk.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "insured_2.muscle_and_bone_disorder_risk.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "patient.blood_pressure_disorder.risk.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "insured_1.blood_pressure_disorder.risk.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        "insured_2.blood_pressure_disorder.risk.yes": get_schema_id(Title.DIABETES) + "." + "risk_factor",
        const.GENERATED_ENTITY + "category": get_schema_id(Title.DIABETES) + "." + "category",
        const.GENERATED_ENTITY + "medical_code": get_schema_id(Title.DIABETES) + "." + "medical_code",
        const.GENERATED_ENTITY + "SNOMED": get_schema_id(Title.DIABETES) + "." + "medical_code",
        const.GENERATED_ENTITY + "snomed": get_schema_id(Title.DIABETES) + "." + "medical_code",
        const.GENERATED_ENTITY + "icd10": get_schema_id(Title.DIABETES) + "." + "medical_code",
        "icd10": get_schema_id(Title.DIABETES) + "." + "medical_code",
        const.GENERATED_ENTITY + "loinc": get_schema_id(Title.DIABETES) + "." + "medical_code",
        "loinc": get_schema_id(Title.DIABETES) + "." + "medical_code",
        const.GENERATED_ENTITY + "medical_code_type": get_schema_id(Title.DIABETES) + "." + "medical_code_type",
        "o.value": get_schema_id(Title.DIABETES) + "." + "findings",
        "lab.value": get_schema_id(Title.DIABETES) + "." + "findings",
        "diagnosis.attribute": get_schema_id(Title.DIABETES) + "." + "findings",
        "o.issued": get_schema_id(Title.DIABETES) + "." + "date",
        "lab.collected": get_schema_id(Title.DIABETES) + "." + "date",
        "lab.received": get_schema_id(Title.DIABETES) + "." + "date",
        "lab.issued": get_schema_id(Title.DIABETES) + "." + "date",
        "pathology.collected": get_schema_id(Title.DIABETES) + "." + "date",
        "pathology.received": get_schema_id(Title.DIABETES) + "." + "date",
        "pathology.issued": get_schema_id(Title.DIABETES) + "." + "date",
        "encounter.date": get_schema_id(Title.DIABETES) + "." + "date",
        const.GENERATED_ENTITY + "o.issued": get_schema_id(Title.DIABETES) + "." + "date",
        const.GENERATED_ENTITY + "lab.collected": get_schema_id(Title.DIABETES) + "." + "date",
        const.GENERATED_ENTITY + "lab.received": get_schema_id(Title.DIABETES) + "." + "date",
        const.GENERATED_ENTITY + "lab.issued": get_schema_id(Title.DIABETES) + "." + "date",
        const.GENERATED_ENTITY + "pathology.collected": get_schema_id(Title.DIABETES) + "." + "date",
        const.GENERATED_ENTITY + "pathology.received": get_schema_id(Title.DIABETES) + "." + "date",
        const.GENERATED_ENTITY + "pathology.issued": get_schema_id(Title.DIABETES) + "." + "date",
        const.GENERATED_ENTITY + "encounter.date": get_schema_id(Title.DIABETES) + "." + "date",
        "diagnosis.date": get_schema_id(Title.DIABETES) + "." + "date",
        "diag.date": get_schema_id(Title.DIABETES) + "." + "date",
        "diagnosis.history.date": get_schema_id(Title.DIABETES) + "." + "date"
    },
    "cancer": {
        "o.name": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "lab.name": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "diagnosis.principal": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "diagnosis.primary": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "diagnosis.secondary": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "diagnosis.history": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "cancer.diagnosis.grade": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "cancer.exam": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "cancer.surgery": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "cancer.type": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "cancer.stage": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "cancer.treatment": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "cancer.recurrence": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "cancer.history.type": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "treatment.name": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "encounter.procedure": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "imaging.exam": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "surgery.procedure": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "assessment.diag": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "death.cause": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "medical.condition": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "history.diag": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "pathology.diagnosis": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "patient.covid.positive": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "insured_1.covid.positive": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "insured_2.covid.positive": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "patient.medical_disorder.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "insured_1.medical_disorder.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "insured_2.medical_disorder.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "patient.heart.disorder.risk.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "insured_1.heart.disorder.risk.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "insured_2.heart.disorder.risk.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "patient.neoplasm.risk.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "insured_1.neoplasm.risk.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "insured_2.neoplasm.risk.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "patient.mental.disorder.risk.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "insured_1.mental.disorder.risk.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "insured_2.mental.disorder.risk.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "patient.brain_hemorrhage.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "insured_1.brain_hemorrhage.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "insured_2.brain_hemorrhage.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "patient.neurological_disorder.risk.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "insured_1.neurological_disorder.risk.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "insured_2.neurological_disorder.risk.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "patient.immune.disorder.risk.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "insured_1.immune.disorder.risk.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "insured_2.immune.disorder.risk.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "patient.std.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "insured_1.std.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "insured_2.std.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "patient.muscleandbone.disorder.risk.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "insured_1.muscleandbone.disorder.risk.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "insured_2.muscleandbone.disorder.risk.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "patient.hepatitis.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "insured_1.hepatitis.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "insured_2.hepatitis.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "patient.endocrine.disorder.risk.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "insured_1.endocrine.disorder.risk.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "insured_2.endocrine.disorder.risk.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "patient.circulatory.disorder.risk.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "insured_1.circulatory.disorder.risk.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "insured_2.circulatory.disorder.risk.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "patient.blood.disorder.risk.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "insured_1.blood.disorder.risk.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "insured_2.blood.disorder.risk.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "patient.respiratory.disorder.risk.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "insured_1.respiratory.disorder.risk.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "insured_2.respiratory.disorder.risk.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "patient.ent_disorder.risk.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "insured_1.ent_disorder.risk.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "insured_2.ent_disorder.risk.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "patient.ent.nose_disorder.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "insured_1.ent.nose_disorder.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "insured_2.ent.nose_disorder.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "patient.ent.ear_disorder.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "insured_1.ent.ear_disorder.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "insured_2.ent.ear_disorder.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "patient.ent.throat_disorder.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "insured_1.ent.throat_disorder.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "insured_2.ent.throat_disorder.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "patient.ent.thyroid_disorder.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "insured_1.ent.thyroid_disorder.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "insured_2.ent.thyroid_disorder.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "patient.lymphatic.disorder.risk.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "insured_1.lymphatic.disorder.risk.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "insured_2.lymphatic.disorder.risk.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "patient.oedema_disease.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "insured_1.oedema_disease.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "insured_2.oedema_disease.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "patient.digestive.disorder.risk.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "insured_1.digestive.disorder.risk.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "insured_2.digestive.disorder.risk.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "patient.liver_disorder.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "insured_1.liver_disorder.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "insured_2.liver_disorder.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "patient.kidney.disorder.risk.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "insured_1.kidney.disorder.risk.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "insured_2.kidney.disorder.risk.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "patient.breast_disorder.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "insured_1.breast_disorder.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "insured_2.breast_disorder.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "patient.cyst.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "insured_1.cyst.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "insured_2.cyst.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "patient.skin_diseases.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "insured_1.skin_diseases.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "insured_2.skin_diseases.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "patient.hormonal_disorder.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "insured_1.hormonal_disorder.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "insured_2.hormonal_disorder.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "patient.female.reproductive.risk.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "insured_1.female.reproductive.risk.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "insured_2.female.reproductive.risk.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "patient.fever.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "insured_1.fever.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "insured_2.fever.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "patient.tuberculosis.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "insured_1.tuberculosis.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "insured_2.tuberculosis.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "patient.physical.disorder.risk.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "insured_1.physical.disorder.risk.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "insured_2.physical.disorder.risk.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "patient.allergies.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "insured_1.allergies.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "insured_2.allergies.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "patient.endocrine_disorder.risk.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "insured_1.endocrine_disorder.risk.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "insured_2.endocrine_disorder.risk.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "patient.congenital.disorder.risk.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "insured_1.congenital.disorder.risk.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "insured_2.congenital.disorder.risk.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "patient.nervous.disorder.risk.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "insured_1.nervous.disorder.risk.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "insured_2.nervous.disorder.risk.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "patient.gastrointestinal.disorder.risk.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "insured_1.gastrointestinal.disorder.risk.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "insured_2.gastrointestinal.disorder.risk.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "patient.chronic_disease.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "insured_1.chronic_disease.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "insured_2.chronic_disease.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "patient.urinary_disorder.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "insured_1.urinary_disorder.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "insured_2.urinary_disorder.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "patient.heart_disease.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "insured_1.heart_disease.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "insured_2.heart_disease.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "patient.cancer_disease.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "insured_1.cancer_disease.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "insured_2.cancer_disease.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "patient.mental_disorder.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "insured_1.mental_disorder.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "insured_2.mental_disorder.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "patient.hiv.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "insured_1.hiv.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "insured_2.hiv.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "patient.muscular_pain.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "insured_1.muscular_pain.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "insured_2.muscular_pain.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "patient.diabetes.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "insured_1.diabetes.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "insured_2.diabetes.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "patient.high_blood_pressure.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "insured_1.high_blood_pressure.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "insured_2.high_blood_pressure.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "patient.blood_disorder.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "insured_1.blood_disorder.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "insured_2.blood_disorder.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "patient.breathing_disorder.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "insured_1.breathing_disorder.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "insured_2.breathing_disorder.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "patient.ent.eyes_disorder.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "insured_1.ent.eyes_disorder.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "insured_2.ent.eyes_disorder.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "patient.lymphatic_system_disease.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "insured_1.lymphatic_system_disease.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "insured_2.lymphatic_system_disease.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "patient.stomach_disorder.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "insured_1.stomach_disorder.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "insured_2.stomach_disorder.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "patient.kidney_disorder.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "insured_1.kidney_disorder.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "insured_2.kidney_disorder.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "patient.reproductive_organs_diseases.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "insured_1.reproductive_organs_diseases.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "insured_2.reproductive_organs_diseases.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "patient.physical_disorder.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "insured_1.physical_disorder.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "insured_2.physical_disorder.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "patient.endocrine_glands_disorder.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "insured_1.endocrine_glands_disorder.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "insured_2.endocrine_glands_disorder.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "patient.nervous_system_disorder.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "insured_1.nervous_system_disorder.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "insured_2.nervous_system_disorder.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "patient.gastrointestinal_disorder.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "insured_1.gastrointestinal_disorder.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "insured_2.gastrointestinal_disorder.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "insured_1.heart_disorder.risk.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "insured_2.heart_disorder.risk.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "patient.heart_disorder.risk.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "insured_1.neoplasm_risk.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "insured_2.neoplasm_risk.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "patient.neoplasm_risk.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "insured_1.mental_disorder_risk.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "insured_2.mental_disorder_risk.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "patient.mental_disorder_risk.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "insured_1.immune_disorder_risk.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "insured_2.immune_disorder_risk.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "patient.immune_disorder_risk.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "insured_1.muscle_and_bone.disorder.risk.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "insured_2.muscle_and_bone.disorder.risk.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "patient.muscle_and_bone.disorder.risk.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "insured_1.circulatory_disorder_risk.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "insured_2.circulatory_disorder_risk.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "patient.circulatory_disorder_risk.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "insured_1.blood_disorder.risk.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "insured_2.blood_disorder.risk.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "patient.blood_disorder.risk.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "insured_1.respiratory_disorder_risk.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "insured_2.respiratory_disorder_risk.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "patient.respiratory_disorder_risk.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "insured_1.ent_disorder_risk.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "insured_2.ent_disorder_risk.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "patient.ent_disorder_risk.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "insured_1.lymphatic_disorder_risk.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "insured_2.lymphatic_disorder_risk.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "patient.lymphatic_disorder_risk.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "insured_1.digestive_disorder_risk.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "insured_2.digestive_disorder_risk.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "patient.digestive_disorder_risk.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "insured_1.kidney_disorder.risk.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "insured_2.kidney_disorder.risk.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "patient.kidney_disorder.risk.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "insured_1.medical_test.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "insured_2.medical_test.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "patient.medical_test.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "insured_1.medication.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "insured_2.medication.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "patient.medication.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "insured_1.hospitalization.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "insured_2.hospitalization.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "patient.hospitalization.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "insured_1.recent.hospitalization.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "insured_2.recent.hospitalization.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "patient.recent.hospitalization.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "insured_1.pregnant.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "insured_2.pregnant.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "patient.pregnant.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "insured_1.female_reproductive_risk.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "insured_2.female_reproductive_risk.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "patient.female_reproductive_risk.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "insured_1.physical_disability.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "insured_2.physical_disability.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "patient.physical_disability.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "insured_1.nervous_disorder_risk.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "insured_2.nervous_disorder_risk.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "patient.nervous_disorder_risk.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "insured_1.gastrointestinal_disorder.risk.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "insured_2.gastrointestinal_disorder.risk.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "patient.gastrointestinal_disorder.risk.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "insured_1.endocrine_disorder_risk.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "insured_2.endocrine_disorder_risk.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "patient.endocrine_disorder_risk.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "insured_1.congenital_disorder_risk.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "insured_2.congenital_disorder_risk.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "patient.congenital_disorder_risk.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "insured_1.other_symptoms.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "insured_2.other_symptoms.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "patient.other_symptoms.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "insured_1.current.treatment.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "insured_2.current.treatment.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "patient.current.treatment.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "insured_1.history.treatment.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "insured_2.history.treatment.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "patient.history.treatment.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "insured_1.consultation.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "insured_2.consultation.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "patient.consultation.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "insured_1.plan.consultation.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "insured_2.plan.consultation.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "patient.plan.consultation.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "insured_1.hepatobiliary_risk.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "insured_2.hepatobiliary_risk.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "patient.hepatobiliary_risk.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "insured_1.gastrointestinal_disorder_risk.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "insured_2.gastrointestinal_disorder_risk.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "patient.gastrointestinal_disorder_risk.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "patient.respiratory_disorder_risk.details": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "insured_1.respiratory_disorder_risk.details": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "insured_2.respiratory_disorder_risk.details": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "patient.neoplasm_risk.details": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "insured_1.neoplasm_risk.details": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "insured_2.neoplasm_risk.details": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "patient.mental_disorder_risk.details": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "insured_1.mental_disorder_risk.details": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "insured_2.mental_disorder_risk.details": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "patient.blood_pressure_disorder.risk.details": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "insured_1.blood_pressure_disorder.risk.details": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "insured_2.blood_pressure_disorder.risk.details": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "patient.muscle_and_bone_disorder_risk.details": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "insured_1.muscle_and_bone_disorder_risk.details": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "insured_2.muscle_and_bone_disorder_risk.details": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "patient.digestive_disorder_risk.details": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "insured_1.digestive_disorder_risk.details": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "insured_2.digestive_disorder_risk.details": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "patient.muscle_and_bone_disorder_risk.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "insured_1.muscle_and_bone_disorder_risk.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "insured_2.muscle_and_bone_disorder_risk.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "patient.blood_pressure_disorder.risk.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "insured_1.blood_pressure_disorder.risk.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        "insured_2.blood_pressure_disorder.risk.yes": get_schema_id(Title.CANCER) + "." + "risk_factor",
        const.GENERATED_ENTITY + "neoplasm": get_schema_id(Title.CANCER) + "." + "risk_factor",
        const.GENERATED_ENTITY + "category": get_schema_id(Title.CANCER) + "." + "category",
        const.GENERATED_ENTITY + "medical_code": get_schema_id(Title.CANCER) + "." + "medical_code",
        const.GENERATED_ENTITY + "SNOMED": get_schema_id(Title.CANCER) + "." + "medical_code",
        const.GENERATED_ENTITY + "snomed": get_schema_id(Title.CANCER) + "." + "medical_code",
        const.GENERATED_ENTITY + "icd10": get_schema_id(Title.CANCER) + "." + "medical_code",
        "icd10": get_schema_id(Title.CANCER) + "." + "medical_code",
        const.GENERATED_ENTITY + "loinc": get_schema_id(Title.CANCER) + "." + "medical_code",
        "loinc": get_schema_id(Title.CANCER) + "." + "medical_code",
        const.GENERATED_ENTITY + "medical_code_type": get_schema_id(Title.CANCER) + "." + "medical_code_type",
        "o.value": get_schema_id(Title.CANCER) + "." + "findings",
        "lab.value": get_schema_id(Title.CANCER) + "." + "findings",
        "diagnosis.attribute": get_schema_id(Title.CANCER) + "." + "findings",
        "cancer.findings": get_schema_id(Title.CANCER) + "." + "findings",
        "cancer.exam.findings": get_schema_id(Title.CANCER) + "." + "findings",
        "o.issued": get_schema_id(Title.CANCER) + "." + "date",
        "lab.collected": get_schema_id(Title.CANCER) + "." + "date",
        "lab.received": get_schema_id(Title.CANCER) + "." + "date",
        "lab.issued": get_schema_id(Title.CANCER) + "." + "date",
        "pathology.collected": get_schema_id(Title.CANCER) + "." + "date",
        "pathology.received": get_schema_id(Title.CANCER) + "." + "date",
        "pathology.issued": get_schema_id(Title.CANCER) + "." + "date",
        "encounter.date": get_schema_id(Title.CANCER) + "." + "date",
        const.GENERATED_ENTITY + "o.issued": get_schema_id(Title.CANCER) + "." + "date",
        const.GENERATED_ENTITY + "lab.collected": get_schema_id(Title.CANCER) + "." + "date",
        const.GENERATED_ENTITY + "lab.received": get_schema_id(Title.CANCER) + "." + "date",
        const.GENERATED_ENTITY + "lab.issued": get_schema_id(Title.CANCER) + "." + "date",
        const.GENERATED_ENTITY + "pathology.collected": get_schema_id(Title.CANCER) + "." + "date",
        const.GENERATED_ENTITY + "pathology.received": get_schema_id(Title.CANCER) + "." + "date",
        const.GENERATED_ENTITY + "pathology.issued": get_schema_id(Title.CANCER) + "." + "date",
        const.GENERATED_ENTITY + "encounter.date": get_schema_id(Title.CANCER) + "." + "date",
        const.GENERATED_ENTITY + "imaging.date": get_schema_id(Title.CANCER) + "." + "date",
        const.GENERATED_ENTITY + "surgery.date": get_schema_id(Title.CANCER) + "." + "date",
        "diagnosis.date": get_schema_id(Title.CANCER) + "." + "date",
        "cancer.diagnosis.date": get_schema_id(Title.CANCER) + "." + "date",
        "cancer.surgery.date": get_schema_id(Title.CANCER) + "." + "date",
        "cancer.treatment.date": get_schema_id(Title.CANCER) + "." + "date",
        "cancer.recurrence.date": get_schema_id(Title.CANCER) + "." + "date",
        "cancer.exam.date": get_schema_id(Title.CANCER) + "." + "date",
        "diag.date": get_schema_id(Title.CANCER) + "." + "date",
        "cancer.history.date": get_schema_id(Title.CANCER) + "." + "date",
        "diagnosis.history.date": get_schema_id(Title.CANCER) + "." + "date"
    },
    "cardiovascular": {
        "o.name": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "lab.name": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "diagnosis.principal": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "diagnosis.primary": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "diagnosis.secondary": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "diagnosis.history": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "cardio.diagnosis": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "cardio.exam": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "imaging.exam": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "cardio.surgery": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "cardio.procedure": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "assessment.diag": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "death.cause": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "medical.condition": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "history.diag": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "pathology.diagnosis": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "patient.covid.positive": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "insured_1.covid.positive": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "insured_2.covid.positive": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "patient.medical_disorder.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "insured_1.medical_disorder.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "insured_2.medical_disorder.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "patient.heart.disorder.risk.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "insured_1.heart.disorder.risk.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "insured_2.heart.disorder.risk.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "patient.neoplasm.risk.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "insured_1.neoplasm.risk.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "insured_2.neoplasm.risk.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "patient.mental.disorder.risk.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "insured_1.mental.disorder.risk.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "insured_2.mental.disorder.risk.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "patient.brain_hemorrhage.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "insured_1.brain_hemorrhage.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "insured_2.brain_hemorrhage.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "patient.neurological_disorder.risk.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "insured_1.neurological_disorder.risk.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "insured_2.neurological_disorder.risk.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "patient.immune.disorder.risk.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "insured_1.immune.disorder.risk.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "insured_2.immune.disorder.risk.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "patient.std.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "insured_1.std.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "insured_2.std.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "patient.muscleandbone.disorder.risk.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "insured_1.muscleandbone.disorder.risk.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "insured_2.muscleandbone.disorder.risk.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "patient.hepatitis.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "insured_1.hepatitis.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "insured_2.hepatitis.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "patient.endocrine.disorder.risk.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "insured_1.endocrine.disorder.risk.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "insured_2.endocrine.disorder.risk.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "patient.circulatory.disorder.risk.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "insured_1.circulatory.disorder.risk.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "insured_2.circulatory.disorder.risk.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "patient.blood.disorder.risk.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "insured_1.blood.disorder.risk.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "insured_2.blood.disorder.risk.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "patient.respiratory.disorder.risk.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "insured_1.respiratory.disorder.risk.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "insured_2.respiratory.disorder.risk.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "patient.ent_disorder.risk.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "insured_1.ent_disorder.risk.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "insured_2.ent_disorder.risk.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "patient.ent.nose_disorder.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "insured_1.ent.nose_disorder.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "insured_2.ent.nose_disorder.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "patient.ent.ear_disorder.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "insured_1.ent.ear_disorder.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "insured_2.ent.ear_disorder.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "patient.ent.throat_disorder.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "insured_1.ent.throat_disorder.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "insured_2.ent.throat_disorder.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "patient.ent.thyroid_disorder.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "insured_1.ent.thyroid_disorder.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "insured_2.ent.thyroid_disorder.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "patient.lymphatic.disorder.risk.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "insured_1.lymphatic.disorder.risk.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "insured_2.lymphatic.disorder.risk.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "patient.oedema_disease.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "insured_1.oedema_disease.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "insured_2.oedema_disease.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "patient.digestive.disorder.risk.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "insured_1.digestive.disorder.risk.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "insured_2.digestive.disorder.risk.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "patient.liver_disorder.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "insured_1.liver_disorder.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "insured_2.liver_disorder.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "patient.kidney.disorder.risk.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "insured_1.kidney.disorder.risk.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "insured_2.kidney.disorder.risk.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "patient.breast_disorder.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "insured_1.breast_disorder.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "insured_2.breast_disorder.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "patient.cyst.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "insured_1.cyst.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "insured_2.cyst.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "patient.skin_diseases.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "insured_1.skin_diseases.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "insured_2.skin_diseases.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "patient.hormonal_disorder.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "insured_1.hormonal_disorder.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "insured_2.hormonal_disorder.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "patient.female.reproductive.risk.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "insured_1.female.reproductive.risk.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "insured_2.female.reproductive.risk.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "patient.fever.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "insured_1.fever.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "insured_2.fever.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "patient.tuberculosis.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "insured_1.tuberculosis.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "insured_2.tuberculosis.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "patient.physical.disorder.risk.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "insured_1.physical.disorder.risk.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "insured_2.physical.disorder.risk.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "patient.allergies.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "insured_1.allergies.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "insured_2.allergies.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "patient.endocrine_disorder.risk.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "insured_1.endocrine_disorder.risk.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "insured_2.endocrine_disorder.risk.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "patient.congenital.disorder.risk.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "insured_1.congenital.disorder.risk.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "insured_2.congenital.disorder.risk.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "patient.nervous.disorder.risk.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "insured_1.nervous.disorder.risk.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "insured_2.nervous.disorder.risk.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "patient.gastrointestinal.disorder.risk.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "insured_1.gastrointestinal.disorder.risk.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "insured_2.gastrointestinal.disorder.risk.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "patient.chronic_disease.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "insured_1.chronic_disease.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "insured_2.chronic_disease.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "patient.urinary_disorder.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "insured_1.urinary_disorder.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "insured_2.urinary_disorder.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "patient.heart_disease.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "insured_1.heart_disease.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "insured_2.heart_disease.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "patient.cancer_disease.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "insured_1.cancer_disease.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "insured_2.cancer_disease.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "patient.mental_disorder.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "insured_1.mental_disorder.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "insured_2.mental_disorder.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "patient.hiv.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "insured_1.hiv.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "insured_2.hiv.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "patient.muscular_pain.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "insured_1.muscular_pain.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "insured_2.muscular_pain.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "patient.diabetes.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "insured_1.diabetes.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "insured_2.diabetes.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "patient.high_blood_pressure.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "insured_1.high_blood_pressure.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "insured_2.high_blood_pressure.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "patient.blood_disorder.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "insured_1.blood_disorder.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "insured_2.blood_disorder.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "patient.breathing_disorder.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "insured_1.breathing_disorder.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "insured_2.breathing_disorder.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "patient.ent.eyes_disorder.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "insured_1.ent.eyes_disorder.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "insured_2.ent.eyes_disorder.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "patient.lymphatic_system_disease.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "insured_1.lymphatic_system_disease.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "insured_2.lymphatic_system_disease.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "patient.stomach_disorder.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "insured_1.stomach_disorder.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "insured_2.stomach_disorder.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "patient.kidney_disorder.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "insured_1.kidney_disorder.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "insured_2.kidney_disorder.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "patient.reproductive_organs_diseases.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "insured_1.reproductive_organs_diseases.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "insured_2.reproductive_organs_diseases.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "patient.physical_disorder.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "insured_1.physical_disorder.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "insured_2.physical_disorder.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "patient.endocrine_glands_disorder.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "insured_1.endocrine_glands_disorder.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "insured_2.endocrine_glands_disorder.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "patient.nervous_system_disorder.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "insured_1.nervous_system_disorder.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "insured_2.nervous_system_disorder.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "patient.gastrointestinal_disorder.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "insured_1.gastrointestinal_disorder.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "insured_2.gastrointestinal_disorder.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "insured_1.heart_disorder.risk.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "insured_2.heart_disorder.risk.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "patient.heart_disorder.risk.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "insured_1.neoplasm_risk.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "insured_2.neoplasm_risk.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "patient.neoplasm_risk.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "insured_1.mental_disorder_risk.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "insured_2.mental_disorder_risk.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "patient.mental_disorder_risk.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "insured_1.immune_disorder_risk.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "insured_2.immune_disorder_risk.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "patient.immune_disorder_risk.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "insured_1.muscle_and_bone.disorder.risk.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "insured_2.muscle_and_bone.disorder.risk.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "patient.muscle_and_bone.disorder.risk.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "insured_1.circulatory_disorder_risk.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "insured_2.circulatory_disorder_risk.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "patient.circulatory_disorder_risk.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "insured_1.blood_disorder.risk.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "insured_2.blood_disorder.risk.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "patient.blood_disorder.risk.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "insured_1.respiratory_disorder_risk.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "insured_2.respiratory_disorder_risk.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "patient.respiratory_disorder_risk.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "insured_1.ent_disorder_risk.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "insured_2.ent_disorder_risk.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "patient.ent_disorder_risk.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "insured_1.lymphatic_disorder_risk.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "insured_2.lymphatic_disorder_risk.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "patient.lymphatic_disorder_risk.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "insured_1.digestive_disorder_risk.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "insured_2.digestive_disorder_risk.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "patient.digestive_disorder_risk.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "insured_1.kidney_disorder.risk.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "insured_2.kidney_disorder.risk.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "patient.kidney_disorder.risk.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "insured_1.medical_test.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "insured_2.medical_test.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "patient.medical_test.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "insured_1.medication.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "insured_2.medication.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "patient.medication.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "insured_1.hospitalization.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "insured_2.hospitalization.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "patient.hospitalization.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "insured_1.recent.hospitalization.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "insured_2.recent.hospitalization.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "patient.recent.hospitalization.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "insured_1.pregnant.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "insured_2.pregnant.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "patient.pregnant.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "insured_1.female_reproductive_risk.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "insured_2.female_reproductive_risk.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "patient.female_reproductive_risk.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "insured_1.physical_disability.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "insured_2.physical_disability.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "patient.physical_disability.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "insured_1.nervous_disorder_risk.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "insured_2.nervous_disorder_risk.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "patient.nervous_disorder_risk.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "insured_1.gastrointestinal_disorder.risk.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "insured_2.gastrointestinal_disorder.risk.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "patient.gastrointestinal_disorder.risk.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "insured_1.endocrine_disorder_risk.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "insured_2.endocrine_disorder_risk.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "patient.endocrine_disorder_risk.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "insured_1.congenital_disorder_risk.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "insured_2.congenital_disorder_risk.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "patient.congenital_disorder_risk.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "insured_1.other_symptoms.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "insured_2.other_symptoms.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "patient.other_symptoms.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "insured_1.current.treatment.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "insured_2.current.treatment.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "patient.current.treatment.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "insured_1.history.treatment.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "insured_2.history.treatment.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "patient.history.treatment.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "insured_1.consultation.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "insured_2.consultation.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "patient.consultation.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "insured_1.plan.consultation.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "insured_2.plan.consultation.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "patient.plan.consultation.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "insured_1.hepatobiliary_risk.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "insured_2.hepatobiliary_risk.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "patient.hepatobiliary_risk.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        const.GENERATED_ENTITY + "heart": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "insured_1.gastrointestinal_disorder_risk.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "insured_2.gastrointestinal_disorder_risk.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "patient.gastrointestinal_disorder_risk.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "patient.respiratory_disorder_risk.details": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "insured_1.respiratory_disorder_risk.details": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "insured_2.respiratory_disorder_risk.details": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "patient.neoplasm_risk.details": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "insured_1.neoplasm_risk.details": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "insured_2.neoplasm_risk.details": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "patient.mental_disorder_risk.details": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "insured_1.mental_disorder_risk.details": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "insured_2.mental_disorder_risk.details": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "patient.blood_pressure_disorder.risk.details": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "insured_1.blood_pressure_disorder.risk.details": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "insured_2.blood_pressure_disorder.risk.details": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "patient.muscle_and_bone_disorder_risk.details": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "insured_1.muscle_and_bone_disorder_risk.details": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "insured_2.muscle_and_bone_disorder_risk.details": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "patient.digestive_disorder_risk.details": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "insured_1.digestive_disorder_risk.details": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "insured_2.digestive_disorder_risk.details": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "patient.muscle_and_bone_disorder_risk.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "insured_1.muscle_and_bone_disorder_risk.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "insured_2.muscle_and_bone_disorder_risk.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "patient.blood_pressure_disorder.risk.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "insured_1.blood_pressure_disorder.risk.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        "insured_2.blood_pressure_disorder.risk.yes": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        const.GENERATED_ENTITY + "circulatory": get_schema_id(Title.CARDIOVASCULAR) + "." + "risk_factor",
        const.GENERATED_ENTITY + "category": get_schema_id(Title.CARDIOVASCULAR) + "." + "category",
        const.GENERATED_ENTITY + "medical_code": get_schema_id(Title.CARDIOVASCULAR) + "." + "medical_code",
        const.GENERATED_ENTITY + "SNOMED": get_schema_id(Title.CARDIOVASCULAR) + "." + "medical_code",
        const.GENERATED_ENTITY + "snomed": get_schema_id(Title.CARDIOVASCULAR) + "." + "medical_code",
        const.GENERATED_ENTITY + "icd10": get_schema_id(Title.CARDIOVASCULAR) + "." + "medical_code",
        "icd10": get_schema_id(Title.CARDIOVASCULAR) + "." + "medical_code",
        const.GENERATED_ENTITY + "loinc": get_schema_id(Title.CARDIOVASCULAR) + "." + "medical_code",
        "loinc": get_schema_id(Title.CARDIOVASCULAR) + "." + "medical_code",
        const.GENERATED_ENTITY + "medical_code_type": get_schema_id(Title.CARDIOVASCULAR) + "." + "medical_code_type",
        "o.value": get_schema_id(Title.CARDIOVASCULAR) + "." + "findings",
        "lab.value": get_schema_id(Title.CARDIOVASCULAR) + "." + "findings",
        "diagnosis.attribute": get_schema_id(Title.CARDIOVASCULAR) + "." + "findings",
        "cardio.findings": get_schema_id(Title.CARDIOVASCULAR) + "." + "findings",
        "imaging.finding": get_schema_id(Title.CARDIOVASCULAR) + "." + "findings",
        "o.issued": get_schema_id(Title.CARDIOVASCULAR) + "." + "date",
        "lab.collected": get_schema_id(Title.CARDIOVASCULAR) + "." + "date",
        "lab.received": get_schema_id(Title.CARDIOVASCULAR) + "." + "date",
        "lab.issued": get_schema_id(Title.CARDIOVASCULAR) + "." + "date",
        "pathology.collected": get_schema_id(Title.CARDIOVASCULAR) + "." + "date",
        "pathology.received": get_schema_id(Title.CARDIOVASCULAR) + "." + "date",
        "pathology.issued": get_schema_id(Title.CARDIOVASCULAR) + "." + "date",
        "encounter.date": get_schema_id(Title.CARDIOVASCULAR) + "." + "date",
        const.GENERATED_ENTITY + "o.issued": get_schema_id(Title.CARDIOVASCULAR) + "." + "date",
        const.GENERATED_ENTITY + "lab.collected": get_schema_id(Title.CARDIOVASCULAR) + "." + "date",
        const.GENERATED_ENTITY + "lab.received": get_schema_id(Title.CARDIOVASCULAR) + "." + "date",
        const.GENERATED_ENTITY + "lab.issued": get_schema_id(Title.CARDIOVASCULAR) + "." + "date",
        const.GENERATED_ENTITY + "pathology.collected": get_schema_id(Title.CARDIOVASCULAR) + "." + "date",
        const.GENERATED_ENTITY + "pathology.received": get_schema_id(Title.CARDIOVASCULAR) + "." + "date",
        const.GENERATED_ENTITY + "pathology.issued": get_schema_id(Title.CARDIOVASCULAR) + "." + "date",
        const.GENERATED_ENTITY + "encounter.date": get_schema_id(Title.CARDIOVASCULAR) + "." + "date",
        "diagnosis.date": get_schema_id(Title.CARDIOVASCULAR) + "." + "date",
        "cardio.diagnosis.date": get_schema_id(Title.CARDIOVASCULAR) + "." + "date",
        "cardio.exam.date": get_schema_id(Title.CARDIOVASCULAR) + "." + "date",
        "imaging.date": get_schema_id(Title.CARDIOVASCULAR) + "." + "date",
        "cardio.surgery.date": get_schema_id(Title.CARDIOVASCULAR) + "." + "date",
        "cardio.procedure.date": get_schema_id(Title.CARDIOVASCULAR) + "." + "date",
        "diag.date": get_schema_id(Title.CARDIOVASCULAR) + "." + "date",
        "diagnosis.history.date": get_schema_id(Title.CARDIOVASCULAR) + "." + "date"
    },
    "mental_nervous_disorder": {
        "o.name": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "lab.name": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "diagnosis.principal": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "diagnosis.primary": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "diagnosis.secondary": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "diagnosis.history": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "assessment.diag": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "death.cause": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "medical.condition": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "history.diag": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "pathology.diagnosis": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "patient.covid.positive": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "insured_1.covid.positive": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "insured_2.covid.positive": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "patient.medical_disorder.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "insured_1.medical_disorder.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "insured_2.medical_disorder.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "patient.heart.disorder.risk.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "insured_1.heart.disorder.risk.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "insured_2.heart.disorder.risk.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "patient.neoplasm.risk.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "insured_1.neoplasm.risk.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "insured_2.neoplasm.risk.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "patient.mental.disorder.risk.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "insured_1.mental.disorder.risk.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "insured_2.mental.disorder.risk.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "patient.brain_hemorrhage.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "insured_1.brain_hemorrhage.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "insured_2.brain_hemorrhage.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "patient.neurological_disorder.risk.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "insured_1.neurological_disorder.risk.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "insured_2.neurological_disorder.risk.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "patient.immune.disorder.risk.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "insured_1.immune.disorder.risk.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "insured_2.immune.disorder.risk.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "patient.std.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "insured_1.std.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "insured_2.std.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "patient.muscleandbone.disorder.risk.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "insured_1.muscleandbone.disorder.risk.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "insured_2.muscleandbone.disorder.risk.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "patient.hepatitis.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "insured_1.hepatitis.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "insured_2.hepatitis.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "patient.endocrine.disorder.risk.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "insured_1.endocrine.disorder.risk.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "insured_2.endocrine.disorder.risk.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "patient.circulatory.disorder.risk.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "insured_1.circulatory.disorder.risk.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "insured_2.circulatory.disorder.risk.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "patient.blood.disorder.risk.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "insured_1.blood.disorder.risk.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "insured_2.blood.disorder.risk.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "patient.respiratory.disorder.risk.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "insured_1.respiratory.disorder.risk.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "insured_2.respiratory.disorder.risk.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "patient.ent_disorder.risk.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "insured_1.ent_disorder.risk.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "insured_2.ent_disorder.risk.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "patient.ent.nose_disorder.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "insured_1.ent.nose_disorder.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "insured_2.ent.nose_disorder.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "patient.ent.ear_disorder.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "insured_1.ent.ear_disorder.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "insured_2.ent.ear_disorder.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "patient.ent.throat_disorder.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "insured_1.ent.throat_disorder.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "insured_2.ent.throat_disorder.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "patient.ent.thyroid_disorder.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "insured_1.ent.thyroid_disorder.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "insured_2.ent.thyroid_disorder.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "patient.lymphatic.disorder.risk.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "insured_1.lymphatic.disorder.risk.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "insured_2.lymphatic.disorder.risk.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "patient.oedema_disease.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "insured_1.oedema_disease.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "insured_2.oedema_disease.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "patient.digestive.disorder.risk.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "insured_1.digestive.disorder.risk.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "insured_2.digestive.disorder.risk.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "patient.liver_disorder.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "insured_1.liver_disorder.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "insured_2.liver_disorder.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "patient.kidney.disorder.risk.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "insured_1.kidney.disorder.risk.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "insured_2.kidney.disorder.risk.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "patient.breast_disorder.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "insured_1.breast_disorder.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "insured_2.breast_disorder.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "patient.cyst.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "insured_1.cyst.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "insured_2.cyst.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "patient.skin_diseases.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "insured_1.skin_diseases.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "insured_2.skin_diseases.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "patient.hormonal_disorder.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "insured_1.hormonal_disorder.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "insured_2.hormonal_disorder.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "patient.female.reproductive.risk.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "insured_1.female.reproductive.risk.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "insured_2.female.reproductive.risk.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "patient.fever.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "insured_1.fever.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "insured_2.fever.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "patient.tuberculosis.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "insured_1.tuberculosis.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "insured_2.tuberculosis.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "patient.physical.disorder.risk.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "insured_1.physical.disorder.risk.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "insured_2.physical.disorder.risk.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "patient.allergies.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "insured_1.allergies.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "insured_2.allergies.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "patient.endocrine_disorder.risk.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "insured_1.endocrine_disorder.risk.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "insured_2.endocrine_disorder.risk.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "patient.congenital.disorder.risk.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "insured_1.congenital.disorder.risk.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "insured_2.congenital.disorder.risk.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "patient.nervous.disorder.risk.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "insured_1.nervous.disorder.risk.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "insured_2.nervous.disorder.risk.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "patient.gastrointestinal.disorder.risk.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "insured_1.gastrointestinal.disorder.risk.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "insured_2.gastrointestinal.disorder.risk.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "patient.chronic_disease.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "insured_1.chronic_disease.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "insured_2.chronic_disease.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "patient.urinary_disorder.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "insured_1.urinary_disorder.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "insured_2.urinary_disorder.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "patient.heart_disease.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "insured_1.heart_disease.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "insured_2.heart_disease.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "patient.cancer_disease.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "insured_1.cancer_disease.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "insured_2.cancer_disease.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "patient.mental_disorder.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "insured_1.mental_disorder.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "insured_2.mental_disorder.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "patient.hiv.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "insured_1.hiv.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "insured_2.hiv.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "patient.muscular_pain.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "insured_1.muscular_pain.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "insured_2.muscular_pain.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "patient.diabetes.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "insured_1.diabetes.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "insured_2.diabetes.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "patient.high_blood_pressure.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "insured_1.high_blood_pressure.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "insured_2.high_blood_pressure.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "patient.blood_disorder.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "insured_1.blood_disorder.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "insured_2.blood_disorder.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "patient.breathing_disorder.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "insured_1.breathing_disorder.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "insured_2.breathing_disorder.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "patient.ent.eyes_disorder.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "insured_1.ent.eyes_disorder.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "insured_2.ent.eyes_disorder.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "patient.lymphatic_system_disease.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "insured_1.lymphatic_system_disease.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "insured_2.lymphatic_system_disease.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "patient.stomach_disorder.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "insured_1.stomach_disorder.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "insured_2.stomach_disorder.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "patient.kidney_disorder.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "insured_1.kidney_disorder.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "insured_2.kidney_disorder.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "patient.reproductive_organs_diseases.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "insured_1.reproductive_organs_diseases.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "insured_2.reproductive_organs_diseases.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "patient.physical_disorder.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "insured_1.physical_disorder.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "insured_2.physical_disorder.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "patient.endocrine_glands_disorder.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "insured_1.endocrine_glands_disorder.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "insured_2.endocrine_glands_disorder.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "patient.nervous_system_disorder.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "insured_1.nervous_system_disorder.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "insured_2.nervous_system_disorder.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "patient.gastrointestinal_disorder.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "insured_1.gastrointestinal_disorder.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "insured_2.gastrointestinal_disorder.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "insured_1.heart_disorder.risk.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "insured_2.heart_disorder.risk.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "patient.heart_disorder.risk.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "insured_1.neoplasm_risk.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "insured_2.neoplasm_risk.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "patient.neoplasm_risk.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "insured_1.mental_disorder_risk.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "insured_2.mental_disorder_risk.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "patient.mental_disorder_risk.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "insured_1.immune_disorder_risk.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "insured_2.immune_disorder_risk.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "patient.immune_disorder_risk.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "insured_1.muscle_and_bone.disorder.risk.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "insured_2.muscle_and_bone.disorder.risk.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "patient.muscle_and_bone.disorder.risk.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "insured_1.circulatory_disorder_risk.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "insured_2.circulatory_disorder_risk.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "patient.circulatory_disorder_risk.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "insured_1.blood_disorder.risk.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "insured_2.blood_disorder.risk.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "patient.blood_disorder.risk.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "insured_1.respiratory_disorder_risk.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "insured_2.respiratory_disorder_risk.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "patient.respiratory_disorder_risk.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "insured_1.ent_disorder_risk.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "insured_2.ent_disorder_risk.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "patient.ent_disorder_risk.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "insured_1.lymphatic_disorder_risk.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "insured_2.lymphatic_disorder_risk.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "patient.lymphatic_disorder_risk.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "insured_1.digestive_disorder_risk.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "insured_2.digestive_disorder_risk.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "patient.digestive_disorder_risk.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "insured_1.kidney_disorder.risk.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "insured_2.kidney_disorder.risk.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "patient.kidney_disorder.risk.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "insured_1.medical_test.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "insured_2.medical_test.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "patient.medical_test.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "insured_1.medication.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "insured_2.medication.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "patient.medication.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "insured_1.hospitalization.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "insured_2.hospitalization.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "patient.hospitalization.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "insured_1.recent.hospitalization.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "insured_2.recent.hospitalization.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "patient.recent.hospitalization.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "insured_1.pregnant.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "insured_2.pregnant.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "patient.pregnant.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "insured_1.female_reproductive_risk.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "insured_2.female_reproductive_risk.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "patient.female_reproductive_risk.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "insured_1.physical_disability.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "insured_2.physical_disability.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "patient.physical_disability.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "insured_1.nervous_disorder_risk.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "insured_2.nervous_disorder_risk.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "patient.nervous_disorder_risk.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "insured_1.gastrointestinal_disorder.risk.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "insured_2.gastrointestinal_disorder.risk.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "patient.gastrointestinal_disorder.risk.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "insured_1.endocrine_disorder_risk.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "insured_2.endocrine_disorder_risk.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "patient.endocrine_disorder_risk.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "insured_1.congenital_disorder_risk.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "insured_2.congenital_disorder_risk.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "patient.congenital_disorder_risk.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "insured_1.other_symptoms.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "insured_2.other_symptoms.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "patient.other_symptoms.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "insured_1.current.treatment.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "insured_2.current.treatment.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "patient.current.treatment.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "insured_1.history.treatment.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "insured_2.history.treatment.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "patient.history.treatment.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "insured_1.consultation.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "insured_2.consultation.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "patient.consultation.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "insured_1.plan.consultation.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "insured_2.plan.consultation.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "patient.plan.consultation.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "insured_1.hepatobiliary_risk.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "insured_2.hepatobiliary_risk.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "patient.hepatobiliary_risk.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "insured_1.gastrointestinal_disorder_risk.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "insured_2.gastrointestinal_disorder_risk.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "patient.gastrointestinal_disorder_risk.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "patient.respiratory_disorder_risk.details": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "insured_1.respiratory_disorder_risk.details": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "insured_2.respiratory_disorder_risk.details": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "patient.neoplasm_risk.details": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "insured_1.neoplasm_risk.details": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "insured_2.neoplasm_risk.details": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "patient.mental_disorder_risk.details": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "insured_1.mental_disorder_risk.details": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "insured_2.mental_disorder_risk.details": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "patient.blood_pressure_disorder.risk.details": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "insured_1.blood_pressure_disorder.risk.details": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "insured_2.blood_pressure_disorder.risk.details": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "patient.muscle_and_bone_disorder_risk.details": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "insured_1.muscle_and_bone_disorder_risk.details": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "insured_2.muscle_and_bone_disorder_risk.details": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
         "patient.digestive_disorder_risk.details": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "insured_1.digestive_disorder_risk.details": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "insured_2.digestive_disorder_risk.details": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "patient.muscle_and_bone_disorder_risk.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "insured_1.muscle_and_bone_disorder_risk.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "insured_2.muscle_and_bone_disorder_risk.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "patient.blood_pressure_disorder.risk.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "insured_1.blood_pressure_disorder.risk.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        "insured_2.blood_pressure_disorder.risk.yes": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        const.GENERATED_ENTITY + "mental": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        const.GENERATED_ENTITY + "nervous": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "risk_factor",
        const.GENERATED_ENTITY + "category": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "category",
        const.GENERATED_ENTITY + "medical_code": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "medical_code",
        const.GENERATED_ENTITY + "SNOMED": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "medical_code",
        const.GENERATED_ENTITY + "snomed": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "medical_code",
        const.GENERATED_ENTITY + "icd10": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "medical_code",
        "icd10": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "medical_code",
        const.GENERATED_ENTITY + "loinc": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "medical_code",
        "loinc": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "medical_code",
        const.GENERATED_ENTITY + "medical_code_type": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "medical_code_type",
        "o.value": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "findings",
        "lab.value": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "findings",
        "diagnosis.attribute": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "findings",
        "o.issued": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "date",
        "lab.collected": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "date",
        "lab.received": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "date",
        "lab.issued": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "date",
        "encounter.date": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "date",
        const.GENERATED_ENTITY + "o.issued": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "date",
        const.GENERATED_ENTITY + "lab.collected": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "date",
        const.GENERATED_ENTITY + "lab.received": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "date",
        const.GENERATED_ENTITY + "lab.issued": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "date",
        const.GENERATED_ENTITY + "pathology.collected": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "date",
        const.GENERATED_ENTITY + "pathology.received": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "date",
        const.GENERATED_ENTITY + "pathology.issued": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "date",
        const.GENERATED_ENTITY + "encounter.date": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "date",
        "diagnosis.date": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "date",
        "diag.date": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "date",
        "diagnosis.history.date": get_schema_id(Title.MENTAL_NERVOUS_DISORDER) + "." + "date"
    },
    "build": {
        "cards_generated_vital.type": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "o.name": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "lab.name": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "diagnosis.principal": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "diagnosis.primary": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "diagnosis.secondary": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "diagnosis.history": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "assessment.diag": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "death.cause": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "medical.condition": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "history.diag": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "pathology.diagnosis": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "patient.covid.positive": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "insured_1.covid.positive": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "insured_2.covid.positive": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "patient.medical_disorder.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "insured_1.medical_disorder.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "insured_2.medical_disorder.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "patient.heart.disorder.risk.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "insured_1.heart.disorder.risk.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "insured_2.heart.disorder.risk.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "patient.neoplasm.risk.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "insured_1.neoplasm.risk.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "insured_2.neoplasm.risk.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "patient.mental.disorder.risk.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "insured_1.mental.disorder.risk.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "insured_2.mental.disorder.risk.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "patient.brain_hemorrhage.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "insured_1.brain_hemorrhage.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "insured_2.brain_hemorrhage.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "patient.neurological_disorder.risk.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "insured_1.neurological_disorder.risk.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "insured_2.neurological_disorder.risk.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "patient.immune.disorder.risk.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "insured_1.immune.disorder.risk.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "insured_2.immune.disorder.risk.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "patient.std.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "insured_1.std.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "insured_2.std.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "patient.muscleandbone.disorder.risk.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "insured_1.muscleandbone.disorder.risk.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "insured_2.muscleandbone.disorder.risk.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "patient.hepatitis.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "insured_1.hepatitis.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "insured_2.hepatitis.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "patient.endocrine.disorder.risk.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "insured_1.endocrine.disorder.risk.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "insured_2.endocrine.disorder.risk.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "patient.circulatory.disorder.risk.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "insured_1.circulatory.disorder.risk.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "insured_2.circulatory.disorder.risk.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "patient.blood.disorder.risk.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "insured_1.blood.disorder.risk.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "insured_2.blood.disorder.risk.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "patient.respiratory.disorder.risk.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "insured_1.respiratory.disorder.risk.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "insured_2.respiratory.disorder.risk.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "patient.ent_disorder.risk.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "insured_1.ent_disorder.risk.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "insured_2.ent_disorder.risk.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "patient.ent.nose_disorder.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "insured_1.ent.nose_disorder.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "insured_2.ent.nose_disorder.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "patient.ent.ear_disorder.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "insured_1.ent.ear_disorder.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "insured_2.ent.ear_disorder.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "patient.ent.throat_disorder.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "insured_1.ent.throat_disorder.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "insured_2.ent.throat_disorder.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "patient.ent.thyroid_disorder.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "insured_1.ent.thyroid_disorder.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "insured_2.ent.thyroid_disorder.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "patient.lymphatic.disorder.risk.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "insured_1.lymphatic.disorder.risk.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "insured_2.lymphatic.disorder.risk.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "patient.oedema_disease.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "insured_1.oedema_disease.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "insured_2.oedema_disease.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "patient.digestive.disorder.risk.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "insured_1.digestive.disorder.risk.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "insured_2.digestive.disorder.risk.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "patient.liver_disorder.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "insured_1.liver_disorder.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "insured_2.liver_disorder.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "patient.kidney.disorder.risk.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "insured_1.kidney.disorder.risk.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "insured_2.kidney.disorder.risk.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "patient.breast_disorder.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "insured_1.breast_disorder.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "insured_2.breast_disorder.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "patient.cyst.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "insured_1.cyst.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "insured_2.cyst.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "patient.skin_diseases.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "insured_1.skin_diseases.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "insured_2.skin_diseases.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "patient.hormonal_disorder.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "insured_1.hormonal_disorder.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "insured_2.hormonal_disorder.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "patient.female.reproductive.risk.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "insured_1.female.reproductive.risk.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "insured_2.female.reproductive.risk.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "patient.fever.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "insured_1.fever.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "insured_2.fever.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "patient.tuberculosis.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "insured_1.tuberculosis.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "insured_2.tuberculosis.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "patient.physical.disorder.risk.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "insured_1.physical.disorder.risk.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "insured_2.physical.disorder.risk.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "patient.allergies.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "insured_1.allergies.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "insured_2.allergies.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "patient.endocrine_disorder.risk.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "insured_1.endocrine_disorder.risk.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "insured_2.endocrine_disorder.risk.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "patient.congenital.disorder.risk.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "insured_1.congenital.disorder.risk.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "insured_2.congenital.disorder.risk.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "patient.nervous.disorder.risk.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "insured_1.nervous.disorder.risk.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "insured_2.nervous.disorder.risk.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "patient.gastrointestinal.disorder.risk.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "insured_1.gastrointestinal.disorder.risk.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "insured_2.gastrointestinal.disorder.risk.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "patient.chronic_disease.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "insured_1.chronic_disease.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "insured_2.chronic_disease.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "patient.urinary_disorder.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "insured_1.urinary_disorder.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "insured_2.urinary_disorder.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "patient.heart_disease.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "insured_1.heart_disease.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "insured_2.heart_disease.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "patient.cancer_disease.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "insured_1.cancer_disease.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "insured_2.cancer_disease.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "patient.mental_disorder.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "insured_1.mental_disorder.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "insured_2.mental_disorder.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "patient.hiv.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "insured_1.hiv.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "insured_2.hiv.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "patient.muscular_pain.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "insured_1.muscular_pain.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "insured_2.muscular_pain.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "patient.diabetes.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "insured_1.diabetes.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "insured_2.diabetes.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "patient.high_blood_pressure.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "insured_1.high_blood_pressure.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "insured_2.high_blood_pressure.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "patient.blood_disorder.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "insured_1.blood_disorder.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "insured_2.blood_disorder.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "patient.breathing_disorder.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "insured_1.breathing_disorder.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "insured_2.breathing_disorder.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "patient.ent.eyes_disorder.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "insured_1.ent.eyes_disorder.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "insured_2.ent.eyes_disorder.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "patient.lymphatic_system_disease.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "insured_1.lymphatic_system_disease.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "insured_2.lymphatic_system_disease.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "patient.stomach_disorder.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "insured_1.stomach_disorder.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "insured_2.stomach_disorder.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "patient.kidney_disorder.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "insured_1.kidney_disorder.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "insured_2.kidney_disorder.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "patient.reproductive_organs_diseases.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "insured_1.reproductive_organs_diseases.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "insured_2.reproductive_organs_diseases.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "patient.physical_disorder.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "insured_1.physical_disorder.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "insured_2.physical_disorder.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "patient.endocrine_glands_disorder.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "insured_1.endocrine_glands_disorder.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "insured_2.endocrine_glands_disorder.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "patient.nervous_system_disorder.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "insured_1.nervous_system_disorder.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "insured_2.nervous_system_disorder.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "patient.gastrointestinal_disorder.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "insured_1.gastrointestinal_disorder.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "insured_2.gastrointestinal_disorder.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "insured_1.heart_disorder.risk.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "insured_2.heart_disorder.risk.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "patient.heart_disorder.risk.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "insured_1.neoplasm_risk.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "insured_2.neoplasm_risk.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "patient.neoplasm_risk.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "insured_1.mental_disorder_risk.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "insured_2.mental_disorder_risk.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "patient.mental_disorder_risk.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "insured_1.immune_disorder_risk.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "insured_2.immune_disorder_risk.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "patient.immune_disorder_risk.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "insured_1.muscle_and_bone.disorder.risk.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "insured_2.muscle_and_bone.disorder.risk.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "patient.muscle_and_bone.disorder.risk.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "insured_1.circulatory_disorder_risk.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "insured_2.circulatory_disorder_risk.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "patient.circulatory_disorder_risk.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "insured_1.blood_disorder.risk.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "insured_2.blood_disorder.risk.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "patient.blood_disorder.risk.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "insured_1.respiratory_disorder_risk.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "insured_2.respiratory_disorder_risk.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "patient.respiratory_disorder_risk.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "insured_1.ent_disorder_risk.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "insured_2.ent_disorder_risk.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "patient.ent_disorder_risk.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "insured_1.lymphatic_disorder_risk.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "insured_2.lymphatic_disorder_risk.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "patient.lymphatic_disorder_risk.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "insured_1.digestive_disorder_risk.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "insured_2.digestive_disorder_risk.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "patient.digestive_disorder_risk.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "insured_1.kidney_disorder.risk.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "insured_2.kidney_disorder.risk.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "patient.kidney_disorder.risk.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "insured_1.medical_test.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "insured_2.medical_test.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "patient.medical_test.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "insured_1.medication.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "insured_2.medication.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "patient.medication.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "insured_1.hospitalization.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "insured_2.hospitalization.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "patient.hospitalization.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "insured_1.recent.hospitalization.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "insured_2.recent.hospitalization.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "patient.recent.hospitalization.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "insured_1.pregnant.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "insured_2.pregnant.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "patient.pregnant.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "insured_1.female_reproductive_risk.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "insured_2.female_reproductive_risk.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "patient.female_reproductive_risk.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "insured_1.physical_disability.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "insured_2.physical_disability.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "patient.physical_disability.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "insured_1.nervous_disorder_risk.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "insured_2.nervous_disorder_risk.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "patient.nervous_disorder_risk.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "insured_1.gastrointestinal_disorder.risk.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "insured_2.gastrointestinal_disorder.risk.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "patient.gastrointestinal_disorder.risk.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "insured_1.endocrine_disorder_risk.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "insured_2.endocrine_disorder_risk.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "patient.endocrine_disorder_risk.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "insured_1.congenital_disorder_risk.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "insured_2.congenital_disorder_risk.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "patient.congenital_disorder_risk.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "insured_1.other_symptoms.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "insured_2.other_symptoms.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "patient.other_symptoms.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "insured_1.current.treatment.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "insured_2.current.treatment.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "patient.current.treatment.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "insured_1.history.treatment.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "insured_2.history.treatment.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "patient.history.treatment.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "insured_1.consultation.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "insured_2.consultation.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "patient.consultation.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "insured_1.plan.consultation.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "insured_2.plan.consultation.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "patient.plan.consultation.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "insured_1.hepatobiliary_risk.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "insured_2.hepatobiliary_risk.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "patient.hepatobiliary_risk.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "insured_1.gastrointestinal_disorder_risk.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "insured_2.gastrointestinal_disorder_risk.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "patient.gastrointestinal_disorder_risk.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "patient.respiratory_disorder_risk.details": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "insured_1.respiratory_disorder_risk.details": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "insured_2.respiratory_disorder_risk.details": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "patient.neoplasm_risk.details": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "insured_1.neoplasm_risk.details": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "insured_2.neoplasm_risk.details": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "patient.mental_disorder_risk.details": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "insured_1.mental_disorder_risk.details": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "insured_2.mental_disorder_risk.details": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "patient.blood_pressure_disorder.risk.details": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "insured_1.blood_pressure_disorder.risk.details": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "insured_2.blood_pressure_disorder.risk.details": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "patient.muscle_and_bone_disorder_risk.details": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "insured_1.muscle_and_bone_disorder_risk.details": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "insured_2.muscle_and_bone_disorder_risk.details": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "patient.digestive_disorder_risk.details": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "insured_1.digestive_disorder_risk.details": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "insured_2.digestive_disorder_risk.details": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "patient.muscle_and_bone_disorder_risk.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "insured_1.muscle_and_bone_disorder_risk.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "insured_2.muscle_and_bone_disorder_risk.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "patient.blood_pressure_disorder.risk.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "insured_1.blood_pressure_disorder.risk.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        "insured_2.blood_pressure_disorder.risk.yes": get_schema_id(Title.BUILD) + "." + "risk_factor",
        const.GENERATED_ENTITY + "build_height_header": get_schema_id(Title.BUILD) + "." + "risk_factor",
        const.GENERATED_ENTITY + "build_weight_header": get_schema_id(Title.BUILD) + "." + "risk_factor",
        const.GENERATED_ENTITY + "build_bmi_header": get_schema_id(Title.BUILD) + "." + "risk_factor",
        const.GENERATED_ENTITY + "build_bmi_header": get_schema_id(Title.BUILD) + "." + "risk_factor",
        const.GENERATED_ENTITY + "category": get_schema_id(Title.BUILD) + "." + "category",
        const.GENERATED_ENTITY + "medical_code": get_schema_id(Title.BUILD) + "." + "medical_code",
        const.GENERATED_ENTITY + "vital.loinc": get_schema_id(Title.BUILD) + "." + "medical_code",
        const.GENERATED_ENTITY + "SNOMED": get_schema_id(Title.BUILD) + "." + "medical_code",
        const.GENERATED_ENTITY + "snomed": get_schema_id(Title.BUILD) + "." + "medical_code",
        const.GENERATED_ENTITY + "icd10": get_schema_id(Title.BUILD) + "." + "medical_code",
        "icd10": get_schema_id(Title.BUILD) + "." + "medical_code",
        const.GENERATED_ENTITY + "loinc": get_schema_id(Title.BUILD) + "." + "medical_code",
        "loinc": get_schema_id(Title.BUILD) + "." + "medical_code",
        const.GENERATED_ENTITY + "medical_code_type": get_schema_id(Title.BUILD) + "." + "medical_code_type",
        const.GENERATED_ENTITY + "build_height": get_schema_id(Title.BUILD) + "." + "findings",
        const.GENERATED_ENTITY + "build_weight": get_schema_id(Title.BUILD) + "." + "findings",
        const.GENERATED_ENTITY + "build_bmi": get_schema_id(Title.BUILD) + "." + "findings",
        "vital.weight_lbs": get_schema_id(Title.BUILD) + "." + "findings",
        "patient.weight_lbs": get_schema_id(Title.BUILD) + "." + "findings",
        "insured_1.weight_lbs": get_schema_id(Title.BUILD) + "." + "findings",
        "insured_2.weight_lbs": get_schema_id(Title.BUILD) + "." + "findings",
        "vital.weight_kg": get_schema_id(Title.BUILD) + "." + "findings",
        "patient.weight_kg": get_schema_id(Title.BUILD) + "." + "findings",
        "insured_1.weight_kg": get_schema_id(Title.BUILD) + "." + "findings",
        "insured_2.weight_kg": get_schema_id(Title.BUILD) + "." + "findings",
        "vital.height_in": get_schema_id(Title.BUILD) + "." + "findings",
        "patient.height_in": get_schema_id(Title.BUILD) + "." + "findings",
        "insured_1.height_in": get_schema_id(Title.BUILD) + "." + "findings",
        "insured_2.height_in": get_schema_id(Title.BUILD) + "." + "findings",
        "vital.height_cm": get_schema_id(Title.BUILD) + "." + "findings",
        "patient.height_cm": get_schema_id(Title.BUILD) + "." + "findings",
        "insured_1.height_cm": get_schema_id(Title.BUILD) + "." + "findings",
        "insured_2.height_cm": get_schema_id(Title.BUILD) + "." + "findings",
        "vital.bmi": get_schema_id(Title.BUILD) + "." + "findings",
        "patient.bmi": get_schema_id(Title.BUILD) + "." + "findings",
        "insured_1.bmi": get_schema_id(Title.BUILD) + "." + "findings",
        "insured_2.bmi": get_schema_id(Title.BUILD) + "." + "findings",
        const.GENERATED_ENTITY + "vital.weight_lbs": get_schema_id(Title.BUILD) + "." + "findings",
		const.GENERATED_ENTITY + "patient.weight_lbs": get_schema_id(Title.BUILD) + "." + "findings",
		const.GENERATED_ENTITY + "insured_1.weight_lbs": get_schema_id(Title.BUILD) + "." + "findings",
		const.GENERATED_ENTITY + "insured_2.weight_lbs": get_schema_id(Title.BUILD) + "." + "findings",
		const.GENERATED_ENTITY + "vital.weight_kg": get_schema_id(Title.BUILD) + "." + "findings",
		const.GENERATED_ENTITY + "patient.weight_kg": get_schema_id(Title.BUILD) + "." + "findings",
		const.GENERATED_ENTITY + "insured_1.weight_kg": get_schema_id(Title.BUILD) + "." + "findings",
		const.GENERATED_ENTITY + "insured_2.weight_kg": get_schema_id(Title.BUILD) + "." + "findings",
		const.GENERATED_ENTITY + "vital.height_in": get_schema_id(Title.BUILD) + "." + "findings",
		const.GENERATED_ENTITY + "patient.height_in": get_schema_id(Title.BUILD) + "." + "findings",
		const.GENERATED_ENTITY + "insured_1.height_in": get_schema_id(Title.BUILD) + "." + "findings",
		const.GENERATED_ENTITY + "insured_2.height_in": get_schema_id(Title.BUILD) + "." + "findings",
		const.GENERATED_ENTITY + "vital.height_cm": get_schema_id(Title.BUILD) + "." + "findings",
		const.GENERATED_ENTITY + "patient.height_cm": get_schema_id(Title.BUILD) + "." + "findings",
		const.GENERATED_ENTITY + "insured_1.height_cm": get_schema_id(Title.BUILD) + "." + "findings",
		const.GENERATED_ENTITY + "insured_2.height_cm": get_schema_id(Title.BUILD) + "." + "findings",
		const.GENERATED_ENTITY + "vital.bmi": get_schema_id(Title.BUILD) + "." + "findings",
		const.GENERATED_ENTITY + "patient.bmi": get_schema_id(Title.BUILD) + "." + "findings",
		const.GENERATED_ENTITY + "insured_1.bmi": get_schema_id(Title.BUILD) + "." + "findings",
		const.GENERATED_ENTITY + "insured_2.bmi": get_schema_id(Title.BUILD) + "." + "findings",
        "o.value": get_schema_id(Title.BUILD) + "." + "findings",
        "lab.value": get_schema_id(Title.BUILD) + "." + "findings",
        "diagnosis.attribute": get_schema_id(Title.BUILD) + "." + "findings",
        "o.issued": get_schema_id(Title.BUILD) + "." + "date",
        "lab.collected": get_schema_id(Title.BUILD) + "." + "date",
        "lab.received": get_schema_id(Title.BUILD) + "." + "date",
        "lab.issued": get_schema_id(Title.BUILD) + "." + "date",
        "encounter.date": get_schema_id(Title.BUILD) + "." + "date",
        const.GENERATED_ENTITY + "o.issued": get_schema_id(Title.BUILD) + "." + "date",
        const.GENERATED_ENTITY + "lab.collected": get_schema_id(Title.BUILD) + "." + "date",
        const.GENERATED_ENTITY + "lab.received": get_schema_id(Title.BUILD) + "." + "date",
        const.GENERATED_ENTITY + "lab.issued": get_schema_id(Title.BUILD) + "." + "date",
        const.GENERATED_ENTITY + "pathology.collected": get_schema_id(Title.BUILD) + "." + "date",
        const.GENERATED_ENTITY + "pathology.received": get_schema_id(Title.BUILD) + "." + "date",
        const.GENERATED_ENTITY + "pathology.issued": get_schema_id(Title.BUILD) + "." + "date",
        const.GENERATED_ENTITY + "encounter.date": get_schema_id(Title.BUILD) + "." + "date",
        "diagnosis.date": get_schema_id(Title.BUILD) + "." + "date",
        "diag.date": get_schema_id(Title.BUILD) + "." + "date",
        "diagnosis.history.date": get_schema_id(Title.BUILD) + "." + "date"
    },
    "informals": {
        "criteria": get_schema_id(Title.INFORMALS) + "." + "criteria",
        "classification": get_schema_id(Title.INFORMALS) + "." + "classification",
        "patient.age": get_schema_id(Title.INFORMALS) + "." + "value",
        "insured_1.age": get_schema_id(Title.INFORMALS) + "." + "value",
        "insured_2.age": get_schema_id(Title.INFORMALS) + "." + "value",
        "applicant.age": get_schema_id(Title.INFORMALS) + "." + "value",
        "social.smoking": get_schema_id(Title.INFORMALS) + "." + "value",
        "insured_1.smoking": get_schema_id(Title.INFORMALS) + "." + "value",
        "insured_2.smoking": get_schema_id(Title.INFORMALS) + "." + "value",
        "patient.smoking": get_schema_id(Title.INFORMALS) + "." + "value",
        "patient.smoking.status.yes": get_schema_id(Title.INFORMALS) + "." + "value",
        "patient.smoking.status.no": get_schema_id(Title.INFORMALS) + "." + "value",
        "patient.smoking.status.former": get_schema_id(Title.INFORMALS) + "." + "value",
        "patient.smoking.status.passive": get_schema_id(Title.INFORMALS) + "." + "value",
        "insured_1.smoking.status.yes": get_schema_id(Title.INFORMALS) + "." + "value",
        "insured_1.smoking.status.no": get_schema_id(Title.INFORMALS) + "." + "value",
        "insured_1.smoking.status.former": get_schema_id(Title.INFORMALS) + "." + "value",
        "insured_1.smoking.status.passive": get_schema_id(Title.INFORMALS) + "." + "value",
        "insured_2.smoking.status.yes": get_schema_id(Title.INFORMALS) + "." + "value",
        "insured_2.smoking.status.no": get_schema_id(Title.INFORMALS) + "." + "value",
        "insured_2.smoking.status.former": get_schema_id(Title.INFORMALS) + "." + "value",
        "insured_2.smoking.status.passive": get_schema_id(Title.INFORMALS) + "." + "value",
        "applicant.smoking.status.yes": get_schema_id(Title.INFORMALS) + "." + "value",
        "applicant.smoking.status.no": get_schema_id(Title.INFORMALS) + "." + "value",
        "applicant.smoking.status.former": get_schema_id(Title.INFORMALS) + "." + "value",
        "applicant.smoking.status.passive": get_schema_id(Title.INFORMALS) + "." + "value",
        "applicant.smoking.quantity": get_schema_id(Title.INFORMALS) + "." + "value",
        "applicant.smoking.device": get_schema_id(Title.INFORMALS) + "." + "value",
        "applicant.smoking.duration": get_schema_id(Title.INFORMALS) + "." + "value",
        "patient.smoking.quantity": get_schema_id(Title.INFORMALS) + "." + "value",
        "patient.smoking.device": get_schema_id(Title.INFORMALS) + "." + "value",
        "patient.smoking.duration": get_schema_id(Title.INFORMALS) + "." + "value",
        "insured_1.smoking.quantity": get_schema_id(Title.INFORMALS) + "." + "value",
        "insured_1.smoking.device": get_schema_id(Title.INFORMALS) + "." + "value",
        "insured_1.smoking.duration": get_schema_id(Title.INFORMALS) + "." + "value",
        "insured_2.smoking.quantity": get_schema_id(Title.INFORMALS) + "." + "value",
        "insured_2.smoking.device": get_schema_id(Title.INFORMALS) + "." + "value",
        "insured_2.smoking.duration": get_schema_id(Title.INFORMALS) + "." + "value",
        "patient.tobacco.status.yes": get_schema_id(Title.INFORMALS) + "." + "value",
        "patient.tobacco.status.no": get_schema_id(Title.INFORMALS) + "." + "value",
        "patient.tobacco.status.former": get_schema_id(Title.INFORMALS) + "." + "value",
        "insured_1.tobacco.status.yes": get_schema_id(Title.INFORMALS) + "." + "value",
        "insured_1.tobacco.status.no": get_schema_id(Title.INFORMALS) + "." + "value",
        "insured_1.tobacco.status.former": get_schema_id(Title.INFORMALS) + "." + "value",
        "insured_2.tobacco.status.yes": get_schema_id(Title.INFORMALS) + "." + "value",
        "insured_2.tobacco.status.no": get_schema_id(Title.INFORMALS) + "." + "value",
        "insured_2.tobacco.status.former": get_schema_id(Title.INFORMALS) + "." + "value",
        "applicant.tobacco.status.yes": get_schema_id(Title.INFORMALS) + "." + "value",
        "applicant.tobacco.status.no": get_schema_id(Title.INFORMALS) + "." + "value",
        "applicant.tobacco.status.former": get_schema_id(Title.INFORMALS) + "." + "value",
        "patient.tobacco.quantity": get_schema_id(Title.INFORMALS) + "." + "value",
        "patient.tobacco.duration": get_schema_id(Title.INFORMALS) + "." + "value",
        "patient.tobacco.product": get_schema_id(Title.INFORMALS) + "." + "value",
        "insured_1.tobacco.quantity": get_schema_id(Title.INFORMALS) + "." + "value",
        "insured_1.tobacco.duration": get_schema_id(Title.INFORMALS) + "." + "value",
        "insured_1.tobacco.product": get_schema_id(Title.INFORMALS) + "." + "value",
        "insured_2.tobacco.quantity": get_schema_id(Title.INFORMALS) + "." + "value",
        "insured_2.tobacco.duration": get_schema_id(Title.INFORMALS) + "." + "value",
        "insured_2.tobacco.product": get_schema_id(Title.INFORMALS) + "." + "value",
        "applicant.tobacco.quantity": get_schema_id(Title.INFORMALS) + "." + "value",
        "applicant.tobacco.duration": get_schema_id(Title.INFORMALS) + "." + "value",
        "applicant.tobacco.product": get_schema_id(Title.INFORMALS) + "." + "value",
        "vital.bmi": get_schema_id(Title.INFORMALS) + "." + "value",
        "patient.bmi": get_schema_id(Title.INFORMALS) + "." + "value",
        "insured_1.bmi": get_schema_id(Title.INFORMALS) + "." + "value",
        "insured_2.bmi": get_schema_id(Title.INFORMALS) + "." + "value",
        "lab.value": get_schema_id(Title.INFORMALS) + "." + "value",
        "o.value": get_schema_id(Title.INFORMALS) + "." + "value",
        "vital.blood": get_schema_id(Title.INFORMALS) + "." + "value",
        "patient.blood": get_schema_id(Title.INFORMALS) + "." + "value",
        "insured_1.blood": get_schema_id(Title.INFORMALS) + "." + "value",
        "insured_2.blood": get_schema_id(Title.INFORMALS) + "." + "value",
        "patient.blood_pressure": get_schema_id(Title.INFORMALS) + "." + "value",
        "insured_1.blood_pressure": get_schema_id(Title.INFORMALS) + "." + "value",
        "insured_2.blood_pressure": get_schema_id(Title.INFORMALS) + "." + "value",
    }
}

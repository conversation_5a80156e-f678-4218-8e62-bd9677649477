import constants as const
import titles as Title
from field_id_entity_type_mapping import FIELD_ID_BLUEPRINTS
from friendlylib.unit_normalization.codify import validate_height, validate_weight, validate_temperature, \
    validate_duration
from friendlylib.unit_normalization.codify_dates import validate


class ENTITY_ROLE:
    MOTHER = 1
    CHILD = 2
    REQUIRED_CHILD = 3
    SHARED = 4


# --------------------------------------------------------------------------------------------------------------------------

BLUEPRINTS = {

    Title.LABORATORY_RESULTS: {
        "title": Title.LABORATORY_RESULTS,
        "row": [
            {"key": "name", "entity_type": "o.name", "entity_object": None, "display_key": "Name",
             "is_container": False, "role": ENTITY_ROLE.MOTHER, 'ner.keys': ['o.name', 'lab.name']},
            {"key": "value", "entity_type": "o.value", "entity_object": None, "display_key": "Value",
             "is_container": False, "role": ENTITY_ROLE.CHILD, 'ner.keys': ['o.value', 'lab.value']},
            {"key": "rating", "entity_type": "o.rating", "entity_object": None, "display_key": "Rating",
             "is_container": False, "role": ENTITY_ROLE.CHILD, 'ner.keys': ['o.rating', 'lab.rating']},
            {"key": "ref_low", "entity_type": "o.ref_low", "entity_object": None, "display_key": "Low",
             "is_container": False, "role": ENTITY_ROLE.CHILD, 'ner.keys': ['o.ref_low', 'lab.ref_low']},
            {"key": "ref_high", "entity_type": "o.ref_high", "entity_object": None, "display_key": "High",
             "is_container": False, "role": ENTITY_ROLE.CHILD, 'ner.keys': ['o.ref_high', 'lab.ref_high']},
            {"key": "ref", "entity_type": "o.ref", "entity_object": None, "display_key": "Descriptive Reference",
             "is_container": False, "role": ENTITY_ROLE.CHILD, 'ner.keys': ['o.ref', 'lab.ref']},
            {"key": "unit", "entity_type": "o.unit", "entity_object": None, "display_key": "Unit",
             "is_container": False, "role": ENTITY_ROLE.CHILD, 'ner.keys': ['o.unit', 'lab.unit']},
            {"key": "panel", "entity_type": "lab.panel", "entity_object": None, "display_key": "Panel",
             "is_container": False, "role": ENTITY_ROLE.CHILD, 'ner.keys': ['lab.panel']},
            {"key": "specimen", "entity_type": "lab.specimen", "entity_object": None, "display_key": "Specimen",
             "is_container": False, "role": ENTITY_ROLE.CHILD, 'ner.keys': ['lab.specimen']},
            {"key": "date", "entity_type": "o.issued", "entity_object": None, "display_key": "Date",
             "is_container": False, "role": ENTITY_ROLE.CHILD,
             'ner.keys': ['o.collected', 'lab.collected', 'o.received', 'lab.received', 'o.issued', 'lab.issued',
                          'pathology.collected', 'pathology.received', 'pathology.issued',
                          const.GENERATED_ENTITY + 'o.collected', const.GENERATED_ENTITY + 'lab.collected', const.GENERATED_ENTITY + 'o.received', 
                          const.GENERATED_ENTITY + 'lab.received', const.GENERATED_ENTITY + 'o.issued', const.GENERATED_ENTITY + 'lab.issued',
                          const.GENERATED_ENTITY + 'pathology.collected', const.GENERATED_ENTITY + 
                          'pathology.received', const.GENERATED_ENTITY + 'pathology.issued',
                          'encounter.date', const.GENERATED_ENTITY + 'encounter.date']},
            {"key": "performer", "entity_type": "lab.performer", "entity_object": None, "display_key": "Laboratory",
             "is_container": False, "role": ENTITY_ROLE.CHILD, 'ner.keys': ['lab.performer']},
            {"key": "loinc", "entity_type": "loinc", "entity_object": None, "display_key": "LOINC",
             "is_container": False, "role": ENTITY_ROLE.CHILD, 'ner.keys': ['loinc']},
            # {"key": "panel_loinc", "entity_type": "panel_loinc", "entity_object": None, "display_key": "Panel LOINC",
            #  "is_container": False, "role": ENTITY_ROLE.CHILD, 'ner.keys': []},
            {"key": "fasting", "entity_type": "fasting.status.yes", "entity_object": None, "display_key": "fasting",
             "is_container": False, "role": ENTITY_ROLE.CHILD,
             'ner.keys': ['fasting.status.yes', 'fasting.in_hours', const.GENERATED_ENTITY + 'fasting.in_hours', 
                          'fasting.in_mins', const.GENERATED_ENTITY + 'fasting.in_mins',
                          'fasting.date_time', const.GENERATED_ENTITY + 'fasting.date_time']},
            {"key": "impairment", "entity_type": const.GENERATED_ENTITY + "impairment", "entity_object": None, "display_key": "Impairment",
             "is_container": False, "role": ENTITY_ROLE.CHILD,
             'ner.keys': [const.GENERATED_ENTITY + "impairment"]}
        ],
        "date-order": ['lab.collected', const.GENERATED_ENTITY + 'lab.collected',
                        'lab.received', const.GENERATED_ENTITY + 'lab.received', 
                        'lab.issued', const.GENERATED_ENTITY + 'lab.issued',
                        'encounter.date', const.GENERATED_ENTITY + 'encounter.date']
    },

    Title.ABNORMAL_OBSERVATIONS: {
        "title": Title.ABNORMAL_OBSERVATIONS,
        "row": [
            {"key": "name", "entity_type": "o.name", "entity_object": None, "display_key": "Name",
             "is_container": False, "role": ENTITY_ROLE.MOTHER, 'ner.keys': ['o.name', 'lab.name']},
            {"key": "value", "entity_type": "o.value", "entity_object": None, "display_key": "Value",
             "is_container": False, "role": ENTITY_ROLE.CHILD, 'ner.keys': ['o.value', 'lab.value']},
            {"key": "rating", "entity_type": "o.rating", "entity_object": None, "display_key": "Rating",
             "is_container": False, "role": ENTITY_ROLE.CHILD, 'ner.keys': ['o.rating', 'lab.rating']},
            {"key": "ref_low", "entity_type": "o.ref_low", "entity_object": None, "display_key": "Low",
             "is_container": False, "role": ENTITY_ROLE.CHILD, 'ner.keys': ['o.ref_low', 'lab.ref_low']},
            {"key": "ref_high", "entity_type": "o.ref_high", "entity_object": None, "display_key": "High",
             "is_container": False, "role": ENTITY_ROLE.CHILD, 'ner.keys': ['o.ref_high', 'lab.ref_high']},
            {"key": "ref", "entity_type": "o.ref", "entity_object": None, "display_key": "Descriptive Reference",
             "is_container": False, "role": ENTITY_ROLE.CHILD, 'ner.keys': ['o.ref', 'lab.ref']},
            {"key": "unit", "entity_type": "o.unit", "entity_object": None, "display_key": "Unit",
             "is_container": False, "role": ENTITY_ROLE.CHILD, 'ner.keys': ['o.unit', 'lab.unit']},
            {"key": "panel", "entity_type": "lab.panel", "entity_object": None, "display_key": "Panel",
             "is_container": False, "role": ENTITY_ROLE.CHILD, 'ner.keys': ['lab.panel']},
            {"key": "specimen", "entity_type": "lab.specimen", "entity_object": None, "display_key": "specimen",
             "is_container": False, "role": ENTITY_ROLE.CHILD, 'ner.keys': ['lab.specimen']},
            {"key": "date", "entity_type": "o.issued", "entity_object": None, "display_key": "Date",
             "is_container": False, "role": ENTITY_ROLE.CHILD,
             'ner.keys': ['o.collected', 'lab.collected', 'o.received', 'lab.received', 'o.issued', 'lab.issued']},
            {"key": "performer", "entity_type": "lab.performer", "entity_object": None, "display_key": "Laboratory",
             "is_container": False, "role": ENTITY_ROLE.CHILD, 'ner.keys': ['lab.performer']},
            {"key": "loinc", "entity_type": "loinc", "entity_object": None, "display_key": "LOINC",
             "is_container": False, "role": ENTITY_ROLE.CHILD, 'ner.keys': ['loinc']},
            {"key": "panel_loinc", "entity_type": "panel_loinc", "entity_object": None, "display_key": "Panel LOINC",
             "is_container": False, "role": ENTITY_ROLE.CHILD, 'ner.keys': []},
            {"key": "fasting", "entity_type": "fasting.status.yes", "entity_object": None, "display_key": "fasting",
             "is_container": False, "role": ENTITY_ROLE.CHILD,
             'ner.keys': ['fasting.status.yes', 'fasting.in_hours', 'fasting.in_mins', 'fasting.date_time']}
        ]},

    Title.PHYSICAL_EXAMS: {
        const.GROUPS: [
            {'Site': ['pe.name', 'pe.body', 'ros.system'], 'Observation': ['pe.value.positive', 'pe.value.abnormal',
                                                                           'pe.patient.value.positive',
                                                                           'pe.patient.value.abnormal',
                                                                           'ros.value.positive', 'ros.value.abnormal'],
             'Visit Date': ['encounter.date']}
        ],
        const.HEADERS: {
            'Site': ['pe.name', 'pe.body', 'ros.system'],
            'Observation': ['pe.value.positive', 'pe.value.abnormal',
                            'ros.value.positive', 'ros.value.abnormal'],
            'Visit Date': ['encounter.date']
        },
        const.POSITIONS: {
            'Observation': {
                "level": [const.SAME_LINE_RIGHT_SIDE, const.DEFAULT]
            },
            'Site': {
                "level": [const.SAME_LINE_RIGHT_SIDE, const.ABOVE_LINE]
            },
            'Visit Date': {
                "level": [const.SAME_LINE, const.ABOVE_LINE, const.DEFAULT]
            }
        }
    },

    Title.VITAL_SIGNS: {
        "row": [
            {"entity_type": "encounter.date", "entity_object": None, "display_key": "Date", "is_container": False,
             "role": ENTITY_ROLE.MOTHER,
             'ner.keys': ['patient.date', 'patient.vital.date', 'vital.date', 'encounter.date',
                          'lab.issued', 'lab.collected', 'lab.received']},
            {"entity_type": "vital.blood", "entity_object": None, "display_key": "Blood Pressure",
             "is_container": False, "role": ENTITY_ROLE.REQUIRED_CHILD, "is_constant_per_date": False,
             'ner.keys': ['vital.blood', 'patient.blood', "vital.systolic", "patient.systolic",
                          'insured_1.blood', 'insured_2.blood',
                          const.GENERATED_ENTITY + "patient.blood",
                          'patient.blood_pressure', 'insured_1.blood_pressure', 'insured_2.blood_pressure'
                          ]},
            {"entity_type": "vital.weight_lbs", "entity_object": None, "display_key": "Weight", "is_container": False,
             "role": ENTITY_ROLE.REQUIRED_CHILD, "is_constant_per_date": True,
             'ner.keys': ['vital.weight_lbs', 'vital.weight_kg', 'patient.weight_lbs', 'patient.weight_kg',
                          'insured_1.weight_lbs', 'insured_1.weight_kg']},
            {"entity_type": "vital.height_in", "entity_object": None, "display_key": "Height", "is_container": False,
             "role": ENTITY_ROLE.REQUIRED_CHILD, "is_constant_per_date": True,
             'ner.keys': ['vital.height_in', 'patient.height_in', 'vital.height_cm', 'patient.height_cm',
                          'insured_1.height_in', 'insured_1.height_cm']},
            {"entity_type": "vital.bmi", "entity_object": None, "display_key": "BMI", "is_container": False,
             "role": ENTITY_ROLE.REQUIRED_CHILD, "is_constant_per_date": True,
             'ner.keys': ['vital.bmi', 'patient.bmi']},
            {"entity_type": "vital.heart_rate", "entity_object": None, "display_key": "Pulse", "is_container": False,
             "role": ENTITY_ROLE.REQUIRED_CHILD, "is_constant_per_date": False,
             'ner.keys': ['vital.heart_rate', 'patient.heart_rate', 'insured_1.heart_rate', 'insured_2.heart_rate']},
            {"entity_type": "vital.oxygen", "entity_object": None, "display_key": "Oxygen", "is_container": False,
             "role": ENTITY_ROLE.REQUIRED_CHILD, "is_constant_per_date": False,
             'ner.keys': ['vital.oxygen', 'patient.oxygen']},
            {"entity_type": "vital.respiration", "entity_object": None, "display_key": "Respiration",
             "is_container": False, "role": ENTITY_ROLE.REQUIRED_CHILD, "is_constant_per_date": False,
             'ner.keys': ['vital.respiration', 'patient.respiration']},
            {"entity_type": "vital.temperature_f", "entity_object": None, "display_key": "Temperature",
             "is_container": False, "role": ENTITY_ROLE.REQUIRED_CHILD, "is_constant_per_date": False,
             'ner.keys': ['vital.temperature_f', 'vital.temperature_c', 'patient.temperature_f',
                          'patient.temperature_c']}
        ],
        const.POSITIONS: {
            'Date': {
                "level": [const.SAME_LINE_RIGHT_SIDE, const.ABOVE_LINE]
            }
        },
        "date_order": ['patient.date', 'vital.date', 'patient.vital.date',
                       'lab.collected', 'lab.received', 'lab.issued',
                       'encounter.date', 'imaging.date']
    },

    Title.CCDA_VITALS: {
        "row": [
            {"entity_type": "vital.type", "entity_object": None, "display_key": "Type", "role": ENTITY_ROLE.CHILD,
             "is_container": False,
             'ner.keys': ['vital.type']},
            {"entity_type": "vital.value", "entity_object": None, "display_key": "Value", "role": ENTITY_ROLE.MOTHER,
             "is_container": False,
             'ner.keys': [
                 'vital.blood', 'patient.blood', 'insured_1.blood', 'insured_2.blood',
                 'patient.blood_pressure', 'insured_1.blood_pressure', 'insured_2.blood_pressure'
                 "insured_1.systolic", "insured_2.systolic", "vital.systolic", "patient.systolic",
                 "insured_1.diastolic", "insured_2.diastolic", "vital.diastolic", "patient.diastolic",
                 const.GENERATED_ENTITY + "insured_1.systolic", const.GENERATED_ENTITY + "insured_2.systolic",
                 const.GENERATED_ENTITY + "patient.systolic",
                 const.GENERATED_ENTITY + "insured_1.diastolic", const.GENERATED_ENTITY + "insured_2.diastolic",
                 const.GENERATED_ENTITY + "patient.diastolic",
                 'vital.weight_lbs', 'vital.weight_kg', 'patient.weight_lbs', 'patient.weight_kg',
                 'insured_1.weight_lbs', 'insured_1.weight_kg',
                 'insured_2.weight_lbs', 'insured_2.weight_kg',
                 'vital.height_in', 'vital.height_cm',
                 'patient.height_in', 'patient.height_cm', 
                 'insured_1.height_in', 'insured_1.height_cm', 
                 'insured_2.height_in', 'insured_2.height_cm',
                 'vital.bmi', 'patient.bmi', 'insured_1.bmi', 'insured_2.bmi',
                 'vital.heart_rate', 'patient.heart_rate', 'insured_1.heart_rate', 'insured_2.heart_rate',
                 'vital.oxygen', 'patient.oxygen',
                 'vital.respiration', 'patient.respiration',
                 'vital.temperature_f', 'vital.temperature_c', 'patient.temperature_f', 'patient.temperature_c',
             ]},
            {"entity_type": "vital.unit", "entity_object": None, "display_key": "Unit", "role": ENTITY_ROLE.CHILD,
             "is_container": False,
             'ner.keys': [const.GENERATED_ENTITY + 'vital.unit']},
            {"entity_type": "vital.date", "entity_object": None, "display_key": "Date", "role": ENTITY_ROLE.CHILD,
             "is_container": False,
             'ner.keys': ['patient.date', 'patient.vital.date', 'vital.date', 'encounter.date',
                          'lab.issued', 'lab.collected', 'lab.received']},
            {"entity_type": "vital.loinc", "entity_object": None, "display_key": "LOINC", "role": ENTITY_ROLE.CHILD,
             "is_container": False,
             'ner.keys': [const.GENERATED_ENTITY + 'vital.loinc']},
        ],
        const.POSITIONS: {
            'Date': {
                "level": [const.SAME_LINE_RIGHT_SIDE, const.ABOVE_LINE]
            }
        },
        "date_order": ['patient.date', 'vital.date', 'patient.vital.date',
                       'lab.collected', 'lab.received', 'lab.issued',
                       'encounter.date', 'imaging.date'],
        'vitals_type': {
            'Systolic': ['vital.systolic', 'patient.systolic', 'insured_1.systolic', 'insured_2.systolic',
                         const.GENERATED_ENTITY + 'patient.systolic', const.GENERATED_ENTITY + 'insured_1.systolic',
                         const.GENERATED_ENTITY + 'insured_2.systolic'],
            'Diastolic': ['vital.diastolic', 'patient.diastolic', 'insured_1.diastolic', 'insured_2.diastolic',
                          const.GENERATED_ENTITY + 'patient.diastolic', const.GENERATED_ENTITY + 'insured_1.diastolic',
                          const.GENERATED_ENTITY + 'insured_2.diastolic'],
            'Blood Pressure': ['vital.blood', 'patient.blood', 'insured_1.blood', 'insured_2.blood', 
                        'patient.blood_pressure', 'insured_1.blood_pressure', 'insured_2.blood_pressure'],
            'Weight': ['vital.weight_lbs', 'vital.weight_kg', 'patient.weight_lbs', 'patient.weight_kg',
                       'insured_1.weight_lbs', 'insured_1.weight_kg'],
            'Height': ['vital.height_in', 'patient.height_in', 'vital.height_cm', 'patient.height_cm',
                       'insured_1.height_in', 'insured_1.height_cm'],
            'BMI': ['vital.bmi', 'patient.bmi'],
            'Heart Rate': ['vital.heart_rate', 'patient.heart_rate', 'insured_1.heart_rate', 'insured_2.heart_rate'],
            'Oxygen': ['vital.oxygen', 'patient.oxygen'],
            'Respiration': ['vital.respiration', 'patient.respiration'],
            'Temperature': ['vital.temperature_f', 'vital.temperature_c', 'patient.temperature_f',
                            'patient.temperature_c']
        }
    },

    Title.DIAGNOSIS: {
        "row": [
            {"entity_type": "assessment.diag", "entity_object": None, "display_key": const.DIAGNOSIS,
             "role": ENTITY_ROLE.MOTHER,
             'ner.keys': ['assessment.diag', 'history.diag', 'diagnosis.history', 'pathology.diagnosis',
                          'diagnosis.primary', 'diagnosis.secondary', 'diagnosis.principal', 'death.cause',
                          'patient.covid.positive', 'insured_1.covid.positive', 'insured_2.covid.positive',
                          'patient.medical_disorder.yes', 'insured_1.medical_disorder.yes',
                          'insured_2.medical_disorder.yes', 'medical.condition',
                          'patient.heart.disorder.risk.yes', 'insured_1.heart.disorder.risk.yes', 'insured_2.heart.disorder.risk.yes',
                          'patient.neoplasm.risk.yes', 'insured_1.neoplasm.risk.yes', 'insured_2.neoplasm.risk.yes',
                          'patient.brain_hemorrhage.yes', 'insured_1.brain_hemorrhage.yes',
                          'insured_2.brain_hemorrhage.yes',
                          'patient.mental.disorder.risk.yes', 'insured_1.mental.disorder.risk.yes',
                          'insured_2.mental.disorder.risk.yes',
                          'patient.neurological_disorder.risk.yes', 'insured_1.neurological_disorder.risk.yes',
                          'insured_2.neurological_disorder.risk.yes',
                          'patient.immune.disorder.risk.yes', 'insured_1.immune.disorder.risk.yes', 'insured_2.immune.disorder.risk.yes',
                          'patient.std.yes', 'insured_1.std.yes', 'insured_2.std.yes',
                          'patient.muscleandbone.disorder.risk.yes', 'insured_1.muscleandbone.disorder.risk.yes', 'insured_2.muscleandbone.disorder.risk.yes',
                          'patient.hepatitis.yes', 'insured_1.hepatitis.yes', 'insured_2.hepatitis.yes',
                          'patient.endocrine.disorder.risk.yes', 'insured_1.endocrine.disorder.risk.yes', 'insured_2.endocrine.disorder.risk.yes',
                          'patient.circulatory.disorder.risk.yes', 'insured_1.circulatory.disorder.risk.yes',
                          'insured_2.circulatory.disorder.risk.yes',
                          'patient.blood.disorder.risk.yes', 'insured_1.blood.disorder.risk.yes', 'insured_2.blood.disorder.risk.yes',
                          'patient.respiratory.disorder.risk.yes', 'insured_1.respiratory.disorder.risk.yes',
                          'insured_2.respiratory.disorder.risk.yes',
                          'patient.ent_disorder.risk.yes', 'insured_1.ent_disorder.risk.yes',
                          'insured_2.ent_disorder.risk.yes',
                          'patient.ent.nose_disorder.yes', 'insured_1.ent.nose_disorder.yes',
                          'insured_2.ent.nose_disorder.yes',
                          'patient.ent.ear_disorder.yes', 'insured_1.ent.ear_disorder.yes',
                          'insured_2.ent.ear_disorder.yes',
                          'patient.ent.throat_disorder.yes', 'insured_1.ent.throat_disorder.yes',
                          'insured_2.ent.throat_disorder.yes',
                          'patient.ent.thyroid_disorder.yes', 'insured_1.ent.thyroid_disorder.yes',
                          'insured_2.ent.thyroid_disorder.yes',
                          'patient.lymphatic.disorder.risk.yes', 'insured_1.lymphatic.disorder.risk.yes',
                          'insured_2.lymphatic.disorder.risk.yes',
                          'patient.oedema_disease.yes', 'insured_1.oedema_disease.yes', 'insured_2.oedema_disease.yes',
                          'patient.digestive.disorder.risk.yes', 'insured_1.digestive.disorder.risk.yes',
                          'insured_2.digestive.disorder.risk.yes',
                          'patient.liver_disorder.yes', 'insured_1.liver_disorder.yes', 'insured_2.liver_disorder.yes',
                          'patient.kidney.disorder.risk.yes', 'insured_1.kidney.disorder.risk.yes',
                          'insured_2.kidney.disorder.risk.yes',
                          'patient.breast_disorder.yes', 'insured_1.breast_disorder.yes',
                          'insured_2.breast_disorder.yes',
                          'patient.cyst.yes', 'insured_1.cyst.yes', 'insured_2.cyst.yes',
                          'patient.skin_diseases.yes', 'insured_1.skin_diseases.yes', 'insured_2.skin_diseases.yes',
                          'patient.hormonal_disorder.yes', 'insured_1.hormonal_disorder.yes',
                          'insured_2.hormonal_disorder.yes',
                          'patient.female.reproductive.risk.yes', 'insured_1.female.reproductive.risk.yes',
                          'insured_2.female.reproductive.risk.yes',
                          'patient.fever.yes', 'insured_1.fever.yes', 'insured_2.fever.yes',
                          'patient.tuberculosis.yes', 'insured_1.tuberculosis.yes', 'insured_2.tuberculosis.yes',
                          'patient.physical.disorder.risk.yes', 'insured_1.physical.disorder.risk.yes',
                          'insured_2.physical.disorder.risk.yes',
                          'patient.allergies.yes', 'insured_1.allergies.yes', 'insured_2.allergies.yes',
                          'patient.endocrine_disorder.risk.yes', 'insured_1.endocrine_disorder.risk.yes',
                          'insured_2.endocrine_disorder.risk.yes',
                          'patient.congenital.disorder.risk.yes', 'insured_1.congenital.disorder.risk.yes',
                          'insured_2.congenital.disorder.risk.yes',
                          'patient.nervous.disorder.risk.yes', 'insured_1.nervous.disorder.risk.yes',
                          'insured_2.nervous.disorder.risk.yes',
                          'patient.gastrointestinal.disorder.risk.yes', 'insured_1.gastrointestinal.disorder.risk.yes',
                          'insured_2.gastrointestinal.disorder.risk.yes',
                          'patient.urinary_disorder.yes', 'insured_1.urinary_disorder.yes',
                          'insured_2.urinary_disorder.yes'
                          'patient.chronic_disease.yes', 'insured_1.chronic_disease.yes',
                          'insured_2.chronic_disease.yes'
                          'patient.heart_disease.yes', 'insured_1.heart_disease.yes', 'insured_2.heart_disease.yes',
                          'patient.cancer_disease.yes', 'insured_1.cancer_disease.yes', 'insured_2.cancer_disease.yes',
                          'patient.mental_disorder.yes', 'insured_1.mental_disorder.yes',
                          'insured_2.mental_disorder.yes',
                          'patient.hiv.yes', 'insured_1.hiv.yes', 'insured_2.hiv.yes',
                          'patient.muscular_pain.yes', 'insured_1.muscular_pain.yes', 'insured_2.muscular_pain.yes',
                          'patient.diabetes.yes', 'insured_1.diabetes.yes', 'insured_2.diabetes.yes',
                          'patient.high_blood_pressure.yes', 'insured_1.high_blood_pressure.yes',
                          'insured_2.high_blood_pressure.yes',
                          'patient.blood_disorder.yes', 'insured_1.blood_disorder.yes', 'insured_2.blood_disorder.yes',
                          'patient.breathing_disorder.yes', 'insured_1.breathing_disorder.yes',
                          'insured_2.breathing_disorder.yes',
                          'patient.ent.eyes_disorder.yes', 'insured_1.ent.eyes_disorder.yes',
                          'insured_2.ent.eyes_disorder.yes',
                          'patient.lymphatic_system_disease.yes', 'insured_1.lymphatic_system_disease.yes',
                          'insured_2.lymphatic_system_disease.yes',
                          'patient.stomach_disorder.yes', 'insured_1.stomach_disorder.yes',
                          'insured_2.stomach_disorder.yes',
                          'patient.kidney_disorder.yes', 'insured_1.kidney_disorder.yes',
                          'insured_2.kidney_disorder.yes',
                          'patient.reproductive_organs_diseases.yes', 'insured_1.reproductive_organs_diseases.yes',
                          'insured_2.reproductive_organs_diseases.yes',
                          'patient.physical_disorder.yes', 'insured_1.physical_disorder.yes',
                          'insured_2.physical_disorder.yes',
                          'patient.endocrine_glands_disorder.yes', 'insured_1.endocrine_glands_disorder.yes',
                          'insured_2.endocrine_glands_disorder.yes',
                          'patient.nervous_system_disorder.yes', 'insured_1.nervous_system_disorder.yes',
                          'insured_2.nervous_system_disorder.yes',
                          'patient.gastrointestinal_disorder.yes', 'insured_1.gastrointestinal_disorder.yes',
                          'insured_2.gastrointestinal_disorder.yes',
                          "insured_1.heart_disorder.risk.yes", "insured_2.heart_disorder.risk.yes",
                          "patient.heart_disorder.risk.yes",
                          "insured_1.neoplasm_risk.yes", "insured_2.neoplasm_risk.yes", "patient.neoplasm_risk.yes",
                          "insured_1.mental_disorder_risk.yes", "insured_2.mental_disorder_risk.yes",
                          "patient.mental_disorder_risk.yes",
                          "insured_1.immune_disorder_risk.yes", "insured_2.immune_disorder_risk.yes",
                          "patient.immune_disorder_risk.yes",
                          "insured_1.muscle_and_bone.disorder.risk.yes", "insured_2.muscle_and_bone.disorder.risk.yes",
                          "patient.muscle_and_bone.disorder.risk.yes",
                          "insured_1.endocrine_disorder_risk.yes", "insured_2.endocrine_disorder_risk.yes",
                          "patient.endocrine_disorder_risk.yes",
                          "insured_1.circulatory_disorder_risk.yes", "insured_2.circulatory_disorder_risk.yes",
                          "patient.circulatory_disorder_risk.yes",
                          "insured_1.blood_disorder.risk.yes", "insured_2.blood_disorder.risk.yes",
                          "patient.blood_disorder.risk.yes",
                          "insured_1.respiratory_disorder_risk.yes", "insured_2.respiratory_disorder_risk.yes",
                          "patient.respiratory_disorder_risk.yes",
                          "insured_1.ent_disorder_risk.yes", "insured_2.ent_disorder_risk.yes",
                          "patient.ent_disorder_risk.yes",
                          "insured_1.lymphatic_disorder_risk.yes", "insured_2.lymphatic_disorder_risk.yes",
                          "patient.lymphatic_disorder_risk.yes",
                          "insured_1.digestive_disorder_risk.yes", "insured_2.digestive_disorder_risk.yes",
                          "patient.digestive_disorder_risk.yes",
                          "insured_1.kidney_disorder.risk.yes", "insured_2.kidney_disorder.risk.yes",
                          "patient.kidney_disorder.risk.yes",
                          "insured_1.medical_test.yes", "insured_2.medical_test.yes", "patient.medical_test.yes",
                          "insured_1.medication.yes", "insured_2.medication.yes",
                          "patient.medication.yes",
                          "insured_1.hospitalization.yes", "insured_2.hospitalization.yes",
                          "patient.hospitalization.yes",
                          "insured_1.recent.hospitalization.yes", "insured_2.recent.hospitalization.yes",
                          "patient.recent.hospitalization.yes",
                          "insured_1.pregnant.yes", "insured_2.pregnant.yes", "patient.pregnant.yes",
                          "insured_1.female_reproductive_risk.yes", "insured_2.female_reproductive_risk.yes",
                          "patient.female_reproductive_risk.yes",
                          "insured_1.physical_disability.yes", "insured_2.physical_disability.yes",
                          "patient.physical_disability.yes",
                          "insured_1.nervous_disorder_risk.yes", "insured_2.nervous_disorder_risk.yes",
                          "patient.nervous_disorder_risk.yes",
                          "insured_1.gastrointestinal_disorder.risk.yes",
                          "insured_2.gastrointestinal_disorder.risk.yes", "patient.gastrointestinal_disorder.risk.yes",
                          "insured_1.endocrine_disorder_risk.yes", "insured_2.endocrine_disorder_risk.yes",
                          "patient.endocrine_disorder_risk.yes",
                          "insured_1.congenital_disorder_risk.yes", "insured_2.congenital_disorder_risk.yes",
                          "patient.congenital_disorder_risk.yes",
                          "insured_1.other_symptoms.yes", "insured_2.other_symptoms.yes", "patient.other_symptoms.yes",
                          "insured_1.current.treatment.yes", "insured_2.current.treatment.yes",
                          "patient.current.treatment.yes",
                          "insured_1.history.treatment.yes", "insured_2.history.treatment.yes",
                          "patient.history.treatment.yes",
                          "insured_1.consultation.yes", "insured_2.consultation.yes", "patient.consultation.yes",
                          "insured_1.plan.consultation.yes", "insured_2.plan.consultation.yes",
                          "patient.plan.consultation.yes",
                          "insured_1.hepatobiliary_risk.yes", "insured_2.hepatobiliary_risk.yes",
                          "patient.hepatobiliary_risk.yes",
                          "patient.gastrointestinal_disorder_risk.yes",
                          "insured_1.gastrointestinal_disorder_risk.yes",
                          "insured_2.gastrointestinal_disorder_risk.yes",
                          "patient.respiratory_disorder_risk.details",
                          "insured_1.respiratory_disorder_risk.details",
                          "insured_2.respiratory_disorder_risk.details",
                          "patient.neoplasm_risk.details",
                          "insured_1.neoplasm_risk.details",
                          "insured_2.neoplasm_risk.details",
                          "patient.mental_disorder_risk.details",
                          "insured_1.mental_disorder_risk.details",
                          "insured_2.mental_disorder_risk.details",
                          "patient.blood_pressure_disorder.risk.details",
                          "insured_1.blood_pressure_disorder.risk.details",
                          "insured_2.blood_pressure_disorder.risk.details",
                          "patient.muscle_and_bone_disorder_risk.details",
                          "insured_1.muscle_and_bone_disorder_risk.details",
                          "insured_2.muscle_and_bone_disorder_risk.details",
                          "patient.digestive_disorder_risk.details",
                          "insured_1.digestive_disorder_risk.details",
                          "insured_2.digestive_disorder_risk.details",
                          "patient.muscle_and_bone_disorder_risk.yes",
                          "insured_1.muscle_and_bone_disorder_risk.yes",
                          "insured_2.muscle_and_bone_disorder_risk.yes",
                          "patient.blood_pressure_disorder.risk.yes",
                          "insured_1.blood_pressure_disorder.risk.yes",
                          "insured_2.blood_pressure_disorder.risk.yes",
                          ]
             },
            {"entity_type": "icd10", "entity_object": None, "display_key": const.ICD10, "role": ENTITY_ROLE.CHILD,
             'ner.keys': ['icd10', const.GENERATED_ENTITY + "icd10"]},
            {"entity_type": "condition", "entity_object": None, "display_key": "Condition", "role": ENTITY_ROLE.CHILD,
             'ner.keys': [const.GENERATED_ENTITY + "diagnosis_condition"]},
            {"entity_type": "icd_description", "entity_object": None, "display_key": "ICD-10 Description", "role": ENTITY_ROLE.CHILD,
             'ner.keys': ["icd_description", const.GENERATED_ENTITY + "icd_description"]},
            {"entity_type": "icd_chapter", "entity_object": None, "display_key": "ICD Chapter",
             "role": ENTITY_ROLE.CHILD,
             'ner.keys': [const.GENERATED_ENTITY + "icd_chapter"]},
            {"entity_type": "icd_section", "entity_object": None, "display_key": "ICD Section",
             "role": ENTITY_ROLE.CHILD,
             'ner.keys': [const.GENERATED_ENTITY + "icd_section"]},
            {"entity_type": "impairment", "entity_object": None, "display_key": "Impairment",
             "role": ENTITY_ROLE.CHILD,
             'ner.keys': [const.GENERATED_ENTITY + "impairment"]},
            {"entity_type": "visit.date", "entity_object": None, "display_key": const.VISIT_DATE,
             "role": ENTITY_ROLE.CHILD, 'ner.keys': ['patient.date', 'vital.date', 'encounter.date', 'pathology.date',
                                                     'assessment.date', 'patient.vital.date']},
            {"entity_type": "diag.date", "entity_object": None, "display_key": const.DIAGNOSIS_DATE,
             "role": ENTITY_ROLE.CHILD, 'ner.keys': ['diag.date', 'diagnosis.date', 'diagnosis.history.date']},
            {"entity_type": "SNOMED", "entity_object": None, "display_key": const.SNOMED,
             "role": ENTITY_ROLE.CHILD, 'ner.keys': [const.GENERATED_ENTITY + const.SNOMED, const.GENERATED_ENTITY + "snomed"]}
        ],
        const.RESTRICTED_SECTIONS: ["sect_immunizations", "sect_medication"],
        const.HEADERS: {
            const.DIAGNOSIS: ['assessment.diag'],
            const.TYPE: ['ignored'],
            const.ICD10: ['icd10'],
            const.ICD10: [const.GENERATED_ENTITY + "diagnosis_condition"],
            "ICD-10 Description": ['icd_description', const.GENERATED_ENTITY + "icd_description"],
            const.VISIT_DATE: ['visit.date'],
            const.DIAGNOSIS_DATE: ['diag.date'],
            "ICD Chapter": ['ignored'],
            "ICD Section": ['ignored'],
            "Impairment": ['ignored'],
            const.SNOMED: ['ignored']
        },
        'diagnosis_types': {
            'Current': ['assessment.diag', 'pathology.diagnosis',
                        'diagnosis.primary', 'diagnosis.secondary', 'diagnosis.principal', 'death.cause',
                        'patient.covid.positive', 'insured_1.covid.positive', 'insured_2.covid.positive',
                        'patient.medical_disorder.yes', 'insured_1.medical_disorder.yes',
                        'insured_2.medical_disorder.yes', 'medical.condition',
                        'patient.heart.disorder.risk.yes', 'insured_1.heart.disorder.risk.yes', 'insured_2.heart.disorder.risk.yes',
                        'patient.neoplasm.risk.yes', 'insured_1.neoplasm.risk.yes', 'insured_2.neoplasm.risk.yes',
                        'patient.brain_hemorrhage.yes', 'insured_1.brain_hemorrhage.yes',
                        'insured_2.brain_hemorrhage.yes',
                        'patient.mental.disorder.risk.yes', 'insured_1.mental.disorder.risk.yes', 'insured_2.mental.disorder.risk.yes',
                        'patient.neurological_disorder.risk.yes', 'insured_1.neurological_disorder.risk.yes',
                        'insured_2.neurological_disorder.risk.yes',
                        'patient.immune.disorder.risk.yes', 'insured_1.immune.disorder.risk.yes', 'insured_2.immune.disorder.risk.yes',
                        'patient.std.yes', 'insured_1.std.yes', 'insured_2.std.yes',
                        'patient.muscleandbone.disorder.risk.yes', 'insured_1.muscleandbone.disorder.risk.yes', 'insured_2.muscleandbone.disorder.risk.yes',
                        'patient.hepatitis.yes', 'insured_1.hepatitis.yes', 'insured_2.hepatitis.yes',
                        'patient.endocrine.disorder.risk.yes', 'insured_1.endocrine.disorder.risk.yes', 'insured_2.endocrine.disorder.risk.yes',
                        'patient.blood.disorder.risk.yes', 'insured_1.blood.disorder.risk.yes', 'insured_2.blood.disorder.risk.yes',
                        'patient.respiratory.disorder.risk.yes', 'insured_1.respiratory.disorder.risk.yes',
                        'insured_2.respiratory.disorder.risk.yes',
                        'patient.ent_disorder.risk.yes', 'insured_1.ent_disorder.risk.yes',
                        'insured_2.ent_disorder.risk.yes',
                        'patient.ent.nose_disorder.yes', 'insured_1.ent.nose_disorder.yes',
                        'insured_2.ent.nose_disorder.yes',
                        'patient.ent.ear_disorder.yes', 'insured_1.ent.ear_disorder.yes',
                        'insured_2.ent.ear_disorder.yes',
                        'patient.ent.throat_disorder.yes', 'insured_1.ent.throat_disorder.yes',
                        'insured_2.ent.throat_disorder.yes',
                        'patient.ent.thyroid_disorder.yes', 'insured_1.ent.thyroid_disorder.yes',
                        'insured_2.ent.thyroid_disorder.yes',
                        'patient.lymphatic.disorder.risk.yes', 'insured_1.lymphatic.disorder.risk.yes',
                        'insured_2.lymphatic.disorder.risk.yes',
                        'patient.oedema_disease.yes', 'insured_1.oedema_disease.yes', 'insured_2.oedema_disease.yes',
                        'patient.digestive.disorder.risk.yes', 'insured_1.digestive.disorder.risk.yes',
                        'insured_2.digestive.disorder.risk.yes',
                        'patient.liver_disorder.yes', 'insured_1.liver_disorder.yes', 'insured_2.liver_disorder.yes',
                        'patient.kidney.disorder.risk.yes', 'insured_1.kidney.disorder.risk.yes', 'insured_2.kidney.disorder.risk.yes',
                        'patient.breast_disorder.yes', 'insured_1.breast_disorder.yes', 'insured_2.breast_disorder.yes',
                        'patient.cyst.yes', 'insured_1.cyst.yes', 'insured_2.cyst.yes',
                        'patient.skin_diseases.yes', 'insured_1.skin_diseases.yes', 'insured_2.skin_diseases.yes',
                        'patient.hormonal_disorder.yes', 'insured_1.hormonal_disorder.yes',
                        'insured_2.hormonal_disorder.yes',
                        'patient.female.reproductive.risk.yes', 'insured_1.female.reproductive.risk.yes',
                        'insured_2.female.reproductive.risk.yes',
                        'patient.fever.yes', 'insured_1.fever.yes', 'insured_2.fever.yes',
                        'patient.tuberculosis.yes', 'insured_1.tuberculosis.yes', 'insured_2.tuberculosis.yes',
                        'patient.physical.disorder.risk.yes', 'insured_1.physical.disorder.risk.yes',
                        'insured_2.physical.disorder.risk.yes',
                        'patient.allergies.yes', 'insured_1.allergies.yes', 'insured_2.allergies.yes',
                        'patient.endocrine_disorder.risk.yes', 'insured_1.endocrine_disorder.risk.yes',
                        'insured_2.endocrine_disorder.risk.yes',
                        'patient.nervous.disorder.risk.yes', 'insured_1.nervous.disorder.risk.yes',
                        'insured_2.nervous.disorder.risk.yes',
                        'patient.gastrointestinal.disorder.risk.yes', 'insured_1.gastrointestinal.disorder.risk.yes',
                        'insured_2.gastrointestinal.disorder.risk.yes',
                        'patient.urinary_disorder.yes', 'insured_1.urinary_disorder.yes',
                        'insured_2.urinary_disorder.yes'
                        'patient.chronic_disease.yes', 'insured_1.chronic_disease.yes',
                        'insured_2.chronic_disease.yes',
                        'patient.heart_disease.yes', 'insured_1.heart_disease.yes', 'insured_2.heart_disease.yes',
                        'patient.cancer_disease.yes', 'insured_1.cancer_disease.yes', 'insured_2.cancer_disease.yes',
                        'patient.mental_disorder.yes', 'insured_1.mental_disorder.yes',
                        'insured_2.mental_disorder.yes',
                        'patient.hiv.yes', 'insured_1.hiv.yes', 'insured_2.hiv.yes',
                        'patient.muscular_pain.yes', 'insured_1.muscular_pain.yes', 'insured_2.muscular_pain.yes',
                        'patient.diabetes.yes', 'insured_1.diabetes.yes', 'insured_2.diabetes.yes',
                        'patient.high_blood_pressure.yes', 'insured_1.high_blood_pressure.yes',
                        'insured_2.high_blood_pressure.yes',
                        'patient.blood_disorder.yes', 'insured_1.blood_disorder.yes', 'insured_2.blood_disorder.yes',
                        'patient.breathing_disorder.yes', 'insured_1.breathing_disorder.yes',
                        'insured_2.breathing_disorder.yes',
                        'patient.ent.eyes_disorder.yes', 'insured_1.ent.eyes_disorder.yes',
                        'insured_2.ent.eyes_disorder.yes',
                        'patient.lymphatic_system_disease.yes', 'insured_1.lymphatic_system_disease.yes',
                        'insured_2.lymphatic_system_disease.yes',
                        'patient.stomach_disorder.yes', 'insured_1.stomach_disorder.yes',
                        'insured_2.stomach_disorder.yes',
                        'patient.kidney_disorder.yes', 'insured_1.kidney_disorder.yes',
                        'insured_2.kidney_disorder.yes',
                        'patient.reproductive_organs_diseases.yes', 'insured_1.reproductive_organs_diseases.yes',
                        'insured_2.reproductive_organs_diseases.yes',
                        'patient.physical_disorder.yes', 'insured_1.physical_disorder.yes',
                        'insured_2.physical_disorder.yes',
                        'patient.endocrine_glands_disorder.yes', 'insured_1.endocrine_glands_disorder.yes',
                        'insured_2.endocrine_glands_disorder.yes',
                        'patient.nervous_system_disorder.yes', 'insured_1.nervous_system_disorder.yes',
                        'insured_2.nervous_system_disorder.yes',
                        'patient.gastrointestinal_disorder.yes', 'insured_1.gastrointestinal_disorder.yes',
                        'insured_2.gastrointestinal_disorder.yes',
                        "insured_1.heart_disorder.risk.yes", "insured_2.heart_disorder.risk.yes",
                        "patient.heart_disorder.risk.yes",
                        "insured_1.neoplasm_risk.yes", "insured_2.neoplasm_risk.yes", "patient.neoplasm_risk.yes",
                        "insured_1.mental_disorder_risk.yes", "insured_2.mental_disorder_risk.yes",
                        "patient.mental_disorder_risk.yes",
                        "insured_1.immune_disorder_risk.yes", "insured_2.immune_disorder_risk.yes",
                        "patient.immune_disorder_risk.yes",
                        "insured_1.muscle_and_bone.disorder.risk.yes", "insured_2.muscle_and_bone.disorder.risk.yes",
                        "patient.muscle_and_bone.disorder.risk.yes",
                        "insured_1.endocrine_disorder_risk.yes", "insured_2.endocrine_disorder_risk.yes",
                        "patient.endocrine_disorder_risk.yes",
                        "insured_1.circulatory_disorder_risk.yes", "insured_2.circulatory_disorder_risk.yes",
                        "patient.circulatory_disorder_risk.yes",
                        "insured_1.blood_disorder.risk.yes", "insured_2.blood_disorder.risk.yes",
                        "patient.blood_disorder.risk.yes",
                        "insured_1.respiratory_disorder_risk.yes", "insured_2.respiratory_disorder_risk.yes",
                        "patient.respiratory_disorder_risk.yes",
                        "insured_1.ent_disorder_risk.yes", "insured_2.ent_disorder_risk.yes",
                        "patient.ent_disorder_risk.yes",
                        "insured_1.lymphatic_disorder_risk.yes", "insured_2.lymphatic_disorder_risk.yes",
                        "patient.lymphatic_disorder_risk.yes",
                        "insured_1.digestive_disorder_risk.yes", "insured_2.digestive_disorder_risk.yes",
                        "patient.digestive_disorder_risk.yes",
                        "insured_1.kidney_disorder.risk.yes", "insured_2.kidney_disorder.risk.yes",
                        "patient.kidney_disorder.risk.yes",
                        "insured_1.medical_test.yes", "insured_2.medical_test.yes", "patient.medical_test.yes",
                        "insured_1.medication.yes", "insured_2.medication.yes",
                        "patient.medication.yes",
                        "insured_1.hospitalization.yes", "insured_2.hospitalization.yes", "patient.hospitalization.yes",
                        "insured_1.recent.hospitalization.yes", "insured_2.recent.hospitalization.yes",
                        "patient.recent.hospitalization.yes",
                        "insured_1.pregnant.yes", "insured_2.pregnant.yes", "patient.pregnant.yes",
                        "insured_1.female_reproductive_risk.yes", "insured_2.female_reproductive_risk.yes",
                        "patient.female_reproductive_risk.yes",
                        "insured_1.physical_disability.yes", "insured_2.physical_disability.yes",
                        "patient.physical_disability.yes",
                        "insured_1.nervous_disorder_risk.yes", "insured_2.nervous_disorder_risk.yes",
                        "patient.nervous_disorder_risk.yes",
                        "insured_1.gastrointestinal_disorder.risk.yes", "insured_2.gastrointestinal_disorder.risk.yes",
                        "patient.gastrointestinal_disorder.risk.yes",
                        "insured_1.endocrine_disorder_risk.yes", "insured_2.endocrine_disorder_risk.yes",
                        "patient.endocrine_disorder_risk.yes",
                        "insured_1.congenital_disorder_risk.yes", "insured_2.congenital_disorder_risk.yes",
                        "patient.congenital_disorder_risk.yes",
                        "insured_1.other_symptoms.yes", "insured_2.other_symptoms.yes", "patient.other_symptoms.yes",
                        "insured_1.current.treatment.yes", "insured_2.current.treatment.yes",
                        "patient.current.treatment.yes",
                        "insured_1.history.treatment.yes", "insured_2.history.treatment.yes",
                        "patient.history.treatment.yes",
                        "insured_1.consultation.yes", "insured_2.consultation.yes", "patient.consultation.yes",
                        "insured_1.plan.consultation.yes", "insured_2.plan.consultation.yes",
                        "patient.plan.consultation.yes",
                        "insured_1.hepatobiliary_risk.yes", "insured_2.hepatobiliary_risk.yes",
                        "patient.hepatobiliary_risk.yes",
                        "patient.gastrointestinal_disorder_risk.yes",
                        "insured_1.gastrointestinal_disorder_risk.yes",
                        "insured_2.gastrointestinal_disorder_risk.yes",
                        "patient.respiratory_disorder_risk.details",
                        "insured_1.respiratory_disorder_risk.details",
                        "insured_2.respiratory_disorder_risk.details",
                        "patient.neoplasm_risk.details",
                        "insured_1.neoplasm_risk.details",
                        "insured_2.neoplasm_risk.details",
                        "patient.mental_disorder_risk.details",
                        "insured_1.mental_disorder_risk.details",
                        "insured_2.mental_disorder_risk.details",
                        "patient.blood_pressure_disorder.risk.details",
                        "insured_1.blood_pressure_disorder.risk.details",
                        "insured_2.blood_pressure_disorder.risk.details",
                        "patient.muscle_and_bone_disorder_risk.details",
                        "insured_1.muscle_and_bone_disorder_risk.details",
                        "insured_2.muscle_and_bone_disorder_risk.details",
                        "patient.digestive_disorder_risk.details",
                        "insured_1.digestive_disorder_risk.details",
                        "insured_2.digestive_disorder_risk.details",
                        "patient.muscle_and_bone_disorder_risk.yes",
                        "insured_1.muscle_and_bone_disorder_risk.yes",
                        "insured_2.muscle_and_bone_disorder_risk.yes",
                        "patient.blood_pressure_disorder.risk.yes",
                        "insured_1.blood_pressure_disorder.risk.yes",
                        "insured_2.blood_pressure_disorder.risk.yes",
                        "patient.lymphatic_disorder_risk.risk.yes",
                        "insured_1.lymphatic_disorder_risk.risk.yes",
                        "insured_2.lymphatic_disorder_risk.risk.yes",
                        ],
            'Historical': ['history.diag', 'diagnosis.history'],
            'Pathology': ['pathology.diagnosis', 'pathology.finding', 'pathology.procedure']
        }
    },
    Title.CLAIMED_INJURY_DETAILS: {
        "row": [
            {"entity_type": "assessment.claimed_injury", "entity_object": None, "display_key": const.CLAIMED_INJURY,
             "role": ENTITY_ROLE.MOTHER,
             'ner.keys': ['injury.type', 'injury.cause', 'history.injury.type', 'injury.history.type',
                          'history.injury.cause', 'injury.history.cause']},
            {"entity_type": "icd10", "entity_object": None, "display_key": const.ICD10, "role": ENTITY_ROLE.CHILD,
             'ner.keys': ['icd10']},
            {"entity_type": "claimed_injury.date", "entity_object": None, "display_key": const.DATE,
             "role": ENTITY_ROLE.CHILD, 'ner.keys': ['injury.date', 'history.injury.date', 'injury.history.date']}
        ],
        const.RESTRICTED_SECTIONS: ["sect_immunizations", "sect_medication"],
        const.HEADERS: {
            const.CLAIMED_INJURY: ['assessment.claimed_injury'],
            const.TYPE: ['ignored'],
            const.ICD10: ['icd10'],
            const.DATE: ['claimed_injury.date']
        },
        'diagnosis_types': {
            'Current': ['injury.type', 'injury.cause', 'history.injury.type', 'injury.history.type',
                        'history.injury.cause', 'injury.history.cause'],
            'Historical': ['history.diag', 'diagnosis.history'],
            'Pathology': ['pathology.diagnosis', 'pathology.finding', 'pathology.procedure']
        }
    },

    Title.REASON_OF_VISIT: {
        "row": [
            {"entity_type": "encounter.date", "entity_object": None, "display_key": "Date", "is_container": False,
             "role": ENTITY_ROLE.MOTHER},
            {"entity_type": "reason", "entity_object": None, "display_key": "Reason", "is_container": False,
             "role": ENTITY_ROLE.CHILD},
            {"entity_type": "encounter.conclusion", "entity_object": None, "display_key": "Reason",
             "is_container": False,
             "role": ENTITY_ROLE.CHILD},
            {"entity_type": "assessment.plan", "entity_object": None, "display_key": "Plan", "is_container": False,
             "role": ENTITY_ROLE.CHILD},
            {"entity_type": "history.surgery", "entity_object": None, "display_key": "Reason", "is_container": False,
             "role": ENTITY_ROLE.CHILD},
            {"entity_type": "surgery.history", "entity_object": None, "display_key": "Reason", "is_container": False,
             "role": ENTITY_ROLE.CHILD},
            {"entity_type": "hpi.symptom.positive", "entity_object": None, "display_key": "Reason",
             "is_container": False, "role": ENTITY_ROLE.CHILD},
            {"entity_type": "history.diag", "entity_object": None, "display_key": "Reason", "is_container": False,
             "role": ENTITY_ROLE.CHILD},
            {"entity_type": "diagnosis.history", "entity_object": None, "display_key": "Reason", "is_container": False,
             "role": ENTITY_ROLE.CHILD},
            {"entity_type": "assessment.diag", "entity_object": None, "display_key": "Reason", "is_container": False,
             "role": ENTITY_ROLE.CHILD},
            {"entity_type": "imaging.exam", "entity_object": None, "display_key": "Reason", "is_container": False,
             "role": ENTITY_ROLE.CHILD},
            {"entity_type": "imaging.finding", "entity_object": None, "display_key": "Reason", "is_container": False,
             "role": ENTITY_ROLE.CHILD},
            {"entity_type": "imaging.impression", "entity_object": None, "display_key": "Reason", "is_container": False,
             "role": ENTITY_ROLE.CHILD},
            {"entity_type": "surgery.procedure", "entity_object": None, "display_key": "Reason", "is_container": False,
             "role": ENTITY_ROLE.CHILD},
            {"entity_type": "imaging.procedure", "entity_object": None, "display_key": "Reason", "is_container": False,
             "role": ENTITY_ROLE.CHILD},
            {"entity_type": "assessment.phytherapy", "entity_object": None, "display_key": "Reason",
             "is_container": False, "role": ENTITY_ROLE.CHILD},
            {"entity_type": "pathology.diagnosis", "entity_object": None, "display_key": "Reason",
             "is_container": False, "role": ENTITY_ROLE.CHILD},
            {"entity_type": "pathology.finding", "entity_object": None, "display_key": "Reason", "is_container": False,
             "role": ENTITY_ROLE.CHILD},
            {"entity_type": "pathology.procedure", "entity_object": None, "display_key": "Reason",
             "is_container": False, "role": ENTITY_ROLE.CHILD}

        ],
        const.GROUPS: [
            {'date': ['encounter.date'],
             'reason': ['reason', 'hpi.reason', 'hpi.symptom.positive', 'hpi.patient.symptom.positive',
                        'encounter.conclusion'],
             'plan': ['assessment.plan', 'plan.evaluation', 'plan.surgery', 'plan.radiology', 'plan.medication'],
             'section': ['sect_assessment_plan', 'sect_hpi', 'sect_medical_history', 'sect_assessment_plan']},
            {'date': ['diag.date', 'encounter.date', 'diagnosis.date', 'assessment.date', 'diagnosis.history.date'],
             'reason': ['assessment.diag', 'history.diag', 'diagnosis.history', 'assessment'],
             'plan': ['assessment.plan', 'plan.evaluation', 'plan.surgery', 'plan.radiology', 'plan.medication'],
             'section': ['sect_assessment_plan', 'sect_hpi', 'sect_medical_history', 'sect_assessment_plan']},
            {'date': ['imaging.date', 'encounter.date'],
             'reason': ['imaging.exam', 'imaging.finding', 'imaging.impression', 'imaging.procedure'],
             'plan': ['plan.radiology'], 'section': ['sect_imaging', 'sect_findings', 'sect_impression']},
            {'date': ['surgery.date', 'encounter.date'],
             'reason': ['history.surgery', 'surgery.history', 'surgery.procedure', 'reason', 'hpi.reason'],
             'plan': ['plan.surgery'],
             'section': ['sect_assessment_plan', 'sect_hpi', 'sect_medical_history', 'sect_assessment_plan']},
            {'date': ['encounter.date'], 'reason': ['assessment.phytherapy'], 'plan': ['plan.phytherapy', 'plan.physiotherapy'],
             'section': ['sect_assessment_plan', 'sect_hpi', 'sect_medical_history', 'sect_assessment_plan']},
            {'date': ['pathology.date'], 'reason': ['pathology.diagnosis', 'pathology.finding', 'pathology.procedure'],
             'plan': ['assessment.plan'], 'section': []},
            {'date': ["lab.collected", "o.collected", "lab.received", "o.received", 'o.issued', "lab.issued"],
             'reason': ['reason'], 'plan': ['plan.lab'], 'section': []}
        ],
        const.HEADERS: {
            'Date': ['encounter.date'],
            'Reason': ['reason'],
            'Plan': ['plan']
        },
        "description_entities": [
            'history.diag', 'diagnosis.history', 'assessment.diag', 'assessment', 'hpi.symptom.positive',
            'hpi.patient.symptom.positive', 'reason',
            'hpi.reason', 'plan.surgery', 'plan.evaluation', 'encounter.conclusion']
    },

    Title.MEDICATIONS: {
        "row": [
            {"entity_type": "illness.condition", "entity_object": None, "display_key": "Condition",
             "is_container": False, "role": ENTITY_ROLE.SHARED},
            {"entity_type": "medication_type", "entity_object": None, "display_key": "Type",
             "is_container": False, "role": ENTITY_ROLE.CHILD},
            {"entity_type": "medication", "entity_object": None, "display_key": "Medication",
             "is_container": False, "role": ENTITY_ROLE.MOTHER},
            {"entity_type": "medication.instruction", "entity_object": None, "display_key": "Instruction",
             "is_container": False, "role": ENTITY_ROLE.CHILD},
            {"entity_type": "medication.duration", "entity_object": None, "display_key": "Duration",
             "is_container": False, "role": ENTITY_ROLE.CHILD},
            {"entity_type": "medication.quantity", "entity_object": None, "display_key": "Quantity", "is_container": False,
             "role": ENTITY_ROLE.CHILD},
            {"entity_type": "medication.dosage", "entity_object": None, "display_key": "Dosage", "is_container": False,
             "role": ENTITY_ROLE.CHILD},
            {"entity_type": "medication.dosage.unit", "entity_object": None, "display_key": "Unit",
             "is_container": False,
             "role": ENTITY_ROLE.CHILD},
            {"entity_type": "medication.start", "entity_object": None, "display_key": "Start", "is_container": False,
             "role": ENTITY_ROLE.CHILD},
            {"entity_type": "medication.end", "entity_object": None, "display_key": "End", "is_container": False,
             "role": ENTITY_ROLE.CHILD},
            {"entity_type": "medication.date", "entity_object": None, "display_key": "Date", "is_container": False,
             "role": ENTITY_ROLE.CHILD},
            {"entity_type": "snomed", "entity_object": None, "display_key": "SNOMED",
             "is_container": False, "role": ENTITY_ROLE.CHILD},
            {"entity_type": "medication.rx_code", "entity_object": None, "display_key": "Rx Code",
             "is_container": False, "role": ENTITY_ROLE.CHILD},
            {"entity_type": "medication.category", "entity_object": None, "display_key": "Category",
             "is_container": False, "role": ENTITY_ROLE.CHILD},
            {"entity_type": "medication.provider", "entity_object": None, "display_key": "Provider",
             "is_container": False, "role": ENTITY_ROLE.CHILD}
        ],
        const.POSITIONS: { 
            'condition': {
                "level": [const.SAME_LINE_RIGHT_SIDE, const.BELOW_LINE]
            },
            'instruction': {
                "level": [const.SAME_LINE_RIGHT_SIDE, const.BELOW_LINE]
            },
            'duration': {
                "level": [const.SAME_LINE_RIGHT_SIDE, const.BELOW_LINE]
            },
            'quantity': {
                "level": [const.SAME_LINE_RIGHT_SIDE, const.BELOW_LINE]
            },
            'dosage': {
                "level": [const.SAME_LINE_RIGHT_SIDE, const.BELOW_LINE]
            },
            'unit': {
                "level": [const.SAME_LINE_RIGHT_SIDE, const.BELOW_LINE]
            },
            'start': {
                "level": [const.SAME_LINE, const.BELOW_LINE]
            },
            'end': {
                "level": [const.SAME_LINE, const.BELOW_LINE]
            },
            'date': {
                "level": [const.SAME_LINE, const.ABOVE_LINE]
            },
            'rx code': {
                "level": [const.SAME_LINE, const.BELOW_LINE]
            },
            'provider': {
                "level": [const.SAME_LINE, const.ABOVE_LINE, const.BELOW_LINE]
            }
        },
        const.GROUPS: [
            {'condition': ['reason', 'reason_of_visit', 'medication.disease.prescription', const.MERGED_ENTITY + 'condition', "medication.reason"],
             'medication': ['current.medication', 'hpi.medication_current', 'history.medication', 'medication.history',
                        'plan.medication', 'hpi.medication_discontinued', 'medication.name'],
             'instruction': ['medication.instruction'], 
             'duration': ['medication.duration'],
             'quantity': ['medication.quantity'], 
             'dosage': ['medication.dosage', 'medication.strength.quantity',], 
             'unit': ['medication.dosage.unit', 'medication.strength.unit'],
             'start': ['medication.start'], 
             'end': ['medication.end'],
             'date': ['encounter.date'],
             'rx code': ['rx.number'],
             'provider': ['doctor.speciality']
             }
        ],
        "medication_types": {
            'Current': ['current.medication', 'hpi.medication_current', 'medication.name'],
            'History': ['history.medication', 'medication.history'],
            'Plan': ['plan.medication'],
            'Discontinued': ['hpi.medication_discontinued']
        }
    },

    Title.IMAGING_STUDY: {
        "row": [
            {"entity_type": "imaging.exam", "entity_object": None, "display_key": "Exam", "role": ENTITY_ROLE.MOTHER,
             'ner.keys': ['imaging.exam', 'cardio.exam', 'diagnostic_tests.name']},
            {"entity_type": "imaging.date", "entity_object": None, "display_key": "Date", "role": ENTITY_ROLE.CHILD,
             'ner.keys': ['imaging.date', 'encounter.date', 'hospitalization.date']},
            {"entity_type": "reason", "entity_object": None, "display_key": "Reason", "role": ENTITY_ROLE.CHILD,
             'ner.keys': ['reason', 'reason_of_visit']},
            {"entity_type": "imaging.finding", "entity_object": None, "display_key": "Finding",
             "role": ENTITY_ROLE.CHILD, 'ner.keys': ['imaging.finding', 'diagnostic_tests.finding']},
            {"entity_type": "imaging.impression", "entity_object": None, "display_key": "Impression",
             "role": ENTITY_ROLE.CHILD, 'ner.keys': ['imaging.impression', 'diagnostic_tests.impression']},
            {"entity_type": "imaging.conclusion", "entity_object": None, "display_key": "Conclusion",
             "role": ENTITY_ROLE.CHILD, 'ner.keys': ['imaging.conclusion', 'diagnostic_tests.conclusion']},
            {"entity_type": const.GENERATED_ENTITY + "impairment", "entity_object": None, "display_key": "Impairment",
             "role": ENTITY_ROLE.CHILD, 'ner.keys': [const.GENERATED_ENTITY + "impairment"]}
        ]
    },

    Title.LIST_OF_EVENTS: {
        const.GROUPS: [
            {"Laboratory Visit": ["o.issued", "lab.issued", "lab.collected", "o.collected", "lab.received",
                                  "o.received"], Title.PERFORMER: ["lab.performer"]},
            {"Office Visit": ["encounter.date", "vital.date"], Title.PERFORMER: ["encounter.performer"]},
            {"Imagery": ["imaging.date"], Title.PERFORMER: ["imaging.performer"]},
            {"Procedure": ["procedure.date"], Title.PERFORMER: ["procedure.performer"]},
            {"Surgery": ["surgery.date"], Title.PERFORMER: ["surgery.performer"]},
            {"Pathology": ["pathology.date"], Title.PERFORMER: ["pathology.performer"]},
            {"Therapy": ["phystherapy.date"], Title.PERFORMER: ["phystherapy.perfomer"]}
        ],
        const.HEADERS: {
            'Event Type': [],
            'Date': ["o.issued", "lab.issued", "lab.collected", "o.collected", "lab.received", "o.received",
                     "encounter.date", "vital.date", "imaging.date", "procedure.date", "surgery.date", "pathology.date",
                     "phystherapy.date"],
            'Performer': ["lab.performer", "encounter.performer", "imaging.performer", "procedure.performer",
                          "surgery.performer", "pathology.performer", "phystherapy.perfomer"]
        }
    },

    Title.FAMILY_HISTORY: {
        const.GROUPS: [
            {"Family": ["family.relation.all"], "Disease": ["family.all", "family.all.disease"],
             const.AGE_AT_DIAG: ["family.all.disease.age", "family.all.age"],
             const.AGE_OF_DEATH: ["family.all.death.age"],
             "SNOMED": [""]},
            {"Family": ["family.relation.father"], "Disease": ["family.father", "family.father.disease"],
             const.AGE_AT_DIAG: ["family.father.disease.age", "family.father.age"],
             const.AGE_OF_DEATH: ["family.father.death.age"],
             "SNOMED": [""]},
            {"Family": ["family.relation.mother"], "Disease": ["family.mother", "family.mother.disease"],
             const.AGE_AT_DIAG: ["family.mother.disease.age", "family.mother.age"],
             const.AGE_OF_DEATH: ["family.mother.death.age"],
             "SNOMED": [""]},
            {"Family": ["family.relation.siblings"], "Disease": ["family.sibling", "family.siblings.disease"],
             const.AGE_AT_DIAG: ["family.siblings.disease.age", "family.sibling.age"],
             const.AGE_OF_DEATH: ["family.siblings.death.age"],
             "SNOMED": [""]},
            {"Family": ["family.relation.parents"], "Disease": ["family.parents", "family.parents.disease"],
             const.AGE_AT_DIAG: ["family.parents.disease.age", "family.parents.age"],
             const.AGE_OF_DEATH: ["family.parents.death.age"],
             "SNOMED": [""]},
            {"Family": ["family.relation.children"], "Disease": ["family.children", "family.children.disease"],
             const.AGE_AT_DIAG: ["family.children.disease.age", "family.children.age"],
             const.AGE_OF_DEATH: ["family.children.death.age"],
             "SNOMED": [""]}
        ],
        "Family_Diseases": {"All": ["family.all", "family.all.disease"],
                            "Father": ["family.father", "family.father.disease"],
                            "Mother": ["family.mother", "family.mother.disease"],
                            "Siblings": ["family.sibling", "family.siblings.disease"],
                            "Parents": ["family.parents", "family.parents.disease"],
                            "Children": ["family.children", "family.children.disease"],
                            },
        const.HEADERS: {
            'Family': [],
            'Diagnosis': [],
            const.AGE_AT_DIAG: [],
            const.AGE_OF_DEATH: [],
            "SNOMED": []
        }
    },

    Title.ENCOUNTER_DETAILS: {
        const.GROUPS: [
            {"Laboratory Visit": ["o.issued", "lab.issued", "lab.collected", "o.collected", "lab.received",
                                  "o.received"], Title.PERFORMER: ["lab.performer"],
             Title.DESCRIPTION: ["encounter.finding"], 'section': ['sect_assessment_plan', 'sect_hpi']},
            {"Office Visit": ["diag.date", "encounter.date"], Title.PERFORMER: ["encounter.performer"],
             Title.DESCRIPTION: ['assessment.diag', 'assessment', 'encounter.conclusion', 'history.diag',
                                 'diagnosis.history', 'hpi.reason', 'reason'],
             'section': ['sect_assessment_plan', 'sect_hpi', 'sect_medical_history', 'sect_assessment_plan']},
            {"Imagery": ["imaging.date"], Title.PERFORMER: ["imaging.performer"],
             Title.DESCRIPTION: ['imaging.finding', 'imaging.impression', 'imaging.conclusion', 'hpi.reason', 'reason'],
             'section': ['sect_findings', 'sect_impression']},
            {"Procedure": ["procedure.date"], Title.PERFORMER: ["procedure.performer"],
             Title.DESCRIPTION: ['encounter.finding'], 'section': ['sect_procedure']},
            {"Surgery": ["surgery.date"], Title.PERFORMER: ["surgery.performer"],
             Title.DESCRIPTION: ['surgery.finding', 'surgery.indications'], 'section': []},
            {"Pathology": ["pathology.date"], Title.PERFORMER: ["pathology.performer"],
             Title.DESCRIPTION: ['pathology.finding'], 'section': ['sect_pathology']},
            {"Therapy": ["phystherapy.date"], Title.PERFORMER: ["phystherapy.perfomer"],
             Title.DESCRIPTION: ['assessment.diag', 'assessment', 'history.diag', 'diagnosis.history'],
             'section': ['sect_assessment_plan', 'sect_hpi']}
        ],
        const.HEADERS: {
            'Date': ['encounter.date'],
            'Type': ['encounter.date'],
            'Department': ['encounter.performer'],
            'Description': ['encounter.finding']
        },
        "description_entities": [
            'history.diag', 'diagnosis.history', 'assessment.diag', 'assessment', 'encounter.conclusion',
            'hpi.symptom.positive', 'hpi.patient.symptom.positive',
            'reason', 'plan.surgery', 'plan.evaluation', 'hpi.reason']
    },

    Title.MEDICAL_EQUIPMENT: {
        "row": [
            {"entity_type": "equipment.date", "entity_object": None, "display_key": "Date", "is_container": False,
             "role": ENTITY_ROLE.MOTHER},
            {"entity_type": "medical.equipment", "entity_object": None, "display_key": const.EQUIPMENT,
             "is_container": False, "role": ENTITY_ROLE.CHILD}
        ],
        const.GROUPS: [
            {"date": ["equipment.date", "encounter.date"],
             const.EQUIPMENT: ["medical.equipment", "patient.medical.equipment", "medical.device"]}
        ],
        const.POSITIONS: {
            'date': {
                "level": [const.SAME_LINE, const.ABOVE_LINE, const.DEFAULT]
            },
        },
        const.HEADERS: {
            const.EQUIPMENT: ["medical.equipment"],
            "Date": ["equipment.date", "encounter.date"]}
    },

    Title.SOCIAL_HISTORY: {
        "row": [
            {"entity_type": "status", "entity_object": None, "display_key": "Status",
             "role": ENTITY_ROLE.REQUIRED_CHILD,
             'ner.keys': []},
            {"entity_type": "date", "entity_object": None, "display_key": "Date", "role": ENTITY_ROLE.REQUIRED_CHILD,
             'ner.keys': ['encounter.date']},
            {"entity_type": "description", "entity_object": None, "display_key": "Description",
             "role": ENTITY_ROLE.MOTHER,
             'ner.keys': ['social.smoking', 'insured_1.smoking', 'insured_2.smoking',
                          'social.alcohol', 'insured_1.alcohol', 'insured_2.alcohol',
                          'social.drugs', 'insured_1.drugs', 'insured_2.drugs',
                          'patient.smoking.status.yes', 'patient.smoking.status.no', 'patient.smoking.status.former',
                          'patient.smoking.status.passive',
                          'insured_1.smoking.status.yes', 'insured_1.smoking.status.no',
                          'insured_1.smoking.status.former', 'insured_1.smoking.status.passive',
                          'insured_2.smoking.status.yes', 'insured_2.smoking.status.no',
                          'insured_2.smoking.status.former', 'insured_2.smoking.status.passive',
                          'applicant.smoking.status.yes', 'applicant.smoking.status.no',
                          'applicant.smoking.status.former', 'applicant.smoking.status.passive',
                          'patient.tobacco.status.yes', 'patient.tobacco.status.no', 'patient.tobacco.status.former',
                          'insured_1.tobacco.status.yes', 'insured_1.tobacco.status.no',
                          'insured_1.tobacco.status.former',
                          'insured_2.tobacco.status.yes', 'insured_2.tobacco.status.no',
                          'insured_2.tobacco.status.former',
                          'applicant.tobacco.status.yes', 'applicant.tobacco.status.no',
                          'applicant.tobacco.status.former',
                          'patient.alcohol.status.yes', 'patient.alcohol.status.no', 'patient.alcohol.status.former',
                          'insured_1.alcohol.status.yes', 'insured_1.alcohol.status.no',
                          'insured_1.alcohol.status.former',
                          'insured_2.alcohol.status.yes', 'insured_2.alcohol.status.no',
                          'insured_2.alcohol.status.former',
                          'applicant.alcohol.status.yes', 'applicant.alcohol.status.no',
                          'applicant.alcohol.status.former',
                          'patient.drugs.status.yes', 'patient.drugs.status.no', 'patient.drugs.status.former',
                          'insured_1.drugs.status.yes', 'insured_1.drugs.status.no', 'insured_1.drugs.status.former',
                          'insured_2.drugs.status.yes', 'insured_2.drugs.status.no', 'insured_2.drugs.status.former',
                          'applicant.drugs.status.yes', 'applicant.drugs.status.no', 'applicant.drugs.status.former',
                          'patient.drugs.product', 'insured_1.drugs.product', 'insured_2.drugs.product',
                          'applicant.drugs.product',
                          'patient.marijuana.status.yes', 'patient.marijuana.status.no', 'patient.marijuana.status.details',
                          'applicant.marijuana.status.yes', 'applicant.marijuana.status.no', 'applicant.marijuana.status.details',
                          'insured_1.marijuana.status.yes', 'insured_1.marijuana.status.no', 'insured_1.marijuana.status.details',
                          'insured_2.marijuana.status.yes', 'insured_2.marijuana.status.no', 'insured_2.marijuana.status.details']},
            {"entity_type": "device", "entity_object": None, "display_key": "Device", "role": ENTITY_ROLE.CHILD,
             'ner.keys': ['patient.smoking.device', 'insured_1.smoking.device', 'insured_2.smoking.device',
                          'applicant.smoking.device',
                          'patient.tobacco.product', 'insured_1.tobacco.product', 'insured_2.tobacco.product',
                          'applicant.tobacco.product',
                          'patient.alcohol.beverage', 'insured_1.alcohol.beverage', 'insured_2.alcohol.beverage',
                          'applicant.alcohol.beverage',
                          'patient.drugs.product', 'insured_1.drugs.product', 'insured_2.drugs.product',
                          'applicant.drugs.product',
                          'patient.marijuana.consumption_method.smoking', 'patient.marijuana.consumption_method.vaporizing',
                          'patient.marijuana.consumption_method.edible', 'patient.marijuana.consumption_method.other',
                          'applicant.marijuana.consumption_method.smoking', 'applicant.marijuana.consumption_method.vaporizing',
                          'applicant.marijuana.consumption_method.edible', 'applicant.marijuana.consumption_method.other',
                          'insured_1.marijuana.consumption_method.smoking', 'insured_1.marijuana.consumption_method.vaporizing',
                          'insured_1.marijuana.consumption_method.edible', 'insured_1.marijuana.consumption_method.other',
                          'insured_2.marijuana.consumption_method.smoking', 'insured_2.marijuana.consumption_method.vaporizing',
                          'insured_2.marijuana.consumption_method.edible', 'insured_2.marijuana.consumption_method.other',
                          ]},
            {"entity_type": "quantity", "entity_object": None, "display_key": "Quantity", "role": ENTITY_ROLE.CHILD,
             "ner.keys": ['patient.smoking.quantity', 'insured_1.smoking.quantity', 'insured_2.smoking.quantity',
                          'insured_2.smoking.quantity',
                          'applicant.smoking.quantity', 'patient.tobacco.quantity', 'insured_1.tobacco.quantity',
                          'insured_2.tobacco.quantity',
                          'insured_2.tobacco.quantity', 'applicant.tobacco.quantity', 'patient.alcohol.quantity',
                          'insured_1.alcohol.quantity',
                          'insured_2.alcohol.quantity', 'insured_2.alcohol.quantity', 'applicant.alcohol.quantity',
                          'patient.drugs.quantity', 'insured_1.drugs.quantity', 'insured_2.drugs.quantity',
                          'insured_2.drugs.quantity', 'applicant.drugs.quantity',
                          'patient.marijuana.quantity', 'applicant.marijuana.quantity', 
                          'insured_1.marijuana.quantity', 'insured_2.marijuana.quantity'
                          ]},
            {"entity_type": "unit", "entity_object": None, "display_key": "Unit", "role": ENTITY_ROLE.CHILD,
             "ner.keys": ['patient.alcohol.quantity.unit', 'insured_1.alcohol.quantity.unit',
                          'insured_2.alcohol.quantity.unit',
                          'patient.marijuana.quantity.unit', 'applicant.marijuana.quantity.unit', 
                          'insured_1.marijuana.quantity.unit', 'insured_2.marijuana.quantity.unit'
                          ]},
            {"entity_type": "start_date", "entity_object": None, "display_key": "Start Date", "role": ENTITY_ROLE.CHILD,
             "ner.keys": ['patient.smoking.start_date', 'insured_1.smoking.start_date', 'insured_2.smoking.start_date',
                          'applicant.smoking.start_date',
                          'patient.tobacco.start_date', 'insured_1.tobacco.start_date', 'insured_2.tobacco.start_date',
                          'applicant.tobacco.start_date',
                          'patient.alcohol.start_date', 'insured_1.alcohol.start_date', 'insured_2.alcohol.start_date',
                          'applicant.alcohol.start_date',
                          'patient.drugs.start_date', 'insured_1.drugs.start_date', 'insured_2.drugs.start_date',
                          'applicant.drugs.start_date',
                          'patient.marijuana.start_date', 'applicant.marijuana.start_date',
                          'insured_1.marijuana.start_date', 'insured_2.marijuana.start_date'
                          ]},
            {"entity_type": "end_date", "entity_object": None, "display_key": "End Date", "role": ENTITY_ROLE.CHILD,
             "ner.keys": ['patient.smoking.end_date', 'insured_1.smoking.end_date', 'insured_2.smoking.end_date',
                          'applicant.smoking.end_date',
                          'patient.tobacco.end_date', 'insured_1.tobacco.end_date', 'insured_2.tobacco.end_date',
                          'applicant.tobacco.end_date',
                          'patient.alcohol.end_date', 'insured_1.alcohol.end_date', 'insured_2.alcohol.end_date',
                          'applicant.alcohol.end_date',
                          'patient.drugs.end_date', 'insured_1.drugs.end_date', 'insured_2.drugs.end_date',
                          'applicant.drugs.end_date', 
                          'patient.marijuana.end_date', 'applicant.marijuana.end_date', 
                          'insured_1.marijuana.end_date', 'insured_2.marijuana.end_date'
                          ]},
            {"entity_type": "duration", "entity_object": None, "display_key": "Duration", "role": ENTITY_ROLE.CHILD,
             "ner.keys": ['patient.smoking.duration', 'insured_1.smoking.duration', 'insured_2.smoking.duration',
                          'applicant.smoking.duration',
                          'patient.tobacco.duration', 'insured_1.tobacco.duration', 'insured_2.tobacco.duration',
                          'applicant.tobacco.duration',
                          'patient.alcohol.duration', 'insured_1.alcohol.duration', 'insured_2.alcohol.duration',
                          'applicant.alcohol.duration',
                          'patient.drugs.duration', 'insured_1.drugs.duration', 'insured_2.drugs.duration',
                          'applicant.drugs.duration',
                          'patient.drugs.duration', 'insured_1.drugs.duration', 'insured_2.drugs.duration',
                          'applicant.drugs.duration',
                          'patient.marijuana.duration', 'applicant.marijuana.duration',
                          'insured_1.marijuana.duration', 'insured_2.marijuana.duration'
                          ]},
            {"entity_type": "frequency", "entity_object": None, "display_key": "Frequency", "role": ENTITY_ROLE.CHILD,
             "ner.keys": ['patient.marijuana.frequency', 'applicant.marijuana.frequency',
                          'insured_1.marijuana.frequency', 'insured_2.marijuana.frequency',
                          ]},
            {"entity_type": "snomed", "entity_object": None, "display_key": "snomed", "role": ENTITY_ROLE.CHILD,
             "ner.keys": ['snomed']}
        ],
        const.HEADERS: {
            'Status': ['smoking.status', 'tobacco.status', 'alcohol.status', 'drugs.status', 'smoking', 'alcohol',
                       'drugs', 'marijuana.status'],
            'Date': ['vital.date', 'patient.date', 'encounter.date'],
            'Description': ['patient.smoking.status.yes', 'patient.smoking.status.no', 'patient.smoking.status.former',
                            'patient.smoking.status.passive',
                            'insured_1.smoking.status.yes', 'insured_1.smoking.status.no',
                            'insured_1.smoking.status.former', 'insured_1.smoking.status.passive',
                            'insured_2.smoking.status.yes', 'insured_2.smoking.status.no',
                            'insured_2.smoking.status.former', 'insured_2.smoking.status.passive',
                            'applicant.smoking.status.yes', 'applicant.smoking.status.no',
                            'applicant.smoking.status.former', 'applicant.smoking.status.passive',
                            'patient.smoking.device', 'insured_1.smoking.device', 'insured_2.smoking.device',
                            'applicant.smoking.device',
                            'patient.tobacco.status.yes', 'patient.tobacco.status.no', 'patient.tobacco.status.former',
                            'insured_1.tobacco.status.yes', 'insured_1.tobacco.status.no',
                            'insured_1.tobacco.status.former',
                            'insured_2.tobacco.status.yes', 'insured_2.tobacco.status.no',
                            'insured_2.tobacco.status.former',
                            'applicant.tobacco.status.yes', 'applicant.tobacco.status.no',
                            'applicant.tobacco.status.former',
                            'patient.tobacco.product', 'insured_1.tobacco.product', 'insured_2.tobacco.product',
                            'applicant.tobacco.product',
                            'patient.alcohol.status.yes', 'patient.alcohol.status.no', 'patient.alcohol.status.former',
                            'insured_1.alcohol.status.yes', 'insured_1.alcohol.status.no',
                            'insured_1.alcohol.status.former',
                            'insured_2.alcohol.status.yes', 'insured_2.alcohol.status.no',
                            'insured_2.alcohol.status.former',
                            'applicant.alcohol.status.yes', 'applicant.alcohol.status.no',
                            'applicant.alcohol.status.former',
                            'patient.alcohol.beverage', 'insured_1.alcohol.beverage', 'insured_2.alcohol.beverage',
                            'applicant.alcohol.beverage',
                            'patient.drugs.product', 'insured_1.drugs.product', 'insured_2.drugs.product',
                            'applicant.drugs.product',
                            'patient.drugs.status.yes', 'patient.drugs.status.no', 'patient.drugs.status.former',
                            'insured_1.drugs.status.yes', 'insured_1.drugs.status.no', 'insured_1.drugs.status.former',
                            'insured_2.drugs.status.yes', 'insured_2.drugs.status.no', 'insured_2.drugs.status.former',
                            'applicant.drugs.status.yes', 'applicant.drugs.status.no', 'applicant.drugs.status.former',
                            'patient.drugs.product', 'insured_1.drugs.product', 'insured_2.drugs.product',
                            'applicant.drugs.product',
                            'patient.marijuana.status.yes', 'patient.marijuana.status.no', 'patient.marijuana.status.details',
                            'applicant.marijuana.status.yes', 'applicant.marijuana.status.no', 'applicant.marijuana.status.details',
                            'insured_1.marijuana.status.yes', 'insured_1.marijuana.status.no', 'insured_1.marijuana.status.details',
                            'insured_2.marijuana.status.yes', 'insured_2.marijuana.status.no', 'insured_2.marijuana.status.details'
                            ],
            'Device': ['patient.smoking.device', 'insured_1.smoking.device', 'insured_2.smoking.device',
                       'applicant.smoking.device',
                       'patient.tobacco.product', 'insured_1.tobacco.product', 'insured_2.tobacco.product',
                       'applicant.tobacco.product',
                       'patient.alcohol.beverage', 'insured_1.alcohol.beverage', 'insured_2.alcohol.beverage',
                       'applicant.alcohol.beverage',
                       'patient.drugs.product', 'insured_1.drugs.product', 'insured_2.drugs.product',
                       'applicant.drugs.product',
                       'patient.marijuana.consumption_method.smoking', 'patient.marijuana.consumption_method.vaporizing',
                       'patient.marijuana.consumption_method.edible', 'patient.marijuana.consumption_method.other',
                       'applicant.marijuana.consumption_method.smoking', 'applicant.marijuana.consumption_method.vaporizing',
                       'applicant.marijuana.consumption_method.edible', 'applicant.marijuana.consumption_method.other',
                       'insured_1.marijuana.consumption_method.smoking', 'insured_1.marijuana.consumption_method.vaporizing',
                       'insured_1.marijuana.consumption_method.edible', 'insured_1.marijuana.consumption_method.other',
                       'insured_2.marijuana.consumption_method.smoking', 'insured_2.marijuana.consumption_method.vaporizing',
                       'insured_2.marijuana.consumption_method.edible', 'insured_2.marijuana.consumption_method.other',
                      ],
            'Quantity': ['patient.smoking.quantity', 'insured_1.smoking.quantity', 'insured_2.smoking.quantity',
                         'insured_2.smoking.quantity',
                         'applicant.smoking.quantity', 'patient.tobacco.quantity', 'insured_1.tobacco.quantity',
                         'insured_2.tobacco.quantity',
                         'insured_2.tobacco.quantity', 'applicant.tobacco.quantity', 'patient.alcohol.quantity',
                         'insured_1.alcohol.quantity', 'insured_2.alcohol.quantity',
                         'insured_2.alcohol.quantity', 'applicant.alcohol.quantity', 'patient.drugs.quantity',
                         'insured_1.drugs.quantity', 'insured_2.drugs.quantity',
                         'insured_2.drugs.quantity', 'applicant.drugs.quantity',
                         'patient.marijuana.quantity', 'applicant.marijuana.quantity', 
                         'insured_1.marijuana.quantity', 'insured_2.marijuana.quantity'
                         ],
            'Unit': ['patient.alcohol.quantity.unit', 'insured_1.alcohol.quantity.unit',
                     'insured_2.alcohol.quantity.unit',
                     'patient.marijuana.quantity.unit', 'applicant.marijuana.quantity.unit', 
                     'insured_1.marijuana.quantity.unit', 'insured_2.marijuana.quantity.unit'
                    ],
            'Start Date': ['patient.smoking.start_date', 'insured_1.smoking.start_date', 'insured_2.smoking.start_date',
                           'applicant.smoking.start_date',
                           'patient.tobacco.start_date', 'insured_1.tobacco.start_date', 'insured_2.tobacco.start_date',
                           'applicant.tobacco.start_date',
                           'patient.alcohol.start_date', 'insured_1.alcohol.start_date', 'insured_2.alcohol.start_date',
                           'applicant.alcohol.start_date',
                           'patient.drugs.start_date', 'insured_1.drugs.start_date', 'insured_2.drugs.start_date',
                           'applicant.drugs.start_date',
                           'patient.marijuana.start_date', 'applicant.marijuana.start_date',
                           'insured_1.marijuana.start_date', 'insured_2.marijuana.start_date'
                           ],
            'End Date': ['patient.smoking.start_date', 'insured_1.smoking.start_date', 'insured_2.smoking.start_date',
                         'applicant.smoking.start_date',
                         'patient.tobacco.start_date', 'insured_1.tobacco.start_date', 'insured_2.tobacco.start_date',
                         'applicant.tobacco.start_date',
                         'patient.alcohol.start_date', 'insured_1.alcohol.start_date', 'insured_2.alcohol.start_date',
                         'applicant.alcohol.start_date',
                         'patient.drugs.start_date', 'insured_1.drugs.start_date', 'insured_2.drugs.start_date',
                         'applicant.drugs.start_date',
                         'patient.marijuana.end_date', 'applicant.marijuana.end_date', 
                         'insured_1.marijuana.end_date', 'insured_2.marijuana.end_date'
                         ],
            'Duration': ['patient.smoking.duration', 'insured_1.smoking.duration', 'insured_2.smoking.duration',
                         'applicant.smoking.duration',
                         'patient.tobacco.duration', 'insured_1.tobacco.duration', 'insured_2.tobacco.duration',
                         'applicant.tobacco.duration',
                         'patient.alcohol.duration', 'insured_1.alcohol.duration', 'insured_2.alcohol.duration',
                         'applicant.alcohol.duration',
                         'patient.drugs.duration', 'insured_1.drugs.duration', 'insured_2.drugs.duration',
                         'applicant.drugs.duration',
                         'patient.drugs.duration', 'insured_1.drugs.duration', 'insured_2.drugs.duration',
                         'applicant.drugs.duration',
                         'patient.marijuana.duration', 'applicant.marijuana.duration',
                         'insured_1.marijuana.duration', 'insured_2.marijuana.duration'
                         ],
            'Frequency': ['patient.marijuana.frequency', 'applicant.marijuana.frequency',
                          'insured_1.marijuana.frequency', 'insured_2.marijuana.frequency',
                        ],
            'snomed': ['snomed'],
        },
        const.POSITIONS: {
            'Date': {
                "level": [const.SAME_LINE, const.ABOVE_LINE, const.DEFAULT]
            },
            'Description': {
                "level": [const.SAME_LINE_RIGHT_SIDE, const.ABOVE_LINE, const.DEFAULT]
            }
        }
    },

    Title.DENTAL_DETAILS: {
        const.GROUPS: [
            {'Type of Service': ['type_of_service'], 'Date of Service': ['encounter.date'],
             'Tooth Code': ['tooth_code'], 'Tooth Surfaces': ['tooth_surfaces'], 'COB Amount': ['cob.amount'],
             'Quantity': ['quantity'], 'Lab Charge': ['lab_charge'], 'Total Amount': ['total_amount'],
             'Procedure Code': ['procedure.code'], 'Tax': ['tax'], 'Units': ['units'], 'Patient Name': ['patient.name']}
        ],
        const.HEADERS: {
            'Type of Service': ['type_of_service'],
            'Date of Service': ['encounter.date'],
            'Tooth Code': ['tooth_code'],
            'Tooth Surfaces': ['tooth_surfaces'],
            'COB Amount': ['cob.amount'],
            'Quantity': ['quantity'],
            'Lab Charge': ['lab_charge'],
            'Total Amount': ['total_amount'],
            'Procedure Code': ['procedure.code'],
            'Tax': ['tax'],
            'Units': ['units'],
            'Patient Name': ['patient.name']
        },
    },

    Title.CLAIMS: {
        const.GROUPS: [
            {'Type of Service': ['type_of_service'], 'Date of Service': ['encounter.date'], 'Quantity': ['quantity'],
             'COB Amount': ['cob.amount'], 'Total Amount': ['total_amount'], 'Patient Name': ['patient.name'],
             'Doctor Name': ['doctor.name', 'ordering_doctor.name'], 'Tax': ['tax'], 'Units': ['units']}
        ],
        const.HEADERS: {
            'Type of Service': ['type_of_service'],
            'Date of Service': ['encounter.date'],
            'COB Amount': ['cob.amount'],
            'Quantity': ['quantity'],
            'Patient Name': ['patient.name'],
            'Total Amount': ['total_amount'],
            'Doctor Name': ['doctor.name', 'ordering_doctor.name'],
            'Tax': ['tax'],
            'Units': ['units']
        },
    },

    Title.RX_DETAILS: {
        const.GROUPS: [
            {'Type of Service': ['type_of_service'], 'Date of Service': ['encounter.date'], 'Fee': ['rx.fee'],
             'Cost': ['rx.cost'], 'DIN Number': ['rx.din'], 'Quantity': ['quantity'], 'COB Amount': ['cob.amount'],
             'Total Amount': ['total_amount'], 'Patient Name': ['patient.name'],
             'Doctor Name': ['doctor.name', 'ordering_doctor.name'],
             'Tax': ['tax'], 'Units': ['units']}
        ],
        const.HEADERS: {
            'Type of Service': ['type_of_service'],
            'Date of Service': ['encounter.date'],
            'Fee': ['rx.fee'],
            'Cost': ['rx.cost'],
            'DIN Number': ['rx.din'],
            'COB Amount': ['cob.amount'],
            'Quantity': ['quantity'],
            'Patient Name': ['patient.name'],
            'Total Amount': ['total_amount'],
            'Doctor Name': ['doctor.name', 'ordering_doctor.name'],
            'Tax': ['tax'],
            'Units': ['units']
        },
    },

    Title.PROVIDER_DETAILS: {
        const.GROUPS: [
            {'Doctor Name': ['doctor.name', 'ordering_doctor.name'],
             'Doctor Registration Number': ['doctor.license_number'],
             'Facility Name': ['encounter.performer', 'lab.performer'], 
             'Facility Address': ['encounter.address', 'lab.address']}
        ],
        const.HEADERS: {
            'Doctor Name': ['doctor.name', 'ordering_doctor.name'],
            'Doctor Registration Number': ['doctor.license_number', 'doctor.licence_number'],
            'Facility Name': ['encounter.performer', 'lab.performer'],
            'Facility Address': ['encounter.address', 'lab.address']
        }
    },

    Title.CLAIM_DETAIL: {
        Title.CLAIM_1: {
            # "Name": ['insured_1.name', 'patient.name'],
            "Policy Number": ["policy_number"],
            "Claim Number": ["claim_number", "company.claim_number"],
            "Policy Effective Date": ["policy.effect_date", "policy.start_date"],
            "Claimed Date of Loss": ["injury.date", "application.date"],
            # "Date of birth": ["insured_1.birth", "patient.birth"],
            # "Sum Insured": ["sum_insured"],
            # "Annuity": ["annuity"],
            # "Duration": ["duration"],
            # "Currency": ["currency"],
            # "Address": ["insured_1.address"],
            # "Zip Code": ["insured_1.zipcode", "insured_1.postal_code"],
            # "Product Type": ["product_type"],
            # "Occupation": ["insured_1.occupation", "social.occupation"],
            # "Occupational Duties": ["occupational.duties"],
            "Primary Diagnosis": ["diagnosis.primary", "assessment.diag"],
            "Autopsy Performed": ["autopsy.performed"]
        },
        Title.CLAIM_2: {
            "Name": ['insured_2.name', 'patient.name'],
            "Policy Number": ["policy_number"],
            "Claim Number": ["claim_number"],
            "Policy Effective Date": ["policy.effect_date"],
            "Claimed Date of Loss": ["injury.date", "application.date"],
            "Date of birth": ["insured_2.birth", "patient.birth"],
            "Sum Insured": ["sum_insured"],
            "Annuity": ["annuity"],
            "Duration": ["duration"],
            "Currency": ["currency"],
            "Address": ["insured_2.address"],
            "Zip Code": ["insured_2.zipcode", "insured_2.postal_code"],
            "Product Type": ["product_type"],
            "Occupation": ["insured_2.occupation", "social.occupation", "patient.occupation"],
            "Occupational Duties": ["occupational.duties"],
            "Primary Diagnosis": ["diagnosis.primary"],
            "Autopsy Performed": ["autopsy.performed"]
        },

        "insured_tech_list": ['Name', 'Date of birth', 'Occupation', "Product Type", "Annuity", "Sum Insured",
                              "Duration", "Currency", 'Address'],
        "claim_detail_list": ["policy_number", "claim_number", "policy.effect_date", "injury.date", "application.date",
                              "insured_1.zipcode",
                              "insured_2.zipcode", "insured_1.postal_code", "insured_2.postal_code",
                              "occupational.duties", "diagnosis.primary", "autopsy.performed"]

    },
    Title.SUBJECTIVE_DETAILS: {
        const.GROUPS: [{
            'Date': ['encounter.date'],
            'Subjective Findings': list(FIELD_ID_BLUEPRINTS['subjective_details'].keys())[2:]
        }
        ],
        const.HEADERS: {
            'Date': ['encounter.date'],
            'Subjective Findings': list(FIELD_ID_BLUEPRINTS['subjective_details'].keys())[2:]
        }
    },
    Title.APPLICATION_DETAILS: {
        "application_number": {
            "entity": None,
            "to_display": "External Application ID",
            "entity_type": "application.number",
            "entity_type_aliases": ["application.number"],
            "field_id": "application_details.application_number",
            "row": 0
        },
        "cedent_name": {
            "entity": None,
            "to_display": "Cedent",
            "entity_type": "company.name",
            "entity_type_aliases": ["company.name"],
            "field_id": "application_details.cedent_name",
            "row": 1
        },
        "application_id": {
            "entity": None,
            "to_display": "Client Company Policy #",
            "entity_type": "policy_number",
            "entity_type_aliases": ["policy_number", "policy.number"],
            "field_id": "application_details.application_id",
            "row": 2
        },
        "date_received": {
            "entity": None,
            "to_display": "Date Received",
            "entity_type": "application.date",
            "entity_type_aliases": ["application.date"],
            "field_id": "application_details.date_received",
            "row": 3
        },
        "company_underwriter": {
            "entity": None,
            "to_display": "Client Company Underwriter",
            "entity_type": "underwriter.name",
            "entity_type_aliases": ["underwriter.name", "email.from.name"],
            "field_id": "application_details.company_underwriter",
            "row": 4
        }

    },
    Title.DIAGNOSTIC_PROCEDURES: {
        const.GROUPS: [
            {"Office Visit": ["encounter.date"], Title.PERFORMER: ["encounter.performer"],
             Title.DESCRIPTION: ['assessment', 'encounter.conclusion']},
            {"Pathology": ["pathology.date", "pathology.collected", "pathology.received", "pathology.issued"],
             Title.PERFORMER: ["pathology.performer"], Title.DESCRIPTION: ['pathology.finding']}
        ],
        const.HEADERS: {
            'Date': ['encounter.date'],
            'Type': ['encounter.date'],
            'Department': ['encounter.performer'],
            'Description': ['assessment']
        }
    },
    Title.PROCEDURES: {
        const.GROUPS: [
            {'Procedure': ['plan.anesthesia', 'plan.lab',
                           'encounter.procedure'],
             'Type': [''], 'Visit Type': [''], 'SNOMED': ['snomed'], 'SNOMED Description': ['snomed.description'], 
             'CPT': ['cpt'], 'CPT Description': ['cpt'],
             'Visit Date': ['diag.date', 'encounter.date'],
             'Finding': ['encounter.finding'], 'Impairment': ['']},
            {'Procedure': ['plan.surgery', 'history.surgery', 'surgery.history', 'surgery.procedure'],
             'Type': [''], 'Visit Type': [''], 'SNOMED': ['snomed'], 'SNOMED Description': ['snomed.description'], 
             'CPT': ['cpt'], 'CPT Description': ['cpt'],
             'Visit Date': ['surgery.date', 'encounter.date'],
             'Finding': ['surgery.finding'], 'Impairment': ['']},
            {'Procedure': ['pathology.procedure'],
             'Type': [''], 'Visit Type': [''], 'SNOMED': ['snomed'], 'SNOMED Description': ['snomed.description'], 
             'CPT': ['cpt'], 'CPT Description': ['cpt'],
             'Visit Date': ['pathology.date', 'encounter.date', 'pathology.issued', 'pathology.collected'],
             'Finding': ['pathology.finding'], 'Impairment': ['']},
            {'Procedure': ['phytherapy.procedure', 'physiotherapy.procedure', 'plan.phytherapy', 'plan.physiotherapy'],
             'Type': [''], 'Visit Type': [''], 'SNOMED': ['snomed'], 'SNOMED Description': ['snomed.description'], 
             'CPT': ['cpt'], 'CPT Description': ['cpt'],
             'Visit Date': ['phytherapy.date', 'encounter.date', 'physiotherapy.date'],
             'Finding': ['assessment.phytherapy'], 'Impairment': ['']},
        ],
        const.HEADERS: {
            'Procedure': ['plan.anesthesia', 'plan.lab', 'plan.phytherapy', 'plan.physiotherapy', 'plan.surgery',
                          'history.surgery', 'surgery.history', 'surgery.procedure', 'encounter.procedure',
                          'pathology.procedure', 'phytherapy.procedure', 'physiotherapy.procedure'],
            'Type': [''],
            'Visit Type': [''],
            'SNOMED': ['snomed'],
            'SNOMED Description': ['snomed.description'],
            'CPT': ['cpt'],
            'CPT Description': ['cpt.description'],
            'Visit Date': ['encounter.date', 'diag.date', 'pathology.date', 'surgery.date',
                           'phytherapy.date', 'physiotherapy.date',
                           'pathology.issued', 'pathology.collected'],
            "Finding": ['encounter.finding', 'surgery.finding', 'pathology.finding', 'assessment.phytherapy'],
            'Impairment': ['']
        },
        const.POSITIONS: {
            'Visit Date': {
                "level": [const.SAME_LINE, const.ABOVE_LINE, const.DEFAULT]
            },
            'Finding': {
                "level": [const.SAME_LINE_RIGHT_SIDE, const.ABOVE_LINE]
            }
        },
        'procedure_types': {
            'Current': ['encounter.procedure', 'pathology.procedure', 'phytherapy.procedure', 'physiotherapy.procedure',
                        'surgery.procedure'
                        ],
            'Historical': ['history.surgery', 'surgery.history'],
            'Plan': ['plan.anesthesia', 'plan.lab', 'plan.phytherapy', 'plan.physiotherapy', 'plan.surgery']
        },
        'visit_types': {
            'Surgery': ['plan.anesthesia', 'plan.surgery', 'history.surgery', 'surgery.history', 'surgery.procedure'],
            'Pathology': ['pathology.procedure'],
            'Physical Therapy': ['phytherapy.procedure', 'plan.phytherapy', 'plan.physiotherapy', 'physiotherapy.procedure'],
            'Office Visit': ['encounter.procedure'],
            'Laboratory Test': ['plan.lab'],
        }
    },
    Title.TREATMENTS: {
        const.GROUPS: [
            {'Procedure': ['plan.anesthesia'],
             'Type': [''], 'Visit Type': [''], 'SNOMED': ['snomed'], 'SNOMED Description': ['snomed.description'], 
             'CPT': ['cpt'], 'CPT Description': ['cpt'],
             'Visit Date': ['diag.date', 'encounter.date'],
             'Finding': [], 'Impairment': ['']},
            {'Procedure': ['plan.surgery', 'history.surgery', 'surgery.history', 'surgery.procedure'],
             'Type': [''], 'Visit Type': [''], 'SNOMED': ['snomed'], 'SNOMED Description': ['snomed.description'], 
             'CPT': ['cpt'], 'CPT Description': ['cpt'],
             'Visit Date': ['surgery.date', 'encounter.date'],
             'Finding': ['surgery.finding'], 'Impairment': ['']},
            {'Procedure': ['pathology.procedure'],
             'Type': [''], 'Visit Type': [''], 'SNOMED': ['snomed'], 'SNOMED Description': ['snomed.description'], 
             'CPT': ['cpt'], 'CPT Description': ['cpt'],
             'Visit Date': ['pathology.date', 'encounter.date', 'pathology.issued', 'pathology.collected'],
             'Finding': ['pathology.finding'], 'Impairment': ['']},
            {'Procedure': ['phytherapy.procedure', 'physiotherapy.procedure', 'plan.phytherapy', 'plan.physiotherapy'],
             'Type': [''], 'Visit Type': [''], 'SNOMED': ['snomed'], 'SNOMED Description': ['snomed.description'], 
             'CPT': ['cpt'], 'CPT Description': ['cpt'],
             'Visit Date': ['phytherapy.date', 'encounter.date', 'physiotherapy.date'],
             'Finding': ['assessment.phytherapy'], 'Impairment': ['']},
        ],
        const.HEADERS: {
            'Procedure': ['plan.anesthesia', 'plan.phytherapy', 'plan.physiotherapy', 'plan.surgery',
                          'history.surgery', 'surgery.history', 'surgery.procedure',
                          'pathology.procedure', 'phytherapy.procedure', 'physiotherapy.procedure'],
            'Type': [''],
            'Visit Type': [''],
            'SNOMED': ['snomed'],
            'SNOMED Description': ['snomed.description'],
            'CPT': ['cpt'],
            'CPT Description': ['cpt.description'],
            'Visit Date': ['encounter.date', 'diag.date', 'pathology.date', 'surgery.date',
                           'phytherapy.date', 'physiotherapy.date',
                           'pathology.issued', 'pathology.collected'],
            "Finding": ['surgery.finding', 'pathology.finding', 'assessment.phytherapy'],
            'Impairment': ['']
        },
        const.POSITIONS: {
            'Visit Date': {
                "level": [const.SAME_LINE, const.ABOVE_LINE, const.DEFAULT]
            },
            'Finding': {
                "level": [const.SAME_LINE_RIGHT_SIDE, const.ABOVE_LINE]
            }
        },
        'procedure_types': {
            'Current': ['pathology.procedure', 'phytherapy.procedure', 'physiotherapy.procedure', 'surgery.procedure'],
            'Historical': ['history.surgery', 'surgery.history'],
            'Plan': ['plan.anesthesia', 'plan.phytherapy', 'plan.physiotherapy', 'plan.surgery']
        },
        'visit_types': {
            'Surgery': ['plan.anesthesia', 'plan.surgery', 'history.surgery', 'surgery.history', 'surgery.procedure'],
            'Pathology': ['pathology.procedure'],
            'Physical Therapy': ['phytherapy.procedure', 'plan.phytherapy', 'plan.physiotherapy', 'physiotherapy.procedure'],
        }
    },
    Title.ENCOUNTER_DX_DETAILS: {
        "row": [
            {"entity_type": "assessment.diag", "entity_object": None, "display_key": const.DIAGNOSIS,
             "role": ENTITY_ROLE.MOTHER,
             'ner.keys': ['diagnosis.history', 'pathology.diagnosis',
                          'diagnosis.primary', 'diagnosis.secondary', 'diagnosis.principal'
                          ]},
            {"entity_type": "icd10", "entity_object": None, "display_key": const.ICD10, "role": ENTITY_ROLE.CHILD,
             'ner.keys': ['icd10', const.GENERATED_ENTITY + const.ICD10]},
            {"entity_type": "icd_chapter", "entity_object": None, "display_key": "ICD Chapter",
             "role": ENTITY_ROLE.CHILD,
             'ner.keys': [const.GENERATED_ENTITY + "icd_chapter"]},
            {"entity_type": "visit.date", "entity_object": None, "display_key": const.VISIT_DATE,
             "role": ENTITY_ROLE.CHILD, 'ner.keys': ['patient.date', 'vital.date', 'encounter.date', 'pathology.date',
                                                     'assessment.date', 'patient.vital.date', 'lab.issued',
                                                     'lab.collected',
                                                     'lab.received', 'surgery.date']},
            {"entity_type": "diag.date", "entity_object": None, "display_key": const.DIAGNOSIS_DATE,
             "role": ENTITY_ROLE.CHILD, 'ner.keys': ['diag.date', 'diagnosis.date', 'diagnosis.history.date']},
            {"entity_type": "performer.name", "entity_object": None, "display_key": "Performer Name",
             "role": ENTITY_ROLE.CHILD, 'ner.keys': ['doctor.name']},
            {"entity_type": "performer.address", "entity_object": None, "display_key": 'Performer Address',
             "role": ENTITY_ROLE.CHILD, 'ner.keys': ['doctor.address']},
            {"entity_type": "SNOMED", "entity_object": None, "display_key": const.SNOMED,
             "role": ENTITY_ROLE.CHILD, 'ner.keys': [const.GENERATED_ENTITY + const.SNOMED, const.GENERATED_ENTITY + "snomed"]}
        ],
        const.RESTRICTED_SECTIONS: ["sect_immunizations", "sect_medication"],
        const.HEADERS: {
            const.DIAGNOSIS: ['assessment.diag'],
            const.TYPE: ['ignored'],
            const.ICD10: ['icd10'],
            const.SNOMED: ['ignored'],
            const.VISIT_DATE: ['visit.date'],
            const.DIAGNOSIS_DATE: ['diag.date'],
            "Reason of Visit": ['ignored'],
            "Performer Name": ['performer.name'],
            "Performer Address": ['performer.address']
        },
        'diagnosis_types': {
            'Current': ['pathology.diagnosis', 'diagnosis.primary', 'diagnosis.secondary', 'diagnosis.principal'],
            'Historical': ['history.diag', 'diagnosis.history'],
            'Pathology': ['pathology.diagnosis', 'pathology.finding', 'pathology.procedure']
        }
    },
    Title.ALLERGENS: {
        const.GROUPS: [
            {'Allergen': ['allergy.medication', 'allergen'],
             'Type': [''],
             'SNOMED': ['snomed'],
             'Visit Date': ['']}
        ],
        const.HEADERS: {
            'Allergen': ['allergy.medication', 'allergen'],
            'Type': [''],
            'SNOMED': ['snomed'],
            'Visit Date': ['']
        },
        const.POSITIONS: {
            'Visit Date': {
                "level": [const.SAME_LINE, const.ABOVE_LINE, const.DEFAULT]
            }
        },
        'allergen_types': {
            'Substance': ['allergen'],
            'Drug': ['allergy.medication']
        }
    },
    Title.IMMUNIZATIONS: {
        const.GROUPS: [
            {'Immunization': ['immunization.name'], 
            'Immunization Date': ['immunization.date'],
            'Rx Code': [const.GENERATED_ENTITY + 'rx_code'],
            'CVX Code': [const.GENERATED_ENTITY +'cvx_code']
            }
        ],
        const.HEADERS: {
            'Immunization': ['immunization.name'],
            'Immunization Date': ['immunization.date'],
            'Rx Code': [const.GENERATED_ENTITY + 'rx_code'],
            'CVX Code': [const.GENERATED_ENTITY +'cvx_code']
        },
        const.POSITIONS: {
            'Immunization Date': {
                "level": [const.SAME_LINE, const.ABOVE_LINE, const.DEFAULT]
            }
        }
    },
    Title.DIABETES: {
        const.GROUPS: [
            {
                'Risk Factor': ['o.name', 'diagnosis.primary', 'diagnosis.secondary', 'diagnosis.principal',
                                'diagnosis.history'],
                'Category': ['cards_generated_category'],
                'Medical Code': ['cards_generated_medical_code'],
                'Medical Code Type': ['cards_generated_medical_code_type'],
                'Findings': ['o.value', 'diagnosis.attribute'],
                'Visit Date': ['encounter.date', 'o.issued', 'lab.collected', 'lab.received', 'lab.issued',
                               'pathology.collected', 'pathology.received', 'pathology.issued', 'diagnosis.date']

            }
        ],
        const.HEADERS: {
            'Risk Factor': ['o.name', 'diagnosis.primary', 'diagnosis.secondary', 'diagnosis.principal',
                            'diagnosis.history'],
            'Category': ['cards_generated_category'],
            'Medical Code': ['cards_generated_medical_code'],
            'Medical Code Type': ['cards_generated_medical_code_type'],
            'Findings': ['o.value', 'diagnosis.attribute', 'lab.value'],
            'Visit Date': ['encounter.date', 'o.issued', 'lab.collected', 'lab.received', 'lab.issued',
                           'pathology.collected', 'pathology.received', 'pathology.issued']

        },
        const.POSITIONS: {
            'Findings': {
                "level": [const.SAME_LINE]
            },
            'Visit Date': {
                "level": [const.SAME_LINE, const.BELOW_LINE]
            }
        }
    },
    Title.CANCER: {
        const.GROUPS: [
            {
                'Risk Factor': ['cancer.diagnosis.grade', 'cancer.exam', 'cancer.surgery',
                                'cancer.stage', 'cancer.treatment', 'cancer.type', 'cancer.recurrence',
                                'cancer.history.type', 'encounter.procedure', 'treatment.name',
                                'imaging.exam', 'surgery.procedure',
                                'cards_generated_neoplasm'],
                'Category': ['cards_generated_category'],
                'Medical Code': ['cards_generated_medical_code'],
                'Medical Code Type': ['cards_generated_medical_code_type'],
                'Findings': ['diagnosis.attribute', 'cancer.findings', 'cancer.exam.findings'],
                'Visit Date': ['encounter.date',
                               'pathology.collected', 'pathology.received', 'pathology.issued', 'cancer.diagnosis.date',
                               'cancer.exam.date', 'cancer.surgery.date', 'cancer.treatment.date',
                               'cancer.recurrence.date', 'cancer.history.date', 'treatment.name'
                               'imaging.date', 'surgery.date']

            }
        ],
        const.HEADERS: {
            'Risk Factor': ['cancer.diagnosis.grade', 'cancer.exam', 'cancer.surgery',
                            'cancer.stage', 'cancer.treatment', 'cancer.type', 'cancer.recurrence',
                            'cancer.history.type', 'encounter.procedure', 'treatment.name',
                            'imaging.exam', 'surgery.procedure',
                            'cards_generated_neoplasm'],
            'Category': ['cards_generated_category'],
            'Medical Code': ['cards_generated_medical_code'],
            'Medical Code Type': ['cards_generated_medical_code_type'],
            'Findings': ['diagnosis.attribute', 'cancer.findings'],
            'Visit Date': ['encounter.date',
                           'pathology.collected', 'pathology.received', 'pathology.issued', 'cancer.diagnosis.date',
                           'cancer.exam.date', 'cancer.surgery.date', 'cancer.treatment.date', 'cancer.recurrence.date',
                           'imaging.date', 'surgery.date']

        },
        const.POSITIONS: {
            'Findings': {
                "level": [const.SAME_LINE]
            },
            'Visit Date': {
                "level": [const.SAME_LINE, const.BELOW_LINE]
            }
        }
    },
    Title.CARDIOVASCULAR: {
        const.GROUPS: [
            {
                'Risk Factor': ['cardio.diagnosis', 'cardio.exam', 'cardio.surgery',
                                'cardio.procedure', 'imaging.exam', 
                                'cards_generated_circulatory', 'cards_generated_heart'],
                'Category': ['cards_generated_category'],
                'Medical Code': ['cards_generated_medical_code'],
                'Medical Code Type': ['cards_generated_medical_code_type'],
                'Findings': ['o.value', 'diagnosis.attribute', 'cardio.findings', 'imaging.finding'],
                'Visit Date': ['encounter.date', 'pathology.collected', 'pathology.received', 'pathology.issued', 'cardio.diagnosis.date',
                               'cardio.exam.date', 'cardio.surgery.date', 'cardio.procedure.date',
                               'imaging.date']

            }
        ],
        const.HEADERS: {
            'Risk Factor': ['cardio.diagnosis', 'cardio.exam', 'cardio.surgery',
                            'cardio.procedure', 'imaging.exam', 
                            'cards_generated_circulatory', 'cards_generated_heart'],
            'Category': ['cards_generated_category'],
            'Medical Code': ['cards_generated_medical_code'],
            'Medical Code Type': ['cards_generated_medical_code_type'],
            'Findings': ['diagnosis.attribute', 'cardio.findings', 'imaging.finding'],
            'Visit Date': ['encounter.date', 'pathology.collected', 'pathology.received', 'pathology.issued', 'cardio.diagnosis.date',
                           'cardio.exam.date', 'cardio.surgery.date', 'cardio.procedure.date',
                           'imaging.date']

        },
        const.POSITIONS: {
            'Findings': {
                "level": [const.SAME_LINE]
            },
            'Visit Date': {
                "level": [const.SAME_LINE, const.BELOW_LINE]
            }
        }
    },
    Title.MENTAL_NERVOUS_DISORDER: {
        const.GROUPS: [
            {
                'Risk Factor': ['cardio.diagnosis', 'cardio.exam', 'cardio.surgery',
                                'cardio.procedure', 'cards_generated_mental', 'cards_generated_nervous'],
                'Category': ['cards_generated_category'],
                'Medical Code': ['cards_generated_medical_code'],
                'Medical Code Type': ['cards_generated_medical_code_type'],
                'Findings': ['diagnosis.attribute', 'cardio.findings', 'imaging.finding'],
                'Visit Date': ['encounter.date',
                               'pathology.collected', 'pathology.received', 'pathology.issued', 'cardio.diagnosis.date',
                               'cardio.exam.date', 'cardio.surgery.date', 'cardio.procedure.date']

            }
        ],
        const.HEADERS: {
            'Risk Factor': ['cardio.diagnosis', 'cardio.exam', 'cardio.surgery',
                            'cardio.procedure', 'cards_generated_mental', 'cards_generated_nervous'],
            'Category': ['cards_generated_category'],
            'Medical Code': ['cards_generated_medical_code'],
            'Medical Code Type': ['cards_generated_medical_code_type'],
            'Findings': ['diagnosis.attribute', 'cardio.findings', 'imaging.finding'],
            'Visit Date': ['encounter.date',
                           'pathology.collected', 'pathology.received', 'pathology.issued', 'cardio.diagnosis.date',
                           'cardio.exam.date', 'cardio.surgery.date', 'cardio.procedure.date']

        },
        const.POSITIONS: {
            'Findings': {
                "level": [const.SAME_LINE]
            },
            'Visit Date': {
                "level": [const.SAME_LINE, const.BELOW_LINE]
            }
        }
    },
    Title.BUILD: {
        const.GROUPS: [
            {
                'Risk Factor': ['cards_generated_build_height_header',
                                'cards_generated_build_weight_header', 'cards_generated_build_bmi_header'],
                'Category': ['cards_generated_category'],
                'Medical Code': ['cards_generated_medical_code'],
                'Medical Code Type': ['cards_generated_medical_code_type'],
                'Findings': ['diagnosis.attribute', 'cards_generated_build_height',
                             'cards_generated_build_weight', 'cards_generated_build_bmi'],
                'Visit Date': ['encounter.date',
                               'pathology.collected', 'pathology.received', 'pathology.issued']

            }
        ],
        const.HEADERS: {
                'Risk Factor': ['cards_generated_build_height_header',
                                'cards_generated_build_weight_header', 'cards_generated_build_bmi_header'],
                'Category': ['cards_generated_category'],
                'Medical Code': ['cards_generated_medical_code'],
                'Medical Code Type': ['cards_generated_medical_code_type'],
                'Findings': ['diagnosis.attribute', 'cards_generated_build_height',
                             'cards_generated_build_weight', 'cards_generated_build_bmi'],
                'Visit Date': ['encounter.date',
                               'pathology.collected', 'pathology.received', 'pathology.issued']

        },
        const.POSITIONS: {
            'Findings': {
                "level": [const.SAME_LINE]
            },
            'Visit Date': {
                "level": [const.SAME_LINE, const.BELOW_LINE]
            }
        }
    },
    Title.INFORMALS: {
        "row": [
            {"entity_type": "informals.criteria", "entity_object": None, "display_key": "Criteria", "role": ENTITY_ROLE.CHILD,
                "is_container": False,
                'ner.keys': ['criteria']},
            {"entity_type": "informals.value", "entity_object": None, "display_key": "Value", "role": ENTITY_ROLE.MOTHER,
                "is_container": False,
                'ner.keys': ['patient.age', 'insured_1.age', 'insured_2.age', 'applicant.age',
                        "social.smoking", "insured_1.smoking", "insured_2.smoking", "patient.smoking", 
                        "patient.smoking.status.yes", "patient.smoking.status.no", 
                        "patient.smoking.status.former", "patient.smoking.status.passive", 
                        "insured_1.smoking.status.yes", "insured_1.smoking.status.no", 
                        "insured_1.smoking.status.former", "insured_1.smoking.status.passive", 
                        "insured_2.smoking.status.yes", "insured_2.smoking.status.no", 
                        "insured_2.smoking.status.former", "insured_2.smoking.status.passive", 
                        "applicant.smoking.status.yes", "applicant.smoking.status.no", 
                        "applicant.smoking.status.former", "applicant.smoking.status.passive", 
                        "applicant.smoking.quantity", "applicant.smoking.device", "applicant.smoking.duration",
                        "patient.smoking.quantity", "patient.smoking.device", "patient.smoking.duration", 
                        "insured_1.smoking.quantity", "insured_1.smoking.device", "insured_1.smoking.duration", 
                        "insured_2.smoking.quantity", "insured_2.smoking.device", "insured_2.smoking.duration", 
                        "patient.tobacco.status.yes", "patient.tobacco.status.no", "patient.tobacco.status.former", 
                        "insured_1.tobacco.status.yes", "insured_1.tobacco.status.no", "insured_1.tobacco.status.former", 
                        "insured_2.tobacco.status.yes", "insured_2.tobacco.status.no", "insured_2.tobacco.status.former", 
                        "applicant.tobacco.status.yes", "applicant.tobacco.status.no", "applicant.tobacco.status.former", 
                        "patient.tobacco.quantity", "patient.tobacco.duration", "patient.tobacco.product", 
                        "insured_1.tobacco.quantity", "insured_1.tobacco.duration", "insured_1.tobacco.product", 
                        "insured_2.tobacco.quantity", "insured_2.tobacco.duration", "insured_2.tobacco.product", 
                        "applicant.tobacco.quantity", "applicant.tobacco.duration", "applicant.tobacco.product",
                        "vital.bmi", "patient.bmi", "insured_1.bmi", "insured_2.bmi",
                        "lab.value", "o.value",
                        'vital.blood', 'patient.blood', 'insured_1.blood', 'insured_2.blood',
                        'patient.blood_pressure', 'insured_1.blood_pressure', 'insured_2.blood_pressure']
            },
            {"entity_type": "informals.classification", "entity_object": None, "display_key": "Classification", "role": ENTITY_ROLE.CHILD,
                "is_container": False,
                'ner.keys': ['classification']},
        ]
    }
}

# ----------------------------------------------------------------------------------------------------------------------
CARD_MOTHER_FIELD_MAPPING = {
    'insured_details': "name",
    "technical_details": "product_type",
    "application_details": "application_number"
}

# ----------------------------------------------------------------------------------------------------------------------
CODIFICATION_INFO_MAPPING = {
    "height_in": {
        "category_unit": "feet",
        "validate_function": validate_height
    },
    "height_cm": {
        "category_unit": "meter",
        "validate_function": validate_height
    },
    "weight_lbs": {
        "category_unit": "pounds",
        "validate_function": validate_weight
    },
    "weight_kg": {
        "category_unit": "kg",
        "validate_function": validate_weight
    },
    "temperature_f": {
        "category_unit": "fahrenheit",
        "validate_function": validate_temperature
    },
    "temperature_c": {
        "category_unit": "celsius",
        "validate_function": validate_temperature
    },
    "date": {
        "category_unit": "date",
        "validate_function": validate
    },
    "birth": {
        "category_unit": "date",
        "validate_function": validate
    },
    "duration": {
        "category_unit": "duration",
        "validate_function": validate_duration
    }
}

DX_DATE_REASON_OF_VISTI_MAPPING = {
    "encounter.date": "Office Visit",
    "lab.issued": "Lab Visit"
}

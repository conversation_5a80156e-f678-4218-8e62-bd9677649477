import re
import sys
import uuid
from copy import deepcopy
from typing import List, Tuple

import constants as const
import edoc_utility
import insured_utils
import patient_dicts
import table_utils
import technical_dicts
import titles as Title
from dateutil.relativedelta import relativedelta
from elements.edocument import EDocument
from friendlylib.iterators import find, lfilter, lmap
from friendlylib.language import contains_indicators
from friendlylib.strings import (
    extract_float_from_string,
    get_prefix_by_delimiter,
    get_suffix_by_delimiter,
)
from miscellaneous import MiscUtility
from patient_dicts import german_product_types, insured_names_blueprint
from prettyprinter import cpprint
from utils.utils import (
    check_document_type_is_aps,
    extract_non_numerical_prefix,
    filter_dict_with_similar_keys_recursively,
)


class PatientUtility:
    def __init__(self):
        self.words_list = []
        self.misc = MiscUtility()
        self.retrieved_data = None
        self.entity_seen = {'insured-1': {"name": None, "address": None, "gender": None, "birth": None, "marital": None,
                                          "occupation": None, "occupation_duty": None, "occupation_risk": None, "weight": None, "height": None, "alcohol": None,
                                          "smoking": None, "drugs": None, "marijuana": None, "ssn": None, "insurance_rated_differentialy_or_declined": None, "filed_bankruptcy": None},
                            'insured-2': {"name": None, "address": None, "gender": None, "birth": None, "marital": None,
                                          "occupation": None, "occupation_duty": None, "occupation_risk": None, "weight": None, "height": None, "alcohol": None,
                                          "smoking": None, "drugs": None, "marijuana": None, "ssn": None, "insurance_rated_differentialy_or_declined": None, "filed_bankruptcy": None}}

    # -----------------------------------------------------------------

    def set_dependencies(self, obj_misc, retrieved_data, words_list: list):
        self.misc = obj_misc
        self.retrieved_data = retrieved_data
        self.words_list = words_list

    # -----------------------------------------------------------------------------------------------------------------------

    def predict_gender_type_from_prefix(self, prefix_text: str) -> str | None:
        if not prefix_text:
            return None
        prefix_text = prefix_text.lower().strip()
        if prefix_text in const.GERMAN_MALE_PREFIXES:
            return "Männlich"
        elif prefix_text in const.GERMAN_FEMALE_PREFIXES:
            return "Weiblich"
        elif prefix_text in const.ENGLISH_MALE_PREFIXES:
            return "Male"
        elif prefix_text in const.ENGLISH_FEMALE_PREFIXES:
            return "Female"
        else:
            return None

    def fabricate_gender_from_prefix(self, edoc_entities: List) -> None:
        prefix_entities = lfilter(
            lambda entity: get_suffix_by_delimiter(entity['type']) == 'prefix' and entity["text"] != "",
            edoc_entities)
        if len(prefix_entities) == 0:
            return
        for prefix_entity in prefix_entities:
            gender_type = self.predict_gender_type_from_prefix(prefix_entity["text"])
            if gender_type is None:
                continue
            new_gender_entity = deepcopy(prefix_entity)
            new_gender_entity.update({
                'type': f"{get_prefix_by_delimiter(prefix_entity['type'])}.gender",
                'id': str(uuid.uuid4()),
                'text': gender_type,
                'confidence': prefix_entity['confidence'] * 0.95,
                'codified_as': '',
                'codify': None
            })
            edoc_entities.append(new_gender_entity)

    def extract_insured_board(self,
                              edoc: EDocument,
                              used_ids: list,
                              insured_num: str,
                              card_status: dict):

        edoc_entities = edoc_utility.edoc_to_entities(edoc)
        self.fabricate_gender_from_prefix(edoc_entities)
        edoc_entities = edoc_utility.sort_by_page_class(
            "insured_details", 
            edoc_entities,
            self.retrieved_data.priorities,
            self.retrieved_data.page_classification_display_name_mapping)
        if insured_num == insured_names_blueprint[0]:
            insured_board = deepcopy(patient_dicts.empty_insured_1)
        elif insured_num == insured_names_blueprint[1]:
            insured_board = deepcopy(patient_dicts.empty_insured_2)

        if check_document_type_is_aps(self.retrieved_data.document_type):
            insured_board = deepcopy(patient_dicts.empty_patient)

        lst = ["insurance_rated_differentialy_or_declined", "filed_bankruptcy", "name", "name.given_name", "name.family_name", "address", "address.line", "address.city", "address.state", "address.zipcode", "address.country",
               "gender", "birth", "place_of_birth", "birth_state", "marital", "occupation", "weight", "height", "alcohol", "smoking", "drugs", "marijuana",
               "avocation", "travel", "behavior", "beneficiary_relationship", "ssn", "owner_relationship", "citizenship",
               "residentship", "license_number", "income", "license_state", "us_citizen", "sign_date", "sign_state",
               "client_company_assessment", "us_resident", "net_worth", "occupation_duty", "occupation_risk", "age", "policy_reason"]

        list_of_entities, page_density, used_ids = insured_utils.get_insured_pages_density(edoc_entities,
                                                                                           lst,
                                                                                           insured_board,
                                                                                           used_ids)
        list_of_entities = edoc_utility.sort_by_confidence(list_of_entities, 'confidence')
        for x in lst:
            this_entity, used_ids = insured_utils.fetch_insured_object(edoc_entities,
                                                                       insured_board[x]["entity_type_aliases"],
                                                                       used_ids)
            insured_board[x]["entity"] = this_entity
            self.entity_seen[insured_num][x] = this_entity
            if this_entity is not None:
                if insured_num == 'insured-2':
                    if None not in (self.entity_seen['insured-1']['name'], self.entity_seen['insured-2']['name']):
                        card_status[insured_num] = True
                else:
                    card_status[insured_num] = True
        return insured_board, used_ids, card_status

    # ---------------------------------------------------------------------------------------------------------------------

    def build_insured_card(self,
                           edoc,
                           card_status: dict,
                           used_ids: list,
                           insured_num: str):

        card_name = Title.INSURED_DETAILS
        insured_board, used_ids, card_status = self.extract_insured_board(edoc,
                                                                          used_ids,
                                                                          insured_num,
                                                                          card_status)
        insured_table = table_utils.key_value_board_to_table(insured_board, card_name)
        insured_card = table_utils.table_to_ui_card(insured_table, insured_num)
        return insured_card, card_status, used_ids

    # ---------------------------------------------------------------------------------------------------------------------

    def suppress_insured_card(self, insured_1_card, insured_2_card):
        insured_1_card, insured_2_card = self.suppress_second_similar_insured_card(insured_1_card, insured_2_card)
        insured_2_card = self.suppress_almost_empty_card(insured_2_card)
        return insured_1_card, insured_2_card

    # ---------------------------------------------------------------------------------------------------------------------

    def suppress_almost_empty_card(self, insured_card):
        if insured_card is None:
            return insured_card
        items_with_type_in_c1 = filter(
            lambda cell: (cell['row']['c'] == 1) and (cell['row']['value'][0]['entity_type'] is not None),
            insured_card['fields_list'])
        types_in_col_1 = lmap(lambda cell: cell['row']['value'][0]['entity_type'], items_with_type_in_c1)
        insured_tag_lst = [x for x in types_in_col_1 if 'insured' in x]  # suppress non insued tag card
        if len(insured_tag_lst) == 0:
            return None

        vital_entities = ['vital.height_in', 'vital.height_cm', 'patient.height_in', 'patient.height_cm',
                          'vital.weight_lbs', 'vital.weight_kg', 'patient.weight_lbs', 'patient.weight_kg']
        remaing_list = set(types_in_col_1) - set(vital_entities)
        if len(remaing_list) < 2:
            return None
        return insured_card

    # ---------------------------------------------------------------------------------------------------------------------

    def suppress_second_similar_insured_card(self, insured_card_1, insured_card_2):
        if (insured_card_1 is None) or (insured_card_2 is None):
            return insured_card_1, insured_card_2
        fields_list_1 = [self._find_text_from_words_list(x) for x in insured_card_1['fields_list'] if x['row']['c'] > 0]
        fields_list_2 = [self._find_text_from_words_list(x) for x in insured_card_2['fields_list'] if x['row']['c'] > 0]
        difference = set(fields_list_2) - set(fields_list_1)
        text = ''.join(list(difference))
        if len(text) == 0:
            return insured_card_1, None
        # same name
        if self.misc.match_score(fields_list_1[0], fields_list_2[0]) > 95.0:
            return insured_card_1, None
        if len(fields_list_1[1]) and self.misc.match_score(fields_list_1[1], fields_list_2[1]) > 99.0:
            return insured_card_1, None
        return insured_card_1, insured_card_2

        # ---------------------------------------------------------------------------------------------------------------------

    def fetch_patient_board(self, edoc):
        edoc_entities = edoc_utility.edoc_to_entities(edoc)
        patient_board = deepcopy(patient_dicts.empty_patient)
        used_ids = []

        lst = ["name", "name.prefix", "name.family_name", "name.suffix", "name.given_name", "address.line",
               "address.city", "address.state", "address.country", "address.zipcode", "gender",
               "birth", "birth_state", "marital", "occupation", "weight", "height", "avocation", "travel", "behavior",
               "beneficiary_relationship", "ssn", "owner_relationship", "citizenship", "residentship", "license_number",
               "income", "license_state", "us_citizen", "sign_date", "sign_state", "client_company_assessment",
               "us_resident", "net_worth", "occupation_duty", "occupation_risk", "age", "policy_reason"]

        lst2 = ["alcohol", "smoking", "drugs", "marijuana"]

        list_of_entities, page_density, used_ids = insured_utils.get_insured_pages_density(edoc_entities,
                                                                                           lst,
                                                                                           patient_board,
                                                                                           used_ids)
        for x in lst:
            this_entity, used_ids = insured_utils.fetch_insured_object(list_of_entities,
                                                                       patient_board[x]["entity_type_aliases"],
                                                                       used_ids)
            patient_board[x]["entity"] = this_entity

        for x in lst2:
            this_entity, used_ids = insured_utils.fetch_insured_object(edoc_entities,
                                                                       patient_board[x]["entity_type_aliases"],
                                                                       used_ids)
            patient_board[x]["entity"] = this_entity

        return patient_board

        # --------------------------------------------------------------------------------------------------------------

    def build_patient_card(self, edoc):
        patient_board = self.fetch_patient_board(edoc)
        patient_table = table_utils.key_value_board_to_table(patient_board, Title.INSURED_DETAILS)
        patient_card = table_utils.table_to_ui_card(patient_table)
        return patient_card

    # ------------------------------------------------------------------------------------------------------------------

    def _find_text_from_words_list(self, row_cell: dict):
        cell = row_cell['row']['value'][0]
        value_name = cell['value_name']
        if value_name is None:
            return const.EMPTY_STRING

        if value_name != '__auto__':
            return value_name

        abs_page = cell.get('abs_page', -1)
        page_num = cell.get('page_num', -1)
        claim_id = cell.get('claimid', '-1')
        word_ids = cell['word_ids']

        for page in [abs_page, page_num]:
            text_list = []
            for w in self.words_list:
                if (w['id'] in word_ids) and (w['page'] == page) and (claim_id == w['claimid']):
                    cell_text = w['text']
                    text_list.append(cell_text)
            if len(text_list):
                return ' '.join(text_list).strip().lower()

        return const.EMPTY_STRING

    # ------------------------------------------------------------------------------------------------------

    def _get_item_names(self):
        return list(map(lambda obj: obj['entity_type'], list(technical_dicts.empty_technical.values())))

    def _get_item_keys(self):
        return list(technical_dicts.empty_technical.keys())

    # ---------------------------------------------------------------------------------------------------------------------

    def _apply_restrictions_per_product_types(self, technical_board, used_ids: list):
        product_type = technical_board.get('product_type', {})
        marked_subscription_type = const.EMPTY_STRING

        if len(product_type) == 0 or product_type.get('entity') is None:
            return technical_board, used_ids, marked_subscription_type

        entity = product_type.get('entity')
        product_type_text = entity['text'].strip().lower()

        for subscription_type, list_of_product_types in german_product_types.items():
            list_of_product_types = [x.lower().strip() for x in list_of_product_types]
            german_product_types[subscription_type] = list_of_product_types

        best_score = -1
        for subscription_type, list_of_product_types in german_product_types.items():
            for pt in list_of_product_types:
                score = self.misc.match_score(pt, product_type_text)
                if score > best_score:
                    best_score = score
                    marked_subscription_type = subscription_type

        if (best_score < 70.0) or (marked_subscription_type == const.EMPTY_STRING):
            return technical_board, used_ids, marked_subscription_type

        possible_subscription_types = list(german_product_types.keys())
        possible_subscription_types = [x for x in possible_subscription_types if x != marked_subscription_type]

        removeable_obj = technical_board.get(possible_subscription_types[0], {})
        removeable_entity = removeable_obj.get('entity')
        if removeable_entity is not None:
            removeable_id = removeable_entity.get('id', const.EMPTY_STRING)
            if removeable_id in used_ids:
                used_ids.remove(removeable_id)
                print('removed', removeable_id)

            empty_obj = {
                "entity": None,
                "to_display": removeable_obj["to_display"],
                "entity_type": removeable_entity["type"],
                "row": removeable_obj['row']
            }
            technical_board[possible_subscription_types[0]] = empty_obj
        return technical_board, used_ids, marked_subscription_type

    # ---------------------------------------------------------------------------------------------------------------------

    def display_entities(self, list_of_entities):
        print('-' * 30)
        for obj in list_of_entities:
            print(
                f"page: {obj['abs_page']}: {obj['type']}  {obj['text']}   confidence:{obj['confidence']}  score:{obj.get('score', '')}")

    # ---------------------------------------------------------------------------------------------------------------------

    def re_order_by_product_1(self, list_of_entities):
        loe = []
        pages = []
        prod_type_pages = []
        prod_types_page_lines = []
        loe_pages = {}
        page_line_prod_types = {}
        policy_entity_types = technical_dicts.empty_technical["product_type"]["entity_type_aliases"]
        sum_insured_entity_types = technical_dicts.empty_technical["sum_insured"]["entity_type_aliases"]
        list_of_entities = sorted(list_of_entities, key=lambda x: (x['abs_page'], x['line_num'], x['left']))
        for obj in list_of_entities:
            abs_page = obj['abs_page']
            this_type = obj['type']
            line_num = obj['line_num']
            page_line = f'{abs_page}#{line_num}'
            if this_type in policy_entity_types:
                prod_type_pages.append(abs_page)
                prod_types_page_lines.append(page_line)
            if (page_line in prod_types_page_lines or
                    (this_type not in policy_entity_types and
                     prod_types_page_lines and
                     abs_page == int(prod_types_page_lines[-1].split('#')[0]) and
                     line_num > int(prod_types_page_lines[-1].split('#')[1]))):
                page_line_prod_types.setdefault(page_line if page_line in prod_types_page_lines else prod_types_page_lines[-1], []).append(obj)

            if this_type in sum_insured_entity_types and page_line not in prod_types_page_lines:
                prev_page_line = f'{abs_page}#{line_num - 1}'
                if page_line_prod_types.get(prev_page_line):
                    page_line_prod_types[page_line] = []  # deepcopy(page_line_prod_types[prev_page_line])
                    for ent in page_line_prod_types.get(prev_page_line):
                        if ent["type"] in sum_insured_entity_types:
                            page_line_prod_types[page_line].append(obj)
                        else:
                            page_line_prod_types[page_line].append(ent)

            pages.append(abs_page)
            loe_pages.setdefault(abs_page, []).append(this_type)

        prod_type_pages = list(set(prod_type_pages))
        prod_type_pages.sort()

        pages_with_prod_type = {}
        for abs_page, item_names in loe_pages.items():
            if abs_page not in prod_type_pages:
                continue

            item_names = list(set(item_names))
            pages_with_prod_type[abs_page] = len(item_names)
        lst = list(pages_with_prod_type.keys())

        prod_type_pages = lst

        pages = list(set(pages))
        pages.sort()
        for page_num in pages:
            for obj in list_of_entities:
                if obj['abs_page'] == page_num:
                    loe.append(obj)

        prod_type_pages.sort()
        if len(prod_type_pages):
            prod_type_pages.append(sys.maxsize)
        return loe, prod_type_pages, page_line_prod_types

    # ---------------------------------------------------------------------------------------------------------------------

    def prepare_list_of_entities(self, item_names, edoc):
        file_wise_entities = {}
        edoc_entities = edoc_utility.edoc_to_entities(edoc)
        list_of_filtered_entities = edoc_utility.sort_by_page_class(
            "technical_details",
            edoc_entities,
            self.retrieved_data.priorities,
            self.retrieved_data.page_classification_display_name_mapping)
        technical_board = deepcopy(technical_dicts.empty_technical)
        file_results = insured_utils.get_tech_pages_density(list_of_filtered_entities, item_names, technical_board, [])
        for file_id, file_data in file_results.items():
            list_of_entities, page_density = file_data
            list_of_entities, prod_type_pages, page_line_prod_types = self.re_order_by_product_1(list_of_entities)
            file_wise_entities[file_id] = (list_of_entities, prod_type_pages, technical_board, page_line_prod_types)

        return file_wise_entities

    # ---------------------------------------------------------------------------------------------------------------------
    def add_annuity_duration_type(self, technical_board):
        if technical_board["duration"]["entity"] is None:
            return
        technical_board["duration_type"]["entity"] = deepcopy(technical_board["duration"]["entity"])
        technical_board["duration_type"]["entity"]['id'] = str(uuid.uuid4())
        technical_board["duration_type"]["entity"]['type'] = 'duration.type'
        duration_entity_type = technical_board["duration"]["entity"]["type"]
        technical_board["duration_type"]["entity"]['text'] = technical_dicts.duration_annuity_type_dict[
            duration_entity_type]
        technical_board["duration_type"]["entity"]['codified_as'] = ''
        technical_board["duration_type"]["entity"]['codify'] = None

    def add_annuity_duration_payment_type(self, technical_board):
        if technical_board["annuity_duration"]["entity"] is None:
            return
        technical_board["annuity_duration_type"]["entity"] = deepcopy(technical_board["annuity_duration"]["entity"])
        technical_board["annuity_duration_type"]["entity"]['id'] = str(uuid.uuid4())
        technical_board["annuity_duration_type"]["entity"]['type'] = 'annuity_duration.type'
        duration_entity_type = technical_board["annuity_duration"]["entity"]["type"]
        technical_board["annuity_duration_type"]["entity"]['text'] = technical_dicts.annuity_duration_mapping[
            duration_entity_type]
        technical_board["annuity_duration_type"]["entity"]['codified_as'] = ''
        technical_board["annuity_duration_type"]["entity"]['codify'] = None

    def add_application_end_date(self, technical_board):
        application_end_date_entity = technical_board["application.end"]["entity"]
        if application_end_date_entity is not None:
            return

        application_date_entity = technical_board["application.date"]["entity"]
        duration_entity = technical_board["duration"]["entity"]
        duration_type_entity = technical_board["duration_type"]["entity"]
        if any(entity is None for entity in [application_date_entity, duration_entity, duration_type_entity]):
            return

        application_date = application_date_entity.get("python_codified_as")
        duration_type = duration_type_entity["text"].lower()
        duration = duration_entity["text"]
        duration = extract_float_from_string(duration)
        application_end_date = self.add_duration_to_date(application_date, duration, duration_type)
        if not application_end_date:
            return

        technical_board["application.end"]["entity"] = deepcopy(technical_board["application.date"]["entity"])
        technical_board["application.end"]["entity"]["id"] = str(uuid.uuid4())
        technical_board["application.end"]["entity"]["type"] = "application.end"
        technical_board["application.end"]["entity"]["text"] = str(application_end_date)
        technical_board["application.end"]["entity"]["codified_as"] = str(application_end_date)
        technical_board["application.end"]["entity"]["python_codified_as"] = application_end_date

    def update_currency_from_sum_insured(self, technical_board):
        sum_insured = technical_board["sum_insured"]["entity"]
        currency = technical_board["currency"]["entity"]
        if sum_insured is None or currency is not None or not extract_non_numerical_prefix(sum_insured['text']):
            return
        technical_board["currency"]["entity"] = deepcopy(sum_insured)
        technical_board["currency"]["entity"]['id'] = str(uuid.uuid4())
        technical_board["currency"]["entity"]['type'] = 'currency'
        technical_board["currency"]["entity"]['text'] = extract_non_numerical_prefix(sum_insured['text'])
        technical_board["currency"]["entity"]['codified_as'] = ''
        technical_board["currency"]["entity"]['codify'] = None

    def extract_technical_board(self, item_names, technical_board, list_of_entities, used_ids, product_type: list,
                                start_page, end_page):

        list_of_entities = edoc_utility.sort_by_page_class(
            "technical_details", 
            list_of_entities,
            self.retrieved_data.priorities,
            self.retrieved_data.page_classification_display_name_mapping)
        for x in item_names:
            this_entity, used_ids = insured_utils.fetch_technical_object(list_of_entities,
                                                                         technical_board[x]["entity_type_aliases"],
                                                                         used_ids,
                                                                         start_page, end_page)
            technical_board[x]["entity"] = this_entity
        self.add_annuity_duration_type(technical_board)
        self.add_annuity_duration_payment_type(technical_board)
        # self.add_application_end_date(technical_board)
        self.update_currency_from_sum_insured(technical_board)
        technical_board, used_ids, marked_subscription_type = self._apply_restrictions_per_product_types(
            technical_board, used_ids)
        return technical_board, used_ids, product_type, marked_subscription_type

    # ---------------------------------------------------------------------------------------------------------------------

    def _make_tech_card(self, item_names, technical_board, list_of_entities, used_ids, product_type, technical_num,
                        start_page, end_page):

        technical_board, used_ids, product_type, subscription_type = self.extract_technical_board(item_names,
                                                                                                  technical_board,
                                                                                                  list_of_entities,
                                                                                                  used_ids,
                                                                                                  product_type,
                                                                                                  start_page,
                                                                                                  end_page)
        technical_table = table_utils.key_value_board_to_table(technical_board, Title.TECHNICAL_DETAILS)
        this_card = table_utils.table_to_ui_card(technical_table, technical_num)
        return used_ids, product_type, this_card, technical_board, subscription_type

    def get_entity_type_from_aliases(self, technical_dict, entity):
        for entity_type, entity_data in technical_dict.items():
            if entity in entity_data['entity_type_aliases']:
                return entity_type

    def get_technical_sub_boards(self, tech_board, page_line_prod_types):
        technical_sub_boards = []
        for page_line, entities in page_line_prod_types.items():
            sorted_page_line_entities = edoc_utility.sort_by_page_class(
                'technical_details',
                entities,
                self.retrieved_data.priorities,
                self.retrieved_data.page_classification_display_name_mapping)
            if not sorted_page_line_entities:
                continue
            temp_tech_board = deepcopy(tech_board)
            for _type in ['duration', 'duration_type', 'sum_insured', 'currency', 'annuity', 'payment_period',
                          'annuity_duration', 'annuity_duration_type', 'annuity_end_date', 'policy_start_date',
                          'premium_amount', 'reinsurance_amount', 'risk_amount', 'indexation', 'deferment_period']:
                temp_tech_board[_type]['entity'] = None
            for entity in sorted_page_line_entities:
                default_entity_type = self.get_entity_type_from_aliases(technical_dicts.empty_technical, entity['type'])
                temp_tech_board[default_entity_type]['entity'] = entity
            self.add_annuity_duration_type(temp_tech_board)
            self.add_annuity_duration_payment_type(temp_tech_board)
            self.update_currency_from_sum_insured(temp_tech_board)
            technical_sub_boards.append(temp_tech_board)
        return technical_sub_boards

    def get_tech_cards_from_tech_boards(self, tech_board_details, subs_type):
        tech_card_num = 1
        technical_sub_cards, subscription_types = [], []
        for tech_board_details in tech_board_details:
            record_locations = tech_board_details['record_locations']
            tech_board = tech_board_details['item']
            technical_table = table_utils.key_value_board_to_table(tech_board, Title.TECHNICAL_DETAILS)
            tech_card = table_utils.table_to_ui_card(technical_table, f'technical-{tech_card_num}')
            tech_card['card']['record_locations'] = record_locations
            technical_sub_cards.append(tech_card)
            subscription_types.append(subs_type)
            tech_card_num += 1
        return technical_sub_cards, subscription_types

    def get_pages_with_multiple_prod_types(self, page_line_prod_types):
        freq_dict = {}
        for page_line, list_of_entities in page_line_prod_types.items():
            # if len(list_of_entities) < 2:
            #     continue
            page_num = int(page_line.split('#')[0])
            freq_dict[page_num] = freq_dict.get(page_num, 0) + 1

        pages_with_multiple_prod_types = [page_num for page_num, freq in freq_dict.items() if freq > 1]
        return set(pages_with_multiple_prod_types)

    # ---------------------------------------------------------------------------------------------------------------------

    def get_currency_list_from_sum_insured(self, sum_insured_entity_list):
        currency_list = []
        for sum_insured_entity in sum_insured_entity_list:
            if not extract_non_numerical_prefix(sum_insured_entity['text']):
                continue
            new_entity = deepcopy(sum_insured_entity)
            new_entity['id'] = str(uuid.uuid4())
            new_entity['text'] = extract_non_numerical_prefix(sum_insured_entity['text'])
            new_entity['type'] = 'currency'
            new_entity['codified_as'] = ''
            new_entity['codify'] = None
            currency_list.append(new_entity)
        return currency_list

    def update_product_name_from_product_type(self, tech_board_details):
        for tech_board in tech_board_details:
            if (tech_board['item'].get('plan_type') and tech_board['item']['plan_type']['entity'] is None and
                    tech_board['item'].get('product_type') and tech_board['item']['product_type']['entity'] is not None):
                tech_board['item']['plan_type']['entity'] = deepcopy(tech_board['item']['product_type']['entity'])
                tech_board['item']['plan_type']['entity']['type'] = 'policy.product.name'
                tech_board['item']['plan_type']['entity']['id'] = str(uuid.uuid4())
                tech_board['item']['plan_type']['entity']['metadata'].update({'card_generated': True})

    def update_currency_for_mumbai_uw(self, tech_board_details):
        for tech_board in tech_board_details:
            if tech_board['item'].get('currency') and tech_board['item']['currency']['entity'] is not None:
                tech_board['item']['currency']['entity']['text'] = 'INR'

    def create_tech_card_with_tpd(self, tech_board):
        tpd_tech_board = deepcopy(tech_board)
        tpd_tech_board['item']['product_type']['entity'] = deepcopy(tpd_tech_board['item']['plan_type']['entity'])
        tpd_tech_board['item']['product_type']['entity']['id'] = str(uuid.uuid4())
        tpd_tech_board['item']['product_type']['entity']['text'] = 'TPD'
        tpd_tech_board['item']['product_type']['entity']['type'] = 'policy.product.type'
        tpd_tech_board['item']['product_type']['entity']['metadata'].update({"card_generated": True})
        return tpd_tech_board

    def add_tech_card_for_tpd(self, tech_board_details):
        for tech_board in tech_board_details[:]:
            product_name_entity = tech_board['item']['plan_type']['entity']
            if product_name_entity and 'tpd' in product_name_entity['text'].lower():
                tpd_tech_board = self.create_tech_card_with_tpd(tech_board)
                tech_board_details.append(tpd_tech_board)

    def build_technical_card(self,
                             edoc,
                             used_ids: list,
                             technical_num: dict,
                             product_type,
                             field_wise_allowed_values):

        subs_type = ""
        tech_boards = []
        item_names = self._get_item_keys()
        currency_entity_final_list = []
        file_wise_entities = self.prepare_list_of_entities(item_names, edoc)
        for file_id, file_data in file_wise_entities.items():
            list_of_entities, prod_type_pages, technical_board, page_line_prod_types = file_data
            money_entity_list = lfilter(
                lambda entity: 'sum_insured' in entity['type']
                               or 'amount' in entity['type']
                               or 'income' in entity['type'],
                list_of_entities
            )
            currency_entity_list = self.get_currency_list_from_sum_insured(money_entity_list)
            currency_entity_final_list.extend(currency_entity_list)
            currency_entities = lfilter(
                lambda entity: 'currency' in entity['type'],
                list_of_entities
            )
            currency_entity_final_list.extend(currency_entities)
            pages_with_multiple_prod_types = self.get_pages_with_multiple_prod_types(page_line_prod_types)
            for ix in range(0, len(prod_type_pages) - 1):
                start_page, end_page = prod_type_pages[ix], prod_type_pages[ix + 1]
                used_ids, product_type, this_card, tech_board, subs_type = self._make_tech_card(item_names,
                                                                                                deepcopy(technical_board),
                                                                                                list_of_entities,
                                                                                                used_ids,
                                                                                                product_type,
                                                                                                f'technical-{prod_type_pages[ix]}',
                                                                                                start_page,
                                                                                                end_page)

                if prod_type_pages[ix] in pages_with_multiple_prod_types:
                    technical_sub_boards = self.get_technical_sub_boards(tech_board, page_line_prod_types)
                    tech_boards.extend(technical_sub_boards)
                else:
                    tech_boards.append(tech_board)

            policy_purpose_entity = edoc_utility.get_merged_policy_purpose_entity(list_of_entities)
            if policy_purpose_entity:
                tech_boards = self._assign_policy_purpose(tech_boards, policy_purpose_entity)

        sorted_currency_entities = edoc_utility.sort_by_page_class(
            'technical_details',
            currency_entity_final_list,
            self.retrieved_data.priorities,
            self.retrieved_data.page_classification_display_name_mapping)
        if len(sorted_currency_entities) > 0:
            tech_boards = self._assign_currency_to_missing_enitites(tech_boards, sorted_currency_entities[0])
        elif (self.retrieved_data.document_type and
              self.retrieved_data.document_type.lower() in technical_dicts.product_type_settings):
            entity_ = self._get_app_no_entity(tech_boards)
            if entity_:
                currency_entity = edoc_utility.get_new_entity(entity_, 'currency',
                                                              technical_dicts.product_type_settings[
                                                                  self.retrieved_data.document_type.lower()]['currency']
                                                              )
                tech_boards = self._assign_currency_to_missing_enitites(tech_boards, currency_entity)
        tech_boards = self._filter_tech_boards(tech_boards, field_wise_allowed_values)
        tech_board_details = self._remove_duplicate_tech_boards(tech_boards)
        if Title.USE_PRODUCT_NAME_FROM_TYPE in self.retrieved_data.feature:
            self.update_product_name_from_product_type(tech_board_details)

        if self.retrieved_data.document_type and 'mumbai underwriting' in self.retrieved_data.document_type.lower():
            self.update_currency_for_mumbai_uw(tech_board_details)

        if self.retrieved_data.document_type and 'cape town and johannesburg underwriting' in self.retrieved_data.document_type.lower():
            self.add_tech_card_for_tpd(tech_board_details)

        technical_cards, subscription_types = self.get_tech_cards_from_tech_boards(tech_board_details, subs_type)

        # technical_cards, subscription_types = self._remove_extra_similar_card(technical_cards, tech_boards, subscription_types)
        # technical_cards, subscription_types = self._diffuse_same_tech_cards(technical_cards, tech_boards, subscription_types)
        # technical_cards, subscription_types = self._suppress_second_empty_tech_card(technical_cards, tech_boards, subscription_types)
        if len(technical_cards) == 0 and file_wise_entities and not field_wise_allowed_values:
            tech_card_num = 1
            used_ids = []
            for file_id, file_data in file_wise_entities.items():
                list_of_entities, prod_type_pages, technical_board, page_line_prod_types = file_data
                used_ids, product_type, this_card, tech_b, subs_type = self._make_tech_card(item_names,
                                                                                            technical_board,
                                                                                            list_of_entities,
                                                                                            used_ids,
                                                                                            product_type,
                                                                                            f'technical-{tech_card_num}',
                                                                                            1,
                                                                                            sys.maxsize)
                if self.get_non_empty_board_element_count(tech_b) < 2 or tech_b['product_type']['entity'] is None:
                    continue
                subscription_types.append(subs_type)
                technical_cards.append(this_card)
                tech_card_num += 1
        if len(technical_cards) == 0:
            technical_board = deepcopy(technical_dicts.empty_technical)
            used_ids, product_type, this_card, _, subs_type = self._make_tech_card(item_names,
                                                                                   technical_board,
                                                                                   [],
                                                                                   used_ids,
                                                                                   product_type,
                                                                                   technical_num[0],
                                                                                   1,
                                                                                   sys.maxsize)
            subscription_types.append(subs_type)
            technical_cards.append(this_card)

        return technical_cards, used_ids, product_type

    # ------------------------------------------------------------------------------------------------------------------
    def remove_unwanted_chars(self, string):
        return re.sub(r'^[^a-zA-Z0-9]+|[^a-zA-Z0-9]+$', '', string)

    def get_non_empty_board_element_count(self, board):
        return len([ele for ele in board.values() if ele['entity'] is not None])

    def remove_whitespace_in_between(self, text):
        return re.sub(r'\s+', '', text)

    def _get_cleaned_entity_data(self, entity, remove_inbetween_whitespace=False):
        if entity.get('codified_as'):
            entity_data = entity.get('codified_as')
        else:
            entity_data = self.remove_unwanted_chars(entity.get('text', '')).lower()
            if remove_inbetween_whitespace:
                entity_data = self.remove_whitespace_in_between(entity_data)
            entity_data = str(float(entity_data)) if entity_data.isdecimal() else entity_data
        return entity_data

    def _remove_duplicate_tech_boards(self, tech_boards):
        final_tech_boards = []
        prod_type_entities_dict = {}
        for tech_board in tech_boards:
            product_type_entity = tech_board['product_type']['entity'] or {}
            sum_insured_entity = tech_board['sum_insured']['entity'] or {}
            premium_amount_entity = tech_board['premium_amount']['entity'] or {}
            duration_entity = tech_board['duration']['entity'] or {}
            annuity_entity = tech_board['annuity']['entity'] or {}
            annuity_duration_entity = tech_board['annuity_duration']['entity'] or {}
            annuity_end_date_entity = tech_board['annuity_end_date']['entity'] or {}
            if not product_type_entity:
                continue
            product_type = self.remove_unwanted_chars(product_type_entity.get('text', '')).lower()
            sum_insured = self._get_cleaned_entity_data(sum_insured_entity, remove_inbetween_whitespace=True)
            premium_amount = self._get_cleaned_entity_data(premium_amount_entity, remove_inbetween_whitespace=True)
            duration = self._get_cleaned_entity_data(duration_entity)
            annuity = self._get_cleaned_entity_data(annuity_entity, remove_inbetween_whitespace=True)
            annuity_duration = self._get_cleaned_entity_data(annuity_duration_entity)
            annuity_end_date = annuity_end_date_entity.get('codified_as') or annuity_end_date_entity.get('text', '').lower().strip()
            if product_type == '':
                continue
            key = (product_type, sum_insured, duration, annuity, annuity_duration, annuity_end_date, premium_amount)
            if prod_type_entities_dict.get(key):
                non_empty_entitities = {
                    item: data['item'][item]
                    for data in prod_type_entities_dict.values()
                    for item in data['item']
                    if data['item'][item]['entity'] is not None
                }
                tech_board_non_empty_entitities = {entity_label: entity_data for entity_label, entity_data in
                                                   tech_board.items() if entity_data.get('entity') is not None}
                # compares current tech board non empty entities count with the tech board with highest non empty entities count till now for a particular product type
                if len(tech_board_non_empty_entitities) > len(non_empty_entitities):
                    prod_type_entities_dict[key] = {'item': tech_board}
            else:
                prod_type_entities_dict[key] = {'item': tech_board}
            prod_type_entities_dict[key].setdefault('record_locations', []).append(f'{product_type_entity["claimid"]}${product_type_entity["abs_page"]}')

        filtered_prod_type_entities_dict = filter_dict_with_similar_keys_recursively(prod_type_entities_dict)

        for key, board in filtered_prod_type_entities_dict.items():
            if len([ele for ele in key if ele != '']) < 2:
                continue
            final_tech_boards.append(board)
        return final_tech_boards

    def _assign_currency_to_missing_enitites(self, tech_boards, currency_entity):
        for board in tech_boards:
            if board['currency']['entity'] is None:
                board['currency']['entity'] = currency_entity
        return tech_boards

    def _assign_policy_purpose(self, tech_boards, entity):
        for board in tech_boards:
            if board['policy_purpose']['entity'] is None:
                board['policy_purpose']['entity'] = entity
        return tech_boards

    def _get_app_no_entity(self, tech_boards):
        for board in tech_boards:
            if board['product_type']['entity'] is not None:
                return board['product_type']['entity']

    def _filter_tech_boards(self, tech_boards, field_wise_allowed_values):
        def is_valid_board(board):
            product_type_entity = board['product_type']['entity'] or {}
            product_type = self.remove_unwanted_chars(product_type_entity.get('text', '')).lower()
            for key, allowed_values_list in field_wise_allowed_values.items():
                entity = board.get(key, {}).get('entity')
                if entity and not contains_indicators(entity['text'], allowed_values_list):
                    return False
            for doc_type, product_type_settings_dict in technical_dicts.product_type_settings.items():
                if (self.retrieved_data.document_type and doc_type == self.retrieved_data.document_type.lower() and
                        product_type in product_type_settings_dict.get('disallowed_product_types', [])):
                    return False
            return True

        filtered_tech_boards = lfilter(is_valid_board, tech_boards)
        return filtered_tech_boards

    def _replace_cell_in_tech_card(self, technical_cards, ner_key, ix_first, ix_second):
        marked_cell = {}
        marked_row = -1
        found_cell = find(
            lambda cell: (cell['row']['c'] == 1) and (cell['row']['value'][0]['entity_type'] == ner_key),
            technical_cards[ix_first]['fields_list']
        )
        if found_cell:
            marked_cell = deepcopy(found_cell)
            marked_row = found_cell['row']['r']
        for i, cell_obj in enumerate(technical_cards[ix_second]['fields_list']):
            c = cell_obj['row']['c']
            if (c == 1) and (marked_row == cell_obj['row']['r']):
                technical_cards[ix_second]['fields_list'][i] = marked_cell
                return technical_cards
        return technical_cards

    # ---------------------------------------------------------------------------------------------------------------------

    def _populate_this_date_in_tech_card(self, extra_cards, tech_boards, ner_key, ix_first, ix_second):
        if (extra_cards[ix_first]) and (not extra_cards[ix_second]):
            first_date = self._get_tech_card_ner_value(tech_boards[ix_first], ner_key)
            second_date = self._get_tech_card_ner_value(tech_boards[ix_second], ner_key)
            if (first_date != const.EMPTY_STRING) and (second_date == const.EMPTY_STRING):
                tech_boards[ix_second][ner_key] = deepcopy(tech_boards[ix_first][ner_key])
                return True
        return False

        # ---------------------------------------------------------------------------------------------------------------------

    def _replace_tech_board(self, extra_cards, tech_boards, technical_cards, subscription_types, ix_first, ix_second):
        lst = ['application.date', 'application.end']
        del_card = False
        for ner_key in lst:
            if self._populate_this_date_in_tech_card(extra_cards, tech_boards, ner_key, ix_first, ix_second):
                technical_cards = self._replace_cell_in_tech_card(technical_cards, ner_key, ix_first, ix_second)
                del_card = True
        if del_card:
            technical_cards[ix_second]['card']['sub_card'] = Title.TECHNICAL_1
            return [technical_cards[ix_second]], [subscription_types[ix_second]]
        return technical_cards, subscription_types

    # ---------------------------------------------------------------------------------------------------------------------

    def _diffuse_same_tech_cards(self, technical_cards: list, tech_boards, subscription_types: list):
        if len(technical_cards) < 2:
            return technical_cards, subscription_types

        if not self._is_both_prod_type_matches(tech_boards):
            return technical_cards, subscription_types

        all_ner_types = deepcopy(list(tech_boards[0].keys()))
        all_ner_types.remove('product_type')
        dct = {0: {}}
        for ner_key in all_ner_types:
            dct[0][ner_key] = const.EMPTY_STRING
        dct[1] = deepcopy(dct[0])

        for ix in [0, 1]:
            for ner_key in all_ner_types:
                val = self._get_tech_card_ner_value(tech_boards[ix], ner_key)
                dct[ix][ner_key] = val
        lst = ['annuity', 'sum_insured', 'duration', 'currency']
        extra_cards = {0: False, 1: False}
        for ix in [0, 1]:
            empty_ners = [x for x in lst if dct[ix][x] == const.EMPTY_STRING]
            extra_cards[ix] = (len(empty_ners) == 4)
        technical_cards, subscription_types = self._replace_tech_board(extra_cards, tech_boards, technical_cards,
                                                                       subscription_types, 0, 1)
        if len(technical_cards) > 1:
            technical_cards, subscription_types = self._replace_tech_board(extra_cards, tech_boards, technical_cards,
                                                                           subscription_types, 1, 0)
        return technical_cards, subscription_types

    # ---------------------------------------------------------------------------------------------------------------------

    def _suppress_second_empty_tech_card(self, technical_cards: list, tech_boards, subscription_types: list) -> Tuple[List, List]:
        if len(technical_cards) < 2:
            return technical_cards, subscription_types

        entities = [(k, v) for k, v in tech_boards[1].items() if v['entity'] is not None]
        if len(entities):
            return technical_cards, subscription_types

        subscription_types = [subscription_types[0]]
        technical_cards = [technical_cards[0]]
        cpprint('Suppressed second empty technical card')
        return technical_cards, subscription_types

    # ---------------------------------------------------------------------------------------------------------------------

    def _get_tech_card_ner_value(self, tech_board, ner_key):
        try:
            return tech_board[ner_key]['entity']['text'].strip().lower()
        except Exception as _ex:
            return const.EMPTY_STRING

    # ---------------------------------------------------------------------------------------------------------------------

    def _is_both_prod_type_matches(self, tech_boards: list) -> bool:
        prod_1 = self._get_tech_card_ner_value(tech_boards[0], 'product_type')
        prod_2 = self._get_tech_card_ner_value(tech_boards[1], 'product_type')
        for sp in patient_dicts.german_similar_prod_names:
            if ((prod_1 in sp) or (sp in prod_1)) and ((prod_2 in sp) or (sp in prod_2)):
                return True
        score = self.misc.match_score(prod_1, prod_2)
        if score >= 95.0:
            return True
        return False

    # ---------------------------------------------------------------------------------------------------------------------

    def _remove_extra_similar_card(self, technical_cards: list, tech_boards, subscription_types: list) -> tuple:
        if len(technical_cards) < 2:
            return technical_cards, subscription_types

        if not self._is_both_prod_type_matches(tech_boards):
            return technical_cards, subscription_types

        all_ner_types = list(tech_boards[0].keys())
        sub_set = self._is_subset(0, 1, all_ner_types, tech_boards)
        if sub_set:
            technical_cards[1]['card']['sub_card'] = Title.TECHNICAL_1
            return [technical_cards[1]], [subscription_types[1]]
        else:
            return [technical_cards[0]], [subscription_types[0]]

    # ---------------------------------------------------------------------------------------------------------------------

    def _is_subset(self, ix_1, ix_2, all_ner_types, tech_boards) -> bool:
        for i in range(1, len(all_ner_types)):
            ner_type = all_ner_types[i]
            card_1_entity = tech_boards[ix_1][ner_type]['entity']
            card_2_entity = tech_boards[ix_2][ner_type]['entity']
            if (card_1_entity is None) and (card_2_entity is not None):
                continue
            if (card_2_entity is None) and (card_1_entity is not None):
                return False
            if (card_1_entity is None) and (card_2_entity is None):
                continue

            score = self.misc.match_score(card_1_entity['text'], card_2_entity['text'])
            if score < 96.0:
                return False

        return True

    def add_duration_to_date(self, date_obj, duration, duration_type):
        end_date_obj = None
        end_date = None

        if duration is None or not date_obj:
            return end_date
        if duration_type == "years":
            end_date_obj = date_obj + relativedelta(years=int(duration))
        elif duration_type == "months":
            end_date_obj = date_obj + relativedelta(months=int(duration))

        if end_date_obj is not None:
            end_date = end_date_obj.date()

        return end_date

import time
import traceback
from copy import deepcopy
from typing import Any, Dict, List
from uuid import uuid4

from friendlylib.iterators import flatten, lfilter
from prettyprinter import cpprint

import constants as const
import titles as Title
from blueprints import ENTITY_ROLE
from elements.edocument import EDocument
from lab_test import LabTest
from medical_codes.card_reference import CardCodingReference
from medical_codes.medical_coding_utility import MedicalCodingUtility
from table_utils import table_to_ui_card
from friendlylib import iterators

from utils.open_ai import OpenAIRequestHandler


class ObservationUtility:
    def __init__(self):
        self.misc = object
        self.medical_coding_service: MedicalCodingUtility = object
        self.retrieved_data = None
        self.insurer_pages = {}
        self.obj_lab_test = LabTest()
        self.card_references: CardCodingReference = object

    # -----------------------------------------------------------------------------

    def set_dependencies(self, objMisc, medical_coding_service: MedicalCodingUtility, retrieved_data, insurer_pages, card_references):
        self.misc = objMisc
        self.medical_coding_service: MedicalCodingUtility = medical_coding_service
        self.retrieved_data = retrieved_data
        self.insurer_pages = insurer_pages
        self.card_references = card_references

    # -----------------------------------------------------------------------------

    def _is_multiple_insurer(self):
        return False
        # return len(self.insurer_pages) > 1

    # -----------------------------------------------------------------------------

    def split_list_of_observations(self, list_of_observations):
        insurer_list = list(set(list(self.insurer_pages.values())))
        insurer_list.sort()
        insurer_obs_lists = {insurer: [] for insurer in insurer_list}

        current_insured_type = 'insured_1.name'
        for row in list_of_observations:
            abs_page = row[0]['entity_object']['abs_page']
            insurer_type = self.insurer_pages.get(abs_page, const.EMPTY_STRING)
            if insurer_type == const.EMPTY_STRING:
                insurer_type = current_insured_type
            insurer_obs_lists[insurer_type].append(row)
            current_insured_type = insurer_type

        cpprint(insurer_list)
        return insurer_obs_lists

    # -----------------------------------------------------------------------------

    def get_lab_tests_list(self, list_of_obs):
        lab_tests_list = []
        for row in list_of_obs:
            if not row[0].get('entity_object'):
                continue
            lab_tests_list.append(row[0]['entity_object']['text'])
        return list(set(lab_tests_list))

    def _update_procedures_with_impairment(self, list_of_obs, impairment_dict, procedure_impairment_mapping):
        for row in list_of_obs:
            if not row[0].get('entity_object'):
                continue

            impairment = procedure_impairment_mapping.get(row[0]['entity_object']['text'])
            if not impairment_dict.get(impairment):
                continue
            row[13]['entity_object'] = impairment_dict[impairment]

    def get_impairment_list(self, edoc):
        impairment_dict = {}
        for page_of_lines in edoc:
            page_of_entities = flatten(page_of_lines)
            impairment_entity_list = lfilter(
                lambda entity_: entity_['type'] == 'cards_generated_impairment',
                page_of_entities
            )
            impairment_dict.update({ent_['text']: ent_ for ent_ in impairment_entity_list})
        return impairment_dict

    def _get_procedure_impairment_mapping_using_openai(self, lab_tests_list, impairments_list):

        input_prompt = f"""
        I have two lists:

        Procedures: A list of laboratory tests. {lab_tests_list}

        Impairments: A list of medical impairments. {impairments_list}

        Your task is to associate one most likely impairment with each lab tests based on medical knowledge, 
        common associations, and clinical relevance.

        Input:
        Lab_Tests: [<list of lab tests>]
        Impairments: [<list of impairments>]

        Output format:
        Return the result as a JSON object, where each key is a lab test name (exactly as provided) and the value is 
        the most likely impairment (exactly as provided).
        """

        try:
            start_time = time.time()

            print("Initializing OpenAI handler...")
            openai_handler = OpenAIRequestHandler()
            openai_handler.initialize()

            print("Sending request to OpenAI...")
            response = openai_handler.get_openai_response(input_prompt, encoded_image_list=[])

            if response:
                print("Processing response...")
                return response

            end_time = time.time()
            print(f"Total Time taken for OpenAI request: {end_time - start_time:.2f} seconds")

        except Exception as e:
            print(f"An error occurred during OpenAI request: {e}")

        return {}

    def get_card(self, edoc: EDocument, failed_cards_list: List[str]):
        try:
            card_name = Title.LABORATORY_RESULTS
            self.obj_lab_test.set_dependencies(self.misc,
                                                self.medical_coding_service,
                                                self.retrieved_data)
            list_of_observations_raw = self.obj_lab_test.extract_lab_test_rows(edoc, card_name)
            list_of_observations = self.correct_lab_rating(list_of_observations_raw)
            self.card_references.match_cell_codings(list_of_observations, ['LOINC'], 'Name', 'Value', ['Date'], True)
            self.card_references.generate_status_loinc_based(list_of_observations, edoc)

            lab_tests_list = self.get_lab_tests_list(list_of_observations)
            impairment_dict = self.get_impairment_list(edoc)
            lab_tests_impairment_mapping = self._get_procedure_impairment_mapping_using_openai(lab_tests_list, list(
                impairment_dict.keys()))
            self._update_procedures_with_impairment(list_of_observations, impairment_dict, lab_tests_impairment_mapping)
            insurer_obs_lists = {}
            if self._is_multiple_insurer():
                insurer_obs_lists = self.split_list_of_observations(list_of_observations)
            else:
                insurer_obs_lists['single'] = list_of_observations
            all_observations_cards = []

            obs_sub_card_names = {'insured_1.name': Title.LAB_OBS_SUB_CARD_1, 'insured_2.name': Title.LAB_OBS_SUB_CARD_2}

            for insurer_name, list_of_observations in insurer_obs_lists.items():
                obs_sub_card_name = obs_sub_card_names.get(insurer_name, const.EMPTY_STRING)
                observations_table = self.obj_lab_test.lab_rows_to_table(list_of_observations, card_name)
                this_obs_card = table_to_ui_card(observations_table, obs_sub_card_name)
                all_observations_cards.append(this_obs_card)
            return all_observations_cards
        except Exception as e:
            print(f'ERROR: Laboratory Results Card Creation Failed: {e}')
            failed_cards_list.append({'card_name':'Laboratory Results', 'message': f'{traceback.format_exc()}'})
            return []

    # -----------------------------------------------------------------------------

    def _get_value_from_row(self, row: List[Dict[str, Any]], entity_types: List[str]) -> Dict[str, Any]:
        match = iterators.find(
            lambda cell: cell["entity_type"] in entity_types
            and cell["entity_object"] is not None,
            row,
        )
        if match:
            return match["entity_object"]["text"], match, row.index(match)

        match = iterators.find(
            lambda cell: cell["entity_type"] in entity_types
            and cell["entity_object"] is None,
            row,
        )
        if match:
            return None, match, row.index(match)

        default_cell = {
            "key": "rating",
            "entity_type": "o.rating",
            "entity_object": None,
            "display_key": "Rating",
            "is_container": False,
            "role": 2,
        }

        return None, default_cell, -1 
    
    # -----------------------------------------------------------------------------

    def correct_lab_rating(self, obsrows):
        obs_rows = deepcopy(obsrows)
        for row_ix, r in enumerate(obs_rows):
            entity_rating, rating_cell, rating_ix = self._get_value_from_row(r, ["o.rating", "lab.rating"])
            lab_test_value, value_cell, value_cell_ix = self._get_value_from_row(r, ["o.value", "lab.value"])
            descriptive_ref, dr_cell, dr_cell_ix = self._get_value_from_row(r, ["lab.ref", "o.ref"])
            low_ref, low_ref_cell, low_ref_cell_ix = self._get_value_from_row(r, ["o.ref_low", "lab.ref_low"])
            high_ref, high_ref_cell, high_ref_cell_ix = self._get_value_from_row(r, ["o.ref_high", "lab.ref_high"])

            rating = self.misc.set_rating_flag(entity_rating, lab_test_value, low_ref, high_ref)
                
            if not rating:
                continue
            
            if (entity_rating or '') != rating:
                if entity_rating:
                    obs_rows = self.fabricate_rating_cell(obs_rows, row_ix, rating_ix, rating_cell, rating)
                elif value_cell is not None:
                    obs_rows = self.fabricate_rating_cell(obs_rows, row_ix, rating_ix, value_cell, rating)
                elif low_ref_cell is not None:
                    obs_rows = self.fabricate_rating_cell(obs_rows, row_ix, rating_ix, low_ref_cell, rating)
                elif high_ref_cell is not None:
                    obs_rows = self.fabricate_rating_cell(obs_rows, row_ix, rating_ix, high_ref_cell, rating)
                elif dr_cell is not None:
                    obs_rows = self.fabricate_rating_cell(obs_rows, row_ix, rating_ix, dr_cell, rating)

        return obs_rows

    # -----------------------------------------------------------------------------

    def fabricate_rating_cell(self, obs_rows, row_ix, cell_ix, cell, rating):
        rating_cell = deepcopy(cell)
        rating_cell["entity_object"]["text"] = rating
        rating_cell["entity_object"]["type"] = "lab.rating"
        rating_cell["entity_object"]["codified_as"] = rating
        rating_cell["entity_object"]["codification_category"] = ""
        rating_cell["entity_object"]["codify"] = None
        rating_cell["entity_object"]["id"] = str(uuid4())
        rating_cell["entity_object"]["metadata"].update({'card_generated': True})
        rating_cell["key"] = "rating"
        rating_cell["entity_type"] = "lab.rating"
        rating_cell["display_key"] = "Rating"
        rating_cell["is_container"] = False
        rating_cell["role"] = ENTITY_ROLE.CHILD
        if cell_ix > -1:
            obs_rows[row_ix][cell_ix] = rating_cell
        else:
            obs_rows[row_ix].append(rating_cell)
        return obs_rows

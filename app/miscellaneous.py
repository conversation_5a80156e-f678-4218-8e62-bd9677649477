#!/usr/bin/env python
# encoding: utf-8
"""
    miscellaneous.py
"""
import datetime
import inspect
import json
import math
import re
import uuid
from copy import deepcopy
from operator import methodcaller
from typing import Tuple, Dict, List

from friendlylib.iterators import lmap, flatten, remove_redundant_items_from_ordered_list, lfilter, get_first
from prettyprinter import cpprint
from rapidfuzz import fuzz

import constants as const
import table_utils
import ui_output_dicts
from blueprints import BLUEPRINTS
from cards_schemas import card_schemas
from elements.edocument import EDocument, EPageData, EPageLine, Entity
from field_id_entity_type_mapping import FIELD_ID_BLUEPRINTS
from utils import dates_utility


# ------------------------------------------------------

class MiscUtility:
    def __init__(self):
        """   Constructor """
        self.date_ners = [
            'date',
            'insured_1.birth',
            'insured_2.birth',
            'lab.issued',
            'o.issued',
            'lab.collected',
            'o.collected',
            'lab.received',
            'o.received',
            'patient.birth',
            'medication.start',
            'medication.end',
            'application.date',
            'application.end'

        ]

    @property
    def inspection(self):
        return self.__inspection

    @inspection.setter
    def inspection(self, val):
        self.__inspection = val

    # ------------------------------------------------------------------------------------------------------------------

    def _get_date_stamp(self):
        today = datetime.datetime.now()
        date_stamp = str(today.hour).zfill(2)
        date_stamp += '__' + str(today.minute).zfill(2)
        date_stamp += '__' + str(today.second).zfill(2)
        date_stamp += '__' + str(today.microsecond)[0:2]
        return date_stamp

    # ------------------------------------------------------

    def track_error(self, obj: list, message: str = '-'):
        """[debug and finds a specific element in passing object list]
        Args:
            obj (list): [description]
            message (str, optional): [dump this object into json for inspection purpose only]. Defaults to const.EMPTY_STRING.
        """
        try:
            frm = inspect.stack()[1]
            module_name = inspect.getmodule(frm[0]).__name__
            curframe = inspect.currentframe()
            calframe = inspect.getouterframes(curframe, 2)
            caller_func = calframe[1][3]
            caller_entity = f"{module_name}-{caller_func}"
            file_name = f"../logs/{message}.json"
            with open(file_name, 'w') as f:
                json.dump(obj, f, indent=4, separators=(',', ': '))
                cpprint(f"{caller_entity} created {message}.json")
        except Exception as ex:
            print(ex)

    # ------------------------------------------------------

    def is_date_ner(self, ner_key: str) -> bool:
        key = ner_key.lower()
        return any([x for x in self.date_ners if x in key])

    # ------------------------------------------------------

    def _populate_edoc_pages(self, pages: dict, cells_with_ners: list):
        for cell in cells_with_ners:
            doc_num = str(cell["doc_num"])
            abs_page = str(cell["abs_page"])
            page_key = f"{doc_num}-{abs_page}"
            pages.setdefault(page_key, []).append(cell)

    # ------------------------------------------------------

    def edoc_to_page_wise(self, edoc: EDocument, blueprint_groups):
        list_of_ners = lmap(methodcaller("values"), blueprint_groups)
        list_of_ners = flatten(list_of_ners, depth=2)
        pages = {}
        for page_data in edoc:
            for entities_in_line in page_data:
                cells_with_ners = lfilter(
                    lambda cell: cell['type'] in list_of_ners,
                    entities_in_line
                )
                self._populate_edoc_pages(pages, cells_with_ners)
        return pages

    # ------------------------------------------------------

    def browse_edoc_pages(self, edoc: EDocument):
        for page_data in edoc:
            for row in page_data:
                for cell in row:
                    yield cell

    # -----------------------------------------------------------------------------

    def clean_card_text(self, text: str) -> str:
        # full duplicate
        this_text = text.strip('}{()[].!,:;-*')
        this_text = re.sub(' +', ' ', this_text)
        return this_text.strip().lower()

    # -----------------------------------------------------------------------------------------------------------------------

    def convert_to_printable_table(self, list_of_rows: list, col_header_names: list, headers: list,
                                   col_header_entity_types: list, card_name: str) -> dict:
        cells, header_count, empty_cell = self.build_header_cells_for_print(col_header_names, col_header_entity_types)
        row_num = 0
        for row_num, row in enumerate(list_of_rows):
            for col_num, header in enumerate(headers):
                list_of_cells = row.get(header, [])
                if len(list_of_cells) > 0:
                    cell = deepcopy(empty_cell)
                    cell['start_row'] = row_num + 1
                    cell['start_column'] = col_num
                    cell['reference'] = list_of_cells[0]
                    cell['to_display'] = cell['reference']['text']
                    cells.append(cell)

        table = {
            "name": card_name,
            "row_count": row_num + 2,
            "col_count": header_count,
            "cells": cells
        }
        return table

    # -------------------------------------------------------------------

    def safe_str_to_float(self, text: str) -> float:
        try:
            return float(text)
        except Exception as ex:
            print(f"ERROR: Failed to convert string {text} {ex}")
            return 0.0

    def _insert_cell_in_smart_ui_card(self, fields_list: list, list_of_cells: list, row_num, col_ix, multiple_cob,
                                      total_amount_len):
        cob_total = 0.0
        type_ = ''
        cell_entity = None
        currency_seen = False
        if len(list_of_cells) > 0:
            for cell_entity in list_of_cells:
                ec = table_utils.entity_to_ui_cell(cell_entity, row_num, col_ix)
                type_ = cell_entity['type']
                if multiple_cob and cell_entity['type'] == 'cob.amount' and row_num >= total_amount_len + 1:
                    if '$' in cell_entity['text']:
                        currency_seen = True
                    val = cell_entity['text'].replace('$', '').replace(",", ".")
                    val = self.safe_str_to_float(val)
                    cob_total += val
                else:
                    row_num += 1
                    fields_list.append(ec)
        if multiple_cob and type_ == 'cob.amount':
            ec = table_utils.entity_to_ui_cell(cell_entity, row_num, col_ix)
            if currency_seen:
                ec['row']['value'][0]['value_name'] = '$' + str(cob_total)
            else:
                ec['row']['value'][0]['value_name'] = str(cob_total)
            for field in fields_list:
                if field['row']['r'] == ec['row']['r'] and field['row']['c'] == ec['row']['c']:
                    fields_list.pop()
                    field = ec
                    fields_list.append(field)
        return row_num

    # -----------------------------------------------------------------------------------------------------------------------

    def build_smart_ui_card(self, dict_of_object, header_list: list, card_name: str,
                            sub_card_name: str = const.EMPTY_STRING) -> dict:
        fields_list = self.map_header_cell_to_table_cell(card_name)
        row_num = 0
        new_row_num = 0
        max_new_row_num = 0
        list_of_cells_len = {}
        multiple_cob = True
        total_amount_len = 0
        for row_obj in dict_of_object:
            row_num = max_new_row_num + 1
            for col_idx, col_name in enumerate(header_list):
                if col_name in row_obj:
                    list_of_cells_len[col_name] = len(row_obj[col_name])
                else:
                    list_of_cells_len[col_name] = 0
            for col_idx, col_name in enumerate(header_list):
                if col_name == 'Total Amount':
                    total_amount_len = list_of_cells_len[col_name]
            for col_idx, col_name in enumerate(header_list):
                if col_name == 'COB Amount' and total_amount_len == list_of_cells_len['COB Amount']:
                    multiple_cob = False
            for col_ix, col_name in enumerate(header_list):
                max_new_row_num = max(new_row_num, max_new_row_num)
                new_row_num = self._insert_cell_in_smart_ui_card(fields_list, row_obj.get(col_name, []), row_num,
                                                                 col_ix, multiple_cob, total_amount_len)
        for cell in fields_list:
            cell['table_info']['row_count'] = row_num
            cell['table_info']['col_count'] = len(header_list)

        ui_card = {"card": {"card_display": card_name, "sub_card": sub_card_name}, "fields_list": fields_list}
        ui_card['card']['schema'] = card_schemas[card_name]
        return ui_card

        # -----------------------------------------------------------------------------------------------------------------------

    def build_header_cells_for_print(self, list_of_display: list, list_of_entity_types: list):
        empty_cell = {
            "to_display": "__auto__",
            "start_row": 0,
            "span_row": 1,
            "start_column": 0,
            "span_column": 1,
            "reference": None
        }
        header_cells = []
        for display_text, entity_type_text in zip(list_of_display, list_of_entity_types):
            cell = deepcopy(empty_cell)
            cell['to_display'] = display_text
            cell['entity_type'] = entity_type_text
            cell['start_column'] = list_of_display.index(display_text)
            header_cells.append(cell)
        return header_cells, len(header_cells), empty_cell

    # ---------------------------------------------------------------------------------------------------------

    def convert_words_id_format(self, list_of_objects: list) -> list:
        word_ids = []
        for obj in list_of_objects:
            for wid in obj['word_ids']:
                if type(wid) == str:
                    word_ids.append(
                        {
                            "word_id": wid,
                            "page_number": obj['abs_page']
                        }
                    )
                elif type(wid) == dict:
                    word_ids.append(wid)
        return word_ids

    # ------------------------------------------------------

    def get_element(self, page_data, this_group):
        vector = {}
        for cell in page_data:
            for key, list_of_ners in this_group.items():
                if cell['type'] in list_of_ners:
                    vector.setdefault(key, []).append(cell)

        return vector

    # -----------------------------------------------------------------------------------------------------------------------

    def remove_duplicate_element(self, index_del: list, list_of_text: list):
        acceptable_indexes = list(
            filter(
                lambda ix: ix not in index_del,
                range(len(list_of_text)
                      )))
        new_list = [x for x in list_of_text if list_of_text.index(x) in acceptable_indexes]
        return new_list

    # ------------------------------------------------------

    def check_subsumption(self, i: int, list_of_text: list, index_del) -> bool:
        prev = list_of_text[i]
        for j in range(i + 1, len(list_of_text)):
            curr = list_of_text[j]
            if self.is_inclusive(prev, curr):
                index_del.append(i)
                return True
        return False

    # ------------------------------------------------------

    def apply_subsumption_upon_list_of_text(self, list_of_text: list):
        list_of_text = remove_redundant_items_from_ordered_list(list_of_text)
        list_of_text.sort(key=len)
        index_del = []
        for i in range(0, len(list_of_text) - 1):
            if self.check_subsumption(i, list_of_text, index_del):
                continue
        list_of_text = self.remove_duplicate_element(index_del, list_of_text)
        return list_of_text

    # ------------------------------------------------------

    def sort_rows_by_date_cells(self, list_of_rows, date_cell_text, date_format):

        rows_with_emnpty_date_cells = list(filter(lambda row_obj: not (len(row_obj.get(date_cell_text, []))),
                                                  list_of_rows
                                                  ))

        rows_with_non_emnpty_date_cells = list(filter(lambda row_obj: len(row_obj.get(date_cell_text, [])),
                                                      list_of_rows
                                                      ))

        rows_with_valid_date_cells = list(filter(
            lambda row_obj: dates_utility.check_date(
                row_obj[date_cell_text][0].get('codified_as') or row_obj[date_cell_text][0]['text'],
                date_format)[const.STATUS],
            rows_with_non_emnpty_date_cells
        ))

        rows_with_invalid_date_cells = list(filter(
            lambda row_obj: dates_utility.check_date(
                row_obj[date_cell_text][0].get('codified_as') or row_obj[date_cell_text][0]['text'],
                date_format)[const.STATUS] == False,
            rows_with_non_emnpty_date_cells
        ))

        date_list = list(map(
            lambda row_obj: dates_utility.check_date(
                row_obj[date_cell_text][0].get('codified_as') or row_obj[date_cell_text][0]['text'],
                date_format)[const.US_DATE],
            rows_with_valid_date_cells
        ))
        date_list = list(set(date_list))
        sorted_dates, _ = dates_utility.sort_dates(date_list, const.DESCENDING)

        new_list_of_rows = lmap(lambda this_date:
                                lfilter(
                                    lambda row_obj:
                                    dates_utility.check_date(
                                        row_obj[date_cell_text][0].get('codified_as') or row_obj[date_cell_text][0][
                                            'text'],
                                        date_format)[const.US_DATE] == this_date,
                                    rows_with_valid_date_cells

                                ),
                                sorted_dates)

        new_list_of_rows = flatten(new_list_of_rows)
        new_list_of_rows.extend(rows_with_invalid_date_cells)
        new_list_of_rows.extend(rows_with_emnpty_date_cells)

        return new_list_of_rows

    # -----------------------------------------------------------------------------------------------------------------------

    def remove_duplicates(self, list_of_rows: list, headers: list) -> list:
        list_of_rows_text = sorted(self.get_list_of_rows_text(headers, list_of_rows))
        list_of_unique_row_text = self.apply_subsumption_upon_list_of_text(list_of_rows_text)
        new_list_of_rows = self.get_list_of_unique_rows(headers, list_of_rows, list_of_unique_row_text)
        return new_list_of_rows

    # -----------------------------------------------------------------------------------------------------------------------

    def get_text_from_object(self, list_of_object: list) -> str:
        list_of_text = list(
            set(map(lambda obj: self.clean_card_text(obj.get('codified_as') or obj['text']), list_of_object)))
        return ' '.join(list_of_text).lower()

    # -----------------------------------------------------------------------------------------------------------------------

    def get_list_of_rows_text(self, headers: list, list_of_rows: list):
        list_of_rows_text = list(map(lambda row_obj:
                                     ' '.join(list(
                                         map(lambda header: self.get_text_from_object(row_obj.get(header, [])),
                                             headers))),
                                     list_of_rows
                                     ))

        return list_of_rows_text

    # -----------------------------------------------------------------------------------------------------------------------

    def get_list_of_unique_rows(self, headers: list, list_of_rows: list, list_of_unique_row_text: list):
        new_list_of_rows = []
        for row_obj in list_of_rows:
            row_text = ' '.join(
                list(map(lambda header: self.get_text_from_object(row_obj.get(header, [])), headers))
            )

            if row_text in list_of_unique_row_text:
                new_list_of_rows.append(row_obj)
                list_of_unique_row_text.remove(row_text)
        return new_list_of_rows

    # -----------------------------------------------------------------------------------------------------------------------

    def map_header_cell_to_table_cell(self, card_name) -> list:
        header_cells = []
        header_ners = BLUEPRINTS[card_name][const.HEADERS]
        col_num = -1
        for header_name, header_entity_type in header_ners.items():
            col_num += 1
            ec = deepcopy(ui_output_dicts.empty_cell)
            ec['row']['value'][0]['value_name'] = header_name
            ec['row']['value'][0]['entity_type'] = header_entity_type[0]
            ec['row']['type'] = 'header'
            ec['row']['r'] = 0
            ec['row']['c'] = col_num
            header_cells.append(ec)
        return header_cells

    # -----------------------------------------------------------------------------------------------------------------------

    def check_attachement(self, position: str, list_of_vectors: list, children: list, children_obj_name, used_ids: list,
                          primary_cell_name: str):
        for child in children:
            if child['id'] in used_ids:
                continue
            child_point = self.get_point(child)
            row_objects_in_required_position = list(filter(
                lambda row_objects: self.is_required_position(position, child_point,
                                                              self.get_point(row_objects[primary_cell_name][0])),
                list_of_vectors
            ))
            if len(row_objects_in_required_position) == 0:
                continue
            closest_primary_cell_index = min(
                range(len(row_objects_in_required_position)),
                key=lambda row_objects_index: self.rect_distance(child_point, self.get_point(
                    row_objects_in_required_position[row_objects_index][primary_cell_name][0]))
            )
            row_objects_in_required_position[closest_primary_cell_index][children_obj_name].append(child)
            used_ids.append(child['id'])
        return list_of_vectors, used_ids

    # -----------------------------------------------------------------------------------------------------------------------

    def prepare_vector(self, obj, col_names: list, primary_cell_name: str):
        vector = {primary_cell_name: [obj]}
        for col_name in col_names:
            vector[col_name] = []
        return vector

    # -----------------------------------------------------------------------------------------------------------------------

    def prepare_children_objects(self, col_objects: list, col_names: list):
        children_objects = {}
        for col_name, col_object in zip(col_names, col_objects):
            children_objects[col_name] = col_object
        return children_objects

    # -----------------------------------------------------------------------------------------------------------------------

    def estimate_primary_cell_attachement(self, primary_objects: list, col_dict: dict, all_positions: dict,
                                          primary_cell_name: str) -> list:
        col_names = list(col_dict.keys())
        col_objects = list(col_dict.values())

        list_of_vectors = []
        for obj in primary_objects:
            vector = self.prepare_vector(obj, col_names, primary_cell_name)
            list_of_vectors.append(vector)

        children_objects = self.prepare_children_objects(col_objects, col_names)
        used_ids = []

        for children_name, children_objects_list in children_objects.items():
            for _level, list_of_position in all_positions.items():
                for position in list_of_position:
                    list_of_vectors, used_ids = self.check_attachement(position, list_of_vectors, children_objects_list,
                                                                       children_name, used_ids, primary_cell_name)
        return list_of_vectors

    # -----------------------------------------------------------------------------------------------------------------------

    def _make_single_object_in_reason_plan_list(self, list_of_rows):
        headers = ['reason', 'plan']
        for i, row_obj in enumerate(list_of_rows):
            for header in headers:
                list_of_objects = row_obj[header]
                word_ids = self.convert_words_id_format(list_of_objects)
                if len(list_of_objects) < 2:
                    if len(list_of_objects) == 1:
                        list_of_rows[i][header][0]['word_ids'] = word_ids
                    continue

                list_of_text = []
                for obj in list_of_objects:
                    text = self.clean_card_text(obj['text'])
                    list_of_text.append(text)

                list_of_text = self.apply_subsumption_upon_list_of_text(list_of_text)

                single_obj = list_of_objects[0]
                single_obj['text'] = ', '.join(list_of_text)
                single_obj['word_ids'] = word_ids
                list_of_rows[i][header] = [single_obj]
        return list_of_rows

    # -------------------------------------------------------------------

    def make_single_object_in_extended_columns(self, list_of_rows, headers: list) -> list:
        for i, row_obj in enumerate(list_of_rows):
            for header in headers:
                list_of_objects = row_obj[header]
                word_ids = self.convert_words_id_format(list_of_objects)

                if len(list_of_objects) < 2:
                    if len(list_of_objects) == 1:
                        list_of_rows[i][header][0]['word_ids'] = word_ids
                    continue

                list_of_text = list(map(
                    lambda obj: self.clean_card_text(obj['text']), list_of_objects
                ))

                list_of_text = remove_redundant_items_from_ordered_list(list_of_text)

                single_obj = deepcopy(list_of_objects[0])
                single_obj['id'] = str(uuid.uuid4())
                single_obj['text'] = ', '.join(list_of_text)
                single_obj['word_ids'] = word_ids
                single_obj['type'] = const.MERGED_ENTITY + header
                list_of_rows[i][header] = [single_obj]
        return list_of_rows

    # -------------------------------------------------------------------
    def filter_by_unique_key(self, key_func, items):
        # currently not in  use
        unique_set = set()
        for item in items:
            key = key_func(item)
            if key not in unique_set:
                yield item

    def is_acceptable_by_page_classifier(self, cell: dict, page_classifiers: dict, acceptable_pages,
                                         excluded_pages) -> bool:
        abs_page = int(cell['abs_page'])
        claimid = cell['claimid']
        return (claimid in page_classifiers
                and abs_page in page_classifiers[claimid]
                and ((page_classifiers[claimid][abs_page] in acceptable_pages)
                     or ('*' in acceptable_pages and page_classifiers[claimid][abs_page] not in excluded_pages))
                )

    # ------------------------------------------------------

    def get_highlight_status(self, obj: Entity, text: str) -> Dict:
        """
        puts a superscript character in pdf summary
        """
        conf_status = {'is_low': False, 'text': text}
        if obj['confidence_threshold'] > obj['confidence']:
            conf_status['text'] = text + "\N{SUPERSCRIPT ONE}"
            conf_status['is_low'] = True
        return conf_status

    # -----------------------------------------------------------------------------------------------------------------------

    def is_required_position(self, position: str, child_point, primary_point) -> bool:
        if position == const.SAME_LINE:
            return self.is_same_line(child_point, primary_point)
        elif position == const.ABOVE_LINE:
            return self.is_above_line(primary_point, child_point)
        elif position == const.BELOW_LINE:
            return self.is_below_line(primary_point, child_point)
        elif position == const.SAME_LINE_RIGHT_SIDE:
            return self.is_same_line_right_side(child_point, primary_point)
        else:
            return True

    # -----------------------------------------------------------------------------------------------------------------------

    def match_score(self, a, b):
        Token_Sort_Ratio = fuzz.token_sort_ratio(a, b)
        Token_Set_Ratio = fuzz.token_set_ratio(a, b)
        score = (Token_Sort_Ratio + Token_Set_Ratio) / 2
        return score

    # ------------------------------------------------------

    def format_word_ids(self, full_cell: dict):
        if 'reference' in full_cell and full_cell['reference'] is not None:
            cell = full_cell['reference']
        else:
            cell = full_cell
        given_word_ids = cell.get('word_ids', [])
        page_number = cell.get('abs_page', -1)
        word_ids = []
        for wid in given_word_ids:
            if type(wid) == str:
                obj = {'word_id': wid, 'page_number': page_number}
                word_ids.append(obj)
            else:
                word_ids.append(wid)
        return word_ids

        # ------------------------------------------------------

    def is_inclusive(self, smal_text: str, big_text: str) -> bool:
        smal_text_parts = smal_text.split(const.space)
        un_common = [x for x in smal_text_parts if x not in big_text]
        return len(un_common) == 0

    # ------------------------------------------------------

    def remove_duplicate_rows(self, rows, headers):

        dict_of_rows = {}
        for i, row in enumerate(rows):
            row_obj = deepcopy(headers)
            for cell in row:
                eo = cell.get('entity_object')
                if eo is not None:
                    entity_type = cell['entity_type']
                    if entity_type in headers:
                        row_obj[cell['entity_type']] = cell['entity_object']['text']

            lst = list(row_obj.values())
            text = ' '.join(lst).strip().lower()
            row_nums = dict_of_rows.get(text, [])
            row_nums.append(i)
            dict_of_rows[text] = row_nums

        retain, remove = [], []
        for k, v in dict_of_rows.items():
            # print(k, v)
            retain.append(v[0])
            if len(v) > 1:
                remove.extend(v[1:])
        retain.sort()
        remove.sort(reverse=True)

        for ix in remove:
            del rows[ix]
        return rows

    # ------------------------------------------------------

    def _remove_unwanted_characters_lab_test(self, text: str) -> str:
        unwanted_characters = ['(', ')', '%', '<', '>', '[', ']', '{', '}', ',']
        for ch in unwanted_characters:
            text = text.replace(ch, const.EMPTY_STRING)
        return text

    # ------------------------------------------------------

    def _fetch_possible_numeric_value(self, value):
        if value is None:
            return None
        else:
            text = self._remove_unwanted_characters_lab_test(value)
        return text

    # ------------------------------------------------------

    def is_number(self, text) -> bool:
        try:
            text = str(text)
            if text.isnumeric():
                return True
            _ = float(text)
            return True
        except ValueError:
            return False

    # ------------------------------------------------------

    def get_point(self, obj: dict):
        return obj['left'], obj['top'], obj['right'], obj['bottom']

        # ------------------------------------------------------

    def is_above_line(self, child_point: list, mother_point: list) -> bool:
        return child_point[1] < mother_point[1]

    def is_below_line(self, child_point: list, mother_point: list) -> bool:
        return child_point[1] > mother_point[1]

    # -------------------------------------------------------

    def is_cell_1_height_within_cell_2_height(self, cell_1_point: list, cell_2_point: list) -> bool:
        cell_1_height = (cell_1_point[1] > cell_2_point[1]) and \
                        (cell_1_point[3] < cell_2_point[3])
        return cell_1_height

    def is_cell_1_width_within_cell_2_width(self, cell_1_point: list, cell_2_point: list) -> bool:
        cell_1_height = (cell_1_point[0] > cell_2_point[0]) and \
                        (cell_1_point[2] < cell_2_point[2])
        return cell_1_height

    # -------------------------------------------------------

    def is_same_line(self, child_point: list, mother_point: list) -> bool:
        margin = 20
        top_disp = abs(child_point[1] - mother_point[1])
        bot_disp = abs(child_point[3] - mother_point[3])

        height_check = self.is_cell_1_height_within_cell_2_height(child_point, mother_point) or \
                       self.is_cell_1_height_within_cell_2_height(mother_point, child_point)

        return (top_disp <= margin) or (bot_disp <= margin) or height_check

    def is_same_column(self, child_point: list, mother_point: list) -> bool:
        margin = 20
        left_display = abs(child_point[0] - mother_point[0])
        right_display = abs(child_point[2] - mother_point[2])

        height_check = self.is_cell_1_width_within_cell_2_width(child_point, mother_point) or \
                       self.is_cell_1_width_within_cell_2_width(mother_point, child_point)

        return (left_display <= margin) or (right_display <= margin) or height_check

    # -------------------------------------------------------

    def is_same_line_right_side(self, child_point: list, mother_point: list) -> bool:
        return (child_point[0] > mother_point[0]) and \
            self.is_same_line(child_point, mother_point)

    # -------------------------------------------------------

    def is_correct_side(self, point_1: list, point_2: list) -> int:
        is_right = point_1[0] < point_2[0]
        is_down = point_1[1] < (point_2[1] - 10)
        return is_right or is_down

    # ------------------------------------------------------

    def _dist(self, point_1: Tuple[int, int], point_2: Tuple[int, int]) -> float:
        # print (point_1, point_2)
        d1 = (point_1[0] - point_2[0])
        d1 = d1 * d1
        d2 = (point_1[1] - point_2[1])
        d2 = d2 * d2
        return math.sqrt(d1 + d2)

    # ------------------------------------------------------

    def rect_distance(self, point1, point2):
        x1, y1, x1b, y1b = point1[0], point1[1], point1[2], point1[3]
        x2, y2, x2b, y2b = point2[0], point2[1], point2[2], point2[3]

        left = x2b < x1
        right = x1b < x2
        bottom = y2b < y1
        top = y1b < y2

        if top and left:
            return self._dist((x1, y1b), (x2b, y2))
        elif left and bottom:
            return self._dist((x1, y1), (x2b, y2b))
        elif bottom and right:
            return self._dist((x1b, y1), (x2, y2b))
        elif right and top:
            return self._dist((x1b, y1b), (x2, y2))
        elif left:
            return x1 - x2b
        elif right:
            return x2 - x1b
        elif bottom:
            return y1 - y2b
        elif top:
            return y2 - y1b
        else:  # rectangles intersect
            return 0.

    # ------------------------------------------------------

    def get_center_to_center_distance(self, point1, point2):
        x1, y1, x1b, y1b = point1[0], point1[1], point1[2], point1[3]
        x2, y2, x2b, y2b = point2[0], point2[1], point2[2], point2[3]

        hc1 = (x1b + x1) // 2
        vc1 = (y1 + y1b) // 2
        hc2 = (x2 + x2b) // 2
        vc2 = (y2 + y2b) // 2

        return self._dist((hc1, vc1), (hc2, vc2))

    # ------------------------------------------------------

    def remove_superscript_character(self, text: str) -> str:
        return str(text).replace('¹', '')

    # ------------------------------------------------------

    def set_rating_flag(self, rating, lab_test_value: str, low_ref: str, high_ref: str):
        if rating:
            return rating
        
        lab_test_value = self.remove_superscript_character(lab_test_value)
        low_ref = self.remove_superscript_character(low_ref)
        high_ref = self.remove_superscript_character(high_ref)

        val = self._fetch_possible_numeric_value(lab_test_value)
        low = self._fetch_possible_numeric_value(low_ref)
        high = self._fetch_possible_numeric_value(high_ref)

        if (val is not None) and (low is not None) and (high is not None):
            if (self.is_number(val) and self.is_number(low)) and self.is_number(high):
                l, h, v = float(low), float(high), float(val)
                if l > h:
                    if v >= l:
                        return const.NEGATIVE
                    elif v < l:
                        return const.LOW

                if l <= v <= h:
                    return ''
                elif v < l:
                    return const.LOW
                elif v > h:
                    return const.HIGH

        if (val is not None) and (high is not None):
            if self.is_number(high) and self.is_number(val):
                h = float(high)
                v = float(val)
                if v > h:
                    return const.HIGH

        if (val is not None) and (low is not None):
            if self.is_number(low) and self.is_number(val):
                l = float(low)
                v = float(val)
                if v < l:
                    return const.LOW

        return rating

    # -----------------------------------------------------------------------------

    def prepare_without_date_dx_row(self, edocs_without_visit_dates):
        rows = []

        for grp in edocs_without_visit_dates:
            for row in grp:
                for cell in row:
                    if not (cell.get('type', '') == "assessment.diag"):
                        continue

                    dx_obj = {
                        "key": "name",
                        "entity_type": "assessment.diag",
                        "entity_object": cell,
                        "display_key": "Diagnosis",
                        "is_container": False,
                        "role": 1
                    }
                    icd_obj = {
                        "key": "value",
                        "entity_type": "icd10",
                        "entity_object": None,
                        "display_key": "ICD-10",
                        "is_container": False,
                        "role": 2
                    }
                    row = [dx_obj, icd_obj]
                    rows.append(row)
        return rows

    # ------------------------------------------------------

    def _get_value_id_from_cell(self, this_card):
        return list(map(lambda cell: cell['row']['value'][0]['value_id'], this_card['fields_list']))

        # ------------------------------------------------------

    def _update_words_list(self, cell: dict, orig_text: str, words_list: list):
        flag = False
        if len(cell["entity_object"]['word_ids']) > 0:
            this_id = cell["entity_object"]['word_ids'][0]
            abs_page = cell["entity_object"]['abs_page']
            text = cell["entity_object"]['text']

            for w in words_list:
                if (w['id'] == this_id) and (w['text'].lower() in orig_text.lower()) and (w['abs_page'] == abs_page):
                    new_id = 'new-' + this_id
                    cell["entity_object"]['word_ids'] = [new_id]
                    word = deepcopy(w)
                    word['text'] = text
                    word['id'] = new_id
                    words_list.append(word)
                    flag = True
                    break

        return words_list, flag

    def _is_float(self, element):
        try:
            float(element)
            return True
        except ValueError:
            return False

    def find_insert_index(self, sorted_list, target_dict):
        # Define a sorting key function to extract the sorting key (top, left) from a dictionary
        key_func = lambda d: (d['top'], d['left'])

        # Determine the sorting key for the target_dict
        target_key = key_func(target_dict)

        # Create a sorted copy of the list
        sorted_copy = sorted(sorted_list, key=key_func)

        # Iterate through the sorted_copy to find the insertion point
        insert_index = 0
        for i, item in enumerate(sorted_copy):
            item_key = key_func(item)
            if abs(target_key[0] - item_key[0]) <= 5 and target_key[1] >= item_key[1]:
                # 'top' values are within ±5 and 'left' values are the same
                insert_index = i + 1
            else:
                break

        return insert_index

    def find_closest_cell_from_previous_pages(self, row_objects, child, previous_page_limit=False):
        same_document_data = lfilter(lambda row: row['doc_num'] == child['doc_num'],
                                     row_objects
                                     )
        if len(same_document_data) != 0:
            if previous_page_limit == True:
                closest_page = lfilter(lambda row: row['abs_page'] == (child['abs_page'] - 1),
                                       same_document_data
                                       )
            else:
                closest_page = lfilter(lambda row: row['abs_page'] < child['abs_page'],
                                       same_document_data
                                       )
            if len(closest_page) != 0:
                closest_page_objects = max(
                    closest_page,
                    key=lambda row: row['abs_page']
                )
                if closest_page_objects:
                    max_candidate_page = closest_page_objects['abs_page']
                    closest_page_object = max(
                        lfilter(lambda cell: cell['abs_page'] == max_candidate_page, closest_page),
                        key=lambda row: row['bottom']
                    )
                    return closest_page_object
        return None

    def get_page_class_from_display_name(self, page_class_list, display_names):
        return flatten([display_names.classes_for_display_name(page_class) for page_class in page_class_list])

    def get_field_wise_priority_mapping(self, priorities, card_id, display_names=None):
        field_wise_priority_mappping = {}
        for priority in priorities:
            if card_id in priority.field_id:
                if display_names is not None:
                    field_wise_priority_mappping[priority.field_id] = {
                        "page_class_exclusion_list": self.get_page_class_from_display_name(
                            priority.page_class_exclusion_list, display_names),
                        "page_class_list": self.get_page_class_from_display_name(priority.page_class_list,
                                                                                 display_names)
                    }
                else:
                    field_wise_priority_mappping[priority.field_id] = {
                        "page_class_exclusion_list": priority.page_class_exclusion_list,
                        "page_class_list": priority.page_class_list
                    }
        return field_wise_priority_mappping

    def entity_with_valid_priority(self, entity, priority_mapping, card_id):
        entity_page_class = entity.get('page_class')
        entity_field_id = FIELD_ID_BLUEPRINTS.get(card_id, {}).get(entity['type'])
        acceptable_page_classes = priority_mapping.get(entity_field_id, {}).get('page_class_list', ['*'])
        excluded_page_classes = priority_mapping.get(entity_field_id, {}).get('page_class_exclusion_list', [])
        if not acceptable_page_classes:
            return False
        return ((entity_page_class in acceptable_page_classes) or
                ('*' in acceptable_page_classes and entity_page_class not in excluded_page_classes))

    def _preprocess_text(self, text: str) -> str:
        if not text:
            return text
        replace_chars = ".*?+/'-&{}()[],\""
        for ch in replace_chars:
            text = text.replace(ch, "")
        return text.strip().lower()

    def remove_duplicates_by_subtext(self, list_of_rows: EPageData, mother: str, is_medical_code: Dict[str, bool] = {}):
        try:
            list_of_rows.sort(
                key=lambda item:
                (len(self.get_display_key_text(item, mother)),
                 len(self.get_children_merged_text(item, mother, is_medical_code))),
                reverse=True
            )
            unique_entries = []
            seen_rows = set()
            seen_empty_children = set()
            for entry in list_of_rows:
                mother_text = self.get_display_key_text(entry, mother)
                all_children_text = self.get_children_merged_text(entry, mother, is_medical_code)
                if all_children_text:
                    if not any((mother_text in seen_mother and all_children_text in seen_children) or
                               (seen_mother in mother_text and seen_children in all_children_text)
                               for seen_mother, seen_children in seen_rows):
                        keys_to_remove = {(seen_mother, seen_children) for seen_mother, seen_children in seen_rows
                                          if seen_mother in mother_text and seen_children in all_children_text}
                        seen_rows -= keys_to_remove
                        unique_entries.append(entry)
                        seen_rows.add((mother_text, all_children_text))
                else:
                    if not any(mother_text in seen_mother[0] for seen_mother in seen_rows):
                        if not any(mother_text in seen_mother[0] for seen_mother in seen_empty_children):
                            keys_to_remove = {(seen_mother, seen_children) for seen_mother, seen_children in
                                              seen_empty_children
                                              if seen_mother in mother_text}
                            seen_empty_children -= keys_to_remove
                            seen_empty_children.add((mother_text, ''))
                            unique_entries.append(entry)

            return unique_entries
        except Exception as e:
            print(f'ERROR: Remove duplicates subtext for {mother}')
            return list_of_rows

    def get_display_key_text(self, item_list: EPageLine, header_display_name: str):
        mother_desc = next((cell for cell in item_list if cell.get("display_key") == header_display_name), None)
        if not mother_desc['entity_object']:
            return ""
        return self._preprocess_text(mother_desc['entity_object']['text'])

    def get_children_merged_text(self, item_list: EPageLine, header_display_name: str,
                                 is_medical_code: Dict[str, bool]) -> str:
        concatenated_string = ''
        for cell in item_list:
            if cell["display_key"] != header_display_name and cell.get('entity_object') is not None:
                if not is_medical_code.get(cell['display_key'], False):
                    text_entry = self._preprocess_text(cell['entity_object']['text'])
                    concatenated_string += cell['display_key'] + text_entry
        return concatenated_string.strip()

    def remove_duplicates_by_subtext_other_format(self, list_of_rows, mother: str,
                                                  is_medical_code: Dict[str, bool] = {}, ignored_cells=[]):
        try:
            list_of_rows.sort(
                key=lambda item:
                (len(self.get_display_key_text_other_format(item, mother)),
                 len(self.get_children_merged_text_other_format(item, mother, is_medical_code, ignored_cells))),
                reverse=True
            )
            unique_entries = []
            seen_rows = set()
            seen_empty_children = set()
            for entry in list_of_rows:
                mother_text = self.get_display_key_text_other_format(entry, mother)
                all_children_text = self.get_children_merged_text_other_format(entry, mother, is_medical_code,
                                                                               ignored_cells)
                if all_children_text:
                    if not any((mother_text in seen_mother and all_children_text in seen_children) or
                               (seen_mother in mother_text and seen_children in all_children_text)
                               for seen_mother, seen_children in seen_rows):
                        keys_to_remove = {(seen_mother, seen_children) for seen_mother, seen_children in seen_rows
                                          if seen_mother in mother_text and seen_children in all_children_text}
                        seen_rows -= keys_to_remove
                        unique_entries.append(entry)
                        seen_rows.add((mother_text, all_children_text))
                else:
                    if not any(mother_text in seen_mother[0] for seen_mother in seen_rows):
                        if not any(mother_text in seen_mother[0] for seen_mother in seen_empty_children):
                            keys_to_remove = {(seen_mother, seen_children) for seen_mother, seen_children in
                                              seen_empty_children
                                              if seen_mother in mother_text}
                            seen_empty_children -= keys_to_remove
                            seen_empty_children.add((mother_text, ''))
                            unique_entries.append(entry)
            return unique_entries
        except Exception as e:
            print(f'ERROR: Remove duplicates subtext for {mother}')
            return list_of_rows

    def get_display_key_text_other_format(self, item_list: EPageLine, header_display_name: str):
        if not item_list.get(header_display_name, None):
            return ""
        if isinstance(item_list[header_display_name], List):
            text_entry = self._preprocess_text(get_first(item_list[header_display_name], []).get('text', ''))
            return text_entry
        return self._preprocess_text(item_list[header_display_name]['text'])

    def get_children_merged_text_other_format(self, item_list: Dict[str, EPageLine], header_display_name: str,
                                              is_medical_code: Dict[str, bool], ignored_cells=[]) -> str:
        concatenated_string = ''
        for key, value in item_list.items():
            if key in ignored_cells:
                continue
            if key == header_display_name or is_medical_code.get(key, False):
                continue
            if isinstance(item_list[header_display_name], List):
                if len(item_list[header_display_name]) != 0:
                    concatenated_string += self._preprocess_text(get_first(value, {}).get('text', ''))
                    continue
                continue
            concatenated_string += self._preprocess_text(value.get('text', ''))
        return concatenated_string.strip()

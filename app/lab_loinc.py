import json
import re
from collections import defaultdict, Counter
from copy import deepcopy
from typing import List, Optional, Set

import pandas as pd
from rapidfuzz import fuzz

import model_input_files_config as Model_Data
from miscellaneous import MiscUtility
from friendlylib.iterators import flatten, lmap
from medical_codes.coding import Laboratory, CodeOutput

class LOINC:
    def __init__(self):
        self.common_lab_loincs = List[str]
        self.common_panels = List[str]
        self.loinc_data_path = Model_Data.LOINC_DATA
        self.misc = MiscUtility()
        self._load_loinc_data()
        self.load_common_lab_tests()
        self._load_top_100_panels()
        self.load_specimen_references()

    # ------------------------------------------------------

    def load_common_lab_tests(self):
        
        common_panel_loincs = {
            "58715-4",  # Adulterants panel
            "93340-8",  # Auto Differential panel
            "58410-2",  # CBC W Auto Differential panel - Blood
            "24326-1",  # Electrolytes 1998 panel - Serum or Plasma
            "24323-8",  # Comprehensive metabolic 2000 panel - Serum or Plasma
            "34549-6",  # Follitropin and Lutropin panel
            "43150-2",  # Hemoglobin A1c measurement device panel
            "24325-3",  # Hepatic function 2000 panel - Serum or Plasma
            "24313-9",  # Hepatitis Panel
            "89365-1",  # HIV 1 and 2 Ab panel - Serum, Plasma or Blood by Rapid immunoassay
            "50190-8",  # Iron and Iron binding capacity panel
            "24331-1",  # Lipid 1996 panel - Serum or Plasma
            "24362-6",  # Renal function 2000 panel - Serum or Plasma
            "24348-5",  # Thyroid Panel
            "24357-6",  # Urinalysis panel
            "58952-3",  # Testosterone free and total panel
            "24346-9",  # Parathyrin Intact Calcium Panel
            "53293-7",  # Urinalysis microscopic panel 
            "75689-0",  # Iron Panel - Serum or Plasma
            "69738-3",  # Differential panel, method unspecified - Blood
            "96805-7",  # Cobalamin (Vitamin B12) and folate panel - Serum
            "96451-0",  # Alpha-1-Fetoprotein and Alpha-1-Fetoprotein.L3 panel - Serum or Plasma
            "53747-2",  # Cocaine panel - Urine
            "48796-7",  # Hepatitis C virus FibroSURE panel - Serum or Plasma
            "58446-6",  # Creatinine 24H renal clearance adjusted for body surface area panel
            "87434-7",  # Protein and creatinine panel - Urine
            "43125-4",  # Nicotine and Metabolites panel [Mass/volume] - Urine
            "53764-7",  # Prostate specific Ag panel
            "42929-0",  # Lactate dehydrogenase panel
            "24335-2",  # Creatine kinase panel
            "97759-5",  # Magnesium and phosphate and lactate panel
            "48811-4",  # Protein fractions 3 panel
            "53315-8",  # Urinalysis microscopic panel
            "55399-0",  # Diabetes tracking panel
            "92890-3",  # Hepatitis A and B and C virus immunity AndOr previous exposure panel 
            "92722-8",  # Lipoprotein subparticle profile panel 
            "78699-6",  # Liver fibrosis score panel 
            "74760-0"   # HYDROcodone and metabolites panel 
        }
        
        self.common_lab_loincs = flatten(lmap(lambda panel_loinc: self.terms[panel_loinc]["children"], common_panel_loincs))
        common_loincs = [ #common tests without panels
            '32294-1', #Albumin/Creatinine [Ratio] in Urine
            '10839-9', #Troponin I.cardiac
            '51584-1', #Granulocytes.immature
            "32676-9", #Diuretics
            "58681-8", #Temp Ur
            "33762-6", #NT-proBNP
            "16249-5", #oxyCODONE
            "12328-1", #Beta blockers
            "19306-0", #Thiazides
            "8220-6",  #Opiates
            "58381-5", #fentaNYL
            "19593-3", #6-Monoacetylmorphine
            "16246-1", #Methadone
            "5645-7",  #Ethanol 
            "4537-7",  #Esr
            "3084-1",  #Urate
            "5793-5",  #granular cast
            "5796-8",  #hyaline casts
            "5643-2",  #alcohol blood
            "14958-3", #microalbumin/creatinine
            "30154-9", #protein/creatinine
            "14631-6", #bilirubin
            "33863-2", #cystatin c
            "15074-8", #glucose mmoll
            "14682-9", #creatine umoll
            "14927-8", #triglyceride mmoll
            "40419-4", #Amphetamine+Methamphetamine
            "19343-3", #Amphetamine
            "19554-5", #Methamphetamine 
            "16250-3", #Codeine
            "16251-1", #morphine
            "17395-5", #oxymorphone
            "94500-6", #sars-cov-2 by Naa
            "29771-3", #blood in stool
            ] 
        ignored_loinc = ['28570-0', '30525-0']
        self.common_lab_loincs = [item for item in self.common_lab_loincs if item not in ignored_loinc]
        self.common_lab_loincs.extend(common_loincs)
        self.common_panels = list(common_panel_loincs)
        
    # ------------------------------------------------------

    def _load_top_100_panels(self) -> None:
        top100_panels_loinc = sorted(
            (value for value in self.terms.values() if value["is_panel"] and value["rank"] != 0),
            key=lambda x: x["rank"]
        )[:100]

        self.top_panel_names: Set[str] = {
            self._preprocess_component_names(entry["component"]) for entry in top100_panels_loinc
        }

        common_panel_names = {
            self._preprocess_component_names(self.terms[code]["component"])
            for code in self.common_panels
            if code in self.terms and self._preprocess_component_names(self.terms[code]["component"]) not in self.top_panel_names
        }

        self.top_panel_names.update(common_panel_names)

    # ------------------------------------------------------
        
    def load_specimen_references(self):
        self.common_specimen = {
            "fld" : "fluid", 
            "plas" : "plasma", 
            "ser": "serum", 
            "sed": "sediment", 
            # "rbc" : "red blood cell", 
            "csf": "cerebral spinal fluid",
            "urn": "urine",
            "urine": "urine",
            "bld" : "blood", 
        }

    # ------------------------------------------------------

    def _read_acronyms(self, path):
        acronyms = {}
        with open(path) as f:
            for line in f:
                key = line.split(",")[0]
                vals = line[len(key) + 1:].split(";")
                vals = [val.strip() for val in vals if val.strip()]
                acronyms[key] = vals
        return acronyms

    # ------------------------------------------------------

    def _read_expansions(self, path):
        expansions = {}
        with open(path) as f:
            for line in f:
                key = line.split(",")[0]
                vals = line[len(key) + 1:].split(";")
                vals = [val.strip() for val in vals if val.strip()]
                expansions[key] = vals
        return expansions

    # ------------------------------------------------------

    def create_loinc_data(self):
        terms_dict = self._read_loinc_terms("loinc-files/Loinc.csv")
        panels_dict = self._read_loinc_panels("loinc-files/PanelsAndForms.csv")
        terms_dict = self._update_panel_children(terms_dict, panels_dict)
        acronyms = self._read_acronyms("loinc-files/loinc_acronyms.txt")
        expansions = self._read_expansions("loinc-files/loinc_expansions.txt")
        terms_inverted_indexes = self._get_inverted_indexes(terms_dict, list(terms_dict.keys()))
        panels_inverted_indexes = self._get_inverted_indexes(terms_dict, list(panels_dict.keys()))
        loinc_data = {
            "terms": list(terms_dict.values()),
            "terms_inverted_indexes": terms_inverted_indexes,
            "panels_inverted_indexes": panels_inverted_indexes,
            "acronyms": acronyms,
            "expansions": expansions
        }
        with open(self.loinc_data_path, "w") as f:
            f.write(json.dumps(loinc_data, indent=4))

    # ------------------------------------------------------

    def process(self, lab_entry: Laboratory) -> CodeOutput:
        lab_entry.name = self._preprocess_name(lab_entry.name)
        lab_entry.panel = self._preprocess_text(lab_entry.panel)
        lab_entry.unit = self._preprocess_unit(lab_entry.unit)   
        lab_entry.specimen = self._preprocess_text(lab_entry.specimen)
        if lab_entry.specimen in self.common_specimen:
            lab_entry.specimen = self.common_specimen[lab_entry.specimen]
            
        if not lab_entry.name:
            return CodeOutput()
        
        lab_entry: Laboratory = self._preprocess_lab_data(lab_entry)

        name_candidate: str = ""
        lab_candidates: List[str] = []
        code_output = CodeOutput()
        common_code_output = CodeOutput()
        if "/" in lab_entry.name:
            name_candidate = lab_entry.name.replace("/"," ").lower()
            name_candidate = self._preprocess_text(name_candidate)
            lab_candidates = self._get_laboratory_name_expansions(name_candidate)
            name_candidate_: str = lab_entry.name.replace("/","").lower()
            lab_candidates.extend(self._get_laboratory_name_expansions(name_candidate_))

        lab_candidates.extend(self._get_laboratory_name_expansions(lab_entry.name))
        expanded_name: List[str] = self._get_expansions(lab_entry.name)
        if expanded_name:
            lab_candidates = expanded_name
        is_common: bool = False
        for lab_candidate in lab_candidates:
            lab_entry.name = lab_candidate
            is_common = False
            code_output_candidate: CodeOutput = self.process_laboratory_entries(lab_entry, is_common)
            if is_common:
                if code_output_candidate.score > common_code_output.score:
                    common_code_output = code_output_candidate
                continue
            if code_output_candidate.score > code_output.score:
                code_output = code_output_candidate
        if common_code_output.medical_code:
            common_code_output.score = 100 if common_code_output.score > 100 else common_code_output.score
            return common_code_output
        if code_output:
            code_output.score = 100 if code_output.score > 100 else code_output.score
            return code_output
        
        return CodeOutput()

    # ------------------------------------------------------

    def process_laboratory_entries(self, lab_entry: Laboratory, is_common: bool) -> CodeOutput:
        panels = self._get_candidate_panels(lab_entry.panel)
        threshold = 70
        best_score = 0
        candidates = {}
        code_output: CodeOutput = self.get_common_lab_candidate(lab_entry)
        if code_output.medical_code:
            is_common = True
            return code_output
        if panels:
            candidates = self._aggregate_panels_terms(panels)
        else:
            candidates = self._get_candidate_terms(lab_entry.name)
        if not candidates:
            return CodeOutput()
        best_code = max(candidates.keys(),
                        key=lambda candidate: self._get_match_score_for_code(candidate, lab_entry) * candidates[
                            candidate] ** 2)
        best_score = self._get_match_score_for_code(best_code, lab_entry)
        if best_score >= threshold:
            return CodeOutput(
                medical_code=best_code,
                score=round(best_score),
                description=self.get_component_by_code(best_code)
            )
        return CodeOutput()

    # ------------------------------------------------------

    def _get_laboratory_name_expansions(self, laboratory_name: str) -> List[str]:
        laboratory_words: List[str] = laboratory_name.split()
        if not laboratory_words:
            return []
        results = []
        self._generate_laboratory_name_expansions_recursive(laboratory_words, [], results)
        return results

    # ------------------------------------------------------

    def _get_expansions(self, laboratory_name):
        if laboratory_name in self.expansions:
            return self.expansions[laboratory_name]
        return []

    # ------------------------------------------------------

    def _generate_laboratory_name_expansions_recursive(self, words, current, results):
        if not words:
            results.append(" ".join(current))
            return
        word = words[0]
        if word in self.acronyms:
            expansions = self.acronyms[word]
            for expansion in expansions:
                self._generate_laboratory_name_expansions_recursive(words[1:], current + [expansion], results)

        self._generate_laboratory_name_expansions_recursive(words[1:], current + [word], results)

    # ------------------------------------------------------

    def _check_specimen_in_laboratory_data(self, entry: str):
        if entry == "":
            return False, "", ""
        first_word = entry.split()[0]
        for key, value in self.common_specimen.items():
            if first_word == key:
                updated_text = entry[len(key):].strip()
                return True, updated_text, value
            elif first_word == value:
                updated_text = entry[len(value):].strip()
                return True, updated_text, value 
            elif key in entry:
                return True, entry, key
        return False, "", ""
    
    # ------------------------------------------------------
    
    def _update_units_from_name(self, lab_entry: Laboratory) -> Laboratory:
        units = {
            "count": "",
            "percentage": "%",
            "percent": "%",
        }
        for key in units:
            if key in lab_entry.name:
                lab_entry.name = lab_entry.name.replace(key, "").strip()
                if not lab_entry.unit:
                    lab_entry.unit = units[key]
                break
        return
        
    # ------------------------------------------------------
    
    def _preprocess_lab_data(self, lab_entry: Laboratory) -> Laboratory:
        self._update_units_from_name(lab_entry)
        updated_specimen = ""
        if "urin" in lab_entry.panel:
            lab_entry.specimen = "urine"
        if_updated, updated_panel, updated_specimen = self._check_specimen_in_laboratory_data(lab_entry.panel)
        if if_updated:
            lab_entry.panel = updated_panel
        elif len(lab_entry.name.split()) > 1:
            if_updated, updated_name, updated_specimen = self._check_specimen_in_laboratory_data(lab_entry.name)
            if if_updated:
                lab_entry.name = updated_name
        if lab_entry.specimen == "" and if_updated:
            lab_entry.specimen = updated_specimen
        return lab_entry

    # ------------------------------------------------------

    def get_common_lab_candidate(self, lab_entry: Laboratory):
        threshold = 70
        panel_best_code = ""
        panel_best_score = 0
        if "urin" in lab_entry.panel:
            lab_entry.specimen = "urine"
        candidate_scores = {
            candidate: self._get_match_score_for_code_common(candidate, lab_entry) for candidate in self.common_lab_loincs
        }
        if 'ratio' in lab_entry.name:
            lab_entry_by_ratio = deepcopy(lab_entry)
            lab_entry_by_ratio.name = lab_entry_by_ratio.name.replace("ratio", "")
            lab_entry_by_ratio.unit = "ratio"
            candidate_scores_ratio = {
                candidate: self._get_match_score_for_code_common(candidate, lab_entry_by_ratio) for candidate in self.common_lab_loincs
            }
            candidate_scores.update(candidate_scores_ratio)
        if lab_entry.panel:
            words = self._split_text(lab_entry.panel)
            panels_list = flatten([self.panels_inverted_indexes[word] for word in words if word in self.panels_inverted_indexes])
            panels_list = [loinc_code for loinc_code in panels_list if loinc_code in self.common_panels]
            candidates = self._get_top_k_frequent_codes(5, panels_list)
            panel_candidates = self._aggregate_panels_terms(candidates)
            if panel_candidates:
                panel_best_code = max(panel_candidates.keys(),
                            key=lambda candidate: self._get_match_score_for_code(candidate, lab_entry) * panel_candidates[
                                candidate] ** 2)
                panel_best_score = self._get_match_score_for_code_common(panel_best_code, lab_entry)

        max_score = max(candidate_scores.values())
        max_candidates = [candidate for candidate, score in candidate_scores.items() if score == max_score]
        if len(max_candidates) > 1:
            best_candidate = min(max_candidates, key=lambda candidate: self.terms[candidate]['rank'])
        else:
            best_candidate = max(candidate_scores, key=candidate_scores.get)
            
        best_score = candidate_scores[best_candidate]
        if panel_best_score >= best_score and panel_best_score > threshold:
            return CodeOutput(
                medical_code=panel_best_code,
                score=round(panel_best_score),
                description=self.get_component_by_code(best_candidate),
            )
        if best_score >= threshold:
            return CodeOutput(
                medical_code=best_candidate,
                score=round(best_score),
                description=self.get_component_by_code(best_candidate),
            )
        return CodeOutput()

    # ------------------------------------------------------

    def get_component_by_code(self, code: str) -> str:
        if len(code.strip()) == 0:
            return ""
        if not self.terms.get(code, None):
            return ""
        return self.terms[code]["component"].lower()
    
    # ------------------------------------------------------

    def get_specimen_by_code(self, code: str) -> str:
        if len(code.strip()) == 0:
            return ""
        specimen = self.terms[code]["system"]
        if specimen == "rbc":
            return "blood"
        for key in self.common_specimen:
            specimen = specimen.replace(key, self.common_specimen[key])
        return specimen

    # ------------------------------------------------------

    def test(self):
        df = pd.read_csv("loinc-files/LOINC_merged.csv")
        df = df.where(pd.notnull(df), "")
        match_count = 0
        detection_count = 0
        for index, data in df.iterrows():
            item = {
                "name": self._preprocess_text(data["Name"]),
                "panel": self._preprocess_text(data["Panel"]),
                "unit": self._preprocess_unit(data["Unit"])
            }
            reference = data["LOINC Code"].strip()
            inference = self.process(item)
            print(f"{index}: {str(item)}")
            if reference:
                print(f"\treference: {reference}: {self.terms[reference]['long_name']}")
            else:
                print(f"\treference: empty")
            if inference:
                detection_count += 1
                print(f"\tinference: {inference}: {self.terms[inference]['long_name']}")
            else:
                print(f"\tinference: empty")
            match_count += 1 if reference == inference else 0
            print('-' * 30)
        print(f"Accuracy: {match_count}/{index}")
        print(f"Detection: {detection_count}/{index}")

    # ------------------------------------------------------

    def _load_loinc_data(self):
        with open(self.loinc_data_path) as f:
            loinc_data = json.load(f)
        ignored_terms_dict = {}
        with open("loinc-files/ignored_terms.json", "r") as json_file:
            excluded_terms = json.load(json_file)
            ignored_terms_dict = {term.lower(): True for term in excluded_terms}
        terms = loinc_data["terms"]
        self.terms = {term["code"]: term for term in terms}
        self.terms_inverted_indexes = loinc_data["terms_inverted_indexes"]
        self.panels_inverted_indexes = loinc_data["panels_inverted_indexes"]
        self.acronyms = loinc_data["acronyms"]
        self.expansions = loinc_data["expansions"]
        self.ignored_terms = ignored_terms_dict
        print("loinc loading completed")

    # ------------------------------------------------------

    def _split_text(self, component):
        text = self._preprocess_text(component)
        return list(set(text.split()))

    # ------------------------------------------------------

    def _preprocess_text(self, text: str)-> str:
        if not text:
            return text
        
        replace_chars = ".*?+/'-&{}()[]_,\""
        for ch in replace_chars:
            text = text.replace(ch, " ")
        text = re.sub(' +', ' ', text.lower())
        text = text.strip()
        return text
    
    # ------------------------------------------------------
    
    def _preprocess_component_names(self, text: str)-> str:
        if not text:
            return text
        text = re.sub(r'\d+', '', text)
        text = re.sub(r'\s+', ' ', text).strip()
        return text
    
    # ------------------------------------------------------
    
    def _preprocess_name(self, text: str)-> str:
        if not text:
            return text
        replace_chars = ".*?+'-&{}()[]_,\""
        for ch in replace_chars:
            text = text.replace(ch, " ")
        text = re.sub(' +', ' ', text.lower())
        text = text.strip()
        return text

    # ------------------------------------------------------

    def _preprocess_display_name(self, text: str)-> str:
        if not text:
            return text
        output = re.sub(r'\([^)]*\)|\[[^]]*\]', '', text)
        return self._preprocess_text(output)

    # ------------------------------------------------------

    def _preprocess_unit(self, unit: str)-> str:
        unit = self._preprocess_text(unit)

        unit = unit.split(";")[0]
        parts = unit.split()
        parts = [part for part in parts if
                 part not in ["calc", "unit", "total", "percent", "thousand", "thous", "million", "mill",
                              "positive", "negative", "reactive", "non", "not", "performed"]]
        return "".join(parts)

    # ------------------------------------------------------

    def _remove_unwanted_terms(self, entry: str)-> str:
            arr = entry.split()
            arr = [x for x in arr if x not in self.ignored_terms]
            return ' '.join(arr).strip()
    
    # ------------------------------------------------------
    
    def _get_top_k_frequent_codes(self, k, codes_list):
        if not codes_list or k == 0:
            return {}

        counter = Counter(codes_list)
        most_common = counter.most_common()

        # selecting top k only performs worse,
        # need to stop at index where number of occurrence decreases
        count_change_index = 0
        count_change_index_found = False
        i = 0
        for i in range(1, len(most_common)):
            if most_common[i - 1][1] != most_common[i][1]:
                if i - count_change_index > 100 and count_change_index > 0:
                    count_change_index_found = True
                    break
                count_change_index = i
                if i > k:
                    count_change_index_found = True
                    break
        if not count_change_index_found:
            if not (i - count_change_index > 100 and count_change_index > 0):
                count_change_index = len(most_common)
        if count_change_index == 0:
            return {}

        most_common = most_common[:count_change_index]
        max_occurrence = most_common[0][1]
        return {key: value / max_occurrence for (key, value) in most_common}

    # ------------------------------------------------------

    def _get_candidate_panels(self, panel):
        if not panel:
            return None
        words = self._split_text(panel)
        panels_list = flatten([self.panels_inverted_indexes[word] for word in words if word in self.panels_inverted_indexes])
        panels_list = [loinc_code for loinc_code in panels_list if loinc_code not in self.common_lab_loincs]
        candidates = self._get_top_k_frequent_codes(5, panels_list)
        return candidates

    # ------------------------------------------------------

    def _get_candidate_terms(self, name):
        if not name:
            return None
        words = self._split_text(name)
        terms_list = flatten([self.terms_inverted_indexes[word] for word in words if word in self.terms_inverted_indexes])
        terms_list = [loinc_code for loinc_code in terms_list if loinc_code not in self.common_lab_loincs]
        candidates = self._get_top_k_frequent_codes(25, terms_list)
        return candidates

    # ------------------------------------------------------

    def _aggregate_panels_terms(self, panels):
        terms = {}
        for panel_code in panels.keys():
            for term_code in self.terms[panel_code]["children"]:
                if term_code not in terms or terms[term_code] < panels[panel_code]:
                    terms[term_code] = panels[panel_code]
        return terms

    # ------------------------------------------------------
    
    def process_panel(self, name: str) -> CodeOutput:
        if name == "":
            return CodeOutput()
        code_output: CodeOutput = CodeOutput()
        name = self._preprocess_text(name)
        name = self._remove_unwanted_terms(name)
        if name == "":
            return CodeOutput()
        panel_candidates = self._get_laboratory_name_expansions(name)
        if 'panel' not in name:
            name += ' panel'
        for lab_candidate in panel_candidates:
            code_output_candidate: CodeOutput = self.process_panel_entries(lab_candidate)
            if code_output_candidate.score > code_output.score:
                code_output = code_output_candidate
        if code_output.score:
            return code_output
        
        return CodeOutput()

    # ------------------------------------------------------

    def process_panel_entries(self, name: str) -> CodeOutput:
        threshold = 70
        words = self._split_text(name)
        code_output: CodeOutput = CodeOutput()
        code_output.medical_code = max(self.common_panels,
                    key=lambda candidate: self._get_match_score_for_panel_code(candidate, name))
        code_output.score = self._get_match_score_for_panel_code(code_output.medical_code, name)
        if code_output.score > threshold:
            return code_output
        terms_list = flatten([self.panels_inverted_indexes[word] for word in words if word in self.panels_inverted_indexes])
        candidates = self._get_top_k_frequent_codes(25, terms_list)
        if not candidates:
            return CodeOutput()
        code_output: CodeOutput = CodeOutput()
        code_output.medical_code = max(candidates.keys(),
                        key=lambda candidate: self._get_match_score_for_panel_code(candidate, name) * candidates[
                            candidate] ** 2)
        code_output.score = self._get_match_score_for_panel_code(code_output.medical_code, name)
        if code_output.score > threshold:
            return code_output
        return CodeOutput()

    # ------------------------------------------------------
    
    def _get_match_score_for_code(self, code: str, lab_entry: Laboratory):
        term = self.terms[code]
        names = [term["component"], term["display_name"]]
        name_score = max([self.misc.match_score(lab_entry.name, name_code) for name_code in names if name_code])
        unit_score = 100
        system_score = 100
        if lab_entry.unit:
            unit_score = self._get_match_score_for_unit(code, lab_entry.unit)
        if lab_entry.specimen:
            system_score = self._get_match_score_for_system(code, lab_entry.specimen)

        return name_score * (unit_score / 100) ** 2  * (system_score / 100) ** 2 
    
    def _get_match_score_for_code_common(self, code: str, lab_entry: Laboratory):
        term = self.terms[code]
        names = [term["component"], term["display_name"]]
        name_score = max([self.misc.match_score(lab_entry.name, name_code) for name_code in names if name_code])
        code_unit = term['unit']
        unit_score = 100
        system_score = 100
        if lab_entry.unit != code_unit:
            unit_score = self._get_match_score_for_unit(code, lab_entry.unit)
        if lab_entry.specimen:
            system_score = self._get_match_score_for_system(code, lab_entry.specimen)
            return (name_score * .77) + (unit_score * .15) + (system_score * .8)
        return (name_score * .80) + (unit_score * .20)

    # ------------------------------------------------------

    def _get_match_score_for_panel_code(self, code: str, name: str):
        term = self.terms[code]
        panel_names = [term["component"], term["display_name"]]
        panel_name_score = max([self.misc.match_score(name, name_code) for name_code in panel_names if name_code])

        return panel_name_score

    # ------------------------------------------------------

    def _get_match_score_for_unit(self, code: str, unit: str):
        term = self.terms[code]
        unit_code = term["unit"]
        if not unit and not unit_code:
            return 100
        elif not unit or not unit_code:
            return 0
        if "%" in unit and "%" in unit_code:
            return 100
        return fuzz.token_sort_ratio(unit_code, unit)

    # ------------------------------------------------------

    def _get_match_score_for_system(self, code, system):
        term = self.terms[code]
        system_code = "urine" if "urine" in term["system"] else term["system"]
        if not system_code and system:
            return 100
        if self._check_system_description(system, system_code):
            return 100
        elif not system_code or not system:
            return 0
        return fuzz.token_sort_ratio(system_code, system)

    # ------------------------------------------------------
    
    def _check_system_description(self, system_entry: str, system_from_code: str) -> bool:
        blood_specimen = ["bld", "blood","ser", "blood", "plas"]
        system_code_substrings = system_from_code.split()
        code_match = any(any(substring in specimen for substring in system_code_substrings) for specimen in blood_specimen)
        return any(system_entry in specimen for specimen in blood_specimen) and code_match
        
    # ------------------------------------------------------
    
    def get_panel_from_substring(self, panel_name: str) -> Optional[str]:
        panel_name = self._preprocess_name(panel_name)
        panel_candidates = self._get_laboratory_name_expansions(panel_name)

        if len(panel_candidates) > 1:
            matched_panel = self._match_panel_name(panel_name)
            if matched_panel:
                return matched_panel

        matched_panel = self._match_panel_name(panel_name)
        if matched_panel:
            return matched_panel

        return None

    # ------------------------------------------------------
    
    def _match_panel_name(self, panel_candidate: str) -> Optional[str]:
        matches = {name for name in self.top_panel_names if panel_candidate in name}
        if matches and panel_candidate not in matches:
            return min(matches, key=len)
        return None
        
    # ------------------------------------------------------

    def _read_large_csv(self, path: str):
        df = pd.read_csv(path, low_memory=False)
        df = df.where(pd.notnull(df), "")
        df = df.reset_index()
        return df

    # ------------------------------------------------------

    def _read_loinc_terms(self, path: str):
        """
            LOINC_NUM           -> code
            COMPONENT           -> component
            STATUS              -> is_active
            DisplayName         -> display_name
            EXAMPLE_UCUM_UNITS  -> unit
            SYSTEM              -> system / or specimen
            PanelType           -> is_panel
            Property, Time aspect, Systme, Method type parts are ignored at the moment
        """
        df = self._read_large_csv(path)
        terms_list = [
            {
                "code": data["LOINC_NUM"],
                "component": self._preprocess_text(data["COMPONENT"]),
                "is_active": data["STATUS"] == "ACTIVE",
                "display_name": self._preprocess_display_name(data["DisplayName"]),
                "unit": self._preprocess_unit(data["EXAMPLE_UCUM_UNITS"]),
                "system": self._preprocess_text(data["SYSTEM"]),
                "is_panel": data["PanelType"] == "Panel",
                "rank": int(data["COMMON_TEST_RANK"]),
                "children": []
            }
            for index, data in df.iterrows()
        ]
        terms_dict = {term["code"]: term for term in terms_list if term["is_active"]}
        return terms_dict

    # ------------------------------------------------------

    def _read_loinc_panels(self, path: str):
        """
        Panels may contain subpanels
        Currently "ObservationRequiredInPanel" field is ignored
        """
        panels_dict = defaultdict(list)
        df = self._read_large_csv(path)
        for index, row in df.iterrows():
            parent = row["ParentLoinc"]
            child = row["Loinc"]
            if parent == child:
                continue
            panels_dict[parent].append(child)
        return panels_dict

    # ------------------------------------------------------

    def _get_panel_terms(self, terms_dict, panels_dict, panel_code):
        results = set()
        children = panels_dict[panel_code]
        for code in children:
            if code in terms_dict:
                if terms_dict[code]["is_panel"]:
                    results |= self._get_panel_terms(terms_dict, panels_dict, code)
                else:
                    results.add(code)
        return results

    # ------------------------------------------------------

    def _get_inverted_indexes(self, terms_dict, list_of_code):
        inverted_indexes = defaultdict(list)
        for code in list_of_code:
            if code not in terms_dict:
                continue
            term = terms_dict[code]
            component = f"{term['component']} {term['display_name']}"
            words = self._split_text(component)
            for word in words:
                inverted_indexes[word].append(code)
        return inverted_indexes

    # ------------------------------------------------------

    def _update_panel_children(self, terms_dict, panels_dict):
        for panel_code in list(panels_dict.keys()):
            if panel_code not in terms_dict or not terms_dict[panel_code]["is_panel"]:
                continue
            children = list(self._get_panel_terms(terms_dict, panels_dict, panel_code))
            terms_dict[panel_code]["children"] = [code for code in children if code in terms_dict]
        return terms_dict

if __name__ == "__main__":
    loinc_service = LOINC()
    laboratory_entry: Laboratory = Laboratory()
    laboratory_entry.name = 'Eosinophil count'
    laboratory_entry.panel = 'fbc'
    laboratory_entry.unit = '10 * 9/L'
    lab_result = loinc_service.process(laboratory_entry)
    print(lab_result)
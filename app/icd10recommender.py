from sentence_transformers import SentenceTransformer
import numpy as np
import faiss
import h5py
from typing import Union


class Icd10Recommender:
    def __init__(self, model_path: str = None, embeddings_path: str = None):
        self.model_path = model_path
        self.embeddings_path = embeddings_path
        self.embedding_model = None
        self.embeddings = None
        self.index_embeddings = None
        self.code_to_disc = {}

    def load_embedding_model(self):
        if not self.embedding_model:
            self.embedding_model = SentenceTransformer(self.model_path)

    def load_embedding_model_and_embeddings(self):
        """
        Loads the sentence transformer model and precomputed embeddings.
        """
        self.load_embedding_model()
        if not self.embeddings:
            with h5py.File(self.embeddings_path, "r") as f:
                sentences = list(map(lambda x: x.decode("utf-8"), f["sentences"][:]))
                codes = list(map(lambda x: x.decode("utf-8"), f["codes"][:]))
                embeddings = f["embeddings"][:]
                self.code_to_disc = dict(zip(codes,sentences))
                self.embeddings = {
                    "embeddings": embeddings,
                    "codes": codes,
                    "sentences": sentences,
                }
            self.create_index()

    def get_embeddings(self, text: list) -> np.ndarray:
        """
        Generates embeddings for the given text.
        """
        self.load_embedding_model_and_embeddings()
        return np.array(self.embedding_model.encode(text, batch_size=32), dtype=np.float32)

    def create_index(self):
        """
        Creates a FAISS index for fast similarity search on precomputed embeddings.
        """
        self.load_embedding_model_and_embeddings()
        dim = self.embeddings["embeddings"].shape[1]
        self.index_embeddings = faiss.IndexFlatL2(dim)
        self.index_embeddings.add(
            np.array(self.embeddings["embeddings"]).astype(np.float32)
        )

    def get_icd10(self, text: list) -> list:
        """
        Recommends an ICD-10 code based on the similarity of input text to stored embeddings.
        """
        predictions = []
        # Get the query embedding
        self.load_embedding_model_and_embeddings()
        query_embeddings = self.get_embeddings(text)
        for embedding in query_embeddings:
            # Search in FAISS index
            distances, indices = self.index_embeddings.search(
                embedding.reshape(1, embedding.shape[0]), 1
            )

            # Retrieve results
            closest_index = indices[0][0]
            icd10 = self.embeddings["codes"][closest_index]
            sentence = self.embeddings["sentences"][closest_index]
            # Determine confidence and contextual prediction
            distance = distances[0][0]
            if distance < 50:
                confidence = 100
                contextual_prediction = True
            elif 50 <= distance < 100:
                confidence = 80
                contextual_prediction = True
            else:
                confidence = 20
                contextual_prediction = False

            predictions.append(
                {
                    "sentence": sentence,
                    "icd10": icd10,
                    "confidence": confidence,
                    "contextual_prediction": contextual_prediction,
                }
            )
        return predictions

    def get_icd10_recommendation(self, text: Union[str, list]) -> dict:
        if isinstance(text, str):
            return self.get_icd10([text])
        elif isinstance(text, list):
            return self.get_icd10(text)

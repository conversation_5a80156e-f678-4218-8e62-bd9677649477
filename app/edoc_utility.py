import json
import traceback
import uuid
from copy import deepcopy
from datetime import datetime, date
from operator import itemgetter
from statistics import mean
from typing import Dict, List, Optional

import constants as const
import pipe
import titles as Title
from cloud.document_type_mapping import DocumentTypeMappingDocumentType
from edoc_dicts import empty_entity
from elements.edocument import EPageData, EDocument
from field_id_entity_type_mapping import FIELD_ID_BLUEPRINTS
from friendlylib.boxes import group_into_lines
from friendlylib.iterators import find, flatten, lfilter, lmap
from friendlylib.strings import (
    get_prefix_by_delimiter,
    get_suffix_by_delimiter,
    remove_prefix_with_delimiter,
)
from medical_codes.coding import occupation_risks
from model_input_files_config import COMPANY_NAIC_CODE_DATA
from sectionning import group_into_lines_smart
from technical_dicts import duration_annuity_type_dict
from utils.dates_utility import get_formatted_date_obj
from utils.utils import check_zero_value, strip_non_alpha


class MedicationColumns:
    MEDICATION_NAME = 1


def unique_items(items, equal_func):
    new_items = []
    for idx, first_item in enumerate(items):
        if not any(equal_func(first_item, second_item) for second_item in items[:max(0, idx - 1)]):
            new_items.append(first_item)
    return new_items


def load_file(file_path):
    with open(file_path, 'rb') as fp:
        return fp.read()


def update_substance_abuse_tags(entity, page_of_lines: EPageData):
    try:
        tag_suffix = get_suffix_by_delimiter(entity['type'])
        if any(sub_tag in entity["type"] for sub_tag in const.SUBSTANCE_ABUSE_TAGS) and tag_suffix in ["yes", "no", "former", "passive"]:
            entity["text"] = tag_suffix
        elif any(sub_tag in entity["type"] for sub_tag in const.SUBSTANCE_ABUSE_PRODUCT_TAGS) and 'unit' not in entity['type']:
            product_tag = next((sub_tag for sub_tag in const.SUBSTANCE_ABUSE_PRODUCT_TAGS if sub_tag in entity["type"]),'').split('.')[0]
            new_description_entity = get_new_entity(entity, entity['type'].replace(product_tag, "status.yes"), "yes")
            add_entity_to_page(new_description_entity, page_of_lines)
        elif any(sub_tag in entity["type"] for sub_tag in const.SUBSTANCE_ABUSE_DURATION_AND_QUANTITY_TAGS) and 'unit' not in entity['type']:
            duration_or_quantity = next((sub_tag for sub_tag in const.SUBSTANCE_ABUSE_DURATION_AND_QUANTITY_TAGS if sub_tag in entity["type"]),'').split('.')[1]
            if check_zero_value(entity["text"]) or "no" in entity["text"].lower() or entity["text"].strip().lower().split(' ')[0] == 'o':
                new_description_entity = get_new_entity(entity, entity['type'].replace(duration_or_quantity, "status.no"), "no")
            else:
                new_description_entity = get_new_entity(entity, entity['type'].replace(duration_or_quantity, "status.yes"), "yes")
            add_entity_to_page(new_description_entity, page_of_lines)
    except Exception as e:
        print(f"ERROR: Update Substance abuse tags failed: {e}")


def assign_line_num(edoc, system_unit):
    try:
        line_num = 0
        for page_num, entities_in_page in enumerate(edoc):
            for line in entities_in_page:
                for entity_num, entity in enumerate(line):
                    entity["page_num"] = page_num
                    entity["line_num"] = line_num
                    entity["entity_num"] = entity_num
                    tag_suffix = get_suffix_by_delimiter(entity['type'])
                    update_substance_abuse_tags(entity, entities_in_page)
                    if (entity['type'] in ["policy_holder.relationship_with_insured_1", "policy_holder.relationship_with_insured_2"] and
                            'yes' in entity['text'].lower()):
                        entity["text"] = "self"
                    if (tag_suffix in const.TRAVEL_SUFFIXES or 'avocation.activity.risk_level' in entity['type']) and tag_suffix not in entity["text"]:
                        entity["text"] = f'{tag_suffix} : {entity["text"]}'
                line_num += 1
    except Exception as e:
        print(f'ERROR: Assign Line Number Failed: {traceback.format_exc()}')


def to_float(codified_as):
    try:
        return float(codified_as)
    except:
        return ""

def to_date(codified_as):
    try:
        date_format ="%Y-%m-%d"
        return datetime.strptime(codified_as, date_format)
    except:
        return ""


def return_string(codified_as):
    return codified_as

python_type_converters = {
    "date" : to_date,
    "height" : to_float,
    "weight" : to_float,
    "temperature" : to_float,
    "gender" : return_string,
    "" : return_string
}

# -----------------------------------------------------------------

def field_to_entity(field):
    entity = deepcopy(empty_entity)
    entity["type"] = field["value"][0]["type"]
    entity["id"] = field["value"][0]["value_id"]
    entity["text"] = field["value"][0]["value_name"]
    entity["word_ids"] = field["value"][0]["word_ids"]
    entity["confidence"] = field["value"][0]["confidence"]
    entity["left"] = field["value"][0]["bbox"]["left"]
    entity["top"] = field["value"][0]["bbox"]["top"]
    entity["right"] = field["value"][0]["bbox"]["right"]
    entity["bottom"] = field["value"][0]["bbox"]["bottom"]
    entity["abs_page"] = field["value"][0]["abs_page"]
    entity["claimid"] = field["value"][0]["claimid"]
    entity[const.SECTION_TAG] = field["value"][0].get(const.SECTION_TAG, [])
    entity[const.EXTRA] = field["value"][0].get(const.EXTRA, False)
    entity["document_id"] = field["file_ui_url"]
    entity["doc_num"] = field["doc_num"]
    entity["page_class"] = field["page_class"]
    if const.CODIFIED_AS in field and field[const.CODIFIED_AS] is not None:
        converter = python_type_converters.get(field.get(const.CODIFICATION_CATEGORY, ""),python_type_converters[""])
        entity["codified_as"] = field['codified_as']
        entity["python_codified_as"] = converter(field.get(const.CODIFIED_AS, ""))
    else:
        entity["codified_as"] = ""  # field["value"][0]["value_name"]
    entity["codification_category"] = field.get("codification_category", "")
    entity["codify"] = field.get("codify")
    entity["codified_datetime_obj"] = field.get("codified_datetime_obj")
    entity["detected_lang"] = field.get("detected_lang")
    entity["metadata"] = field.get("metadata") or {}
    entity["word_type"] = field.get("word_type")
    entity["entry_id"] = field.get("entry_id", "")
    entity["source"] = field.get("source", "")
    entity["layout_uuid"] = field.get("layout_uuid", "")
    entity["ui_id"] = field.get("ui_id", "")
    entity["xpath"] = field.get("xpath", "")
    entity["context"] = field["value"][0].get("context") or field.get("context")
    entity["section_info"] = field.get("section_info")
    entity["is_skipped"] = field.get("is_skipped", False)
    return entity


# -----------------------------------------------------------------


def is_other_entity(entity):
    return entity["type"] == "O"


def is_not_other_entity(entity):
    return not is_other_entity(entity)


def is_required_entity(entity) -> bool:
    return not entity.get("is_skipped")


def by_type(edoc_entities, filtering_type):
    return list(filter(lambda entity: entity["type"] == filtering_type, edoc_entities))


def by_many_types(edoc_entities, many_types):
    return list(filter(lambda entity: entity["type"] in many_types, edoc_entities))


def by_type_best_confidence(edoc_entities, filtering_type):
    entities_by_type = by_type(edoc_entities, filtering_type)
    if len(entities_by_type) == 0:
        return None
    return max(entities_by_type, key=lambda entity: entity["confidence"])


def add_entity_to_row(entity, blueprint):
    if entity is not None:
        blueprint_column = list(filter(lambda b: b["entity_type"] == entity["type"], blueprint))[0]
        blueprint_column["entity_object"] = entity


def add_many_entities_to_row(list_of_entities, blueprint):
    for entity in list_of_entities:
        add_entity_to_row(entity, blueprint)


def add_diag_entity_to_row(entity, blueprint):
    if entity is not None:
        blueprint_column = list(filter(lambda b: b["entity_type"] == entity["type"], blueprint))[0]
        blueprint_column["entity_object"] = entity


def add_many_diag_entities_to_row(list_of_entities, blueprint):
    for entity in list_of_entities:
        add_diag_entity_to_row(entity, blueprint)


def get_nearest_diag_shared_entities(shared_ent_collections, mother_entities):
    mother_page_num = mother_entities['page_num']
    positives, negatives = [], []
    num_dict = {}
    for page_num, shared_entities in shared_ent_collections.items():
        dif = mother_page_num - page_num
        num_dict[dif] = {'mother_page_num': mother_page_num, 'page_num:': page_num,
                         'shared_entities': deepcopy(shared_entities), 'mother_entities': deepcopy(mother_entities)}
        if dif > -1:
            positives.append(dif)
        else:
            negatives.append(dif)

    positives.sort()
    negatives.sort(reverse=True)
    if len(positives):
        shared_entities = num_dict[positives[0]]['shared_entities']
    elif len(negatives):
        shared_entities = num_dict[negatives[0]]['shared_entities']
    else:
        shared_entities = [None]
    return shared_entities


# -----------------------------------------------------------------------------------------------------------------------

def update_shared_ent_collections(shared_ent_collections, shared_entities):
    if shared_entities[0] is None:
        return shared_ent_collections

    page_num = deepcopy(shared_entities[0]['page_num'])
    shared_ent_collections[page_num] = deepcopy(shared_entities)
    return shared_ent_collections


# -----------------------------------------------------------------------------------------------------------------------

def get_nearest_shared_entities(shared_ent_collections, mother_entities):
    mother_page_num = mother_entities[0]['page_num']
    positives, negatives = [], []
    num_dict = {}
    for page_num, shared_entities in shared_ent_collections.items():
        dif = mother_page_num - page_num
        num_dict[dif] = {'mother_page_num': mother_page_num, 'page_num:': page_num,
                         'shared_entities': deepcopy(shared_entities), 'mother_entities': deepcopy(mother_entities)}
        if dif > -1:
            positives.append(dif)
        else:
            negatives.append(dif)

    positives.sort()
    negatives.sort(reverse=True)
    if len(positives):
        shared_entities = num_dict[positives[0]]['shared_entities']
    elif len(negatives):
        shared_entities = num_dict[negatives[0]]['shared_entities']
    else:
        shared_entities = [None]
    return shared_entities


# ----------------------------------------------------------------------------------------------------------------------

def _get_word_ids_from_cell(cell):
    if type(cell['word_ids'][0]) == dict:
        word_ids = list(map(
            lambda obj: obj['word_id'], cell['word_ids']
        ))
        return word_ids
    else:
        return cell['word_ids']


# ----------------------------------------------------------------------------------------------------------------------

# noinspection PyUnusedLocal,PyUnusedLocal
def build_new_word_id(words_list, cell_ref, text, claimid, conf, id):
    obj = deepcopy(words_list[0])
    word_ids = _get_word_ids_from_cell(cell_ref)
    word_ids = word_ids[0].replace(word_ids[0][0], 't')
    obj['id'] = word_ids
    obj['page'] = cell_ref['abs_page']
    obj['text'] = text
    obj['left'] = cell_ref['left']
    obj['right'] = cell_ref['right']
    obj['top'] = cell_ref['top']
    obj['bottom'] = cell_ref['bottom']
    obj['height'] = cell_ref['height']
    obj['width'] = cell_ref['width']
    obj['bbox'] = [obj['left'], obj['top'], obj['right'], obj['bottom']]
    obj['confidence'] = cell_ref['confidence']
    obj['conf'] = conf
    obj['claimid'] = cell_ref['claimid']
    obj['abs_page'] = cell_ref['abs_page']
    words_list.append(deepcopy(obj))
    return words_list, [word_ids]


def find_word_id(words_list, word_id):
    return next(filter(lambda word: word.get("id", "-1") == word_id, words_list))


def find_word_from_id(words_list, words_ids):
    words = map(lambda word_id: find_word_id(words_list, word_id), words_ids)
    words_not_none = filter(lambda word: word is not None, words)
    return list(words_not_none)


def get_updated_word_id_list(cell_words_ids, words_list):
    words_list_shortened = deepcopy(find_word_from_id(words_list, cell_words_ids))
    for word in words_list_shortened:
        word["id"] = "a" + word["id"]
    words_list.extend(words_list_shortened)
    return [word["id"] for word in words_list_shortened], words_list


def get_dict_of_rows(all_cells):
    all_rows = {}
    for cell in all_cells:
        all_rows.setdefault(str(cell["start_row"]), []).append(cell)
    return all_rows


def get_row_values(row):
    return (
            row
            | pipe.where(
        lambda cell: cell["start_column"] != MedicationColumns.MEDICATION_NAME and cell["reference"] is not None)
            | pipe.map(lambda cell: cell["reference"]["text"])
    )


def insert_confidence_column(table, words_list):
    new_table = {
        'name': deepcopy(table['name']),
        'row_count': deepcopy(table['row_count']),
        'col_count': deepcopy(table['col_count'] + 1),
        'cells': []
    }

    if (table['row_count'] < 2) or (len(table['cells']) == 0):
        return table, words_list

    ui_list_of_cells = []

    conf_header_column = deepcopy(table['cells'][0])
    conf_header_column["to_display"] = "Confidence %"
    conf_header_column["start_column"] = new_table['col_count'] - 1
    prev_row = -1
    confidence_sum = 0.0
    confidence_threshold_sum = 0.0
    counter = 0
    counter_last = -100

    for i, cell in enumerate(table['cells']):
        start_row = cell["start_row"]
        start_column = cell["start_column"]
        if cell['reference'] is not None:
            counter += 1
            confidence_sum += cell['reference']['confidence']
            confidence_threshold_sum += cell['reference']['confidence_threshold']
        if start_column == 0:
            cell_0 = deepcopy(cell)
        if (start_column == 0) and (prev_row > -1):
            if prev_row == 0:
                ui_list_of_cells.append(conf_header_column)
            else:
                confidence = round(confidence_sum / counter, 1)
                confidence_threshold = round(confidence_threshold_sum / counter, 1)
                cell_0['reference']['confidence'] = confidence
                cell_0['reference']['confidence_threshold'] = confidence_threshold
                cell_0['reference']['type'] = 'Confidence'
                id = str(uuid.uuid4())
                cell_0['reference']['id'] = id
                cell_0["start_row"] = prev_row
                cell_0["start_column"] = new_table['col_count'] - 1
                text = str(round(confidence, 0))
                cell_0['reference']['text'] = text
                claimid = cell_0['reference']['claimid']
                words_list, word_ids = build_new_word_id(words_list, text, claimid, confidence, id)
                cell_0['reference']["word_ids"] = word_ids
                ui_list_of_cells.append(cell_0)
            counter_last = counter
            confidence_sum_last = confidence_sum
            confidence_threshold_sum_last = confidence_threshold_sum

            counter = 0
            confidence_sum = 0
            confidence_threshold_sum = 0
        ui_list_of_cells.append(cell)
        prev_row = start_row

    if counter_last > 0:
        confidence = round(confidence_sum_last / counter_last, 1)
        confidence_threshold = round(confidence_threshold_sum_last / counter_last, 2)
        last_cell = deepcopy(cell)
        last_cell['reference']['confidence'] = confidence
        last_cell['reference']['confidence_threshold'] = confidence_threshold
        last_cell['reference']['type'] = 'Confidence'
        id = str(uuid.uuid4())
        last_cell['reference']['id'] = id
        last_cell["start_row"] = prev_row
        last_cell["start_column"] = new_table['col_count'] - 1
        text = str(round(confidence, 0))
        last_cell['reference']['text'] = text
        claimid = last_cell['reference']['claimid']
        words_list, word_ids = build_new_word_id(words_list, text, claimid, confidence, id)
        last_cell['reference']["word_ids"] = word_ids
        ui_list_of_cells.append(last_cell)

    new_table["cells"] = ui_list_of_cells
    return new_table, words_list


def group_entities_into_lines(list_of_entities, words, feature_list):
    try:
        if Title.DISABLE_SECTION in feature_list:
            words_in_lines = group_into_lines(words)
        else:
            words_in_lines = group_into_lines_smart(words)
        for line_num, line_of_words in enumerate(words_in_lines):
            for word in line_of_words:
                word["smart_line"] = line_num
        tagged_words = flatten(words_in_lines)
        entities_in_lines_dict = {index: [] for index in range(len(words_in_lines))}
        for one_entity in list_of_entities:
            words_per_entity = find_word_from_id(tagged_words, one_entity["word_ids"])
            if len(words_per_entity) == 0:
                line_num = 0
            else:
                line_num = min([word["smart_line"] for word in words_per_entity])
            entities_in_lines_dict[line_num].append(one_entity)
        for line_num in entities_in_lines_dict.keys():
            entities_in_lines_dict[line_num] = sorted(entities_in_lines_dict[line_num], key=itemgetter('left'))
        lines_in_one_page = []
        for line_num in sorted(entities_in_lines_dict.keys()):
            lines_in_one_page.append(entities_in_lines_dict[line_num])
        counter = 0
        for line_of_entities in lines_in_one_page:
            for entity in line_of_entities:
                entity["entity_num"] = counter
                counter += 1
        return lines_in_one_page
    except Exception as e:
        print(f'ERROR: Group Entities Into Lines Failed: {traceback.format_exc()}')
        return []


def add_document_id_to_field(field, document_claim_ids):
    document_claim_id = document_claim_ids.get(field["value"][0]["claimid"])
    field["document_claim_id"] = document_claim_id


def raw_input_to_edoc(retrieved_data):
    edoc = []
    for page_results in retrieved_data.pages:
        if page_results.skipped_reason is not None:
            continue
        list_of_fields = page_results.fields
        list_of_entities = lmap(field_to_entity, list_of_fields)
        list_of_entities = lfilter(is_not_other_entity, list_of_entities)
        list_of_entities = lfilter(is_required_entity, list_of_entities)
        words = page_results.words
        lines_of_entities = group_entities_into_lines(list_of_entities, words, retrieved_data.feature)
        edoc.append(lines_of_entities)
    system_unit = retrieved_data.system_unit.lower() if retrieved_data.system_unit else 'metric'
    assign_line_num(edoc, system_unit)
    return edoc


def edoc_to_lines(edoc):
    return flatten(edoc)


def edoc_to_entities(edoc):
    edoc_lines = edoc_to_lines(edoc)
    return flatten(edoc_lines)


def sort_by_confidence(list_of_entities, sort_key):
    ent_dict = {}
    for obj in list_of_entities:
        ent_dict.setdefault(obj['type'], []).append(obj)
    new_list_of_entities = []
    for _, lst in ent_dict.items():
        this_list = sorted(lst, key=lambda x: x.get(sort_key, 0), reverse=True)
        new_list_of_entities.extend(this_list)
    return new_list_of_entities


def sort_by_prefix_type_and_confidence(item):
    type_order = {'insured_1': 1, 'insured_2': 2, 'patient': 3, 'applicant': 4}
    client_company_assessment_order = {"policy.decision": 1}
    ssn_priority_order = {"ssn": 1, "id_number": 2, "tax.number": 3}
    underwriter_priority_order = {"email.from.name": 1, "underwriter.name": 2}
    codification_preference = 1 if item.get('codified_as') else 2
    substance_abuse_order = {'status.yes': 1, 'product': 2, 'quantity': 3, 'status.former': 4, 'duration': 5,
                             'status.passive': 6, 'status.no': 7}
    type_prefix = item['type'].split('.')[0]
    type_suffix = remove_prefix_with_delimiter(item['type'])
    product_type_priority = {"policy.product.type": 1, "policy.product.cover_type": 2}
    page_type_priority_order = {"templated": 1, "non-templated": 2}
    metadata = item.get('metadata') or {}
    page_type = metadata.get('page_type')
    if item['type'] in ["beneficiary.relationship_with_insured_1", "beneficiary.relationship_with_insured_2"]:
        return 0
    if any(tag in item["type"] for tag in const.SUBSTANCE_ABUSE_TAGS):
        substrings = item["type"].split(".")
        suffix = ".".join(substrings[2:])
        return substance_abuse_order.get(suffix, 7), type_order.get(type_prefix, 5), -item['confidence'], item['claimid'], item['abs_page']
    else:
        return (type_order.get(type_prefix, 5), client_company_assessment_order.get(type_suffix, 2), codification_preference,
                ssn_priority_order.get(type_suffix, 3), underwriter_priority_order.get(item["type"], 3), product_type_priority.get(item["type"], 3),
                page_type_priority_order.get(page_type, 3), -item['confidence'], item['claimid'], item['abs_page'])


def filter_by_priority(entities, page_classes, excluded_page_classes=None, display_names: Optional[DocumentTypeMappingDocumentType]=None):
    if excluded_page_classes:
        excluded_page_class_ids = []
        for excluded_page_class in excluded_page_classes:
            if display_names is not None:
                excluded_page_class_ids.extend(display_names.classes_for_display_name(excluded_page_class))
            else:
                excluded_page_class_ids.append(excluded_page_class)
        entities = lfilter(
            lambda entity: entity["page_class"] not in excluded_page_class_ids, 
            entities
        )
    # updated_entities = []
    all_page_class_ids = []
    for page_class in page_classes:
        if display_names is not None:
            page_class_ids = display_names.classes_for_display_name(page_class)
        else:
            page_class_ids = [page_class]
        all_page_class_ids.extend(page_class_ids)
        entities_with_this_page_class = lfilter(
            lambda entity: entity["page_class"] in page_class_ids and entity["text"] != "",
            entities
        )
        if len(entities_with_this_page_class):
            # updated_entities.extend(sorted(entities_with_this_page_class, key=sort_by_prefix_type_and_confidence))
            return sorted(entities_with_this_page_class, key=sort_by_prefix_type_and_confidence)
    if "*" in page_classes:
        return sorted(entities, key=sort_by_prefix_type_and_confidence)
    return []
    # if "*" in page_classes:
    #     entities_without_classified_page_class = lfilter(
    #         lambda entity: entity["page_class"] not in all_page_class_ids and entity["text"] != "",
    #         entities
    #     )
    #     if len(entities_without_classified_page_class):
    #         updated_entities.extend(sorted(entities_without_classified_page_class, key=sort_by_prefix_type_and_confidence))

    # return updated_entities


def sort_by_page_class(card_id, list_of_entities, priorities, display_names: Optional[DocumentTypeMappingDocumentType]):
    entities_by_field_id_dict = {}
    for entity in list_of_entities:
        if entity['type'] in FIELD_ID_BLUEPRINTS[card_id]:
            entities_by_field_id_dict.setdefault(FIELD_ID_BLUEPRINTS[card_id][entity['type']], []).append(entity)
    sorted_and_filtered_entities = []
    for priority in priorities:
        if priority.field_id not in entities_by_field_id_dict.keys():
            continue
        these_entities = entities_by_field_id_dict[priority.field_id]
        sorted_and_filtered_entities.extend(
            filter_by_priority(
                these_entities, 
                priority.page_class_list, 
                priority.page_class_exclusion_list,
                display_names=display_names
            )
        )
        entities_by_field_id_dict.pop(priority.field_id)
    for non_prioritized_field_id_entities in entities_by_field_id_dict.values():
        sorted_and_filtered_entities.extend(sorted(non_prioritized_field_id_entities, key=sort_by_prefix_type_and_confidence))
    return sorted_and_filtered_entities


def merge_rows(entity_row_list: List):
    row_value = {
        "value_name": " ".join(row["value_name"] for row in entity_row_list),
        "page_num": entity_row_list[0]["page_num"],
        "bbox": {key: func(row["bbox"][key] for row in entity_row_list)
                 for key, func in [('left', min), ('right', max), ('top', min), ('bottom', max)]},
        "word_ids": flatten([row["word_ids"] for row in entity_row_list]),
        "confidence": entity_row_list[0]["confidence"],
        "is_highlighted": entity_row_list[0]["is_highlighted"],
        "document_id": entity_row_list[0]["document_id"],
        "claimid": entity_row_list[0]["claimid"],
        "abs_page": entity_row_list[0]["abs_page"],
        "extra": entity_row_list[0]["extra"]
    }

    return row_value


def merge_entities(list_of_entities: List[Dict], entity_type, order_to_be_followed=None, text=None, deliminator=" ") -> Optional[Dict]:
    if len(list_of_entities) == 0:
        return None
    list_of_entities = order_by_naming_convention(list_of_entities, order_to_be_followed)
    first_entity = min(list_of_entities, key=lambda _entity: (_entity["abs_page"], _entity["top"], _entity["left"]))
    entity = deepcopy(empty_entity)
    entity["type"] = entity_type
    entity["id"] = str(uuid.uuid4())
    entity["text"] = text.strip() if text else deliminator.join(_entity["text"].strip() for _entity in list_of_entities)
    entity["word_ids"] = first_entity["word_ids"] if text else flatten([_entity["word_ids"] for _entity in list_of_entities])
    entity["left"] = first_entity["left"] if text else min([_entity["left"] for _entity in list_of_entities])
    entity["top"] = first_entity["top"] if text else min([_entity["top"] for _entity in list_of_entities])
    entity["right"] = first_entity["right"] if text else max([_entity["right"] for _entity in list_of_entities])
    entity["bottom"] = first_entity["bottom"] if text else max([_entity["bottom"] for _entity in list_of_entities])
    entity["abs_page"] = first_entity["abs_page"]
    entity["claimid"] = first_entity["claimid"]
    entity["document_id"] = first_entity["document_id"]
    entity["doc_num"] = first_entity["doc_num"]
    entity["page_class"] = first_entity["page_class"]
    entity["page_num"] = first_entity["page_num"]
    entity["line_num"] = first_entity["line_num"]
    entity["entity_num"] = first_entity["entity_num"]
    entity["detected_lang"] = first_entity["detected_lang"]
    entity["codify"] = first_entity["codify"]
    entity["codified_as"] = first_entity["codified_as"]
    entity["metadata"] = first_entity["metadata"]
    entity["word_type"] = first_entity["word_type"]
    entity["entry_id"] = first_entity["entry_id"]
    entity["source"] = first_entity["source"]
    entity["layout_uuid"] = first_entity["layout_uuid"]
    entity["ui_id"] = first_entity["ui_id"]
    entity["xpath"] = first_entity["xpath"]
    entity["context"] = first_entity["context"]
    entity["codified_datetime_obj"] = first_entity["codified_datetime_obj"]
    entity["section_info"] = first_entity["section_info"]
    entity["is_skipped"] = first_entity["is_skipped"]
    if 'address' in entity_type and not text:
        entity["confidence"] = mean([_entity["confidence"] for _entity in list_of_entities]) * (len(list_of_entities) / 5)
    else:
        entity["confidence"] = mean([_entity["confidence"] for _entity in list_of_entities])
    return entity


def order_by_naming_convention(list_of_name_entities, order_to_be_followed):
    if not order_to_be_followed:
        return list_of_name_entities
    ordered_list_of_name_entities = []
    for part_name in order_to_be_followed:
        filtered_list_of_name_entities = lfilter(
            lambda entity: entity["type"].split('.')[-1] == part_name,
            list_of_name_entities
        )
        ordered_list_of_name_entities.extend(filtered_list_of_name_entities)
    return ordered_list_of_name_entities


def field_list_item(fields_list: List[Dict], row_index: int) -> Dict:
    return fields_list[row_index]['row']['value'][0]


def find_entity_by_name(fields_list: List[Dict], value_name: str) -> Dict:
    field_entities = lmap(
        lambda field_item: field_item["row"]["value"][0],
        fields_list
    )
    return find(
        lambda item: item["value_name"] == value_name,
        field_entities
    )


def find_next_entity_index_by_name(fields_list: List[Dict], value_name: str):
    row_index = find(
        lambda row_index: field_list_item(fields_list, row_index)["value_name"] == value_name,
        range(len(fields_list))
    )
    if row_index is None:
        return None
    return row_index + 1


def find_next_entity_by_name(fields_list: List[Dict], value_name: str):
    next_entity_index = find_next_entity_index_by_name(fields_list, value_name)
    if next_entity_index is None:
        return None
    return field_list_item(fields_list, next_entity_index)


def get_duration_type(field_list):
    durations = list(duration_annuity_type_dict.keys())
    for field in field_list:
        e_type = field['row']['value'][0]['entity_type']
        if isinstance(e_type, str) and e_type in durations:
            if field['row']['value'][0]['value_name'] not in ('', 'Duration'):
                return e_type
    return None

def edoc_to_pages(edoc):
    return [flatten(page_of_lines) for page_of_lines in edoc]


def remove_duplicate_entities(list_of_entities):
    if not list_of_entities:
        return []

    unique_entities = {}

    for entity in list_of_entities:
        text = entity['text'].lower()
        key = (entity['type'], entity['abs_page'], entity['line_num'])
        if key not in unique_entities or unique_entities[key]['text'].lower() in text:
            unique_entities[key] = entity

    return list(unique_entities.values())


def get_field_wise_allowed_values(card_wise_field_validations, card_name):
    field_wise_allowed_values = {}
    field_details = get_field_validations(card_wise_field_validations, card_name)
    for field_dict in field_details:
        field = get_suffix_by_delimiter(field_dict.get('field_id', ''))
        allowed_values = field_dict.get('allowed_values', [])
        if field and allowed_values:
            field_wise_allowed_values[field] = allowed_values
    return field_wise_allowed_values


def get_field_validations(card_wise_field_validations, card_name):
    for card_details in card_wise_field_validations:
        if card_details.get('card', '').lower() == card_name.lower():
            return card_details['fields']
    return []


def add_entity_to_page(entity, page_of_lines):
    matching_line = find(
        lambda line: len(line) and line[0]["line_num"] == entity["line_num"],
        page_of_lines
    )
    if matching_line:
        matching_line.append(entity)


def get_new_entity(entity, entity_type, entity_text):
    new_entity = deepcopy(entity)
    new_entity['id'] = str(uuid.uuid4())
    new_entity['type'] = entity_type
    new_entity['text'] = entity_text
    new_entity['codified_as'] = entity_text
    new_entity['codify'] = None
    new_entity['metadata'].update({'card_generated': True})
    return new_entity


def add_us_citizen_tag(edoc):
    for page_of_lines in edoc:
        page_of_entities = flatten(page_of_lines)
        citizenship_entity_list = lfilter(
            lambda entity: 'citizenship' in entity['type'],
            page_of_entities
        )
        us_citizen_entity_list = lfilter(
            lambda entity: 'us_citizen.yes' in entity['type'],
            page_of_entities
        )
        if len(citizenship_entity_list) == 0 and len(us_citizen_entity_list) == 0:
            continue
        for entity in citizenship_entity_list:
            entity_type = entity['type'].replace('citizenship', 'us_citizen')
            if strip_non_alpha(entity['text'].lower()) in const.US_NAMES:
                entity_text = 'yes'
            else:
                entity_text = 'no'
            new_entity = get_new_entity(entity, entity_type, entity_text)
            add_entity_to_page(new_entity, page_of_lines)

        for entity in us_citizen_entity_list:
            entity_type = entity['type'].replace('us_citizen.yes', 'citizenship')
            new_entity = get_new_entity(entity, entity_type, entity_text='United States')
            add_entity_to_page(new_entity, page_of_lines)


def add_us_resident_tag(edoc):
    for page_of_lines in edoc:
        page_of_entities = flatten(page_of_lines)
        country_and_state_entity_list = lfilter(
            lambda entity: 'address.country' in entity['type']
                           or 'address.state' in entity['type']
                           or 'resident_state' in entity['type'],
            page_of_entities
        )
        if len(country_and_state_entity_list) == 0:
            continue
        for entity in country_and_state_entity_list:
            entity_prefix = get_prefix_by_delimiter(entity['type'])
            us_entity_type = f'{entity_prefix}.{const.GENERATED_ENTITY + "us_resident"}'
            stripped_text = strip_non_alpha(entity['text'].lower())
            if not stripped_text:
                continue
            if stripped_text in const.US_NAMES + const.US_STATES + const.US_STATES_ABS:
                entity_text = 'yes'
            else:
                entity_text = 'no'
            new_entity = get_new_entity(entity, us_entity_type, entity_text)
            add_entity_to_page(new_entity, page_of_lines)

            if 'country' in entity['type']:
                residentship_entity_type = f'{entity_prefix}.{const.GENERATED_ENTITY + "residentship"}'
                new_entity = get_new_entity(entity, residentship_entity_type, stripped_text)
                add_entity_to_page(new_entity, page_of_lines)

            if 'state' in entity['type'] and entity_text == 'yes':
                residentship_entity_type = f'{entity_prefix}.{const.GENERATED_ENTITY + "residentship"}'
                new_entity = get_new_entity(entity, residentship_entity_type, "USA")
                add_entity_to_page(new_entity, page_of_lines)


def add_build_vitals_tag(edoc):
    for page_of_lines in edoc:
        page_of_entities = flatten(page_of_lines)
        height_entity_list = lfilter(
            lambda entity_: 'height_cm' in entity_['type'] or 'height_in' in entity_['type'],
            page_of_entities
        )
        for entity in height_entity_list:
            header_entity_type = const.GENERATED_ENTITY + "build_height_header"
            new_header_entity = get_new_entity(entity, header_entity_type, 'body height')
            add_entity_to_page(new_header_entity, page_of_lines)

            entity_type = const.GENERATED_ENTITY + "build_height"
            entity_text = entity.get('codified_as') + ' cm' if entity.get('codified_as') else entity['text']
            new_entity = get_new_entity(entity, entity_type, entity_text)
            add_entity_to_page(new_entity, page_of_lines)

        weight_entity_list = lfilter(
            lambda entity_: 'weight_kg' in entity_['type'] or 'weight_lbs' in entity_['type'],
            page_of_entities
        )
        for entity in weight_entity_list:
            entity_type = const.GENERATED_ENTITY + "build_weight_header"
            new_entity = get_new_entity(entity, entity_type, 'body weight')
            add_entity_to_page(new_entity, page_of_lines)

            entity_type = const.GENERATED_ENTITY + "build_weight"
            entity_text = entity.get('codified_as') + ' kg' if entity.get('codified_as') else entity['text']
            new_entity = get_new_entity(entity, entity_type, entity_text)
            add_entity_to_page(new_entity, page_of_lines)

        bmi_entity_list = lfilter(
            lambda entity_: 'bmi' in entity_['type'],
            page_of_entities
        )
        for entity in bmi_entity_list:
            entity_type = const.GENERATED_ENTITY + "build_bmi_header"
            new_entity = get_new_entity(entity, entity_type, 'body mass index')
            add_entity_to_page(new_entity, page_of_lines)

            entity_text = entity.get('codified_as') if entity.get('codified_as') else entity['text']
            entity_type = const.GENERATED_ENTITY + "build_bmi"
            new_entity = get_new_entity(entity, entity_type, entity_text)
            add_entity_to_page(new_entity, page_of_lines)


def add_impairment_specific_tag(edoc):
    for page_of_lines in edoc:
        page_of_entities = flatten(page_of_lines)
        impairment_specific_entity_list = lfilter(
            lambda entity_: entity_['type'] in const.IMPAIRMENT_SPECIFIC_TAGS,
            page_of_entities
        )
        for entity in impairment_specific_entity_list:
            entity_type_parts = entity['type'].split('.')
            entity_type = const.GENERATED_ENTITY + entity_type_parts[1]
            entity_text = entity_type_parts[1]
            new_entity = get_new_entity(entity, entity_type, entity_text)
            add_entity_to_page(new_entity, page_of_lines)


def create_given_name_from_other_name(edoc):
    for page_of_lines in edoc:
        page_of_entities = flatten(page_of_lines)
        other_name_entity_list = lfilter(
            lambda entity_: 'name.other_name' in entity_['type'],
            page_of_entities
        )
        for entity in other_name_entity_list:
            entity_type = entity['type'].replace('other_name', 'given_name')
            new_entity = get_new_entity(entity, entity_type, entity['text'])
            add_entity_to_page(new_entity, page_of_lines)

def contains_occupation_risk(occupation_entry: str) -> bool:
    normalized_input = occupation_entry.lower()
    for risk in occupation_risks:
        if risk.lower() in normalized_input:
            return True
    return False


def add_occupation_risk(edoc):
    for page_of_lines in edoc:
        page_of_entities = flatten(page_of_lines)
        occupation_entity_list = lfilter(
            lambda entity_: 'occupation' in entity_['type'],
            page_of_entities
        )
        for entity in occupation_entity_list:
            is_risk = "no"
            confidence = 90
            if contains_occupation_risk(entity["text"]):
                is_risk = "yes"
                confidence = 100
            new_entity = get_new_entity(entity, get_prefix_by_delimiter(entity['type']) + ".occupation.risk." + is_risk, is_risk)
            new_entity['confidence'] = confidence
            add_entity_to_page(new_entity, page_of_lines)

def add_treaty_company_naic_code(edoc):
    with open(COMPANY_NAIC_CODE_DATA, "r") as f:
        company_naic_dict = json.load(f)
    for page_of_lines in edoc:
        page_of_entities = flatten(page_of_lines)
        company_entity_list = lfilter(
            lambda entity_: entity_['type'] in ['cedent.company.name', 'reinsurer.company.name'],
            page_of_entities
        )
        for entity in company_entity_list:
            if naic_code := company_naic_dict.get(entity["text"].lower().strip()):
                new_entity = get_new_entity(entity, get_prefix_by_delimiter(entity['type']) + ".naic_company_code", naic_code)
                add_entity_to_page(new_entity, page_of_lines)


def add_treaty_inforce_status(edoc):
    close_date = None

    for page_of_lines in edoc:
        page_of_entities = flatten(page_of_lines)
        agreement_termination_date_entity_list = lfilter(
            lambda entity_: entity_['type'] == 'agreement.status.termination_date',
            page_of_entities
        )
        for entity in agreement_termination_date_entity_list:
            # if not entity.get('codified_as'):
            #     continue
            # close_date = datetime.strptime(entity['codified_as'], "%Y-%m-%d").date()
            close_date = get_formatted_date_obj(entity['codified_as'] or entity['text'], allow_none=True)
            if not close_date:
                continue
            today = date.today()
            if close_date < today:
                inforce_status = 'Terminated'
            else:
                inforce_status = 'Open'
            new_entity = get_new_entity(entity, 'agreement.status.inforce.active', inforce_status)
            add_entity_to_page(new_entity, page_of_lines)
            break

    if not close_date:
        for page_of_lines in edoc:
            page_of_entities = flatten(page_of_lines)
            agreement_end_date_entity_list = lfilter(
                lambda entity_: entity_['type'] == 'agreement.status.end_date',
                page_of_entities
            )
            for entity in agreement_end_date_entity_list:
                if not entity.get('codified_as'):
                    continue
                close_date = datetime.strptime(entity['codified_as'], "%Y-%m-%d").date()
                today = date.today()
                if close_date < today:
                    inforce_status = 'Closed'
                else:
                    inforce_status = 'Open'
                new_entity = get_new_entity(entity, 'agreement.status.inforce.active', inforce_status)
                add_entity_to_page(new_entity, page_of_lines)
                break


def get_merged_policy_purpose_entity(list_of_entities):
    policy_purpose_entity_list = lfilter(
        lambda entity_: entity_['type'] == 'policy.purpose',
        list_of_entities
    )
    if not policy_purpose_entity_list:
        return None
    merged_entity = merge_entities(policy_purpose_entity_list, const.MERGED_ENTITY + 'policy.purpose', deliminator=', ')
    merged_entity['metadata'].update({'card_generated': True})
    return merged_entity
            

def insert_customized_tags(edoc: EDocument):
    try:
        add_us_citizen_tag(edoc)
        add_us_resident_tag(edoc)
        # add_build_vitals_tag(edoc)
        add_impairment_specific_tag(edoc)
        create_given_name_from_other_name(edoc)
        add_occupation_risk(edoc)
        add_treaty_company_naic_code(edoc)
        add_treaty_inforce_status(edoc)
    except Exception as e:
        print(f'ERROR: Insert Customized Tags Failed: {traceback.format_exc()}')


def get_related_tags_list(edoc, related_tags):
    try:
        related_entities = []
        for page_of_lines in edoc:
            page_of_entities = flatten(page_of_lines)
            pagewise_related_entity_list = lfilter(
                lambda entity: entity['type'] in related_tags,
                page_of_entities
            )
            related_entities.extend(pagewise_related_entity_list)
        return related_entities
    except Exception as e:
        print(f'ERROR: Get Related Tags List Failed: {traceback.format_exc()}')
        return []


def get_value_wise_category_frequency(list_of_entities):
    try:
        value_wise_category_frequency = {}
        for entity in list_of_entities:
            cleaned_value = entity['text'].strip().lower()
            category = get_prefix_by_delimiter(entity['type'])

            # If the text is not in the map, add it with an empty category count
            if cleaned_value not in value_wise_category_frequency:
                value_wise_category_frequency[cleaned_value] = {}

            # Update the category count for the text
            value_wise_category_frequency[cleaned_value][category] = value_wise_category_frequency[cleaned_value].get(category, 0) + 1

        return value_wise_category_frequency

    except Exception as e:
        print(f'ERROR: Get Value Wise Category Frequency Failed: {traceback.format_exc()}')
        return {}


def get_value_wise_average_confidence(list_of_entities):
    try:
        confidence_sum = {}
        count = {}

        for entity in list_of_entities:
            cleaned_value = entity['text'].strip().lower()
            confidence = entity.get("confidence")

            if cleaned_value is not None and confidence is not None:
                confidence_sum[cleaned_value] = confidence_sum.get(cleaned_value, 0) + confidence
                count[cleaned_value] = count.get(cleaned_value, 0) + 1

        average_confidence = {name: confidence_sum[name] / count[name] for name in confidence_sum}

        return average_confidence

    except Exception as e:
        print(f'ERROR: Get Value Wise Average Confidence Failed: {traceback.format_exc()}')
        return {}


def identify_insured_persons(edoc):
    """
    1. Calculate category wise frequency count and average confidence for every name
    2. Find 2nd insured name by sorting insured_2 count, count of name and average confidence of name
    3. Repeat step 2 for 1st insured while excluding the insured_2 person if found
    4. return {'insured_1': insured_1_name, 'insured_2': insured_2_name}
    """
    try:
        insured_1_counts = {}
        insured_2_counts = {}
        insured_name_related_tags = ['insured_1.name', 'insured_2.name', 'patient.name', 'applicant.name']

        insured_related_name_entity_list = get_related_tags_list(edoc, insured_name_related_tags)
        namewise_category_frequency = get_value_wise_category_frequency(insured_related_name_entity_list)
        namewise_average_confidence = get_value_wise_average_confidence(insured_related_name_entity_list)

        for name, category_dict in namewise_category_frequency.items():
            if 'insured_1' in category_dict:
                insured_1_counts[name] = (category_dict.get('insured_1'), sum(category_dict.values()), namewise_average_confidence[name])

            if 'insured_2' in category_dict:
                insured_2_counts[name] = (category_dict.get('insured_2'), sum(category_dict.values()), namewise_average_confidence[name])

        max_insured_2_name = max(insured_2_counts,
                                 key=lambda name: insured_2_counts[name]) if insured_2_counts else None

        insured_1_counts.pop(max_insured_2_name, None)

        max_insured_1_name = max(insured_1_counts,
                                 key=lambda name: insured_1_counts[name]) if insured_1_counts else None

        insured_person_names = {'insured_1': max_insured_1_name, 'insured_2': max_insured_2_name}

        return insured_person_names
    except Exception as e:
        print(f'ERROR: Indentify Insured Persons Failed: {traceback.format_exc()}')
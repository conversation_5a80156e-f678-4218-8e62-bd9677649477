import traceback
from copy import deepcopy
from typing import Dict

import edoc_utility
import insured_utils
import patient_dicts
import table_utils
import titles as Title
from allergy_utility import AllergenUtility
from application_details_utility import ApplicationDetailsUtility
from cancer_utility import CancerUtility
from cardiovascular_utility import CardiovascularUtility
from ccda_vitals_utility import VitalsUtility
from claim_injury_utility import ClaimInjuryUtility
from claim_utility import ClaimUtility
from claims_details_utility import ClaimsDetailsUtility
from dental_details_utility import DentalDetailsUtility
from diabetes_utility import DiabetesUtility
from diag_utility import DiagnosisUtility
from elements.address_utility import Address, insert_address
from elements.edocument import EDocument, Entity
from elements.human_name import HumanName, insert_human_names
from elements.person_clustering import Person, create_person_object
from elements.smoking_status import calculate_smoking_status
from encounter_details_utility import EncounterDetails
from encounter_dx_details_utility import EncounterDxDetailsUtility
from excel_summary import ExcelSummary
from family_utility import FamilyHistoryUtility
from field_id_entity_type_mapping import get_schema_id
from friendlylib import simpleprofile
from friendlylib.iterators import lfilter
from immunizations_utility import ImmunizationUtility
from impairment_build_card_utility import ImpairmentBuildCardUtility
from impairment_utility import ImpairmentUtility
from informals_utility import InformalsUtility
from medical_codes.card_reference import CardCodingReference
from medical_codes.medical_coding_utility import (
    MedicalCodingUtility,
    populate_coding_tags,
    update_checklist_tags_text
)
from medical_equipment_utility import MedicalEquipment
from medication_conditions import MedicationConditions
from mental_nervous_disorder import MentalNervousDisorderUtility
from observation_utility import ObservationUtility
from patient_dicts import insured_names_blueprint
from patient_utils import PatientUtility
from physical_exam_utility import PhysicalExamUtility
from procedures_utility import ProceduresUtility
from provider_details_utility import ProviderDetailsUtility
from reason_utility import ReasonsUtility
from social_history_utility import SocialHistoryUtility
from study_utility import StudyUtility
from subjective_details_utility import SubjectiveDetailsUtility
from technical_dicts import technical_names_blueprint
from user_defined_cards.new_card import create_user_defined_cards
from utils.utils import check_document_type_is_aps
from vital_signs import VitalSigns


class EndPoint:

    def __init__(self):
        """   Constructor """
        self.umls_service = None
        self.words_list = None
        self.smoking_service = None
        self.alcohol_service = None
        self.misc = None
        self.patient_util_service = None
        self.retrieved_data = None
        self.local_testing = False
        self.card_wise_field_validations = None
        self.failed_cards_list = []
        self.card_references = None

    # ----------------------------------------------------------------------------------------------------------------------------------------------

    def set_dependencies(self, obj_misc,
                         smoking_service,
                         alcohol_service,
                         words_list: list,
                         retrieved_data,
                         local_testing: bool,
                         card_wise_field_validations,
                         medical_coding_service: MedicalCodingUtility,
                         card_references: CardCodingReference):

        self.misc = obj_misc
        self.smoking_service = smoking_service
        self.alcohol_service = alcohol_service
        self.words_list = words_list
        self.patient_util_service = PatientUtility()
        self.retrieved_data = retrieved_data
        self.local_testing = local_testing
        self.card_wise_field_validations = card_wise_field_validations
        self.medical_coding_service: MedicalCodingUtility = medical_coding_service
        self.card_references = card_references

    # ------------------------------------------------------------------------------------------------------------------

    def multiple_insured_cards(self, edoc: EDocument):
        try:
            card_status = {insured_names_blueprint[0]: False, insured_names_blueprint[1]: False}
            used_ids = []
            insured_1_card, card_status, used_ids = self.patient_util_service.build_insured_card(edoc, card_status,
                                                                                                 used_ids,
                                                                                                 insured_names_blueprint[
                                                                                                     0])
            # insured_2_card, card_status, used_ids = self.patient_util_service.build_insured_card(edoc, card_status,
            #                                                                                      used_ids,
            #                                                                                      insured_names_blueprint[1])
            # if not card_status[insured_names_blueprint[1]]:
            #     insured_2_card = None
            # insured_1_card, insured_2_card = self.patient_util_service.suppress_insured_card(insured_1_card, insured_2_card)

            return [insured_1_card, None]
        except Exception as e:
            print(f'ERROR: Insured Card Creation Failed: {e}')
            self.failed_cards_list.append({'card_name': 'Insured Details', 'message': f'{traceback.format_exc()}'})
            return [None, None]

    # -----------------------------------------------------------------

    def multiple_technical_cards(self, edoc: EDocument):
        try:
            used_ids = []
            product_type = []
            field_wise_allowed_values = edoc_utility.get_field_wise_allowed_values(self.card_wise_field_validations,
                                                                                   'technical_details')
            technical_cards, used_ids, product_type = self.patient_util_service.build_technical_card(edoc,
                                                                                                     used_ids,
                                                                                                     technical_names_blueprint,
                                                                                                     product_type,
                                                                                                     field_wise_allowed_values)
            return technical_cards
        except Exception as e:
            print(f'ERROR: Technical Details Card Creation Failed: {e}')
            self.failed_cards_list.append({'card_name': 'Technical Details', 'message': f'{traceback.format_exc()}'})
            return []

    # -----------------------------------------------------------------

    @staticmethod
    def get_split_names(name_entity: Dict[str, Entity]):
        name_entity_list = []
        for attr_name, attr_val in name_entity.items():
            if attr_name in ["given_name", "family_name"] and attr_val:
                name_entity_list.append(attr_val)
        return name_entity_list

    @staticmethod
    def get_split_addresses(address_entity: Dict):
        address_entity_list = []
        for attr_name, attr_val in address_entity.items():
            if attr_name in ["line", "city", "state", "country", "zipcode"] and attr_val:
                address_entity_list.append(attr_val)
        return address_entity_list

    def get_list_of_entities(self, person_object: Person):
        list_of_entities = []
        for attr_name, attr_val in vars(person_object).items():
            if attr_val is None:
                continue
            if isinstance(attr_val, dict):
                list_of_entities.append(attr_val)
            elif isinstance(attr_val, HumanName):
                list_of_entities.append(vars(attr_val).get('text'))
                name_entities = self.get_split_names(vars(attr_val))
                list_of_entities.extend(name_entities)
            elif isinstance(attr_val, Address):
                list_of_entities.append(vars(attr_val).get('text'))
                address_entities = self.get_split_addresses(vars(attr_val))
                list_of_entities.extend(address_entities)
        return list_of_entities

    def get_multiple_insured_cards(self, filtered_unique_person_objects: Dict[str, Person]):
        try:
            insured_cards = []
            used_ids = []
            card_name = Title.INSURED_DETAILS
            insured_number = 1
            for id_, person_obj in filtered_unique_person_objects.items():
                insured_board = deepcopy(patient_dicts.empty_insured_1)
                list_of_entities = self.get_list_of_entities(person_obj)
                lst = ["insurance_rated_differentialy_or_declined", "filed_bankruptcy",
                       "name", "insured_type", "name.given_name", "name.family_name", "address", "address.line",
                       "address.city", "address.state", "address.zipcode", "address.country",
                       "gender", "birth", "place_of_birth", "birth_state", "marital",
                       "weight", "height",
                       "alcohol", "smoking",
                       "drugs", "marijuana", "ssn", "avocation", "travel", "behavior", "owner_relationship",
                       "beneficiary_relationship",
                       "citizenship", "residentship", "license_number", "income", "license_state", "us_citizen",
                       "sign_date",
                       "sign_state", "client_company_assessment", "us_resident", "net_worth",
                       "occupation", "occupation_risk", "occupation_duty", "age", "policy_reason"]
                for x in lst:
                    this_entity, used_ids = insured_utils.fetch_insured_object(list_of_entities,
                                                                               insured_board[x]["entity_type_aliases"],
                                                                               used_ids)
                    insured_board[x]["entity"] = this_entity
                insured_table = table_utils.key_value_board_to_table(insured_board, card_name)
                insured_card = table_utils.table_to_ui_card(insured_table, f"insured-{insured_number}")
                insured_card['card']['record_locations'] = person_obj.name_record_locations
                insured_cards.append(insured_card)
                if insured_number == 1:
                    self.card_references.informals.age_entity = insured_board['age']['entity']
                    self.card_references.informals.smoking_entity = insured_board['smoking']['entity']
                insured_number += 1
            return insured_cards
        except Exception as e:
            print(f'ERROR: Multiple Insured Card Creation Failed: {e}')
            self.failed_cards_list.append(
                {'card_name': 'Multiple Insured Details', 'message': f'{traceback.format_exc()}'})
            return []

    def set_dependencies_and_build_card(self, card_service, dependencies, edoc, failed_cards_list):
        service_instance = card_service()
        if hasattr(service_instance, 'set_dependencies'):
            dep_args = [getattr(self, dep) if isinstance(dep, str) else dep for dep in dependencies]
            service_instance.set_dependencies(*dep_args)
        if hasattr(service_instance, 'build_card'):
            return service_instance.build_card(edoc, failed_cards_list)

    def extract_cards(self, ccda_data, insurer_pages):
        profile = simpleprofile.SimpleProfile('Cards Construction')
        selected_recall_list = self.retrieved_data.recall_cards_list
        update_checklist_tags_text(self.retrieved_data)
        edoc: EDocument = edoc_utility.raw_input_to_edoc(self.retrieved_data)
        profile.mark('edoc.making')

        medicalcoding_obj = None
        if not self.retrieved_data.has_medical_codes:
            medicalcoding_obj: MedicalCodingUtility = populate_coding_tags(edoc, self.medical_coding_service)

        if medicalcoding_obj:
            self.medical_coding_service: MedicalCodingUtility = medicalcoding_obj

        profile.mark('Populate Coding Tags')

        calculate_smoking_status(edoc, self.card_references)
        edoc_utility.insert_customized_tags(edoc)

        insert_human_names(edoc, self.retrieved_data, self.misc)
        insured_persons_info = edoc_utility.identify_insured_persons(edoc)
        filtered_unique_person_objects, person_json_list = create_person_object(edoc, insured_persons_info,
                                                                                self.retrieved_data, self.misc)
        insert_address(edoc)

        self.patient_util_service.set_dependencies(self.misc,
                                                   self.retrieved_data,
                                                   self.words_list)

        card_id_class_mapping = {
            "family_history": {"class_name": FamilyHistoryUtility,
                               "dependencies": ["misc", "retrieved_data", "medical_coding_service"]},
            "social_history": {"class_name": SocialHistoryUtility,
                               "dependencies": ["retrieved_data", "misc", "smoking_service",
                                                "alcohol_service"]},
            "ccda_vitals": {"class_name": VitalsUtility,
                            "dependencies": ["misc", "retrieved_data", "medical_coding_service", "card_references"]},
            "medical_equipment": {"class_name": MedicalEquipment,
                                  "dependencies": ["retrieved_data", "misc"]},
            "provider_details": {"class_name": ProviderDetailsUtility,
                                 "dependencies": ["retrieved_data", "misc"]},
            "subjective_details": {"class_name": SubjectiveDetailsUtility,
                                   "dependencies": ["retrieved_data", "misc"]}
        }

        extracted_cards = []

        for card_id, class_attrs in card_id_class_mapping.items():
            if not selected_recall_list or card_id in selected_recall_list:
                service, dependencies = class_attrs['class_name'], class_attrs['dependencies']
                card = self.set_dependencies_and_build_card(service, dependencies, edoc, self.failed_cards_list)
                if card:
                    extracted_cards.append(card)
                    profile.mark(card_id)

        # family_history_util_service = FamilyHistoryUtility()
        # family_history_util_service.set_dependencies(self.misc, self.retrieved_data, self.snomed_service,
        #                                              self.medcat_utility)
        # family_history_card = family_history_util_service.build_card(edoc, self.failed_cards_list)
        # profile.mark(Title.FAMILY_HISTORY)

        insured_cards = []
        insured_1_card, insured_2_card = None, None
        if not selected_recall_list or get_schema_id(Title.INSURED_DETAILS) in selected_recall_list:
            if len(filtered_unique_person_objects) == 0 or (Title.USE_PATIENT_TAGS in self.retrieved_data.feature and
                                                            check_document_type_is_aps(
                                                                self.retrieved_data.document_type)):
                insured_cards = self.multiple_insured_cards(edoc)
            else:
                insured_cards = self.get_multiple_insured_cards(filtered_unique_person_objects)

            if len(insured_cards) > 0:
                insured_1_card = insured_cards[0]
            profile.mark(Title.INSURED_DETAILS)

        technical_cards = []
        technical_1_card, technical_2_card = None, None
        if not selected_recall_list or get_schema_id(Title.TECHNICAL_DETAILS) in selected_recall_list:
            technical_cards = self.multiple_technical_cards(edoc)

            if len(technical_cards) > 0:
                technical_1_card = technical_cards[0]
            profile.mark(Title.TECHNICAL_DETAILS)

        application_details_util_card = []
        if not selected_recall_list or get_schema_id(Title.APPLICATION_DETAILS) in selected_recall_list:
            application_details_util = ApplicationDetailsUtility()
            application_details_util.set_dependencies(self.retrieved_data)
            application_details_util_card = application_details_util.build_application_card(edoc,
                                                                                            self.failed_cards_list)
            profile.mark(Title.APPLICATION_DETAILS)

        # social_status_service = SocialHistoryUtility()
        # social_status_service.set_dependencies(self.retrieved_data, self.misc, self.smoking_service,
        #                                        self.alcohol_service)
        # social_status_cards = social_status_service.build_card(edoc, self.failed_cards_list)
        # profile.mark(Title.SOCIAL_HISTORY)

        claim_1_card = None
        if not selected_recall_list or get_schema_id(Title.CLAIM_DETAIL) in selected_recall_list:
            claim_util_service = ClaimUtility()
            claim_util_service.set_dependencies(self.misc,
                                                self.retrieved_data,
                                                insured_1_card,
                                                insured_2_card,
                                                technical_1_card,
                                                technical_2_card,
                                                self.words_list)
            claim_1_card = claim_util_service.build_claim_cards(edoc, self.failed_cards_list)
            profile.mark(Title.CLAIM_DETAIL)

        physical_exam_card = None
        if not selected_recall_list or get_schema_id(Title.PHYSICAL_EXAMS) in selected_recall_list:
            physical_exam_service = PhysicalExamUtility()
            physical_exam_service.set_dependencies(self.retrieved_data, self.misc)
            physical_exam_card = physical_exam_service.build_card(edoc, self.failed_cards_list)
            profile.mark(Title.PHYSICAL_EXAMS)

        vital_signs_card = None
        if not selected_recall_list or get_schema_id(Title.VITAL_SIGNS) in selected_recall_list:
            vital_service = VitalSigns()
            vital_service.set_dependencies(self.misc, self.retrieved_data)
            vital_signs_card, self.words_list = vital_service.build_vital_card(edoc, ccda_data, self.words_list,
                                                                               self.failed_cards_list)
            profile.mark(Title.VITAL_SIGNS)

        profile.mark(Title.CCDA_VITALS)
        diagnosis_procedures = []
        diag_card = None
        if not selected_recall_list or get_schema_id(Title.DIAGNOSIS) in selected_recall_list:
            diag_util_service = DiagnosisUtility()
            diag_util_service.set_dependencies(self.misc, self.retrieved_data, self.medical_coding_service,
                                               self.card_references)
            diag_card, diagnosis_procedures = diag_util_service.build_diag_card(edoc,
                                                                                ccda_data,
                                                                                self.failed_cards_list)
            profile.mark(Title.DIAGNOSIS)

        encounter_dx_card = None
        if not selected_recall_list or get_schema_id(Title.ENCOUNTER_DX_DETAILS) in selected_recall_list:
            encounter_dx_service = EncounterDxDetailsUtility()
            encounter_dx_service.set_dependencies(self.misc, self.retrieved_data,
                                                  self.medical_coding_service)
            encounter_dx_card = encounter_dx_service.build_encounter_dx_card(edoc,
                                                                             ccda_data,
                                                                             self.failed_cards_list)
            profile.mark(Title.ENCOUNTER_DX_DETAILS)

        claim_injury_card = None
        if not selected_recall_list or get_schema_id(Title.CLAIMED_INJURY_DETAILS) in selected_recall_list:
            claim_injury_service = ClaimInjuryUtility()
            claim_injury_service.set_dependencies(self.misc, self.medical_coding_service)
            claim_injury_card, self.words_list = claim_injury_service.build_claim_injury_card(edoc,
                                                                                              ccda_data,
                                                                                              self.retrieved_data,
                                                                                              self.words_list,
                                                                                              self.failed_cards_list)
            profile.mark(Title.CLAIMED_INJURY_DETAILS)

        # medical_equipment_util = MedicalEquipment()
        # medical_equipment_util.set_dependencies(self.retrieved_data, self.misc)
        # medical_equipment_card = medical_equipment_util.build_card(edoc, self.failed_cards_list)
        # profile.mark(Title.MEDICAL_EQUIPMENT)

        reason_of_visit_card = None
        if not selected_recall_list or get_schema_id(Title.REASON_OF_VISIT) in selected_recall_list:
            reason_util = ReasonsUtility()
            reason_util.set_dependencies(self.retrieved_data, self.misc)
            reason_of_visit_card = reason_util.build_reason_of_visit_card(edoc,
                                                                          self.failed_cards_list)  # REASON OF VISIT
            profile.mark(Title.REASON_OF_VISIT)

        medications = None
        if not selected_recall_list or get_schema_id(Title.MEDICATIONS) in selected_recall_list:
            medication_service = MedicationConditions()
            medication_service.set_dependencies(self.retrieved_data, self.medical_coding_service)
            medications = medication_service.build_medication_card(edoc,
                                                                    Title.MEDICATIONS,
                                                                    ccda_data,
                                                                    self.failed_cards_list)
            profile.mark(Title.MEDICATIONS)

        all_observations_cards = []
        if not selected_recall_list or get_schema_id(Title.LABORATORY_RESULTS) in selected_recall_list:
            obs_util_service = ObservationUtility()
            obs_util_service.set_dependencies(self.misc,
                                              self.medical_coding_service,
                                              self.retrieved_data, insurer_pages,
                                              self.card_references)
            all_observations_cards = obs_util_service.get_card(edoc, self.failed_cards_list)
            profile.mark(Title.LABORATORY_RESULTS)

        study_card = None
        if not selected_recall_list or get_schema_id(Title.IMAGING_STUDY) in selected_recall_list:
            study_util_service = StudyUtility()
            study_util_service.set_dependencies(self.retrieved_data, self.misc)
            study_card = study_util_service.build_study_card(edoc, self.failed_cards_list)
            profile.mark(Title.IMAGING_STUDY)

        encounter_details_card = None
        if not selected_recall_list or get_schema_id(Title.ENCOUNTER_DETAILS) in selected_recall_list:
            encounter_details_util = EncounterDetails()
            encounter_details_util.set_dependencies(self.retrieved_data,
                                                    self.misc,
                                                    all_observations_cards,
                                                    study_card,
                                                    physical_exam_card,
                                                    vital_signs_card, diag_card,
                                                    reason_of_visit_card)
            encounter_details_card = encounter_details_util.build_card(edoc,
                                                                       self.failed_cards_list)  # ENCOUNTER DETAILS
            profile.mark(Title.ENCOUNTER_DETAILS)

        dental_details_util_card = None
        dental = False
        if not selected_recall_list or get_schema_id(Title.DENTAL_DETAILS) in selected_recall_list:
            dental_details_util = DentalDetailsUtility()
            dental_details_util.set_dependencies(self.misc)
            dental_details_util_card = dental_details_util.build_card(edoc, self.failed_cards_list)
            dental = dental_details_util.dental
            profile.mark(Title.DENTAL_DETAILS)

        # NOT IN USE
        # Rx Details cards be used during Claims Proces and not Underwriting
        # rx_details_util = RXDetailsUtility()
        # rx_details_util.set_dependencies(self.misc)
        # rx_detail_util_card = rx_details_util.build_card(edoc)
        # rx = rx_details_util.rx
        # profile.mark(Title.RX_DETAILS)

        claims_details_util_card = None
        if not selected_recall_list or get_schema_id(Title.CLAIMS) in selected_recall_list:
            claims_details_util = ClaimsDetailsUtility()
            claims_details_util.set_dependencies(self.misc)
            claims_details_util_card = claims_details_util.build_card(edoc, dental, rx=False,
                                                                      failed_cards_list=self.failed_cards_list)
            profile.mark(Title.CLAIMS)

        # provider_details_util = ProviderDetailsUtility()
        # provider_details_util.set_dependencies(self.retrieved_data, self.misc)
        # provider_details_util_card = provider_details_util.build_card(edoc, self.failed_cards_list)
        # profile.mark(Title.PROVIDER_DETAILS)

        # subjective_details_util = SubjectiveDetailsUtility()
        # subjective_details_util.set_dependencies(self.retrieved_data, self.misc)
        # subjective_details_util_card = subjective_details_util.build_card(edoc, self.failed_cards_list)
        # profile.mark(Title.SUBJECTIVE_DETAILS)

        procedures_card = None
        if not selected_recall_list or get_schema_id(Title.PROCEDURES) in selected_recall_list:
            procedures_util = ProceduresUtility()
            procedures_util.set_dependencies(self.retrieved_data, self.misc, self.medical_coding_service)
            procedures_card = procedures_util.build_card(edoc, 
                                                         diagnosis_procedures, 
                                                         Title.PROCEDURES,
                                                         self.failed_cards_list)
            profile.mark(Title.PROCEDURES)
        
        treatments_card = None
        if not selected_recall_list or get_schema_id(Title.TREATMENTS) in selected_recall_list:
            treatments_util = ProceduresUtility()
            treatments_util.set_dependencies(self.retrieved_data, self.misc, self.medical_coding_service)
            treatments_card = procedures_util.build_card(edoc, 
                                                         [],
                                                         Title.TREATMENTS, 
                                                         self.failed_cards_list)
            profile.mark(Title.TREATMENTS)

        # NOT IN USE
        # diagnostic_procedures_util = DiagnosticProcedures()
        # diagnostic_procedures_util.set_dependencies(self.retrieved_data,
        #                                         self.misc)
        # diagnostic_procedures_card = diagnostic_procedures_util.build_card(edoc)  # ENCOUNTER DETAILS
        # profile.mark(Title.DIAGNOSTIC_PROCEDURES)

        allergens_card = None
        if not selected_recall_list or get_schema_id(Title.ALLERGENS) in selected_recall_list:
            allergen_util = AllergenUtility()
            allergen_util.set_dependencies(self.retrieved_data, self.misc, self.medical_coding_service)
            allergens_card = allergen_util.build_card(edoc, self.failed_cards_list)
            profile.mark(Title.ALLERGENS)

        immunization_card = None
        if not selected_recall_list or get_schema_id(Title.IMMUNIZATIONS) in selected_recall_list:
            immunization_util = ImmunizationUtility()
            immunization_util.set_dependencies(self.retrieved_data, self.misc, self.medical_coding_service)
            immunization_card, self.words_list = immunization_util.build_card(edoc, self.words_list,
                                                                              self.failed_cards_list)
            profile.mark(Title.IMMUNIZATIONS)

        
        informals_card = None
        if not selected_recall_list or get_schema_id(Title.INFORMALS) in selected_recall_list:
            informals_util = InformalsUtility()
            informals_util.set_dependencies(self.retrieved_data, self.misc, self.card_references)
            informals_card = informals_util.build_informals_card(self.failed_cards_list)

            profile.mark(Title.INFORMALS)

        # IMPAIRMENT CARDS :: START
        # diabetes_card, cancer_card, cardiovascular_card, mental_nervous_disorder_card, = None, None, None, None
        # impairment_build_card = None
        if Title.ENABLE_IMPAIRMENT_CARDS in self.retrieved_data.feature:

            impairment_utility = ImpairmentUtility()
            impairment_utility.set_dependencies(self.misc, self.retrieved_data, self.medical_coding_service,
                                                self.card_references.code_entities_list)

            impairment_card_mapping = {
                "diabetes": DiabetesUtility,
                "cancer": CancerUtility,
                "cardiovascular": CardiovascularUtility,
                "mental_nervous_disorder": MentalNervousDisorderUtility,
                "build": ImpairmentBuildCardUtility
            }

            dependencies = ["retrieved_data", "misc", impairment_utility]
            for card_id, class_name in impairment_card_mapping.items():
                if not selected_recall_list or card_id in selected_recall_list:
                    card = self.set_dependencies_and_build_card(class_name, dependencies, edoc, self.failed_cards_list)
                    if card:
                        extracted_cards.append(card)
                        profile.mark(card_id)

            # diabetes_service = DiabetesUtility()
            # diabetes_service.set_dependencies(self.retrieved_data, self.misc, impairment_utility)
            # diabetes_card = diabetes_service.build_card(edoc, self.failed_cards_list)
            # profile.mark(Title.DIABETES)
            #
            # cancer_service = CancerUtility()
            # cancer_service.set_dependencies(self.retrieved_data, self.misc, impairment_utility)
            # cancer_card = cancer_service.build_card(edoc, self.failed_cards_list)
            # profile.mark(Title.CANCER)
            #
            # cardiovascular_service = CardiovascularUtility()
            # cardiovascular_service.set_dependencies(self.retrieved_data, self.misc, impairment_utility)
            # cardiovascular_card = cardiovascular_service.build_card(edoc, self.failed_cards_list)
            # profile.mark(Title.CARDIOVASCULAR)
            #
            # mental_nervous_disorder_service = MentalNervousDisorderUtility()
            # mental_nervous_disorder_service.set_dependencies(self.retrieved_data, self.misc, impairment_utility)
            # mental_nervous_disorder_card = mental_nervous_disorder_service.build_card(edoc, self.failed_cards_list)
            # profile.mark(Title.MENTAL_NERVOUS_DISORDER)
            #
            # impairment_build_service = ImpairmentBuildCardUtility()
            # impairment_build_service.set_dependencies(self.retrieved_data, self.misc, impairment_utility)
            # impairment_build_card = impairment_build_service.build_card(edoc, self.failed_cards,_list)
            # profile.mark(Title.BUILD)

        # IMPAIRMENT CARDS :: END

        extracted_cards.extend([*application_details_util_card, *insured_cards, *technical_cards, claim_1_card])

        extracted_cards.extend(all_observations_cards)

        extracted_cards.extend(
            [physical_exam_card, vital_signs_card, diag_card, reason_of_visit_card,
             medications, study_card, encounter_details_card, dental_details_util_card, claims_details_util_card,
             claim_injury_card, procedures_card, treatments_card, encounter_dx_card, allergens_card,
             immunization_card, informals_card])

        if Title.USER_DEFINED_CARDS in self.retrieved_data.feature:
            new_cards = create_user_defined_cards(edoc, self.retrieved_data.userdefined_card_url,
                                                  self.retrieved_data.priorities, self.failed_cards_list,
                                                  selected_recall_list, self.medical_coding_service,
                                                  self.retrieved_data.feature)
            extracted_cards.extend(new_cards)
        extracted_cards = lfilter(lambda card_: card_ is not None, extracted_cards)

        profile.mark(Title.USER_DEFINED_CARDS)

        for card in extracted_cards:
            for field in card["fields_list"]:
                field["row"]["table_info"] = field["table_info"]

        profile.final()
        print(f'FAILED CARDS LIST: {self.failed_cards_list}')
        return extracted_cards, self.words_list, edoc, self.failed_cards_list

    # ------------------------------------------------------------------------------------------------------------------

    def make_summary(self, extracted_cards):
        excel_summary_service = ExcelSummary()
        excel_summary_service.set_dependencies(self.misc,
                                               None,
                                               None,
                                               None,
                                               extracted_cards,
                                               self.retrieved_data,
                                               self.local_testing)
        excel_summary_bytes = excel_summary_service.generate_consolidated_summary()

        return excel_summary_bytes

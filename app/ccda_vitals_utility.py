import traceback
import uuid
import constants as const
import titles as Title
from copy import deepcopy
from friendlylib.iterators import flatten, lfilter, get_first
from blueprints import BLUEPRINTS, ENTITY_ROLE
from elements.edocument import EDocument
from table_utils import rows_to_table, table_to_ui_card
from typing import Dict, List
from elements.edocument import Entity
from medical_codes.card_reference import CardCodingReference
from medical_codes.medical_coding_utility import MedicalCodingUtility


class VitalsUtility:
    def __init__(self) -> None:
        self.misc = None
        self.medical_coding_service = None
        self.retrieved_data = None
        self.card_schema_id = "ccda_vitals"
        self.bp_ners = ['vital.blood', 'patient.blood', 'insured_1.blood', 'insured_2.blood', 
                        'patient.blood_pressure', 'insured_1.blood_pressure', 'insured_2.blood_pressure']
        self.card_references = object

    # ------------------------------------------------------------------------------------------------------------------

    def set_dependencies(self, obj_misc, retrieved_data, medical_coding_service: MedicalCodingUtility, card_references: CardCodingReference):
        self.misc = obj_misc
        self.medical_coding_service: MedicalCodingUtility = medical_coding_service
        self.retrieved_data = retrieved_data
        self.card_references: CardCodingReference = card_references

    # ------------------------------------------------------------------------------------------------------------------

    def _prepare_blueprint_data(self):
        self.row_blueprint = BLUEPRINTS[Title.CCDA_VITALS]["row"]
        self.mother = flatten([e["ner.keys"] for e in self.row_blueprint if e["role"] == ENTITY_ROLE.MOTHER])
        self.children = flatten([e["ner.keys"] for e in self.row_blueprint if e["role"] == ENTITY_ROLE.CHILD])
        self.positions = BLUEPRINTS[Title.CCDA_VITALS][const.POSITIONS]
        self.date_order = BLUEPRINTS[Title.CCDA_VITALS]["date_order"]
        self.vitals_type = {value: key for key, values in BLUEPRINTS[Title.CCDA_VITALS]["vitals_type"].items() for value
                            in values}
        self.ner_keys = {ner_keys: item['display_key'] for item in self.row_blueprint
                         for ner_keys in item['ner.keys']}
        self.custom_unit = {
            'temperature_c': 'celsius',
            'temperature_f': 'farenheight',
            'weight_kg': 'kg',
            'weight_lbs': 'lbs',
            'height_cm': 'cm',
            'height_in': 'in',
        }

    # ------------------------------------------------------------------------------------------------------------------

    def _separate_blood_pressure(self, edoc: EDocument) -> EDocument:
        for i, rows in enumerate(edoc):
            for j, row in enumerate(rows):
                for cell in row:
                    cell_type = cell['type']
                    if cell_type not in self.bp_ners:
                        continue
                    systolic_cell, diastolic_cell = self._generate_systolic_diastolic(cell)
                    if systolic_cell:
                        edoc[i][j].append(systolic_cell)
                    if diastolic_cell:
                        edoc[i][j].append(diastolic_cell)

        return edoc

    # ------------------------------------------------------------------------------------------------------------------

    def _generate_systolic_diastolic(self, bp_entity: dict):
        blood_text = bp_entity['text'].strip()
        if blood_text == "":
            return None, None
        blood_list = blood_text.split('/')
        if len(blood_list) == 1:
            systolic_value = self._get_bp_new_entity(deepcopy(bp_entity), blood_list[0].strip(), 'diastolic')
            return systolic_value, None
        elif len(blood_list) >= 2:
            systolic_text = blood_list[0].strip()
            diastolic_text = blood_list[1].strip()
            systolic_value = self._get_bp_new_entity(deepcopy(bp_entity), systolic_text, 'systolic')
            diastolic_value = self._get_bp_new_entity(deepcopy(bp_entity), diastolic_text, 'diastolic')
            return systolic_value, diastolic_value
        return None, None

    # ------------------------------------------------------------------------------------------------------------------

    def _get_bp_new_entity(self, blood_dict: dict, bp_text: str, bp_part: str) -> dict:
        if bp_text == "":
            return None
        bp_type = blood_dict['type'].split('.')[0]
        blood_dict['text'] = bp_text
        blood_dict['codified_as'] = bp_text
        blood_dict['type'] = const.GENERATED_ENTITY + bp_type + '.' + bp_part
        blood_dict['id'] = str(uuid.uuid4())

        return blood_dict

    # ------------------------------------------------------------------------------------------------------------------

    def _edoc_to_page_wise(self, edoc: EDocument):
        priority_mapping = self.misc.get_field_wise_priority_mapping(self.retrieved_data.priorities,
                                                                     self.card_schema_id,
                                                                     self.retrieved_data.page_classification_display_name_mapping)
        pages = {}
        doc_dates = {}
        for page_data in edoc:
            for row in page_data:
                for cell in row:
                    if not self.misc.entity_with_valid_priority(cell, priority_mapping, self.card_schema_id):
                        continue
                    if (cell["type"] in self.mother) or (cell["type"] in self.children):
                        key = str(cell["doc_num"]) + const.hyphen + str(cell['abs_page'])
                        if key not in pages:
                            pages[key] = {'mother': [], 'children': []}
                        if cell["type"] in self.mother:
                            pages[key]['mother'].append(cell)
                        elif cell["type"] in self.children:
                            pages[key]['children'].append(cell)
                        if cell["doc_num"] not in doc_dates and cell["type"] in self.date_order:
                            doc_dates.setdefault(cell['doc_num'], [])
                        if cell["type"] in self.date_order:
                            doc_dates[cell["doc_num"]].append(cell)
        return pages, doc_dates

    # ------------------------------------------------------------------------------------------------------------------

    def _prepare_empty_table(self) -> dict:
        row = {}
        ner_keys = {}
        for cell in self.row_blueprint:
            display_key = cell['display_key']
            row[display_key] = {}
            for k in cell['ner.keys']:
                ner_keys[k] = display_key
        return row

    # ------------------------------------------------------------------------------------------------------------------

    def _estimate_cell_attachment(self, date_points, child_point, positions) -> dict:
        if len(date_points) == 0:
            return None
        for position in positions:
            row_objects_in_required_position = lfilter(
                lambda child_object: self.misc.is_required_position(position, child_point,
                                                                    self.misc.get_point(child_object)),
                date_points
            )
            if len(row_objects_in_required_position) == 0:
                continue
            if position == const.SAME_LINE_RIGHT_SIDE:
                same_line_closest_date_cell = min(
                    row_objects_in_required_position,
                    key=lambda cell: self.misc.rect_distance(self.misc.get_point(cell), child_point)
                )
                if len(row_objects_in_required_position) != 0:
                    return same_line_closest_date_cell
            else:
                nearest_height_primary_object = max(
                    row_objects_in_required_position,
                    key=lambda child_object: self.misc.get_point(child_object)[1]
                )
                if len(row_objects_in_required_position) != 0:
                    return nearest_height_primary_object
        return None

    # ------------------------------------------------------------------------------------------------------------------

    def _populate_with_children(self, child: dict, page_dates: dict, doc_dates: list) -> list:
        child_point = self.misc.get_point(child)
        closest_date_found = False
        for date_type in self.date_order:
            filtered_date_candidates = lfilter(lambda date_cell: date_cell['type'] == date_type, page_dates)
            closest_date = self._estimate_cell_attachment(filtered_date_candidates, child_point,
                                                          self.positions["Date"]["level"])
            if not closest_date:
                child_entry_id = child.get("entry_id", None)
                filtered_ccda_dates = lfilter(
                    lambda date_cell:
                    date_cell['entry_id'] == child_entry_id and date_cell.get('source', '') in ['ccda', 'friendlyllm'],
                    filtered_date_candidates
                )
                if filtered_ccda_dates:
                    closest_date = get_first(filtered_ccda_dates, None)
                    return closest_date
            if closest_date:
                return closest_date
        if not closest_date_found:
            prev_page_date = self._get_previous_page_dates(child, doc_dates)
            if prev_page_date is not None:
                return prev_page_date

    # ------------------------------------------------------------------------------------------------------------------

    def _remove_duplicate_objects(self, list_of_objects: list) -> list:
        unique_line_objects = []
        filtered_list_of_objects = []
        for vital_entry in list_of_objects:
            vital_row = []
            for display_key, vital_cell in vital_entry.items():
                if vital_cell:
                    unique_vital_text = vital_cell['text'].strip().lower()
                    vital_row.append(unique_vital_text)
            if vital_row not in unique_line_objects:
                unique_line_objects.append(vital_row)
                filtered_list_of_objects.append(vital_entry)

        return filtered_list_of_objects

    # ------------------------------------------------------------------------------------------------------------------

    def _remove_duplicate_rows(self, selected_rows):
        list_of_rows = []
        unique_line_objects = []
        for row in selected_rows:
            vital_row = []
            for row_item in row:
                if not row_item.get('entity_object', None):
                    continue
                vital_row.append(row_item['entity_object']['text'])
            if vital_row not in unique_line_objects:
                unique_line_objects.append(vital_row)
                list_of_rows.append(row)
        return list_of_rows

    # ------------------------------------------------------------------------------------------------------------------

    def _get_previous_page_dates(self, child: dict, doc_dates: list):
        if len(doc_dates) == 0:
            return None
        for date_type in self.date_order:
            filtered_date_candidates = lfilter(
                lambda date_cell: date_cell['type'] == date_type and date_cell['abs_page'] < child['abs_page'],
                doc_dates)
            if len(filtered_date_candidates) != 0:
                bottom_date = max(
                    filtered_date_candidates,
                    key=lambda child: (child['abs_page'], child['bottom']))
                return bottom_date
        return None

    # ------------------------------------------------------------------------------------------------------------------

    def _generate_type_of_vital_cell(self, vital_row: dict, value_cell: dict) -> dict:
        vitals_type = self.vitals_type.get(value_cell['type'], None)
        if not vitals_type:
            return
        vital_type_cell = deepcopy(value_cell)
        vital_type_cell['text'] = vitals_type
        vital_type_cell['codified_as'] = vitals_type
        vital_type_cell['type'] = 'vital.type'
        vital_type_cell['metadata'].update({'card_generated': True})
        vital_type_cell['id'] = str(uuid.uuid4())

        vital_row['Type'] = vital_type_cell

    # ------------------------------------------------------------------------------------------------------------------

    def _get_manual_unit(self, entry_type: str) -> str:
        for unit_key in self.custom_unit.keys():
            if unit_key in entry_type:
                return self.custom_unit[unit_key]
        return None

    # ------------------------------------------------------------------------------------------------------------------

    def _get_vital_unit(self, vital_row: dict, value_cell: dict) -> dict:
        vitals_type = self.vitals_type.get(value_cell['type'], None)
        codified_unit = value_cell.get('codify', {}).get('unit', None) if value_cell.get('codify') is not None else None
        if not codified_unit:
            codified_unit = self.medical_coding_service.vital_loincs.get(vitals_type, {}).get('unit', None)
        if not codified_unit:
            codified_unit = self._get_manual_unit(value_cell['type'])
        if not codified_unit:
            return
        value_cell['text'] = codified_unit
        value_cell['codified_as'] = codified_unit
        value_cell['type'] = const.GENERATED_ENTITY + 'vital.unit'
        value_cell['id'] = str(uuid.uuid4())

        vital_row['Unit'] = value_cell

    # ------------------------------------------------------------------------------------------------------------------

    def _get_vital_loinc(self, vital_row: Dict[str, Entity], value_cell: Entity) -> str:
        vitals_type = self.vitals_type.get(value_cell['type'], None)
        vital_loinc = self.medical_coding_service.vital_loincs.get(vitals_type, {}).get('loinc', '')
        code_description = self.medical_coding_service.vitals_loinc_dict.get(vital_loinc, '')
        value_cell['text'] = vital_loinc
        value_cell['codified_as'] = vital_loinc
        value_cell['type'] = const.GENERATED_ENTITY + 'vital.loinc'
        value_cell['id'] = str(uuid.uuid4())
        value_cell['metadata'] = {"info": f"{code_description}","type": "medical_code"}

        vital_row['LOINC'] = value_cell

        return vital_loinc

    # ------------------------------------------------------------------------------------------------------------------

    def _populate_all_data(self, page_data, doc_dates):
        mothers = page_data['mother']
        children = page_data['children']
        vital_rows = []
        bp_rows = []
        for mother in mothers:
            vital_row = self._prepare_empty_table()
            vital_row['Value'] = mother
            self._generate_type_of_vital_cell(vital_row, mother)
            self._get_vital_unit(vital_row, deepcopy(mother))
            loinc_code = self._get_vital_loinc(vital_row, deepcopy(mother))
            self.medical_coding_service._assign_loinc_code(mother['text'], loinc_code)
            vital_row['Date'] = self._populate_with_children(mother, children, doc_dates)
            if mother['type'] in self.bp_ners:
                bp_rows.append(vital_row)
            else:
                vital_rows.append(vital_row)

        return vital_rows, bp_rows

    # ------------------------------------------------------------------------------------------------------------------

    def _convert_dict_to_rows(self, sub_table: list):
        rows = []
        if not sub_table:
            return rows

        for vital_entry in sub_table:
            vital_row = []
            for display_key, vital_cell in vital_entry.items():
                if vital_cell:
                    vital_row.append(vital_cell)
            rows.append(vital_row)
        return rows

    # ------------------------------------------------------------------------------------------------------------------

    def _fill_missing_dummy_cell(self, rows):
        new_rows = []
        for row in rows:
            new_row = deepcopy(self.row_blueprint)
            for i, bp in enumerate(self.row_blueprint):
                for j, cell in enumerate(row):
                    if cell['type'] in bp['ner.keys']:
                        new_row[i]['entity_object'] = deepcopy(cell)
                        del row[j]
                        break
            new_rows.append(new_row)
        return new_rows

    # ------------------------------------------------------------------------------------------------------------------

    def _extract_ccda_vital_rows(self, edoc: EDocument) -> List:
        pages, doc_dates = self._edoc_to_page_wise(edoc)
        vitals_table = []
        selected_rows = []
        bp_table = []
        bp_selected_rows = []

        for doc_page_num, page_data in pages.items():
            if len(page_data['mother']) == 0:
                continue
            current_document_num, current_page_num = map(int, doc_page_num.split('-'))
            list_of_vectors, bp_vectors = self._populate_all_data(page_data, doc_dates.get(current_document_num, []))
            sub_table = self._remove_duplicate_objects(list_of_vectors)
            vitals_table.extend(sub_table)
            bp_table.extend(bp_vectors)

        if len(bp_table) != 0:
            bp_rows = self._convert_dict_to_rows(bp_table)
            bp_rows = self._fill_missing_dummy_cell(bp_rows)
            bp_selected_rows = self._remove_duplicate_rows(bp_rows)

        if len(vitals_table) != 0:
            vital_rows = self._convert_dict_to_rows(vitals_table)
            vital_rows = self._fill_missing_dummy_cell(vital_rows)
            selected_rows = self._remove_duplicate_rows(vital_rows)
            
            return selected_rows, bp_selected_rows

        return selected_rows, bp_selected_rows

    # ------------------------------------------------------------------------------------------------------------------

    def build_card(self, edoc: EDocument, failed_cards_list: list):
        try:
            self._prepare_blueprint_data()
            edoc = self._separate_blood_pressure(edoc)
            bp_rows = []
            rows, bp_rows = self._extract_ccda_vital_rows(edoc)
            self.card_references.match_cell_codings(rows, ['LOINC'], 'Type', 'Value', ['Date'], True)
            self.card_references.match_cell_codings(bp_rows, ['LOINC'], 'Type', 'Value', ['Date'], True)
            
            table = rows_to_table(rows, Title.CCDA_VITALS)
            card = table_to_ui_card(table)
            return card
        except Exception as e:
            print(f'ERROR: {Title.CCDA_VITALS} Card Creation Failed: {e}')
            failed_cards_list.append({'card_name': 'CCDA Vital Signs', 'message': f'{traceback.format_exc()}'})
            return None

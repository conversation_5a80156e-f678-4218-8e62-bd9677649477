import re
from copy import deepcopy
from typing import List
from uuid import uuid4

from friendlylib.strings import get_suffix_by_delimiter

import constants as const
import titles as Title
from blueprints import BLUEPRINTS, CARD_MOTHER_FIELD_MAPPING
from cloud.recallv2.data_value import DataValue
from field_id_entity_type_mapping import FIELD_ID_BLUEPRINTS
from patient_dicts import empty_insured_1
from record_schema_id_entity_type_map import RECORD_SCHEMA_ID_BLUEPRINTS
from friendlylib.iterators import lmap, find, lfilter

from utils.utils import check_same_file, nearest_smaller, get_distribution_score, get_similarity_score


class PortfolioUtils:

    def __init__(self):
        self.misc = object
        self.retrieved_data = None
        self.unified_words_list = []
        self.portfolio = {
            const.WORDS: [],
            const.DATA_VALUES: [],
            const.CARDS: [],
            const.SUMMARY_EVENTS: [],
            const.FILE_PAGE_CLASSES: [],
            const.SUMMARY_CUSTOM_DATA: {}
        }
        self.data_value_ids_lookup = {}
        self.field_id_type_mapping = {}
        self.card_ids_lookup = {}
        self.field_id_priority_mapping = {}

    # ------------------------------------------------------------------------------------------------------------------

    def add_data_value(self, data_value):
        if data_value["data_value_id"] not in self.data_value_ids_lookup:
            tag_suffix = get_suffix_by_delimiter(data_value['entity_type'])
            if any(sub_tag in data_value['entity_type'] for sub_tag in const.SUBSTANCE_ABUSE_TAGS) and tag_suffix in ["yes", "no", "former", "passive"]:
                data_value["text"] = tag_suffix
            self.portfolio["data_values"].append(data_value)
            self.data_value_ids_lookup[data_value["data_value_id"]] = data_value

    def set_dependencies(self, objMisc, retrieved_data, unified_words_list):
        self.misc = objMisc
        self.retrieved_data = retrieved_data
        self.unified_words_list = deepcopy(unified_words_list)
        if retrieved_data.file_page_classes is not None:
            self.portfolio[const.FILE_PAGE_CLASSES] = [page_classes.dict() for page_classes in
                                                       retrieved_data.file_page_classes]

    def update_data_value_ids_lookup(self, datavalues: List[DataValue]):
        for dv in datavalues:
            self.data_value_ids_lookup[dv.data_value_id] = dv.to_dict()
        return list(self.data_value_ids_lookup.values())

    def inherit_data_from_existing_portfolio(self, recalled_card_ids):
        if self.retrieved_data.input_portfolio:
            if self.retrieved_data.input_portfolio.data_values:
                existing_datavalues = self.retrieved_data.input_portfolio.input_data_values
                dv_list = self.update_data_value_ids_lookup(existing_datavalues)
                self.portfolio[const.DATA_VALUES] = dv_list
            if self.retrieved_data.input_portfolio.cards:
                existing_cards = self.retrieved_data.input_portfolio.cards
                self.portfolio[const.CARDS] = lfilter(lambda card: card['card_id'] not in recalled_card_ids,
                                                      existing_cards)

    # ------------------------------------------------------------------------------------------------------------------

    def _populate_vertical_card(self, full_card, card_name: str, blueprint_cells, sub_card: str = ''):
        card_type = full_card['card'].get('card_type', 'old')
        destination_card = full_card['card'].get('destination_card')
        mother_field = full_card['card'].get('mother_field')

        if card_name == 'encounter_details' and full_card.get("encounter_dates"):
            self.portfolio[const.SUMMARY_CUSTOM_DATA]["encounter_dates"] = full_card.get("encounter_dates", {})

        repeating_field = False
        seen = {cell[const.FIELD_ID]: False for cell in list(blueprint_cells)}
        card_index = self.calculate_card_index(sub_card)
        card = {
            const.CARD_ID: card_name,
            const.DEFAULT_SCHEMA: card_name,
            const.POSSIBLE_SCHEMAS: [card_name],
            const.CARD_INDEX: card_index,
            const.RECORDS: []
        }
        record = {
            const.RECORD_ID: str(uuid4()),
            const.RECORD_SCHEMA_ID: card_name,
            const.ITEMS: [],
            const.RECORD_LOCATIONS: full_card['card'].get('record_locations', [])
        }

        prev_r = 0
        for field in full_card[const.FIELDS_LIST]:
            r = field['row']['r']
            c = field['row']['c']
            if prev_r != r and card[const.CARD_ID] in ["laboratory_results", "abnormal_observations", "reason_of_visit",
                                                       "vital_signs", "ccda_vitals", "diagnoses",
                                                       "medication_equipments", "medications",
                                                       "encounter_details", "diagnostic_procedures", 'informals']:
                if prev_r != 0:
                    card[const.RECORDS].append(record)
                prev_r = r
                record = {
                    const.RECORD_ID: str(uuid4()),
                    const.RECORD_SCHEMA_ID: card_name,
                    const.ITEMS: [],
                    const.RECORD_LOCATIONS: full_card['card'].get('record_locations', [])
                }
                if card_name in ['vital_signs', 'laboratory_results'] and field['row']['value'][0][
                    'claimid'] is not None:
                    file_id = field['row']['value'][0]['claimid']
                    page_num = field['row']['value'][0]['abs_page']
                    record_loc = f'{file_id}${page_num}'
                    record[const.RECORD_LOCATIONS].append(record_loc)
                    related_insured_data = self._get_same_page_related_card_data(file_id, page_num, "insured_details")
                    record.setdefault(const.RELATIONSHIPS, []).append(related_insured_data)
            datavalue_id = field["row"]['value'][0]['value_id']
            datavalue_ids = []
            if datavalue_id is not None:
                datavalue_ids = [datavalue_id]
            if datavalue_id is not None and field["row"]['value'][0]['value_name'] != "":
                if datavalue_id not in self.data_value_ids_lookup:
                    print(f"WARNING: datavalue_id {datavalue_id} {card_name} r{r} c{c} NOT found in datavalue list")

            entity_type = field['row']['value'][0]['entity_type']

            if isinstance(entity_type, str):
                entities = [entity_type]
            else:
                entities = entity_type

            for entity_type in entities:
                if FIELD_ID_BLUEPRINTS.get(card_name) and entity_type not in FIELD_ID_BLUEPRINTS[card_name]:
                    continue

                if card_type == 'new':
                    field_id = full_card['card'].get('field_id_entity_type_mapping', {}).get(entity_type)
                else:
                    field_id = FIELD_ID_BLUEPRINTS.get(card_name, {}).get(entity_type)

                for item in record[const.ITEMS]:
                    if item['field_id'] != field_id:
                        continue
                    for val in datavalue_ids:
                        if val not in item['data_values']:
                            datavalue = lfilter(
                                lambda data_value: data_value['data_value_id'] == val,
                                self.portfolio[const.DATA_VALUES]
                            )
                            if datavalue and datavalue[0]['entity_type'] == entity_type:
                                item['data_values'].append(val)
                    repeating_field = True
                    continue

                if repeating_field:
                    repeating_field = False
                    continue

                item = {
                    const.FIELD_ID: field_id,
                    const.CARD_CONFIDENCE: 0,
                    const.DATA_VALUES: datavalue_ids
                }
                seen[field_id] = True
                record[const.ITEMS].append(item)

        for field_id, val in seen.items():
            if not val:
                item = {
                    const.FIELD_ID: field_id,
                    const.CARD_CONFIDENCE: 0,
                    const.DATA_VALUES: []
                }
                record[const.ITEMS].append(item)
        if destination_card:
            card['destination_card'] = destination_card
        card['mother_field'] = mother_field
        if card_name == 'insured_details':
            insured_dv_dict = self.get_insured_dv_dict()
            for item in record[const.ITEMS]:
                self._update_multivariate_data(item, insured_dv_dict)
        card[const.RECORDS].append(record)
        self.portfolio[const.CARDS].append(card)

    # ------------------------------------------------------------------------------------------------------------------

    def get_insured_dv_dict(self):
        insured_dv_dict = {}

        for attribute_name, attribute_details in empty_insured_1.items():
            if attribute_name == 'birth':
                field_id = "insured_details.date_of_birth"
            elif attribute_name == 'marital':
                field_id = "insured_details.marital_status"
            else:
                field_id = f"insured_details.{attribute_name}"

            insured_dv_dict[field_id] = [dv['data_value_id'] for dv in self.portfolio[const.DATA_VALUES] if
                                         dv['entity_type'] in attribute_details['entity_type_aliases']]

        return insured_dv_dict

    def _update_multivariate_data(self, item, insured_dv_dict):
        if not item.get(const.DATA_VALUES):
            item['all_data_values'] = []
            return
        selected_dv_id = item[const.DATA_VALUES][0]
        selected_dv = self.data_value_ids_lookup.get(selected_dv_id)
        all_dv_ids = insured_dv_dict.get(item[const.FIELD_ID])
        if not all_dv_ids:
            item['all_data_values'] = []
            return
        is_priority_set = self.field_id_priority_mapping.get(item['field_id'])
        all_dv_text = [self.data_value_ids_lookup.get(dv_id)['text'] for dv_id in all_dv_ids if
                       self.data_value_ids_lookup.get(dv_id)]
        distribution_score_mapping = get_distribution_score(all_dv_text)

        all_data_values = []
        for dv in all_dv_ids:
            other_dv = self.data_value_ids_lookup.get(dv)
            if not other_dv:
                continue
            note = self.get_discrepancy_note(selected_dv, other_dv, distribution_score_mapping, is_priority_set)
            dv_obj = {
                "data_value": dv,
                "page_class": other_dv.get("page_class"),
                "note": note,
                "distribution_score": distribution_score_mapping.get(other_dv['text']),
                "similarity_score": get_similarity_score(selected_dv['text'].lower().strip(),
                                                         other_dv['text'].lower().strip())
            }
            all_data_values.append(dv_obj)

        item['all_data_values'] = all_data_values

    def get_discrepancy_note(self, selected_dv, other_dv, distribution_score_mapping, is_priority_set):
        if selected_dv['data_value_id'] == other_dv['data_value_id']:
            return "Selected by Cards"
        if distribution_score_mapping[other_dv['text']] < distribution_score_mapping[selected_dv['text']]:
            return "Ignored due to lower frequency"
        elif is_priority_set and other_dv['page_class'] != selected_dv['page_class']:
            return "Ignored due to lower priority page class"
        elif other_dv['entity_confidence'] < selected_dv['entity_confidence']:
            return "Ignored due to lower confidence level"
        else:
            return "Ignored due to tag prefix mismatch or late occurrence"

    def _group_full_card_rows_by_row(self, full_card):
        full_card_rows = {}
        for field in full_card[const.FIELDS_LIST]:
            r = field['row']['r']
            full_card_rows.setdefault(r, []).append(field['row'])
        return full_card_rows

    def _find_data_value_in_all_data_values(self, datavalue_id: str):
        return self.data_value_ids_lookup.get(datavalue_id)

    def _find_item_by_field_id_in_record(self, record: dict, field_id: str):
        for item in record[const.ITEMS]:
            if item['field_id'] == field_id:
                return item
        return None

    def _update_nearest_destination_card_mapping(self, nearest_card_mapping, default_relationship, source_locations,
                                                 destination_locations):
        min_diff = float('inf')
        for loc in source_locations:
            file_id, page_num = loc.split('$')
            page_num = int(page_num)
            matching_file_locs = lfilter(lambda loc_: loc_.split('$')[0] == file_id, destination_locations)
            if not matching_file_locs:
                continue
            page_num_list = [int(loc_.split('$')[1]) for loc_ in matching_file_locs]
            nearest_page_num = nearest_smaller(page_num_list, page_num)
            if not nearest_page_num:
                continue
            if page_num - nearest_page_num < min_diff:
                min_diff = page_num - nearest_page_num
                nearest_card_mapping[min_diff] = default_relationship

    def _get_same_page_related_card_data(self, file_id, page_num, destination_card):
        related_card_data = {}
        no_of_dest_cards = 0
        for card in self.portfolio['cards']:
            if card['card_id'] != destination_card:
                continue
            mother_field = card.get('mother_field') or CARD_MOTHER_FIELD_MAPPING.get(destination_card)
            if mother_field is None:
                continue
            no_of_dest_cards += 1
            destination_mother_field_id = f'{destination_card}.{mother_field}'
            file_page_list = card['records'][0].get(const.RECORD_LOCATIONS, [])
            for file_page in file_page_list:
                file, page = file_page.split('$')
                if file == file_id and int(page) == int(page_num):
                    related_card_data = {
                        'card_id': card['card_id'],
                        'card_index': card['card_index'],
                        'record_id': card['records'][0]['record_id'],
                        'field_id': destination_mother_field_id
                    }

        nearest_card_mapping = {}
        if not related_card_data:
            for card in self.portfolio['cards']:
                if card['card_id'] != destination_card:
                    continue
                mother_field = card.get('mother_field') or CARD_MOTHER_FIELD_MAPPING.get(destination_card)
                if mother_field is None:
                    continue
                destination_mother_field_id = f'{destination_card}.{mother_field}'
                destination_locations: set = set(card['records'][0].get(const.RECORD_LOCATIONS, []))
                source_locations: set = {f'{file_id}${page_num}'}
                default_relationship = {
                    'card_id': card['card_id'],
                    'card_index': card['card_index'],
                    'record_id': card['records'][0]['record_id'],
                    'field_id': destination_mother_field_id
                }

                if no_of_dest_cards == 1:
                    related_card_data = default_relationship
                else:
                    self._update_nearest_destination_card_mapping(nearest_card_mapping, default_relationship,
                                                                  source_locations,
                                                                  destination_locations)

            if nearest_card_mapping:
                related_card_data = nearest_card_mapping[min(nearest_card_mapping)]

        return related_card_data

    def _get_related_card(self, source_card, destination_card):
        related_card_list = []
        default_relationship = {}
        for card in self.portfolio['cards']:
            if card['card_id'] != destination_card or not card['records']:
                continue
            mother_field = card.get('mother_field') or CARD_MOTHER_FIELD_MAPPING.get(destination_card)
            if mother_field is None:
                continue
            destination_mother_field_id = f'{destination_card}.{mother_field}'
            default_relationship = {
                'card_id': card['card_id'],
                'card_index': card['card_index'],
                'record_id': card['records'][0]['record_id'],
                'field_id': destination_mother_field_id
            }
            destination_locations = set(card['records'][0].get(const.RECORD_LOCATIONS, []))
            source_locations = set(source_card['records'][0].get(const.RECORD_LOCATIONS, []))
            if destination_locations.intersection(source_locations) or (destination_card == 'application_details'
                                                                        and check_same_file(source_locations,
                                                                                            destination_locations)):
                related_card_list.append({
                    'card_id': card['card_id'],
                    'card_index': card['card_index'],
                    'record_id': card['records'][0]['record_id'],
                    'field_id': destination_mother_field_id
                })
        if not related_card_list:
            related_card_list.append(default_relationship)
        return related_card_list

    def _populate_horizontal_card(self, full_card, card_name: str, sub_card: str = ''):
        card_index = self.calculate_card_index(sub_card)
        card_type = full_card['card'].get('card_type', 'old')
        destination_card = full_card['card'].get('destination_card')
        groups = full_card['card'].get('groups')
        card = {
            const.CARD_ID: card_name,
            const.DEFAULT_SCHEMA: card_name,
            const.POSSIBLE_SCHEMAS: [card_name],
            const.CARD_INDEX: card_index,
            const.RECORDS: []
        }

        full_card_rows = self._group_full_card_rows_by_row(full_card)
        if 0 not in full_card_rows.keys() and 1 in full_card_rows.keys():
            del full_card_rows[1]
        elif 0 in full_card_rows.keys():
            del full_card_rows[0]

        for _, list_of_fields in full_card_rows.items():
            record = {
                const.RECORD_ID: str(uuid4()),
                const.RECORD_SCHEMA_ID: card_name,
                const.ITEMS: [],
                const.RECORD_LOCATIONS: [],
                const.RELATIONSHIPS: []
            }

            for field in list_of_fields:
                c = field['c']
                cards_with_left_type_headers = ['list_of_events']
                if c == 0 and card_name in RECORD_SCHEMA_ID_BLUEPRINTS:
                    record[const.RECORD_SCHEMA_ID] = RECORD_SCHEMA_ID_BLUEPRINTS[card_name][
                        field['value'][0]['entity_type']]
                # cards_with_left_type_headers_will be removed once all record schema ids are added to RECORD_SCHEMA_ID_BLUEPRINTS
                elif c == 0 and card_name in cards_with_left_type_headers:
                    record[const.RECORD_SCHEMA_ID] = card_name + "." + field['value'][0]['entity_type'].replace('.',
                                                                                                                '_')
                entity_type = field['value'][0]['entity_type']
                datavalue_id = field['value'][0]['value_id']
                datavalue_ids = []
                if datavalue_id is not None and field['value'][0]['value_name'] != "":
                    datavalue_ids = [datavalue_id]
                    if datavalue_id not in self.data_value_ids_lookup:
                        print(f"WARNING: datavalue_id {datavalue_id} {card_name} c{c} NOT found in datavalue list")

                if card_type == 'new':
                    field_id = full_card['card'].get('field_id_entity_type_mapping', {}).get(entity_type)
                else:
                    field_id = FIELD_ID_BLUEPRINTS.get(card_name, {}).get(entity_type)
                if field_id is None:
                    print(f"WARNING: field_id not found for card {card_name} entity_type {entity_type}")
                    continue

                preexisting_item = self._find_item_by_field_id_in_record(record, field_id)
                if preexisting_item is not None:
                    for val in datavalue_ids:
                        if val not in preexisting_item['data_values']:
                            datavalue = self._find_data_value_in_all_data_values(val)
                            if datavalue is None:
                                print(f"DATAVALUEID not found {val} for {field_id}")
                            elif datavalue['entity_type'] == entity_type:
                                preexisting_item['data_values'].append(val)
                    continue

                item = {
                    const.FIELD_ID: field_id,
                    const.CARD_CONFIDENCE: 0,
                    const.DATA_VALUES: datavalue_ids
                }
                record[const.ITEMS].append(item)

            if card_name == 'social_history':
                file_id = list_of_fields[0]['value'][0]['claimid']
                page_num = list_of_fields[0]['value'][0]['abs_page']
                record_loc = f'{file_id}${page_num}'
                record[const.RECORD_LOCATIONS].append(record_loc)
                related_insured_data = self._get_same_page_related_card_data(file_id, page_num, "insured_details")
                record[const.RELATIONSHIPS].append(related_insured_data)
            if card_type == 'new' and destination_card:
                file_id = list_of_fields[0]['value'][0]['claimid']
                page_num = list_of_fields[0]['value'][0]['abs_page']
                related_card_data = self._get_same_page_related_card_data(file_id, page_num, destination_card)
                record.setdefault(const.RELATIONSHIPS, []).append(related_card_data)

            card[const.RECORDS].append(record)
        if groups:
            self.portfolio[const.SUMMARY_CUSTOM_DATA]['rdx_links'] = groups
        self.portfolio[const.CARDS].append(card)

    # ------------------------------------------------------------------------------------------------------------------
    def _populate_empty_hybrid_items(self, field_ids_row_num):
        field_items = []
        for field, field_value in field_ids_row_num.items():
            item = {
                const.FIELD_ID: field_value,
                const.CARD_CONFIDENCE: 0,
                const.DATA_VALUES: []
            }
            field_items.append(item)
        return field_items

    def _populate_hybrid_card(self, full_card, card_name: str, field_ids_row_num, sub_card: str = ''):
        card_index = self.calculate_card_index(sub_card)
        empty_items = self._populate_empty_hybrid_items(field_ids_row_num)
        repeating_field = False
        card = {
            const.CARD_ID: card_name,
            const.DEFAULT_SCHEMA: card_name,
            const.POSSIBLE_SCHEMAS: [card_name],
            const.CARD_INDEX: card_index,
            const.RECORDS: []
        }
        record = {
            const.RECORD_ID: str(uuid4()),
            const.RECORD_SCHEMA_ID: card_name,
            const.ITEMS: []
        }

        for field in full_card[const.FIELDS_LIST]:
            c = field['row']['c']
            value_name = field['row']['value'][0]['value_name']
            if (value_name == 'Exam') and (c == 0):
                if len(record[const.ITEMS]):
                    card[const.RECORDS].append(record)
                record = {
                    const.RECORD_ID: str(uuid4()),
                    const.RECORD_SCHEMA_ID: card_name,
                    const.ITEMS: deepcopy(empty_items)
                }
                continue
            if (c == 0) or value_name == const.EMPTY_STRING:
                continue

            datavalue_id = field["row"]['value'][0]['value_id']
            datavalue_ids = []
            if datavalue_id is not None:
                datavalue_ids = [datavalue_id]
            if datavalue_id is not None and field["row"]['value'][0]['value_name'] != "":
                found_datavalue = self._find_data_value_in_all_data_values(datavalue_id)
                if found_datavalue is None:
                    print(f"WARNING: datavalue_id {datavalue_id} c{c} NOT found in datavalue list")

            if field['row']['value'][0]['entity_type'] not in FIELD_ID_BLUEPRINTS[card_name].keys():
                continue
            for item in record[const.ITEMS]:
                if item['field_id'] == FIELD_ID_BLUEPRINTS[card_name][field['row']['value'][0]['entity_type']]:
                    for val in datavalue_ids:
                        if val not in item['data_values']:
                            datavalue = list(filter(lambda data_value: data_value['data_value_id'] == val,
                                                    self.portfolio[const.DATA_VALUES]))
                            if datavalue[0]['entity_type'] == field['row']['value'][0]['entity_type']:
                                item['data_values'].append(val)
                    repeating_field = True
                    continue

            if repeating_field:
                repeating_field = False
                continue

        card[const.RECORDS].append(record)
        self.portfolio[const.CARDS].append(card)

    # ------------------------------------------------------------------------------------------------------------------

    def _print_item(self, item):
        field_id = item[const.FIELD_ID]
        datavalues = item[const.DATA_VALUES]
        if len(datavalues) == 0:
            print(f"    {field_id:45}: None")
            return
        if len(datavalues) == 1:
            datavalue_id = datavalues[0]
            datavalue = self._find_data_value_in_all_data_values(datavalue_id)
            datavalue_text = datavalue[0]["text"]
            print(f"    {field_id:45}: {datavalue_text:60} ({datavalue_id})")
            return
        multiple_values = []
        for datavalue_id in datavalues:
            datavalue = self._find_data_value_in_all_data_values(datavalue_id)
            datavalue_text = datavalue[0]["text"]
            multiple_values.append(f"{datavalue_text} ({datavalue_id})")
        multiple_values_text = ", ".join(multiple_values)
        print(f"    {field_id:45}: [{multiple_values_text}]")

    def _print_record(self, record):
        record_id = record[const.RECORD_ID]
        schema_id = record[const.RECORD_SCHEMA_ID]
        items = record[const.ITEMS]
        if len(items) == 0:
            print(f"  RECORD {record_id} {schema_id} -- NO ITEMS")
            return
        print(f"  RECORD {record_id} {schema_id}")
        for item in items:
            self._print_item(item)

    def _print_card(self, card):
        card_id = card[const.CARD_ID]
        records = card[const.RECORDS]
        if len(records) == 0:
            print(f"CARD {card_id} -- NO RECORDS")
            return
        print(f"CARD {card_id}")
        for record in records:
            self._print_record(record)

    def _print_cards(self):
        for card in self.portfolio[const.CARDS]:
            self._print_card(card)

    def _update_field_id_priority_mapping(self):
        for priority in self.retrieved_data.priorities:
            is_priority_set = not (priority.page_class_list != ['*'] or priority.page_class_exclusion_list == [])
            self.field_id_priority_mapping[priority.field_id] = is_priority_set

    def populate_with_cards(self, extracted_cards):
        for full_card in extracted_cards:
            self._get_datavalues_from_full_card_fields_list(full_card["fields_list"])

        self._update_field_id_priority_mapping()

        vertical_cards = [
            Title.LABORATORY_RESULTS,
            Title.ABNORMAL_OBSERVATIONS,
            Title.DIAGNOSIS,
            Title.VITAL_SIGNS,
            Title.CCDA_VITALS,
            Title.REASON_OF_VISIT,
            Title.MEDICATIONS,
            Title.INSURED_DETAILS,
            Title.TECHNICAL_DETAILS,
            Title.CLAIM_DETAIL,
            Title.ENCOUNTER_DETAILS,
            Title.APPLICATION_DETAILS,
            Title.DIAGNOSTIC_PROCEDURES,
            Title.INFORMALS
        ]
        horizontal_cards = [
            Title.PROCEDURES,
            Title.TREATMENTS,
            Title.FAMILY_HISTORY,
            Title.LIST_OF_EVENTS,
            Title.SOCIAL_HISTORY,
            Title.PHYSICAL_EXAMS,
            Title.DENTAL_DETAILS,
            Title.RX_DETAILS,
            Title.CLAIMS,
            Title.PROVIDER_DETAILS,
            Title.CLAIMED_INJURY_DETAILS,
            Title.SUBJECTIVE_DETAILS,
            Title.ENCOUNTER_DX_DETAILS,
            Title.MEDICAL_EQUIPMENT,
            Title.ALLERGENS,
            Title.IMMUNIZATIONS,
            Title.DIABETES,
            Title.CANCER,
            Title.CARDIOVASCULAR,
            Title.MENTAL_NERVOUS_DISORDER,
            Title.BUILD
        ]
        for full_card in extracted_cards:
            card_display = full_card['card']['card_display']
            card_layout = full_card['card'].get('card_layout', '')
            schema_id = card_display.lower().replace(' ', '_')
            sub_card = full_card['card']['sub_card']
            if self.retrieved_data.recall_cards_list and schema_id not in self.retrieved_data.recall_cards_list:
                continue
            if card_display in vertical_cards or 'vertical' in card_layout.lower():
                self._populate_vertical_card(full_card, schema_id, [], sub_card)

            elif card_display in horizontal_cards or 'horizontal' in card_layout.lower():
                self._populate_horizontal_card(full_card, schema_id, sub_card)

            elif card_display in [Title.IMAGING_STUDY]:
                bp = BLUEPRINTS[card_display]['row']
                field_ids_row_num = {cell['display_key']: f"{schema_id}.{cell['display_key'].lower().replace(' ', '_')}"
                                     for cell in bp}
                self._populate_hybrid_card(full_card, schema_id, field_ids_row_num, sub_card)
        return self.portfolio

    # ------------------------------------------------------------------------------------------------------------------
    def update_application_relationship(self):
        for card in self.portfolio['cards']:
            if card['card_id'] in ['insured_details', 'technical_details'] and card['records']:
                application_card_data = self._get_related_card(card, 'application_details')
                card['records'][0][const.RELATIONSHIPS] = application_card_data

    # ------------------------------------------------------------------------------------------------------------------
    def update_user_defined_card_relationship(self):
        for card in self.portfolio['cards']:
            destination_card = card.get('destination_card')
            if not destination_card:
                continue
            if card['records']:
                related_card_data = self._get_related_card(card, destination_card)
                card['records'][0][const.RELATIONSHIPS] = related_card_data

    # ------------------------------------------------------------------------------------------------------------------

    def update_relationship(self):
        self.update_application_relationship()
        self.update_user_defined_card_relationship()

    # ------------------------------------------------------------------------------------------------------------------

    def _browse_input_pages(self):
        for page_data in self.retrieved_data.pages:
            yield page_data.fields, page_data.words, page_data.number

    # ------------------------------------------------------------------------------------------------------------------

    def _empty_datavalue_cell(self):
        return {
            const.DATAVALUE_ID: const.EMPTY_STRING,
            const.ENTITY_TYPE: const.EMPTY_STRING,
            const.ENTITY_CONFIDENCE: 100.0,
            const.TEXT: const.EMPTY_STRING,
            const.IS_USER_EDITED: False,
            const.WORDS: [],
            const.CODIFIED_AS: const.EMPTY_STRING,
            const.CODIFICATION_CATEGORY: const.EMPTY_STRING,
            const.CODIFY: None,
            const.DETECTED_LANG: None,
            const.ENTRY_ID: None,
            const.SOURCE: const.EMPTY_STRING,
            const.LAYOUT_UUID: None,
            "metadata": {},
            "word_type": None,
            const.UI_ID: None,
            const.XPATH: None,
            const.CONTEXT: None,
            const.CODIFIED_DATETIME_OBJECT: const.EMPTY_STRING,
            const.SECTION_INFO: const.SECTION_INFO,
            const.IS_SKIPPED: False
        }

    # ------------------------------------------------------------------------------------------------------------------

    def _prepare_datavalues_cells(self, fields_list, pageNo):
        empty_cell = self._empty_datavalue_cell()
        for field in fields_list:
            cell = deepcopy(empty_cell)
            cell[const.DATAVALUE_ID] = field['value'][0]['value_id']
            cell[const.ENTITY_TYPE] = field['value'][0]['type']
            cell[const.TEXT] = field['value'][0]['value_name']
            cell[const.ENTITY_CONFIDENCE] = field['value'][0]['confidence']
            cell[const.CODIFIED_AS] = field.get(const.CODIFIED_AS, '')
            cell[const.CODIFICATION_CATEGORY] = field.get(const.CODIFICATION_CATEGORY, '')
            cell[const.CODIFY] = field.get(const.CODIFY)
            cell[const.DETECTED_LANG] = field.get(const.DETECTED_LANG)
            cell["metadata"] = field.get("metadata") or {}
            cell["word_type"] = field.get("word_type")
            cell["page_class"] = field.get("page_class")
            cell[const.ENTRY_ID] = field.get(const.ENTRY_ID)
            cell[const.SOURCE] = field.get(const.SOURCE)
            cell[const.LAYOUT_UUID] = field.get(const.LAYOUT_UUID)
            cell[const.UI_ID] = field.get(const.UI_ID)
            cell[const.XPATH] = field.get(const.XPATH)
            cell[const.CONTEXT] = field.get(const.CONTEXT)
            cell[const.CODIFIED_DATETIME_OBJECT] = field.get(const.CODIFIED_DATETIME_OBJECT)
            cell[const.SECTION_INFO] = field.get(const.SECTION_INFO)
            cell[const.IS_SKIPPED] = field.get(const.IS_SKIPPED)
            if const.SECTION_TAG in field['value'][0]:
                cell[const.SECTION_TAG] = field['value'][0][const.SECTION_TAG]
            else:
                cell[const.SECTION_TAG] = ''
            for w in field['value'][0]['word_ids']:
                word_id_obj = {
                    const.CLAIM_FILE_ID: field['value'][0]['claimid'],
                    const.PAGE: pageNo,
                    const.WORD_ID: w
                }
                cell[const.WORDS].append(word_id_obj)
            self.add_data_value(cell)

    def _get_datavalues_from_full_card_fields_list(self, fields_list):
        empty_cell = self._empty_datavalue_cell()
        for row in fields_list:
            card_cell = row["row"]["value"][0]
            if card_cell['value_id'] is not None and card_cell["value_name"] != "":
                datavalue_id = card_cell['value_id']
                if datavalue_id is not None:
                    cell = deepcopy(empty_cell)
                    cell[const.DATAVALUE_ID] = card_cell['value_id']
                    cell[const.ENTITY_TYPE] = card_cell['entity_type']
                    cell[const.TEXT] = card_cell['value_name']
                    cell[const.ENTITY_CONFIDENCE] = card_cell['confidence']
                    cell[const.CODIFIED_AS] = card_cell['codified_as']
                    cell[const.CODIFICATION_CATEGORY] = card_cell['codification_category']
                    cell[const.CODIFY] = card_cell['codify']
                    cell[const.DETECTED_LANG] = card_cell.get(const.DETECTED_LANG)
                    cell[const.ENTRY_ID] = card_cell.get(const.ENTRY_ID)
                    cell[const.SOURCE] = card_cell.get(const.SOURCE)
                    cell[const.LAYOUT_UUID] = card_cell.get(const.LAYOUT_UUID)
                    cell["metadata"] = card_cell.get("metadata") or {}
                    cell["word_type"] = card_cell.get("word_type")
                    cell["page_class"] = card_cell.get("page_class")
                    cell[const.UI_ID] = card_cell.get(const.UI_ID)
                    cell[const.XPATH] = card_cell.get(const.XPATH)
                    cell[const.CONTEXT] = card_cell.get(const.CONTEXT)
                    cell[const.CODIFIED_DATETIME_OBJECT] = card_cell.get(const.CODIFIED_DATETIME_OBJECT)
                    cell[const.SECTION_INFO] = card_cell.get(const.SECTION_INFO)
                    cell[const.IS_SKIPPED] = card_cell.get(const.IS_SKIPPED)
                    if const.SECTION_TAG in card_cell:
                        cell[const.SECTION_TAG] = card_cell[const.SECTION_TAG]
                    else:
                        cell[const.SECTION_TAG] = ''
                    if len(card_cell["word_ids"]) > 0:
                        for w in card_cell['word_ids']:
                            word_id_obj = {
                                const.CLAIM_FILE_ID: card_cell['claimid'],
                                const.PAGE: w["page_number"],
                                const.WORD_ID: w["word_id"]
                            }
                            cell[const.WORDS].append(word_id_obj)
                    self.add_data_value(cell)

    # ------------------------------------------------------------------------------------------------------------------

    def _prepare_word_cells_from_input_pages(self, claim_file_id, words, pageNo):
        list_of_cells = []
        for word in words:
            cell = {
                const.WORD_ID: word['id'],
                const.TEXT: word[const.TEXT],
                const.CONFIDENCE: word.get(const.CONFIDENCE, 0),
                const.BBOX: {
                    const.LEFT: word[const.LEFT],
                    const.TOP: word[const.TOP],
                    const.RIGHT: word[const.RIGHT],
                    const.BOTTOM: word[const.BOTTOM]
                }
            }
            list_of_cells.append(cell)

        word_cells = {
            const.PAGE: pageNo,
            const.WORDS: list_of_cells
        }

        for page_words in self.portfolio[const.WORDS]:
            if page_words.get(const.CLAIM_FILE_ID, '') == claim_file_id:
                page_words[const.PAGES].append(word_cells)
                return

        page_words = {
            const.CLAIM_FILE_ID: claim_file_id,
            const.PAGES: [word_cells]
        }
        self.portfolio[const.WORDS].append(page_words)

    # ------------------------------------------------------------------------------------------------------------------
    def word_to_v2(self, word):
        return {
            const.WORD_ID: word['id'],
            const.TEXT: word[const.TEXT],
            const.CONFIDENCE: word.get(const.CONFIDENCE, 0),
            const.OCR_CONFIDENCE: word.get(const.OCR_CONFIDENCE, 0),
            const.WORD_TYPE: word.get(const.WORD_TYPE),
            const.BBOX: {
                const.LEFT: word[const.LEFT],
                const.TOP: word[const.TOP],
                const.RIGHT: word[const.RIGHT],
                const.BOTTOM: word[const.BOTTOM]
            }
        }

    def prepare_word_cells(self):
        for page in self.retrieved_data.pages:
            words = page.words
            if len(words) == 0:
                continue
            page_number = words[0]["page"]
            claim_file_id = words[0]["claimid"]
            list_of_cells = lmap(self.word_to_v2, words)

            word_cells = {
                const.PAGE: page_number,
                const.WORDS: list_of_cells
            }
            file_words = find(
                lambda page_words: page_words[const.CLAIM_FILE_ID] == claim_file_id,
                self.portfolio[const.WORDS]
            )
            if file_words is not None:
                file_words[const.PAGES].append(word_cells)
            else:
                page_words = {
                    const.CLAIM_FILE_ID: claim_file_id,
                    const.PAGES: [word_cells]
                }
                self.portfolio[const.WORDS].append(page_words)

    def prepare_word_cells_from_unified_words_list(self):
        claim_files = {}
        for word in self.unified_words_list:
            claim_file_id = word.get('claimid')
            page = word.get('page')
            claim_files.setdefault(claim_file_id, {
                "claim_file_id": claim_file_id,
                "pages": {}
            })["pages"].setdefault(page, {
                "page": page,
                "words": []
            })["words"].append(word)

        claim_file_words = lmap(
            lambda claim_file: {
                const.CLAIM_FILE_ID: claim_file["claim_file_id"],
                const.PAGES: lmap(
                    lambda page: {
                        const.PAGE: page["page"],
                        const.WORDS: lmap(self.word_to_v2, page["words"])
                    },
                    claim_file["pages"].values()
                )
            },
            claim_files.values()
        )
        self.portfolio[const.WORDS] = claim_file_words

    # ------------------------------------------------------------------------------------------------------------------

    def add_data_values_from_full_extraction(self):
        for fields_list, words, pageNo in self._browse_input_pages():
            self._prepare_datavalues_cells(fields_list, pageNo)

    def calculate_card_index(self, sub_card: str = ''):
        card_index = re.search(r'\d+', sub_card)
        if card_index is None:
            card_index = 0
        else:
            card_index = int(card_index.group(0)) - 1
        return card_index

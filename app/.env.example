# Environment variables for Cards service
# Copy this file to .env and adjust values as needed

# AWS Profile for accessing S3 and other AWS services
AWS_PROFILE=friendly

# Optional: Set to true for debug mode
# DEBUG=false

# Optional: OpenAI API key for AI-based impairment mapping
# OPENAI_API_KEY=your_openai_api_key_here

# Optional: Prompt name for impairment mapping (default: clarice)
# Available options: clarice, fixed impairments list, no impairments list
# IMPAIRMENT_PROMPT_NAME=clarice
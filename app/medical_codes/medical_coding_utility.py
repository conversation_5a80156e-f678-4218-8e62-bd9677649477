from typing import Dict, Any, Tu<PERSON>, Optional, List, Union
from pydantic import BaseModel
from copy import deepcopy
import re
import traceback
import titles as Title
import constants as const
from elements.edocument import EDocument
import json
from multiprocessing import Manager, Pool

# import pickle as pk
from functools import lru_cache

# from memory_profiler import profile
import logging
import polars as pl

from cvx_rx_utility import CvxRxCodeUtility
from dx_to_icd import DiagICD
from lab_loinc import LOINC
from snomed_services import SnomedServices
from umls_utility import UMLSUtility
from medical_codes.coding import (
    CodeOutput,
    Laboratory,
    icd_chapters,
    icd_chapter_mapping,
    atc_to_icd_chapter,
)
from medical_codes.icd_sections_utility import ICD10Sections
from medical_codes.medical_coding import MedicalCode
from medical_codes.medical_code_tags import medical_tags_dict
from medical_codes.rx_norm import RxNorm
from medical_codes.cpt import CPTCode
from medcat_utility import MedCatUtility
from icd10recommender import Icd10Recommender
import model_input_files_config as Model_Data

import gc

logging.basicConfig(level=logging.DEBUG)


class MedicalCodeSystem:
    def __init__(self, embeddings):
        self.recommender = None
        self.embeddings = embeddings
        self.acronyms = None

    def init(self):
        self.recommender = Icd10Recommender(
            Model_Data.EMBEDDINGS_MODEL, self.embeddings
        )
        Icd10Recommender
        self.acronyms = self.load_acronyms()

    def load_semantics_model(self):
        if self.recommender:
            self.recommender.load_embedding_model_and_embeddings()

    def _get_description_by_code(self, code):
        return self.recommender.code_to_disc.get(code, "").lower()

    def load_acronyms(self) -> Dict[str, List[str]]:
        with open(self.acronyms_file) as f:
            return json.load(f)

    def get_expansion(self, term):
        words = term.split()
        results = []
        self._generate_term_expansion_recursive(words, [], results, 1)
        return (
            results[0] if results != [] else term
        )  # Return expanded term or original term

    def _generate_term_expansion_recursive(
        self, words, current, results, max_results=None
    ):
        if max_results is not None and len(results) >= max_results:
            return

        if not words:
            results.append(" ".join(current))
            return

        word = words[0]

        if word.lower() in self.acronyms:
            expansions = self.acronyms[word.lower()]
            for expansion in expansions:
                self._generate_term_expansion_recursive(
                    words[1:], current + [expansion], results, max_results
                )
        else:
            self._generate_term_expansion_recursive(
                words[1:], current + [word], results, max_results
            )


class ICD(MedicalCodeSystem):
    def __init__(self):
        super().__init__(Model_Data.ICD10_EMBEDDINGS)
        self.acronyms_file = Model_Data.ACRONYMS


class SNOMED(MedicalCodeSystem):
    def __init__(self):
        super().__init__(Model_Data.SNOMED_EMBEDDINGS)
        self.acronyms_file = Model_Data.SNOMED_ACRONYMS


class SharedResources:

    def __init__(self, feature: List[str], medical_codes: object) -> None:
        self.feature = feature
        self.medical_codes: MedicalCode = medical_codes
        self.medcat_service = None
        self.snomed_service = None
        self.loinc_service = None
        self.cvx_services = None
        self.icd_service = None
        self.rx_code_services = None
        self.umls_utility = None
        self.cpt_utility = None
        self.semantic = None


    def get_service(self, code_type: str):
        # print(f"Requested service: {code_type}")
        if code_type in [
            "snomed",
            "snomed_diagnosis",
            "snomed_medications",
            "snomed_procedures",
            "snomed_allergen",
        ]:
            if self.snomed_service is None:
                if Title.ENABLE_MEDCAT in self.feature:
                    if self.medcat_service is None:
                        print("Initializing MedCatUtility service for SNOMED")
                        self.medcat_service = MedCatUtility()
                        self.medcat_service._load_medcat_model()
                else:
                    print("Initializing snomed_service service")
                    self.snomed_service = SNOMED()
                    self.snomed_service.init()
                    self.snomed_service.load_semantics_model()
            
                    if self.semantic:
                        self.snomed_service.recommender.embedding_model = self.semantic
                    else:
                        self.semantic = self.snomed_service.recommender.embedding_model

        elif code_type == "loinc":
            if self.loinc_service is None:
                print("Initializing LOINC service")
                self.loinc_service = LOINC()

        elif code_type == "cvxcode":
            if self.cvx_services is None:
                print("Initializing CvxRxCodeUtility service")
                self.cvx_services = CvxRxCodeUtility()

        elif code_type == "icd10":
            if Title.ENABLE_MEDCAT in self.feature:
                if self.medcat_service is None:
                    print("Initializing MedCatUtility service for ICD10")
                    self.medcat_service = MedCatUtility()
                    self.medcat_service._load_medcat_model()
            else:
                if self.icd_service is None:
                    print("Initializing icd_service service")
                    self.icd_service = ICD()
                    self.icd_service.init()
                    self.icd_service.load_semantics_model()
        
                if self.semantic:
                    self.icd_service.recommender.embedding_model = self.semantic
                else:
                    self.semantic = self.icd_service.recommender.embedding_model

        elif code_type == "rxcode":
            if self.rx_code_services is None:
                print("Initializing RxNorm service")
                self.rx_code_services = RxNorm()
                if self.semantic:
                    self.rx_code_services.init()
                    self.rx_code_services.recommender.embedding_model = self.semantic
                    self.rx_code_services.load_semantics_model()
                else:
                    self.rx_code_services.init()
                    self.rx_code_services.load_semantics_model()
                    self.semantic = self.rx_code_services.recommender.embedding_model

        elif code_type == "atc":
            if self.umls_utility is None:

                print("Initializing UMLS service")
                self.umls_utility = UMLSUtility()
        elif code_type == "cpt":
            if self.cpt_utility is None:
                print("Initializing CPT service")
                self.cpt_utility = CPTCode()
                self.cpt_utility.load_semantics_model()
                self.semantic = self.cpt_utility.recommender.embedding_model
                
        else:
            raise ValueError(f"Unsupported code_type: {code_type}")

        logging.debug(f"Returning service from cache: {code_type}")

    def de_cache_services(self):
        """Release memory from services and force garbage collection."""
        for attr in [
            "medcat_service",
            "snomed_service",
            "loinc_service",
            "cvx_services",
            "icd_service",
            "rx_code_services",
            "umls_utility",
            "semantic",
        ]:
            if getattr(self, attr) is not None:
                delattr(self, attr)  # Remove the attribute completely
        gc.collect()  # Force memory cleanup

    def de_cache_medical_codes(self):
        self.medical_codes = {}


class MedicalCodingUtility:

    def __init__(self, feature: List[str], medical_codes: object) -> None:
        self.feature = feature
        self.medical_codes = medical_codes
        self.shared_resources = SharedResources(feature,medical_codes)
        self.meta = medical_tags_dict
        self.icd_dataset = {}
        self.load_vital_loinc_references()
        self._load_icd_utility()

    def load_vital_loinc_references(self):
        vital_loincs = {
            "Systolic": {"unit": "mm Hg", "loinc": "8480-6"},
            "Diastolic": {"unit": "mm Hg", "loinc": "8462-4"},
            "Blood Pressure": {"unit": None, "loinc": "18684-1"},
            "Weight": {"unit": None, "loinc": "29463-7"},
            "Height": {"unit": None, "loinc": "8302-2"},
            "BMI": {"unit": "kg/m²", "loinc": "39156-5"},
            "Heart Rate": {"unit": "bpm", "loinc": "8867-4"},
            "Oxygen": {"unit": "%", "loinc": "2708-6"},
            "Respiration": {"unit": "/min", "loinc": "9279-1"},
            "Temperature": {"unit": None, "loinc": "8310-5"},
        }
        self.vital_loincs: Dict[str, Dict[str, str]] = vital_loincs
        self.vitals_loinc_dict = {
            value["loinc"]: key for key, value in self.vital_loincs.items()
        }

    def _load_icd_utility(self) -> None:
        self.icd_sections: ICD10Sections = ICD10Sections()
        self.icd_sections._load_icd10_sections_data()
            
    def _load_icd_description_dataset(self) -> None:
        if self.icd_dataset:
            return
        fn = Model_Data.ICD_DATA_2022
        with open(fn) as f:
            icd_dataset = json.load(f)
            self.icd_dataset = {data["code"]: data['long']['descriptions'][0] for data in icd_dataset["data_list"]}
        return    
        
    
    def set_dependencies(self, medical_codes: MedicalCode) -> None:
        self.medical_codes: MedicalCode = medical_codes

    @lru_cache(maxsize=100)
    def _preprocess_text(self, text: str) -> str:
        if not text:
            return text
        formatted_text = deepcopy(text)
        replace_chars = ".*?+/'-&{}()[],\""
        for ch in replace_chars:
            formatted_text = formatted_text.replace(ch, " ")
        formatted_text = re.sub(" +", " ", formatted_text.lower())
        formatted_text = formatted_text.strip()
        return formatted_text

    def _preprocess_lab_dict(self, entry: Laboratory) -> Tuple[str, Laboratory]:
        new_lab: Laboratory = Laboratory()
        new_lab.name = self._preprocess_text(entry.name)
        new_lab.unit = self._preprocess_text(entry.unit)
        new_lab.panel = self._preprocess_text(entry.panel)
        new_lab.specimen = self._preprocess_text(entry.specimen)
        merged_text = f"{new_lab.name}{new_lab.unit}{new_lab.panel}{new_lab.specimen}"
        return merged_text, new_lab

    def _get_snomed_value(self, entry: str, medical_code_type: str) -> CodeOutput:
        entry_key = self._preprocess_text(entry)
        if Title.DISABLE_SNOMED in self.feature:
            return CodeOutput()
        if entry_key in self.shared_resources.medical_codes.snomed:
            return self.shared_resources.medical_codes.snomed[entry_key]
        if entry_key in self.medical_codes.snomed:
            return self.medical_codes.snomed[entry_key]
        self.shared_resources.get_service(medical_code_type)
        if self.shared_resources.medcat_service:
            _, snomed_code, snomed_score, code_description = (
                self.shared_resources.medcat_service.process_diagnosis_icd_snomed(entry)
            )
            self.shared_resources.medical_codes.snomed[entry_key] = CodeOutput(
                medical_code=snomed_code,
                score=snomed_score,
                description=code_description,
            )
            return self.shared_resources.medical_codes.snomed[entry_key]
        code = self.semantic_snomed(entry, medical_code_type)
        return code if code else CodeOutput()
    
    def _get_snomed_diagnoses(self, entry: str, medical_code_type: str) -> CodeOutput:
        entry_key = self._preprocess_text(entry)
        if Title.DISABLE_SNOMED in self.feature:
            return CodeOutput()
        if entry_key in self.shared_resources.medical_codes.snomed_diagnoses:
            return self.shared_resources.medical_codes.snomed_diagnoses[entry_key]
        if entry_key in self.medical_codes.snomed_diagnoses:
            return self.medical_codes.snomed_diagnoses[entry_key]
        self.shared_resources.get_service(medical_code_type)
        if self.shared_resources.medcat_service:
            new_icd_code, snomed_code, snomed_score, code_description = (
                self.shared_resources.medcat_service.process_diagnosis_icd_snomed(entry)
            )
            icd_chapter: int = self.icd_to_chapter(new_icd_code)
            self.shared_resources.medical_codes.icd10[entry_key] = CodeOutput(
                medical_code=new_icd_code,
                score=snomed_score,
                description=code_description,
                chapter=icd_chapter,
            )
            self.shared_resources.medical_codes.snomed_diagnoses[entry_key] = CodeOutput(
                medical_code=snomed_code,
                score=snomed_score,
                description=code_description,
            )
            return self.shared_resources.medical_codes.snomed_diagnoses[entry_key]
        code = self.semantic_snomed(entry, medical_code_type)#add here to check if diagnoses or procedures
        return code if code else CodeOutput()

    def _get_snomed_all_types(self, entry: str, medical_code_type: str) -> CodeOutput:
        entry_key = self._preprocess_text(entry)
        if Title.DISABLE_SNOMED in self.feature:
            return CodeOutput()
        if entry_key in self.shared_resources.medical_codes.snomed:
            return self.shared_resources.medical_codes.snomed[entry_key]
        if entry_key in self.medical_codes.snomed:
            return self.medical_codes.snomed[entry_key]
        self.shared_resources.get_service(medical_code_type)
        if self.shared_resources.medcat_service:
            snomed_code, snomed_score = (
                self.shared_resources.medcat_service.process_all_type_snomed(entry)
            )
            code_description = (
                self.shared_resources.medcat_service._get_description_by_snomed_code(
                    snomed_code
                )
            )
            self.shared_resources.medical_codes.snomed[entry_key] = CodeOutput(
                medical_code=snomed_code,
                score=snomed_score,
                description=code_description,
            )
            return self.shared_resources.medical_codes.snomed[entry_key]
        code = self.semantic_snomed(entry, medical_code_type)
        return code if code else CodeOutput()

    def _get_icd_value(self, entry: str, medical_code_type: str) -> Tuple[str, float]:
        entry_key = self._preprocess_text(entry)
        if Title.DISABLE_ICD in self.feature:
            return CodeOutput()
        if entry_key in self.medical_codes.icd10:
            return self.shared_resources.medical_codes.icd10[entry_key]
        self.shared_resources.get_service(medical_code_type)
        if self.shared_resources.medcat_service:
            icd_code, snomed_code, icd_score, code_description = (
                self.shared_resources.medcat_service.process_diagnosis_icd_snomed(entry)
            )
            icd_chapter = self.icd_to_chapter(icd_code)
            self.shared_resources.medical_codes.icd10[entry_key] = CodeOutput(
                medical_code=icd_code,
                score=icd_score,
                description=code_description,
                chapter=icd_chapter,
            )
            self.shared_resources.medical_codes.snomed_diagnoses[entry_key] = CodeOutput(
                medical_code=snomed_code, score=icd_score, description=code_description
            )
            return self.shared_resources.medical_codes.icd10[entry_key]
        if self.shared_resources.icd_service:
            code = self.semantic_icd10(entry,"icd10")
            return code
        return CodeOutput()


    def _assign_icd_code(
        self, entry: str, code: str, new_description: str = ""
    ) -> None:
        entry_key = self._preprocess_text(entry)
        if not code or not entry_key:
            return
        if entry_key in self.shared_resources.medical_codes.icd10:
            self.shared_resources.medical_codes.icd10[entry_key].medical_code = code
            self.shared_resources.medical_codes.icd10[entry_key].chapter = (
                self.icd_to_chapter(code)
            )
            self.shared_resources.medical_codes.icd10[entry_key].score = 100
            if new_description:
                self.shared_resources.medical_codes.icd10[entry_key].description = (
                    new_description.lower()
                )
            elif not self.shared_resources.medical_codes.icd10[entry_key].description:
                icd_description = self._get_description_by_code(code, "icd10")
                self.shared_resources.medical_codes.icd10[entry_key].description = (
                    icd_description
                )
        else:
            self.shared_resources.medical_codes.icd10[entry_key] = CodeOutput(
                medical_code=code,
                score=100,
                description=self._get_description_by_code(code, "icd10"),
                chapter=self.icd_to_chapter(code)
            )
        return


    def _assign_loinc_code(self, entry: str, code: str) -> None:
        entry_key = self._preprocess_text(entry)
        if entry_key in self.shared_resources.medical_codes.loinc:
            if self.shared_resources.medical_codes.loinc[
                entry_key
            ].loinc_code.medical_code:
                self.shared_resources.medical_codes.loinc[
                    entry_key
                ].loinc_code.medical_code == code
        self.shared_resources.get_service("loinc")
        code_description: str = (
            self.shared_resources.loinc_service.get_component_by_code(code)
        )
        self.shared_resources.medical_codes.loinc[entry_key] = Laboratory(
            name=entry,
            loinc_code=CodeOutput(
                medical_code=code, score=100, description=code_description
            ),
        )


    def _get_lab_loinc(self, entry: Laboratory, medical_code_type: str) -> CodeOutput:
        entry_key, loinc_output = self._preprocess_lab_dict(entry)
        if entry_key in self.shared_resources.medical_codes.loinc:
            return self.shared_resources.medical_codes.loinc[entry_key].loinc_code
        self.shared_resources.get_service(medical_code_type)
        if not self.shared_resources.loinc_service:
            return CodeOutput()
        code_output: CodeOutput = self.shared_resources.loinc_service.process(
            deepcopy(entry)
        )
        code_description: str = (
            self.shared_resources.loinc_service.get_component_by_code(
                code_output.medical_code
            )
        )
        loinc_output.loinc_code = code_output
        loinc_output.loinc_code.description = code_description
        self.shared_resources.medical_codes.loinc[entry_key] = loinc_output
        self._assign_specimen(entry, code_output)
        return code_output


    def _get_lab_panel_loinc(
        self, panel_entry: str, medical_code_type: str
    ) -> CodeOutput:
        entry_key = self._preprocess_text(panel_entry)
        if entry_key in self.shared_resources.medical_codes.loinc_panel:
            return self.shared_resources.medical_codes.loinc_panel[entry_key]
        self.shared_resources.get_service(medical_code_type)
        if not self.shared_resources.loinc_service:
            return CodeOutput()
        code_output: CodeOutput = CodeOutput()
        if self.shared_resources.loinc_service:
            code_output = self.shared_resources.loinc_service.process_panel(panel_entry)
            self.shared_resources.medical_codes.loinc_panel[entry_key] = code_output
        return code_output


    def get_specimen_by_code(self, loinc_output: CodeOutput) -> str:
        if (
            loinc_output.medical_code
            not in self.shared_resources.medical_codes.loinc_specimen
        ):
            specimen: str = self.shared_resources.loinc_service.get_specimen_by_code(
                loinc_output.medical_code
            )
            return specimen
        return self.shared_resources.medical_codes.loinc_specimen[
            loinc_output.medical_code
        ].medical_code


    def _assign_specimen(
        self, loinc_entry: Laboratory, code_output: CodeOutput
    ) -> None:
        if (
            code_output.medical_code
            not in self.shared_resources.medical_codes.loinc_specimen
        ):
            if loinc_entry.specimen:
                self.shared_resources.medical_codes.loinc_specimen[
                    code_output.medical_code
                ] = CodeOutput(medical_code=loinc_entry.specimen, score=100)
            else:
                specimen: str = self.get_specimen_by_code(code_output)
                self.shared_resources.medical_codes.loinc_specimen[
                    code_output.medical_code
                ] = CodeOutput(medical_code=specimen, score=code_output.score)


    def _get_cvx_code(self, entry: str, medical_code_type: str) -> Tuple[str, float]:
        entry_key = self._preprocess_text(entry)
        if Title.DISABLE_RX in self.feature:
            return "", 0.0
        if entry_key in self.shared_resources.medical_codes.rx_code:
            return self.shared_resources.medical_codes.rx_code[entry_key]
        self.shared_resources.get_service(medical_code_type)
        cvx_code, cvx_score = self.shared_resources.cvx_services.process_cvx(entry)
        code_description = self.shared_resources.cvx_services._get_str_of_cvx_code(
            cvx_code
        )
        self.shared_resources.medical_codes.cvx_code[entry_key] = CodeOutput(
            medical_code=cvx_code, score=cvx_score, description=code_description
        )
        return self.shared_resources.medical_codes.cvx_code[entry_key]

    def _get_rx_code_by_cvx(
        self, entry: str, medical_code_type: str
    ) -> Tuple[str, float]:
        code_output: CodeOutput = CodeOutput()
        if Title.DISABLE_RX in self.feature:
            return code_output.medical_code, code_output.score
        entry_key = self._preprocess_text(entry)
        if entry_key in self.medical_codes.rx_code:
            return self.shared_resources.medical_codes.rx_code[entry_key]
        self.shared_resources.get_service(medical_code_type)
        cvx_code = const.EMPTY_STRING
        cvx_code_data = self.medical_codes.cvx_code.get(entry_key)
        if cvx_code_data:
            cvx_code = cvx_code_data.medical_code
        code_output.medical_code, code_output.score = (
            self.shared_resources.cvx_services._process_rx_by_cvx_code(entry, cvx_code)
        )
        code_output.description = (
            self.shared_resources.cvx_services._get_str_of_rx_code(
                code_output.medical_code
            )
        )
        self.medical_codes.rx_code[entry_key] = code_output
        return self.medical_codes.rx_code[entry_key]

    def _get_snomed_medication(self, entry: str, medical_code_type: str) -> CodeOutput:
        entry_key = self._preprocess_text(entry)
        if Title.DISABLE_SNOMED in self.feature:
            return CodeOutput()
        if entry_key in self.shared_resources.medical_codes.snomed:
            return self.shared_resources.medical_codes.snomed[entry_key]
        if entry_key in self.medical_codes.snomed:
            return self.medical_codes.snomed[entry_key]
        self.shared_resources.get_service(medical_code_type)
        if self.shared_resources.medcat_service:
            snomed_code, snomed_score = (
                self.shared_resources.medcat_service.process_medication(entry)
            )
            code_description = (
                self.shared_resources.medcat_service._get_description_by_snomed_code(
                    snomed_code
                )
            )
            self.shared_resources.medical_codes.snomed[entry_key] = CodeOutput(
                medical_code=snomed_code,
                score=snomed_score,
                description=code_description,
            )
            return self.shared_resources.medical_codes.snomed[entry_key]
        code = self.semantic_snomed(entry, medical_code_type)
        return code if code else CodeOutput()

    def semantic_snomed(
        self, entry: Union[str, List[str]], medical_code_type: str
    ) -> Union[CodeOutput, List[CodeOutput]]:
        if Title.DISABLE_SNOMED in self.feature:
            return ("", 0.0) if isinstance(entry, str) else [("", 0.0) for _ in entry]
        is_single_entry = isinstance(entry, str)
        entries = [entry] if is_single_entry else entry

        # Ensure the necessary service is loaded.
        self.shared_resources.get_service(medical_code_type)

        # Preprocess each entry to generate a key and preserve order.
        entry_keys = [self._preprocess_text(ent) for ent in entries]

        # Identify indices for entries that are not in the cache.
        uncached_indices = [
            i
            for i, key in enumerate(entry_keys)
            if key not in self.shared_resources.medical_codes.snomed
        ]

        if uncached_indices:
            # Gather the uncached entries based on their indices.
            uncached_entries = [
                self.shared_resources.snomed_service.get_expansion(entries[i])
                for i in uncached_indices
            ]
            batch_outputs = self.shared_resources.snomed_service.recommender.get_icd10_recommendation(
                uncached_entries
            )

            # Update the cache for each uncached entry.
            for idx, output in zip(uncached_indices, batch_outputs):
                snomed_code = output["icd10"]
                snomed_score = output["confidence"]
                code_description = output["sentence"]
                code_output = CodeOutput(
                    medical_code=snomed_code,
                    score=snomed_score,
                    description=code_description,
                )
                self.shared_resources.medical_codes.snomed[entry_keys[idx]] = (
                    code_output
                )

        # Build results in original order.
        results = [
            self.shared_resources.medical_codes.snomed[key] for key in entry_keys
        ]

        return results[0] if is_single_entry else results

    def semantic_icd10(
        self, entry: Union[str, List[str]], medical_code_type: str
    ) -> Union[CodeOutput, List[CodeOutput]]:
        if Title.DISABLE_SNOMED in self.feature:
            return ("", 0.0) if isinstance(entry, str) else [("", 0.0) for _ in entry]

        is_single_entry = isinstance(entry, str)
        entries = [entry] if is_single_entry else entry

        # Ensure the necessary service is loaded.
        self.shared_resources.get_service(medical_code_type)

        # Preprocess each entry to generate a key and preserve the order.
        entry_keys = [self._preprocess_text(ent) for ent in entries]

        # Identify indices for entries that are not in the cache.
        uncached_indices = [
            i
            for i, key in enumerate(entry_keys)
            if key not in self.shared_resources.medical_codes.icd10
        ]

        if uncached_indices:
            # Gather the uncached entries based on their indices.
            uncached_entries = [
                 self.shared_resources.icd_service.get_expansion(entries[i])
                for i in uncached_indices
            ]
            batch_outputs = (
                self.shared_resources.icd_service.recommender.get_icd10_recommendation(
                    uncached_entries
                )
            )

            # Update the cache for each uncached entry.
            for idx, output in zip(uncached_indices, batch_outputs):
                rx_code = output["icd10"]
                rx_score = output["confidence"]
                code_description = output["sentence"]
                code_output = CodeOutput(
                    medical_code=rx_code, score=rx_score, description=code_description
                )
                self.shared_resources.medical_codes.icd10[entry_keys[idx]] = code_output

        # Build the results list in the original order.
        results = [self.shared_resources.medical_codes.icd10[key] for key in entry_keys]

        return results[0] if is_single_entry else results

    def _get_snomed_procedures(self, entry: str, medical_code_type: str) -> CodeOutput:
        entry_key = self._preprocess_text(entry)
        snomed_code: str = const.EMPTY_STRING
        snomed_score: float = 0.0
        if Title.DISABLE_SNOMED in self.feature:
            return CodeOutput()
        if entry_key in self.shared_resources.medical_codes.snomed:
            return self.shared_resources.medical_codes.snomed[entry_key]
        if entry_key in self.medical_codes.snomed:
            return self.medical_codes.snomed[entry_key]
        self.shared_resources.get_service(medical_code_type)
        if self.shared_resources.medcat_service:
            snomed_code, snomed_score = (
                self.shared_resources.medcat_service.process_procedures(entry)
            )
            code_description = (
                self.shared_resources.medcat_service._get_description_by_snomed_code(
                    snomed_code
                )
            )
            self.shared_resources.medical_codes.snomed[entry_key] = CodeOutput(
                medical_code=snomed_code,
                score=snomed_score,
                description=code_description,
            )
            return self.shared_resources.medical_codes.snomed[entry_key]
        code = self.semantic_snomed(entry, medical_code_type)
        return code if code else CodeOutput()

    def _get_snomed_allergen(
        self, entry: str, medical_code_type: str
    ) -> Tuple[str, float]:
        entry_key = self._preprocess_text(entry)
        snomed_code: str = const.EMPTY_STRING
        snomed_score: float = 0.0
        if Title.DISABLE_SNOMED in self.feature:
            return CodeOutput()
        if entry_key in self.shared_resources.medical_codes.snomed:
            return self.shared_resources.medical_codes.snomed[entry_key]
        if entry_key in self.medical_codes.snomed:
            return self.medical_codes.snomed[entry_key]
        self.shared_resources.get_service(medical_code_type)
        if self.shared_resources.medcat_service:
            snomed_code, snomed_score = (
                self.shared_resources.medcat_service.process_allergen(entry)
            )
            code_description = (
                self.shared_resources.medcat_service._get_description_by_snomed_code(
                    snomed_code
                )
            )
            self.shared_resources.medical_codes.snomed[entry_key] = CodeOutput(
                medical_code=snomed_code,
                score=snomed_score,
                description=code_description,
            )
            return self.shared_resources.medical_codes.snomed[entry_key]
        code = self.semantic_snomed(entry, medical_code_type)
        return code if code else CodeOutput()

    def _get_rxnorm_medication(
        self, entry: Union[str, List[str]], medical_code_type: str
    ) -> Union[CodeOutput, list[CodeOutput]]:
        if Title.DISABLE_RX in self.feature:
            return ("", 0.0) if isinstance(entry, str) else [("", 0.0) for _ in entry]

        is_single_entry = isinstance(entry, str)
        entries = [entry] if is_single_entry else entry

        results = []
        self.shared_resources.get_service(medical_code_type)

        uncached_entries = []
        uncached_keys = []

        for ent in entries:
            entry_key = self._preprocess_text(ent)
            if entry_key in self.shared_resources.medical_codes.rx_code:
                results.append(self.shared_resources.medical_codes.rx_code[entry_key])
            else:
                uncached_entries.append(ent)
                uncached_keys.append(entry_key)

        if uncached_entries:
            batch_outputs = self.shared_resources.rx_code_services.recommender.get_icd10_recommendation(
                uncached_entries
            )

            for key, output in zip(uncached_keys, batch_outputs):
                rx_code, rx_score, code_description = (
                    output["icd10"],
                    output["confidence"],
                    output["sentence"],
                )
                code_output = CodeOutput(
                    medical_code=rx_code, score=rx_score, description=code_description
                )

                self.shared_resources.medical_codes.rx_code[key] = code_output
                results.append(code_output)

        return results[0] if is_single_entry else results

    def _get_cpt_code(
    self, entry: Union[str, List[str]], medical_code_type: str
    ) -> Union[CodeOutput,list[CodeOutput]]:
        is_single_entry = isinstance(entry, str)
        entries = [entry] if is_single_entry else entry

        results = []
        self.shared_resources.get_service(medical_code_type)  

        uncached_entries = []
        uncached_keys = []
        
        for ent in entries:
            entry_key = self._preprocess_text(ent)
            if entry_key in self.shared_resources.medical_codes.cpt_code:
                results.append(self.shared_resources.medical_codes.cpt_code[entry_key])
            else:
                uncached_entries.append(ent)
                uncached_keys.append(entry_key)

        if uncached_entries:
            batch_outputs = self.shared_resources.cpt_utility.get_semantic_codes(uncached_entries)

            for key, output in zip(uncached_keys, batch_outputs):
                cpt_code, cpt_score, code_description = output["icd10"], output["confidence"], output["sentence"]
                code_output = CodeOutput(medical_code=cpt_code, score=cpt_score, description=code_description)

                self.shared_resources.medical_codes.cpt_code[key] = code_output
                results.append(code_output)

        return results[0] if is_single_entry else results
    
    def _get_atc_code(self, entry: str, medical_code_type: str) -> Tuple[str, float]:
        entry_key = self._preprocess_text(entry)
        if Title.DISABLE_ATC in self.feature:
            return CodeOutput()
        if entry_key in self.shared_resources.medical_codes.atc_code:
            return self.shared_resources.medical_codes.atc_code[entry_key]
        self.shared_resources.get_service(medical_code_type)
        atc_code, atc_score = self.shared_resources.umls_utility.get_atc_code(entry)
        code_description = self.shared_resources.umls_utility.get_atc_header(atc_code)
        self.medical_codes.atc_code[entry_key] = CodeOutput(
            medical_code=atc_code, score=atc_score, description=code_description
        )
        return self.medical_codes.atc_code[entry_key]


    def _get_medical_code(self, text_entry: str, medical_code_type: str) -> Tuple[str, float]:
        if medical_code_type == 'snomed':
            code_output: CodeOutput = self._get_snomed_all_types(text_entry, "snomed")
            return code_output
        if medical_code_type == "snomed_diagnosis":
            code_output: CodeOutput = self._get_snomed_diagnoses(text_entry, "snomed_diagnosis")
            return code_output
        if medical_code_type == "snomed_medications":
            code_output: CodeOutput = self._get_snomed_medication(text_entry, "snomed")
            return code_output
        if medical_code_type == "snomed_procedures":
            code_output: CodeOutput = self._get_snomed_procedures(text_entry, "snomed")
            return code_output
        if medical_code_type == "snomed_allergen":
            code_output: CodeOutput = self._get_snomed_allergen(text_entry, "snomed")
            return code_output
        if medical_code_type == "icd10":
            code_output: CodeOutput = self._get_icd_value(text_entry, medical_code_type)
            return code_output
        if medical_code_type == "loinc":
            laboratory_entry: Laboratory = Laboratory()
            laboratory_entry.name = text_entry
            lab_result: CodeOutput = self._get_lab_loinc(
                laboratory_entry, medical_code_type
            )
            return lab_result
        if medical_code_type == "cvxcode":
            code_output: CodeOutput = self._get_cvx_code(text_entry, medical_code_type)
            return code_output
        if medical_code_type == "cvx_to_rxcode":
            code_output: CodeOutput = self._get_rx_code_by_cvx(text_entry, "cvxcode")
            return code_output
        if medical_code_type == "rxcode_medications":
            code_output: CodeOutput = self._get_rxnorm_medication(text_entry, "rxcode")
            return code_output
        if medical_code_type == "atc":
            code_output: CodeOutput = self._get_atc_code(text_entry, medical_code_type)
            return code_output
        if medical_code_type == "cpt":
            code_output: CodeOutput = self._get_cpt_code(text_entry, "cpt")
            return code_output
        return CodeOutput()

    def get_batch_medical_code(self, text_entries: list, medical_code_type: str):
        if medical_code_type == "rxcode_medications":
            code_output: CodeOutput = self._get_rxnorm_medication(
                text_entries, "rxcode"
            )
            return code_output
        if medical_code_type == "snomed":
            code_output: CodeOutput = self.semantic_snomed(text_entries, "snomed")
            return code_output
        if medical_code_type == "icd10":
            code_output: CodeOutput = self.semantic_icd10(text_entries, "icd10")
            return code_output

    def _get_description_by_code(
        self, medical_code: str, medical_code_type: str
    ) -> str:
        if medical_code_type == "icd10":
            code_output: CodeOutput = self.medical_codes.icd10_medical_codes().get(
                medical_code, ""
            )
            if code_output and code_output.description:
                return code_output.description
            if self.shared_resources.medcat_service:
                medcat_desc = self.shared_resources.medcat_service._get_description_by_icd_code(
                    medical_code
                )
                if medcat_desc:
                    return medcat_desc
            elif self.shared_resources.icd_service:
                icd_service_desc = self.shared_resources.icd_service._get_description_by_code(
                    medical_code
                )
                if icd_service_desc:
                    return icd_service_desc
            self._load_icd_description_dataset()
            return self.icd_dataset.get(medical_code, "")
        elif medical_code_type == "snomed":
            code_output = self.shared_resources.medical_codes.snomed_diagnoses.get(
                medical_code, ""
            )
            if code_output:
                return code_output.description
            code_output = self.shared_resources.medical_codes.snomed.get(
                medical_code, ""
            )
            if code_output:
                return code_output.description
            if self.shared_resources.medcat_service:
                return (
                    self.shared_resources.medcat_service._get_description_by_snomed_code(
                        medical_code
                    )
                )
        elif medical_code_type == "cvxcode_to_rxcode":
            self.shared_resources.get_service("cvxcode")
            return self.shared_resources.cvx_services._get_str_of_rx_code(medical_code)
        elif medical_code_type == "atc_category":
            self.shared_resources.get_service("atc")
            return self.shared_resources.umls_utility.get_atc_header([medical_code])
        elif medical_code_type == "snomed_category":
            self.shared_resources.get_service("atc")
            return self._get_medication_categories(medical_code, medical_code_type)
        elif medical_code_type == "rxcode_category":
            self.shared_resources.get_service("atc")
            return self._get_medication_categories(medical_code, medical_code_type)
        elif medical_code_type == "rxcode_medications":
            self.shared_resources.get_service("rxcode")
            return self.shared_resources.rx_code_services._get_description_by_rx_code(
                medical_code
            )
        elif medical_code_type == "medication_nomenclature":
            self.shared_resources.get_service("atc")
            return self.shared_resources.umls_utility.get_drug_nomenclature(
                medical_code
            )

        return ""

    def _get_medication_categories(self, medical_code: str, type: str) -> str:
        if not medical_code:
            return ""
        atc_codes: List[str] = []
        if type == "rxcode_category":
            atc_codes: List[str] = (
                self.shared_resources.umls_utility.get_atc_codes_by_rxcode(medical_code)
            )
        elif type == "snomed_category":
            atc_codes: List[str] = (
                self.shared_resources.umls_utility.get_atc_codes_by_snomed(medical_code)
            )

        if len(atc_codes) == 1:
            return self.shared_resources.umls_utility.get_category_by_rx_code(
                medical_code
            )

        rx_atc_chapter_reference = {
            atc_code: atc_to_icd_chapter.get(atc_code[0]) for atc_code in atc_codes
        }

        atc_to_atc_chapters = {
            atc_to_icd_chapter.get(atc_code[0]) for atc_code in atc_codes
        }
        diagnosis_codes = self.shared_resources.medical_codes.icd10_chapters()
        mutual_codes = atc_to_atc_chapters & diagnosis_codes

        filtered_atc_codes = [
            key
            for key, value in rx_atc_chapter_reference.items()
            if value in mutual_codes
        ]

        return self.shared_resources.umls_utility.group_atc_headers(filtered_atc_codes)

    def get_panel_by_substring(self, panel_name: str) -> Optional[str]:
        self.shared_resources.get_service("loinc")
        return self.shared_resources.loinc_service.get_panel_from_substring(panel_name)

    def icd_to_chapter(self, icd_input: str) -> Optional[int]:
        if len(icd_input) < 3:
            return 0
        if (
            not icd_input[1].isdigit()
            or not icd_input[2].isdigit()
            or not icd_input[0].isalpha()
        ):
            return 0

        icd_prefix = icd_input[0].upper()
        result = 0
        if icd_prefix in icd_chapter_mapping and isinstance(
            icd_chapter_mapping[icd_prefix], dict
        ):
            icd_digit = int(str(icd_input[1]) + str(icd_input[2]))
            possible_chapters = icd_chapter_mapping[icd_prefix]

            for chapter, (start_range, end_range) in possible_chapters.items():
                if start_range <= icd_digit <= end_range:
                    result = chapter
                    break
        return result

    def get_icd_chapter(self, icd_code: str) -> str:
        icd_chapter = ""
        icd_detail: (
            CodeOutput
        ) = self.shared_resources.medical_codes.icd10_medical_codes().get(
            icd_code.upper()
        )
        if icd_detail:
            if icd_detail.chapter == 0:
                icd_detail.chapter = self.icd_to_chapter(icd_code)
            return icd_chapters.get(icd_detail.chapter, "")

    def get_icd_section(self, icd_code: str) -> str:
        return self.icd_sections.get_icd10_section(icd_code)


def populate_coding_tags(
    edoc: EDocument, medicalcoding_obj: MedicalCodingUtility
) -> None:
    try:
        medical_tags = sum(
            [
                tag_list
                for medical_code_type, tag_list in medicalcoding_obj.meta.items()
            ],
            [],
        )
        for page_ents in edoc:
            if page_ents:
                try:
                    filtered_list = [
                        rec
                        for rec in sum(
                            [sublist for sublist in page_ents if sublist], []
                        )
                        if rec["type"] in medical_tags
                    ]
                    if filtered_list:
                        df = pl.DataFrame(filtered_list)
                        results, medicalcoding_obj = assign_medical_coding_tags(
                            df, medicalcoding_obj
                        )
                except Exception as e:
                    print(e)
        return medicalcoding_obj
    except Exception as e:
        logging.error(
            f"ERROR: Populate Medical Coding Tags Failed: {traceback.format_exc()}"
        )

def process_meta_item(
    item: Tuple[str, List[str]],
    pages_of_entities: pl.DataFrame,
    serialized_obj,
    parallel: bool,
) -> List[Dict[str, Any]]:
    try:
        medical_code_type, tag_list = item
        results = []
        batched = ["rxcode_medications", "snomed", "icd10"]
        category_map = {
            "snomed_diagnosis": "snomed",
            "snomed_medications": "snomed",
            "snomed_procedures": "snomed",
            "snomed_allergen": "snomed",
        }

        medicalcoding_obj: MedicalCodingUtility = serialized_obj
        enable_medcat = Title.ENABLE_MEDCAT in medicalcoding_obj.feature  # Store check as a boolean

        # Process filtered entities (excluding positive/yes)
        filtered_entities = pages_of_entities.filter(
            (pl.col("type").is_in(tag_list))
            & (~pl.col("type").str.to_lowercase().str.contains("positive|yes"))
        )

        if filtered_entities.shape[0] > 0:
            mapped_category = category_map.get(medical_code_type, medical_code_type)
            if mapped_category in batched and not enable_medcat:
                codes = medicalcoding_obj.get_batch_medical_code(
                    filtered_entities["text"].to_list(), mapped_category
                )
                results.extend(codes)
            else:
                filtered_entities_data = filtered_entities["text"].apply(
                    lambda x: medicalcoding_obj._get_medical_code(x, medical_code_type),
                    return_dtype=pl.List(pl.Utf8),  # Adjust dtype if needed
                )
                results.extend(list(filtered_entities_data))  # Ensure consistent data type

        # Process checklist entities (including positive/yes)
        checklist_entities = pages_of_entities.filter(
            (pl.col("type").is_in(tag_list))
            & (pl.col("type").str.to_lowercase().str.contains("positive|yes"))
        )

        if checklist_entities.shape[0] > 0:
            mapped_category = category_map.get(medical_code_type, medical_code_type)
            if mapped_category in batched and not enable_medcat:
                checklist_codes = medicalcoding_obj.get_batch_medical_code(
                    checklist_entities["type"].to_list(), mapped_category
                )
            else:
                checklist_codes = checklist_entities["type"].apply(
                    lambda x: medicalcoding_obj._get_medical_code(x, medical_code_type),
                    return_dtype=pl.List(pl.Utf8),
                )
                checklist_codes = list(checklist_codes)  # Convert to list for consistency

            results.extend(checklist_codes)

        return results, medicalcoding_obj

    except Exception as e:
        logging.error(f"An error occurred while processing item {item}: {e}")
        logging.error(traceback.format_exc())
        return [], serialized_obj

def assign_medical_coding_tags(
    pages_of_entities: pl.DataFrame,
    medicalcoding_obj: MedicalCodingUtility,
    parallel=False,
) -> List[Dict[str, Any]]:
    try:
        results = []
        # if parallel:
        #     manager = Manager()
        #     shared_dict = manager.dict()
        #     shared_dict['serialized_obj'] = pk.dumps(medicalcoding_obj)
        #     del medicalcoding_obj
        #     args = [(item, pages_of_entities, shared_dict['serialized_obj'],parallel) for item in medical_tags_dict.items()]
        #     with Pool(processes=1) as pool:
        #         results = pool.starmap(process_meta_item, args)
        #     return results
        # else:
        for item in medical_tags_dict.items():
            result, cache_medicalcoding_obj = process_meta_item(
                item, pages_of_entities, medicalcoding_obj, parallel
            )
            medicalcoding_obj = cache_medicalcoding_obj
            results.append(result)
        return results, medicalcoding_obj

    except Exception as e:
        logging.error(f"An error occurred in assign_medical_coding_tags: {e}")
        logging.error(traceback.format_exc())
        return []


def _get_checklist_tag_text(entity_type_entry: str) -> str:
    checklist_keys = ["yes", "positive"]
    entity_type = entity_type_entry.split(".")
    if entity_type[-1] not in checklist_keys:
        return entity_type_entry
    if "_" in entity_type[1]:
        return entity_type[1].replace("_", " ")
    elif entity_type[2] in checklist_keys:
        return entity_type[1]
    else:
        return f"{entity_type[1].replace('_', ' ')} {entity_type[2].replace('_', ' ')}"


def update_checklist_tags_text(retrieved_data):
    for page_data in retrieved_data.pages:
        for field in page_data.fields:
            before = field["value"][0]["value_name"]
            update_diagnosis_checklist_text(field)
            _update_word_ids(field, page_data, before)


def _update_word_ids(field, page_data, before: str) -> None:
    word_ids = field["value"][0]["word_ids"]
    if before == field["value"][0]["value_name"] or len(word_ids) == 0:
        return
    wid = str(word_ids[0])
    list_of_words = [w for w in page_data.words if w["id"] in word_ids]
    if len(list_of_words):
        new_word = deepcopy(list_of_words[0])
        new_word["text"] = field["value"][0]["value_name"]
        new_word["id"] = wid
        field["value"][0]["word_ids"] = [wid]
        if len(list_of_words) > 1:
            ids = [x["id"] for x in list_of_words]
            field["value"][0]["word_ids"].extend(ids[1:])
        page_data.words.append(new_word)


def update_diagnosis_checklist_text(field) -> None:
    field_type = field["value"][0]["type"]
    if field_type.split(".")[-1] not in ["yes", "positive"]:
        return
    if field_type not in medical_tags_dict.get("icd10", []):
        return
    field["value"][0]["value_name"] = _get_checklist_tag_text(field_type)

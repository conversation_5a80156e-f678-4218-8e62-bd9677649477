from copy import deepcopy
from typing_extensions import Dict, List, Any, Optional
from datetime import datetime
import constants as const
from edoc_utility import add_entity_to_page, get_new_entity
from elements.edocument import Entity, EPageLine
from friendlylib.iterators import find, flatten, lfilter
from medical_codes.coding import CodeOutput, loinc_alcohol, loinc_drugs, loinc_smoking
from medical_codes.medical_coding import MedicalCode
from pydantic import BaseModel


class CodeEntities(BaseModel):
    date_cell: Entity = None
    findings_cell: Entity = None
    medical_code: CodeOutput = None
    medical_code_entity: Entity = None
    medical_code_text: str = ""
    risk_factor_cell: Entity = None


class CodeEntitiesList(BaseModel):
    snomed_entities: List[CodeEntities] = []
    icd_entities: List[CodeEntities] = []
    loinc_entities: List[CodeEntities] = []


class SmokingEntities(BaseModel):
    smoking_status_text: str = ""
    smoking_status: Entity = None
    date_cell: str = ""

class ApplicationEntities(BaseModel):
    application_date: Entity = None
    file_id: str = ""

class DateEntities(BaseModel):
    latest_date: Optional[datetime] = None
    date_entities: List[Entity] = []

class InformalEntitiesList(BaseModel):
    age_entity: Entity = None
    smoking_entity: Entity = None
    application_date: Optional[datetime] = None
    last_smoked_date: Optional[datetime] = None


class CardCodingReference:
    
    
    def __init__(self) -> None:
        self.code_entities_list: CodeEntitiesList = CodeEntitiesList()
        self.informals: InformalEntitiesList = InformalEntitiesList()


    def set_dependencies(self, medical_coding_service: MedicalCode):
        self.medical_coding_service: MedicalCode = medical_coding_service


    def match_cell_codings(self, list_of_rows, coding_types: List[str], risk_name: str, findings: str,
                           date_entries: List[str], required_findings=False) -> None:

        for row in list_of_rows:
            impairment_row = CodeEntities()
            medical_codes: Dict[str, CodeOutput] = {}
            medical_code_entities: Dict[str, Entity] = {}
            impairment_row.risk_factor_cell = find(lambda cell: cell["display_key"] == risk_name, row, default={}).get(
                "entity_object", "")
            impairment_findings_cell = find(lambda cell: cell["display_key"] == findings, row, default={}).get(
                "entity_object", "") or None

            if required_findings is True and (
                    impairment_findings_cell is None or impairment_findings_cell.get("text", "") == ""):
                continue

            impairment_row.findings_cell = self._codify_findings(impairment_findings_cell)

            for date_header in date_entries:
                date_entry = find(lambda cell: cell["display_key"] == date_header, row, default={}).get("entity_object",
                                                                                                        "") or None

                if not impairment_row.date_cell and date_entry:
                    impairment_row.date_cell = date_entry

            for medical_code_type in coding_types:
                medical_code_entities[medical_code_type] = \
                find(lambda cell: cell["display_key"] == medical_code_type, row)["entity_object"] or []
                self._assign_medical_code(medical_code_type, medical_codes, medical_code_entities[medical_code_type])

            self._assign_code_list(impairment_row, medical_codes, medical_code_entities)

        return


    def _assign_code_list(self, reference: CodeEntities, medical_codes: Dict[str, CodeOutput],
                          medical_code_entities: Dict[str, Entity]) -> None:
        if 'LOINC' in medical_codes:
            reference.medical_code = medical_codes['LOINC']
            reference.medical_code_entity = medical_code_entities['LOINC']
            reference.medical_code_text = medical_codes['LOINC'].medical_code
            self.code_entities_list.loinc_entities.append(deepcopy(reference))
        if 'ICD-10' in medical_codes:
            reference.medical_code = medical_codes['ICD-10']
            reference.medical_code_entity = medical_code_entities['ICD-10']
            reference.medical_code_text = medical_codes['ICD-10'].medical_code
            self.code_entities_list.icd_entities.append(deepcopy(reference))
        if 'SNOMED' in medical_codes:
            reference.medical_code = medical_codes['SNOMED']
            reference.medical_code_entity = medical_code_entities['SNOMED']
            reference.medical_code_text = medical_codes['SNOMED'].medical_code
            self.code_entities_list.snomed_entities.append(deepcopy(reference))
        return


    def _assign_medical_code(self, medical_code_type: str, reference: Dict[str, CodeEntities],
                             medical_code_entity: Entity) -> None:
        if not medical_code_entity:
            return
        medical_code_text = medical_code_entity['text']
        if medical_code_type == 'ICD-10':
            if medical_code_text in self.medical_coding_service.medical_codes.icd10_medical_codes():
                reference[medical_code_type] = self.medical_coding_service.medical_codes.icd10_medical_codes().get(
                    medical_code_text)
        elif medical_code_type == 'LOINC':
            if medical_code_text in self.medical_coding_service.medical_codes.loinc_medical_codes():
                reference[medical_code_type] = self.medical_coding_service.medical_codes.loinc_medical_codes().get(
                    medical_code_text)
        elif medical_code_type == 'SNOMED':
            if medical_code_text in self.medical_coding_service.medical_codes.snomed_medical_codes():
                reference[medical_code_type] = self.medical_coding_service.medical_codes.snomed_medical_codes().get(
                    medical_code_text)
        return


    def _codify_findings(self, findings: Entity) -> Entity:

        if not findings:
            return findings
        codify = findings.get('codify') or {}
        if findings.get('codified_as', '') or not codify:
            return findings

        codified_as = codify.get('codified_as', '') or ''
        codified_unit = codify.get('unit', '') or ''
        codified_text = (codified_as + " " + codified_unit).strip()
        if codified_text.upper() == findings['text'].upper().strip() or codified_text == "":
            return findings
        formatted_codified_finding = get_new_entity(findings, const.GENERATED_ENTITY + findings['type'], codified_text)
        return formatted_codified_finding


    def generate_status_loinc_based(self, list_of_rows, edoc) -> None:
        for row in list_of_rows:
            rating = find(lambda cell: cell["display_key"] == "Rating", row, default={}).get(
                "entity_object", {}) or {}
            loinc_code = find(lambda cell: cell["display_key"] == "LOINC", row, default={}).get(
                    "entity_object", {}) or None
            value = find(lambda cell: cell["display_key"] == "Value", row, default={}).get(
                "entity_object", {}) or None
            entity_to_place = value or rating
            if rating.get('text', '') in [const.ABNORMAL, const.HIGH] and loinc_code:
                if loinc_code['text'] in loinc_smoking:
                    generated_status = get_new_entity(entity_to_place, "patient.smoking.status.yes", "yes")
                    self._add_entity_to_edoc(generated_status, edoc)
                    continue
                if loinc_code['text'] in loinc_alcohol:
                    generated_status = get_new_entity(entity_to_place, "patient.alcohol.status.yes", "yes")
                    self._add_entity_to_edoc(generated_status, edoc)
                    continue
                if loinc_code['text'] in loinc_drugs:
                    generated_status = get_new_entity(entity_to_place, "patient.drugs.status.yes", "yes")
                    self._add_entity_to_edoc(generated_status, edoc)
                    continue
    
    def _add_entity_to_edoc(self, generated_status: Entity, edoc):
        for page_of_lines in edoc:
            add_entity_to_page(generated_status, page_of_lines)
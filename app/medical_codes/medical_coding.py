from pydantic import BaseModel
from typing import Dict, Set
from medical_codes.coding import CodeOutput, Laboratory

class MedicalCode(BaseModel):
    snomed: Dict[str, CodeOutput] = {}
    snomed_diagnoses: Dict[str, CodeOutput] = {}
    loinc: Dict[str, Laboratory] = {}
    loinc_panel: Dict[str, CodeOutput] = {}
    loinc_specimen: Dict[str, CodeOutput] = {}
    icd10: Dict[str, CodeOutput] = {}
    rx_code: Dict[str, CodeOutput] = {}
    cvx_code: Dict[str, CodeOutput] = {}
    atc_code: Dict[str, CodeOutput] = {}
    cpt_code: Dict[str, CodeOutput] = {}
    
    
    def __init__(self, **data) -> None:
        super().__init__(**data)    
        
    
    def snomed_medical_codes(self) -> Dict[str, str]:
        combined = {output.medical_code: output for output in self.snomed.values()}
        combined.update({output.medical_code: output for output in self.snomed_diagnoses.values()})
        return combined
    
    
    def loinc_medical_codes(self) -> Dict[str, str]:
        return {output.loinc_code.medical_code: output.loinc_code for output in self.loinc.values()}
    
    
    def icd10_medical_codes(self) -> Dict[str, str]:
        return {output.medical_code: output for output in self.icd10.values()}
    
    
    def icd10_chapters(self) -> Set[str]:
        return {output.chapter for output in self.icd10.values() if output.chapter != 0}


    def all_empty(self) -> bool:
        return all(not medical_codes for medical_codes in self.dict().values())
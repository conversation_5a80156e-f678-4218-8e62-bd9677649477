from typing import Any, Dict, List, Union

from icd10recommender import Icd10Recommender

import model_input_files_config as Model_Data

class CPTCode:
    def __init__(self):
        self.recommender = None

    def load_semantics_model(self):
        if not self.recommender:
            self.recommender = Icd10Recommender(
                # "././model_data/icd10_embedding/icd_10_sentence_transformer_128_dim_model",
                # "./model_data/icd10_embedding/cpt_embeddings.h5"
                Model_Data.EMBEDDINGS_MODEL,
                Model_Data.CPT_EMBEDDINGS
            )
            self.recommender.load_embedding_model_and_embeddings()
            

    def get_semantic_codes(self, text: Union[str, List[str]]):
        predictions: List[Dict[str, Any]] = self.recommender.get_icd10_recommendation(text)
        return predictions


if __name__ == "__main__":
    model = CPTCode()
    model.load_semantics_model()
    
    while True:
        text = input("Text to lookup >")
        entry_text = model.get_semantic_codes(text)
        print(entry_text)

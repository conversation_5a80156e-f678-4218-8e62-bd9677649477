import json
import os
import re
import sys
import time
import traceback
import uuid
from copy import deepcopy
from collections import defaultdict
from itertools import chain
from typing import Any, Dict, List, Optional, Tuple
import constants as const
import table_dicts
import table_utils
import titles as Title
from blueprints import BLUEPRINTS, ENTITY_ROLE
from cards_schemas import card_schemas
from edoc_utility import add_entity_to_page
from elements.edocument import EDocument, Entity, EPageLine
from friendlylib.iterators import find, get_first, lfilter, list_minus, lmap
from icd10recommender import Icd10Recommender
from medical_codes.card_reference import CardCodingReference
from medical_codes.coding import CodeOutput
from medical_codes.medical_coding_utility import MedicalCodingUtility
from utils import dates_utility
from utils.open_ai import OpenAIRequestHandler
import model_input_files_config as Model_Data


class DiagnosisUtility:
    def __init__(self):
        self.misc = object
        self.card_name = Title.DIAGNOSIS
        self.retrieved_data = None
        self.blueprint_data = {}
        self.dx_icds = {}
        self.icd10_sections: Dict[str, List[str]] = {}
        self.tagged_icd10: Dict[str, Dict] = {}
        self.medical_coding_service: MedicalCodingUtility = object
        self.card_references = object
        self.prompts = self._load_prompts()

    # -----------------------------------------------------------------------------

    def set_dependencies(
            self,
            objMisc,
            retrieved_data,
            medical_coding_service: MedicalCodingUtility,
            card_references,
    ):
        self.misc = objMisc
        self.retrieved_data = retrieved_data
        self.medical_coding_service: MedicalCodingUtility = medical_coding_service
        self.card_references: CardCodingReference = card_references

    # -----------------------------------------------------------------------------

    def _load_prompts(self) -> dict[str, dict[str, str]]:
        """
        Load the diagnosis impairment prompts from the JSON file.

        Returns:
            Dictionary of prompts keyed by prompt name.
            
        Raises:
            ValueError: If the prompts file cannot be loaded.
        """
        prompts_file = os.path.join(os.path.dirname(__file__), 'prompts', 'diagnosis_impairment_prompts.json')
        try:
            with open(prompts_file, 'r') as f:
                prompts = json.load(f)
            return prompts
        except FileNotFoundError:
            raise ValueError(f"Prompts file not found: {prompts_file}")
        except json.JSONDecodeError as e:
            raise ValueError(f"Invalid JSON in prompts file {prompts_file}: {e}")
        except Exception as e:
            raise ValueError(f"Error loading prompts file {prompts_file}: {e}")

    # -----------------------------------------------------------------------------

    def _filter_unique_rows(self, list_of_rows: list):
        list_of_list_of_text = list()
        rows = []
        for row in list_of_rows:
            cells = list(filter(lambda cell: cell["entity_object"] is not None, row))
            list_of_text = list(
                map(lambda cell: cell["entity_object"]["text"].strip().lower(), cells)
            )

            list_of_text = sorted(list_of_text)
            equal_list = list(
                filter(lambda lot: lot == list_of_text, list_of_list_of_text)
            )
            if len(equal_list) == 0:
                list_of_list_of_text.append(list_of_text)
                rows.append(cells)
        return rows

    # ---------------------------------------------------------------------------------------------------------

    def _diag_rows_to_table(self, list_of_rows):
        rows = self._filter_unique_rows(list_of_rows)
        return self._build_ui_card(rows)

    # -----------------------------------------------------------------------------------------------------------------------

    # noinspection PyUnusedLocal
    def _make_all_cells_of_row(self, row_num: int, row: list, ner_keys: dict):
        full_cells = list(filter(lambda cell: cell["entity_object"] is not None, row))
        table_row = []
        doc_num = 1
        claimid = ""

        for _label, ner_list in ner_keys.items():
            cells = list(
                filter(
                    lambda cell: cell["entity_object"]["type"] in ner_list, full_cells
                )
            )
            if len(cells):
                cell = cells[0]["entity_object"]
                table_row.append(cell)
                doc_num = cell["doc_num"]
                claimid = cell["claimid"]
            else:
                cell = deepcopy(table_dicts.empty_big_cell)
                cell["type"] = ner_list[0]
                cell["id"] = str(uuid.uuid4())
                cell["doc_num"] = doc_num
                cell["claimid"] = claimid
                table_row.append(cell)
        return table_row

    # -----------------------------------------------------------------------------------------------------------------------

    def _insert_type_of_diagnosis_cell(
            self, row: list, cell_type="diagnoses.type"
    ) -> list:
        diagnosis_types = self.blueprint_data["diagnosis_types"]
        diag_type_cell = deepcopy(row[0])
        diag_type_cell["text"] = diagnosis_types.get(diag_type_cell["type"], "")
        diag_type_cell["codified_as"] = diagnosis_types.get(diag_type_cell["type"], "")
        diag_type_cell["type"] = const.GENERATED_ENTITY + cell_type
        diag_type_cell["id"] = str(uuid.uuid4())
        row.insert(1, diag_type_cell)

        # -----------------------------------------------------------------------------------------------------------------------

    def _build_ui_card(self, rows: list):
        fields_list = self.misc.map_header_cell_to_table_cell(Title.DIAGNOSIS)
        total_cols = len(fields_list)
        row_num = 0

        ner_keys = {
            const.DIAGNOSIS: [],
            const.ICD10: [],
            "Condition": [],
            "ICD-10 Description": [],
            "ICD Chapter": [],
            "ICD Section": [],
            "Impairment": [],
            const.VISIT_DATE: [],
            const.DIAGNOSIS_DATE: [],
            const.SNOMED: [],
        }

        for label in ner_keys:
            this_list = [
                x["ner.keys"]
                for x in BLUEPRINTS[Title.DIAGNOSIS]["row"]
                if x["display_key"] == label
            ]
            ner_keys[label] = list(chain(*this_list))
        for row in rows:
            row_num += 1
            table_row = self._make_all_cells_of_row(row_num, row, ner_keys)
            self._insert_type_of_diagnosis_cell(table_row)
            for col_num, cell in enumerate(table_row):
                ec = table_utils.entity_to_ui_cell(cell, row_num, col_num)
                fields_list.append(ec)

        for cell in fields_list:
            cell["table_info"]["row_count"] = row_num
            cell["table_info"]["col_count"] = total_cols

        ui_card = {
            "card": {"card_display": Title.DIAGNOSIS, "sub_card": ""},
            const.FIELDS_LIST: fields_list,
        }
        ui_card["card"]["schema"] = card_schemas[Title.DIAGNOSIS]
        return ui_card

    # -----------------------------------------------------------------------------------------------------------------------

    def _prepare_blueprint_data(self):
        self.blueprint_data = {}
        row_blueprint = BLUEPRINTS[self.card_name]["row"]
        row_diagnosis_types = BLUEPRINTS[Title.DIAGNOSIS]["diagnosis_types"]
        mother = [
            e["ner.keys"] for e in row_blueprint if e["role"] == ENTITY_ROLE.MOTHER
        ]
        children = [
            e["ner.keys"]
            for e in row_blueprint
            if e["role"] in (ENTITY_ROLE.CHILD, ENTITY_ROLE.REQUIRED_CHILD)
        ]
        mother = list(chain(*mother))
        children = list(chain(*children))
        dx_date_ners = [
            e["ner.keys"] for e in row_blueprint if e["entity_type"] == "diag.date"
        ]
        visit_date_ners = [
            e["ner.keys"] for e in row_blueprint if e["entity_type"] == "visit.date"
        ]
        dx_date_ners = list(chain(*dx_date_ners))
        visit_date_ners = list(chain(*visit_date_ners))
        children = [
            x for x in children if x not in dx_date_ners and x not in visit_date_ners
        ]

        self.blueprint_data["row_blueprint"] = row_blueprint
        self.blueprint_data["mother"] = mother
        self.blueprint_data["children"] = children
        self.blueprint_data["dx_date_ners"] = dx_date_ners
        self.blueprint_data["visit_date_ners"] = visit_date_ners
        self.blueprint_data[const.RESTRICTED_SECTIONS] = BLUEPRINTS[self.card_name][
            const.RESTRICTED_SECTIONS
        ]
        self.blueprint_data["diagnosis_types"] = {
            diagnosis: diagnosis_type
            for diagnosis_type, diagnosis_list in row_diagnosis_types.items()
            for diagnosis in diagnosis_list
        }
        self.page_classifiers = {
            file.file_id: {
                page.page_number: page.page_class for page in file.page_classes
            }
            for file in self.retrieved_data.file_page_classes
        }

    # -----------------------------------------------------------------------------------------------------------------------

    def _fill_missing_dummy_cell(self, list_of_vectors):
        rows = []
        for vector in list_of_vectors:
            row = deepcopy(self.blueprint_data["row_blueprint"])
            for i, bp in enumerate(self.blueprint_data["row_blueprint"]):
                display_key = bp["display_key"]
                row[i].pop("ner.keys")
                if display_key in vector:
                    cell = vector[display_key]
                    if len(cell):
                        row[i]["entity_type"] = cell["type"]
                        row[i]["entity_object"] = deepcopy(cell)

            rows.append(row)
        return rows

    # ------------------------------------------------------------------------------------------------------------------

    def _is_in_restricted_section(self, cell: dict):
        return any(
            [
                x
                for x in cell.get(const.SECTION_TAG, [])
                if x in self.blueprint_data[const.RESTRICTED_SECTIONS]
            ]
        )

        # --------------------------------------------------------------------------------------------------------------

    def _edoc_to_page_wise(self, edoc: EDocument) -> dict:
        pages = {}
        document = {}
        for page_data in edoc:
            for row in page_data:
                for cell in row:
                    if (len(cell["text"]) == 0) or self._is_in_restricted_section(cell):
                        continue
                    document.setdefault(cell["doc_num"], [])
                    if (
                            (cell["type"] in self.blueprint_data["mother"])
                            or (cell["type"] in self.blueprint_data["children"])
                            or (cell["type"] in self.blueprint_data["dx_date_ners"])
                            or (cell["type"] in self.blueprint_data["visit_date_ners"])
                    ):

                        key = (
                                str(cell["doc_num"]) + const.hyphen + str(cell["abs_page"])
                        )
                        if key not in pages:
                            pages[key] = {
                                "mother": [],
                                "children": [],
                                "dx.dates": [],
                                "visit.dates": [],
                            }
                        if cell["type"] in self.blueprint_data["mother"]:
                            is_positive_or_yes = cell["type"].strip().split(".")[
                                                     -1
                                                 ].lower() in {"positive", "yes"}
                            if (
                                    is_positive_or_yes
                                    and "none" in cell["text"].lower().strip()
                            ):
                                continue
                            pages[key]["mother"].append(cell)
                        if cell["type"] in self.blueprint_data["children"]:
                            pages[key]["children"].append(cell)
                        if cell["type"] in self.blueprint_data["dx_date_ners"]:
                            pages[key]["dx.dates"].append(cell)
                        if cell["type"] in self.blueprint_data["visit_date_ners"]:
                            pages[key]["visit.dates"].append(cell)
                            document[cell["doc_num"]].append(cell)

        return pages, document

    # ------------------------------------------------------------------------------------------------------------------

    def _estimate_icd_cell_attachement(
            self,
            vector: Dict[str, Entity],
            mother: Entity,
            mother_point: Tuple,
            children: EPageLine,
            used_ids: List[str],
    ) -> Tuple[Dict[str, Entity], List[str]]:
        min_dist: int = sys.maxsize
        current_child: Optional[Entity] = None
        diagnosis_name = mother.get("text", "").strip().lower()

        for child in children:
            if len(child["text"]) == 0 or not self._is_icd10_format(child["text"]):
                continue
            if child["id"] in used_ids:
                continue
            child_point = self.misc.get_point(child)
            if self.misc.is_same_line(child_point, mother_point):
                dist = self.misc.rect_distance(child_point, mother_point)
                if dist < min_dist:
                    min_dist = dist
                    current_child = child
        if current_child is not None:
            self.medical_coding_service._assign_icd_code(diagnosis_name, current_child["text"])
            icd_description = self.medical_coding_service._get_description_by_code(current_child['text'], "icd10")
            if icd_description:
                current_child['metadata'] = {
                    "info": f"{icd_description}",
                    "type": "medical_code",
                }

            used_ids.append(current_child["id"])
            vector["ICD-10"] = current_child
            self.tagged_icd10[diagnosis_name] = current_child
        return vector, used_ids

    # -----------------------------------------------------------------------------------------------------------------------

    def _is_icd10_format(self, icd10_entry: str):
        icd10_entry = icd10_entry.strip().upper()
        icd10_pattern = r"^[A-Z]\d{2}(\.[A-Z0-9]{1,4})?$"
        if len(icd10_entry) >= 3 and re.match(icd10_pattern, icd10_entry):
            return True
        return False

    # -----------------------------------------------------------------------------------------------------------------------

    def _is_date_position(
            self, position: str, child_point: list, mother_point: list
    ) -> bool:
        if position == "same line":
            return self.misc.is_same_line(child_point, mother_point)
        elif position == "above line":
            return self.misc.is_above_line(child_point, mother_point)
        elif position == "below line":
            return self.misc.is_above_line(mother_point, child_point)
        elif position == "same line right side":
            return self.misc.is_same_line_right_side(child_point, mother_point)

    # -----------------------------------------------------------------------------------------------------------------------

    def _get_date_attachement(
            self,
            position: str,
            vector: dict,
            mother_point: dict,
            dates: list,
            date_type: str,
    ) -> dict:
        min_dist = sys.maxsize
        current_child = None
        attachement_found = False
        for child in dates:
            if len(child["text"]) == 0:
                continue

            child_point = self.misc.get_point(child)
            if self._is_date_position(position, child_point, mother_point):
                dist = self.misc.get_center_to_center_distance(
                    child_point, mother_point
                )
                if dist < min_dist:
                    min_dist = dist
                    current_child = child

        if current_child is not None:
            vector[date_type] = current_child
            attachement_found = True

        return vector, attachement_found

    # -----------------------------------------------------------------------------------------------------------------------

    def _estimate_diag_date_cell_attachment(
            self,
            vector: dict,
            mother_point: dict,
            dates: list,
            date_type: str,
            used_diag_date_ids: list,
            positions: list,
    ):
        unused_dates = lfilter(
            lambda date_entry: date_entry["id"] not in used_diag_date_ids, dates
        )
        for position in positions:
            vector, attachement_found = self._get_date_attachement(
                position, vector, mother_point, unused_dates, date_type
            )
            if attachement_found:
                used_diag_date_ids.append(vector["Diagnosis Date"]["id"])
                return vector, used_diag_date_ids
        return vector, used_diag_date_ids

    # -----------------------------------------------------------------------------------------------------------------------

    # noinspection PyUnusedLocal
    def _estimate_primary_cell_attachement(
            self,
            vector: dict,
            mother: dict,
            mother_point: dict,
            dates: list,
            date_type: str,
    ) -> dict:
        if len(dates) == 0:
            return vector

        if len(dates) == 1 and date_type == const.VISIT_DATE:
            vector["Visit Date"] = dates[0]
            return vector
        positions = ["same line", "above line"]
        for position in positions:
            vector, attachement_found = self._get_date_attachement(
                position, vector, mother_point, dates, date_type
            )
            if attachement_found:
                return vector

        return vector

    # -----------------------------------------------------------------------------------------------------------------------

    def _populate_with_all_data(
            self, page_data: Dict[str, EPageLine]
    ) -> List[Dict[str, Dict]]:
        mothers: EPageLine = page_data["mother"]
        children: EPageLine = page_data["children"]
        diag_dates: EPageLine = page_data["dx.dates"]
        visit_dates: EPageLine = page_data["visit.dates"]
        list_of_vectors = []
        used_ids: List[str] = []
        used_diag_date_ids: List[str] = []
        diag_dates_positions: List[str] = [
            "same line right side",
            "same line",
            "below line",
        ]
        for mother in reversed(mothers):
            vector: Dict[str, Entity] = {"Diagnosis": mother}
            mother_point = self.misc.get_point(mother)
            vector, used_ids = self._estimate_icd_cell_attachement(
                vector, mother, mother_point, children, used_ids
            )
            vector = self._estimate_primary_cell_attachement(
                vector, mother, mother_point, visit_dates, const.VISIT_DATE
            )
            if not vector.get(const.ICD10):
                icd_cell: Optional[Entity] = self._get_medical_code(
                    mother, "icd10", "icd10"
                )
                if icd_cell:
                    vector[const.ICD10] = icd_cell
            snomed_cell: Optional[Entity] = self._get_medical_code(
                mother, "snomed", "snomed_diagnosis"
            )

            if snomed_cell:
                vector[const.SNOMED] = snomed_cell

            diag_dates_by_group = self._filter_diag_dates(mother, deepcopy(diag_dates))
            vector, used_diag_date_ids = self._estimate_diag_date_cell_attachment(
                vector,
                mother_point,
                diag_dates_by_group,
                const.DIAGNOSIS_DATE,
                used_diag_date_ids,
                diag_dates_positions,
            )
            list_of_vectors.append(vector)
        if len(diag_dates) != len(used_diag_date_ids):
            for vector in list_of_vectors:
                if vector.get("Diagnosis Date", None):
                    continue
                mother_point = self.misc.get_point(vector["Diagnosis"])
                diag_dates_by_group = self._filter_diag_dates(
                    mother, deepcopy(diag_dates)
                )
                vector, used_diag_date_ids = self._estimate_diag_date_cell_attachment(
                    vector,
                    mother_point,
                    diag_dates_by_group,
                    const.DIAGNOSIS_DATE,
                    used_diag_date_ids,
                    ["above line"],
                )
        return reversed(list_of_vectors)

    # -----------------------------------------------------------------------------------------------------------------------

    def _filter_diag_dates(self, mother: dict, diag_dates: list) -> list:
        if len(diag_dates) == 0:
            return []
        if "history" in mother["type"]:
            diag_dates = lfilter(lambda diag: "history" in diag["type"], diag_dates)
            return diag_dates
        return diag_dates

    # -----------------------------------------------------------------------------------------------------------------------

    def remove_similar_codes_empty_children(self, list_of_rows):
        new_rows = []
        empty_dates_icds = {}
        ignored_cells = {
            const.ICD10: True,
            const.SNOMED: True,
            const.VISIT_DATE: False,
            const.DIAGNOSIS_DATE: False,
            "Diagnosis Date": False,
            "ICD Chapter": True,
            "ICD Section": True,
            "Impairment": True,
            "Condition": True,
            "ICD-10 Description": True,
            "Diagnosis Category": True,
        }

        for row in list_of_rows:
            icd10_row = find(lambda cell: cell["display_key"] == "ICD-10", row)
            children_text = self.misc.get_children_merged_text(
                row, const.DIAGNOSIS, ignored_cells
            )
            if children_text:
                new_rows.append(row)
                continue
            if children_text == "" and icd10_row.get('entity_object', {}):
                icd_text = icd10_row.get('entity_object', {}).get('text', '')
                # Skip if it's similar to known ICDs
                if any(icd_text in icd_candidate for icd_candidate in self.dx_icds):
                    continue
                # Skip if already seen as a substring
                if any(icd_text in icd_candidate for icd_candidate in empty_dates_icds):
                    continue
                for icd_candidate in list(empty_dates_icds.keys()):
                    if icd_candidate in icd_text:
                        del empty_dates_icds[icd_candidate]
                        # Remove similar existing ICDs (where row_icd is broader)
                empty_dates_icds = {
                    icd: entities for icd, entities in empty_dates_icds.items()
                    if icd_text not in icd
                }
                empty_dates_icds[icd_text] = row
                continue
            
            new_rows.append(row)
            
            
        return new_rows + list(empty_dates_icds.values())

    # -----------------------------------------------------------------------------------------------------------------------

    def _deduplicate_diagnoses_by_icd_and_date(self, list_of_rows: List[List[Dict[str, Any]]]) -> List[
        List[Dict[str, Any]]]:
        filtered_rows = []
        grouped_by_date = defaultdict(list)

        for row in list_of_rows:
            icd_code = self.misc.get_display_key_text(row, const.ICD10)
            visit_date = self.misc.get_display_key_text(row, const.VISIT_DATE)
            if icd_code and visit_date:
                grouped_by_date[visit_date].append((icd_code, row))
            else:
                filtered_rows.append(row)

        for date_key, icd_row_pairs in grouped_by_date.items():
            icd_row_pairs.sort(key=lambda x: len(x[0]), reverse=True)
            icd_seen = set()
            for icd_code, row in icd_row_pairs:
                if any(icd_code in seen for seen in icd_seen):
                    continue
                icd_seen.add(icd_code)
                filtered_rows.append(row)

        return filtered_rows

    # -----------------------------------------------------------------------------------------------------------------------

    def _get_icd_section(self, diagnosis: str, icd10: str) -> Optional[str]:
        icd_section: str = self.medical_coding_service.get_icd_section(icd10)
        if icd_section:
            self.icd10_sections[diagnosis] = icd_section
            return icd_section
        return None

    # -----------------------------------------------------------------------------------------------------------------------

    def _get_medical_code(
            self, diagnosis_cell: Entity, code_type: str, coding_service: str
    ) -> Optional[Entity]:
        diag_value = diagnosis_cell["text"]
        diagnosis_text = diag_value.lower().strip()

        code_output: CodeOutput = self.medical_coding_service._get_medical_code(
            diagnosis_text, coding_service
        )

        if not code_output.medical_code:
            return None

        medical_code_cell = deepcopy(diagnosis_cell)
        medical_code_cell["type"] = const.GENERATED_ENTITY + code_type
        medical_code_cell["id"] = str(uuid.uuid4())
        medical_code_cell["text"] = code_output.medical_code
        medical_code_cell["codify"] = None
        medical_code_cell["metadata"] = {
            "card_generated": True,
            "info": f"{code_output.description}",
            "type": "medical_code",
        }

        return medical_code_cell

    # -----------------------------------------------------------------------------------------------------------------------

    def _check_diagnosis_by_chapter(self, icd_value: str) -> bool:
        if len(icd_value) < 3:
            return False
        diagnosis_procedure_blocks = {
            "z": [
                30,
                31,
                34,
                35,
                36,
                39,
                98,
                *range(23, 30),
                *range(0, 14),
                *range(40, 55),
                *range(55, 66),
                *range(70, 77),
                *range(80, 100),
            ],
            "o": [*range(0, 9), *range(32, 37), *range(60, 76), *range(80, 85)],
            "y": [*range(35, 37)],
        }
        first_char = icd_value[0].strip().lower()
        if icd_value[1:3].isdigit():
            icd_block = int(icd_value[1:3])
            if first_char in diagnosis_procedure_blocks:
                return icd_block in diagnosis_procedure_blocks[first_char]
        return False

    # ---------------------------------------------------------------------------------------------------------

    def _contains_procedure_keyword(self, text: str) -> bool:
        if "screening" in text or "exam" in text:
            return True
        
        if "encounter" in text and not (
            "initial encounter" in text or "subsequent encounter" in text
        ):
            return True
        
        return False

    # ---------------------------------------------------------------------------------------------------------

    def _diagnosis_to_procedures(self, list_of_rows: list, diagnosis_procedures: List):
        diagnosis_rows = []

        for i, row in enumerate(list_of_rows):
            icd_chapter = find(lambda cell: cell["display_key"] == "ICD-10", row)
            diagnosis_text = find(
                lambda cell: cell["display_key"] == const.DIAGNOSIS, row
            )["entity_object"]["text"]
            if self._contains_procedure_keyword(diagnosis_text):
                diagnosis_procedures.append(row)
                continue
            if icd_chapter.get("entity_object"):
                chapter_value = icd_chapter["entity_object"]["text"]
                if self._check_diagnosis_by_chapter(chapter_value):
                    diagnosis_procedures.append(row)
                    continue
            diagnosis_rows.append(row)
        return diagnosis_rows

    # -----------------------------------------------------------------------------------------------------------------------

    def _create_dx_cell(self, observation_cell, entity_type: str, cell_text: str):
        if observation_cell.get("entity_object", None) is None:
            return observation_cell
        if cell_text:
            observation_cell["entity_object"]["type"] = (
                    const.GENERATED_ENTITY + entity_type
            )
            observation_cell["entity_object"]["text"] = cell_text
            observation_cell["entity_object"]["id"] = str(uuid.uuid4())
            observation_cell["entity_object"]["codified_as"] = cell_text
            observation_cell["role"] = ENTITY_ROLE.CHILD
        else:
            observation_cell["entity_object"] = None

        return observation_cell["entity_object"]

    # ---------------------------------------------------------------------------------------------------------

    def _contains_this_date(self, row, this_date):
        return any(
            (cell["display_key"] == "Visit Date")
            and dates_utility.check_date(
                cell["entity_object"]["text"], self.retrieved_data.date_format
            )[const.US_DATE]
            == this_date
            for cell in row
        )

    # -----------------------------------------------------------------------------------------------------------------------

    def _is_row_contains_valid_date_cell(self, row: list):
        return any(
            (cell["display_key"] == "Visit Date")
            and cell["entity_object"]
            and dates_utility.check_date(
                cell["entity_object"]["text"], self.retrieved_data.date_format
            )[const.US_DATE]
            and dates_utility.check_date(
                cell["entity_object"]["text"], self.retrieved_data.date_format
            )[const.STATUS]
            for cell in row
        )

    # -----------------------------------------------------------------------------------------------------------------------

    def _sort_by_medical_codes(self, list_of_rows):
        diagnoses_sorted_list = []
        codes_present = lfilter(
            lambda diag_row: any(
                d["display_key"] == "SNOMED" and d["entity_object"] is not None
                for d in diag_row
            )
                             and any(
                d["display_key"] == "ICD-10" and d["entity_object"] is not None
                for d in diag_row
            ),
            list_of_rows,
        )
        codes_present = self._sort_rows_by_dates(codes_present)
        diagnoses_sorted_list.extend(codes_present)

        either_codes_present = lfilter(
            lambda diag_row: (
                                     any(
                                         d["display_key"] == "SNOMED"
                                         and d["entity_object"] is not None
                                         and d["entity_object"].get("confidence", 0) >= 78
                                         for d in diag_row
                                     )
                                     and not any(
                                 d["display_key"] == "ICD-10" and d["entity_object"] is not None
                                 for d in diag_row
                             )
                             )
                             or (
                                     not any(
                                         d["display_key"] == "SNOMED"
                                         and d["entity_object"] is not None
                                         and d["entity_object"].get("confidence", 0) >= 78
                                         for d in diag_row
                                     )
                                     and any(
                                 d["display_key"] == "ICD-10" and d["entity_object"] is not None
                                 for d in diag_row
                             )
                             ),
            list_of_rows,
        )
        codes_with_high_confidence = lfilter(
            lambda diag_row: any(
                d["display_key"] == "Diagnosis"
                and d["entity_object"].get("confidence", 0) >= 78
                for d in diag_row
            ),
            either_codes_present,
        )
        codes_with_high_confidence = self._sort_rows_by_dates(
            codes_with_high_confidence
        )
        diagnoses_sorted_list.extend(codes_with_high_confidence)

        diagnosis_with_low_confidence = list_minus(list_of_rows, diagnoses_sorted_list)
        diagnosis_with_low_confidence = self._sort_rows_by_dates(
            diagnosis_with_low_confidence
        )
        diagnoses_sorted_list.extend(diagnosis_with_low_confidence)

        return diagnoses_sorted_list

    # -----------------------------------------------------------------------------------------------------------------------

    def _sort_rows_by_dates(self, list_of_rows):
        date_format = self.retrieved_data.date_format
        all_visit_date_cells = lmap(
            lambda row: lfilter(lambda cell: cell["display_key"] == "Visit Date", row),
            list_of_rows,
        )

        all_visit_date_cells = list(chain(*all_visit_date_cells))
        list_of_non_empty_date_cells = list(
            filter(lambda cell: cell["entity_object"], all_visit_date_cells)
        )
        list_of_valid_date_cells = lfilter(
            lambda cell: dates_utility.check_date(
                cell["entity_object"]["text"], date_format
            )[const.STATUS],
            list_of_non_empty_date_cells,
        )
        list_of_dates = lmap(
            lambda cell: dates_utility.check_date(
                cell["entity_object"]["text"], date_format
            )[const.US_DATE],
            list_of_valid_date_cells,
        )
        list_of_dates = list(set(list_of_dates))
        sorted_dates, _ = dates_utility.sort_dates(list_of_dates, const.DESCENDING)
        valid_date_rows = lfilter(
            lambda row: self._is_row_contains_valid_date_cell(row), list_of_rows
        )
        sorted_rows = []
        for this_date in sorted_dates:
            this_list = lfilter(
                lambda row: self._contains_this_date(row, this_date), valid_date_rows
            )
            sorted_rows.extend(this_list)
        invalid_date_rows = lfilter(
            lambda row: not self._is_row_contains_valid_date_cell(row), list_of_rows
        )
        sorted_rows.extend(invalid_date_rows)
        return sorted_rows

    @staticmethod
    def filter_row_by_display_key(row, display_key):
        return lfilter(lambda cell: cell["display_key"] == display_key, row)

    # ------------------------------------------------------------------------------------------------------------------

    def _get_acceptable_pages(self, display_names=None):
        page_class_set = set()
        excluded_page_class_set = set()
        for priority in self.retrieved_data.priorities:
            if "diagnoses" in priority.field_id:
                if display_names is not None:
                    page_class_set.update(
                        self.misc.get_page_class_from_display_name(
                            priority.page_class_list, display_names
                        )
                    )
                    excluded_page_class_set.update(
                        self.misc.get_page_class_from_display_name(
                            priority.page_class_exclusion_list, display_names
                        )
                    )
                else:
                    page_class_set.update(priority.page_class_list)
                    excluded_page_class_set.update(priority.page_class_exclusion_list)
        return page_class_set, excluded_page_class_set

    # ------------------------------------------------------------------------------------------------------------------

    def _filter_pages_by_page_classifier(self, pages, page_classifiers):
        acceptable_pages, excluded_pages = self._get_acceptable_pages(
            self.retrieved_data.page_classification_display_name_mapping
        )
        if (
                len(page_classifiers) == 0
                or len(acceptable_pages) == 0
                or ("*" in acceptable_pages and len(excluded_pages) == 0)
        ):
            return pages
        new_pages = {}
        for doc_page, page_data in pages.items():
            for _role_type, list_objects in page_data.items():
                if len(list_objects):
                    cell = list_objects[0]
                    if self.misc.is_acceptable_by_page_classifier(
                            cell, page_classifiers, acceptable_pages, excluded_pages
                    ):
                        new_pages[doc_page] = page_data
                        break
        return new_pages

    # ------------------------------------------------------------------------------------------------------------------

    def _get_prev_candidates_by_page_class(
            self, candidates: list, file_id: str, page_num: int
    ) -> list:
        if len(candidates) == 0:
            return []
        last_page_by_page_class = page_num
        current_page_class = self.page_classifiers[file_id][last_page_by_page_class]
        while (
                last_page_by_page_class in self.page_classifiers[file_id]
                and self.page_classifiers[file_id][last_page_by_page_class]
                == current_page_class
        ):
            last_page_by_page_class -= 1

        filtered_children_candidates = lfilter(
            lambda child: child["abs_page"] > last_page_by_page_class
                          and child["abs_page"] < page_num,
            candidates,
        )

        return filtered_children_candidates

    # ------------------------------------------------------------------------------------------------------------------

    def _fill_previous_page_data(
            self,
            lab_rows: list,
            column_children_candidates: list,
            column_name: str,
            tag_order: list,
    ) -> None:
        if len(column_children_candidates) == 0:
            return
        for row in lab_rows:
            cell_detail = find(lambda cell: cell["display_key"] == column_name, row)
            if cell_detail.get("entity_object"):
                continue
            for tag in tag_order:
                filtered_children_candidates = lfilter(
                    lambda child: child["type"] == tag, column_children_candidates
                )
                if len(filtered_children_candidates) != 0:
                    bottom_child = max(
                        filtered_children_candidates,
                        key=lambda child: (child["abs_page"], child["bottom"]),
                    )
                    cell_detail["entity_object"] = bottom_child
                    break

    # ------------------------------------------------------------------------------------------------------------------

    def _extract_diag_rows(self, edoc: EDocument) -> List[List[Dict]]:
        selected_rows = []
        pages, date_tags = self._edoc_to_page_wise(edoc)
        page_classifiers = {
            file.file_id: {
                page.page_number: page.page_class for page in file.page_classes
            }
            for file in self.retrieved_data.file_page_classes
        }
        pages = self._filter_pages_by_page_classifier(pages, page_classifiers)
        for _doc_page_num, page_data in pages.items():
            if len(page_data["mother"]) == 0:
                continue
            list_of_vectors = self._populate_with_all_data(page_data)
            rows = self._fill_missing_dummy_cell(list_of_vectors)
            current_document_num, current_page_num = map(int, _doc_page_num.split("-"))
            if (
                    date_tags.get(current_document_num, None) is not None
                    and len(page_data.get("mother", [])) != 0
            ):
                file_id = get_first(page_data["mother"], {}).get("claimid", "")
                prev_encounter_date_candidates = (
                    self._get_prev_candidates_by_page_class(
                        date_tags[current_document_num], file_id, current_page_num
                    )
                )
                if prev_encounter_date_candidates:
                    self._fill_previous_page_data(
                        rows,
                        prev_encounter_date_candidates,
                        "Visit Date",
                        self.blueprint_data["visit_date_ners"],
                    )
            selected_rows.extend(rows)

        section_date_filter = ['sect.hpi', 'sect.assessment', 'sect.plan', 'sect.diagnosis']
        for row in selected_rows:
            diagnosis_object = find(lambda cell: cell["display_key"] == const.DIAGNOSIS, row)["entity_object"]

            section_name = '' if not diagnosis_object['section_info'] else diagnosis_object['section_info'].get(
                'section_name', '')
            if section_name not in section_date_filter:
                visit_date_section = find(
                    lambda cell: cell["display_key"] == const.VISIT_DATE, row
                )
                visit_date_section["entity_object"] = None
        selected_rows = self.misc.remove_duplicates_by_subtext(
            selected_rows, const.DIAGNOSIS, {"SNOMED": True, "ICD-10": True}
        )
        return selected_rows

    # ------------------------------------------------------------------------------------------------------------------

    def _add_icd_to_missing_diagnosis(self, list_of_rows, valid_diagnosis=None):
        for row in list_of_rows:
            if (
                    row[0]["entity_object"]
                    and row[0]["entity_object"]["text"] not in valid_diagnosis
            ):
                continue
            icd_value = valid_diagnosis[row[0]["entity_object"]["text"]].get("icd10")
            if not icd_value:
                continue
            self.medical_coding_service._assign_icd_code(
                row[0]["entity_object"]["text"], icd_value
            )
            icd_description = self.medical_coding_service._get_description_by_code(icd_value, "icd10")
            icd_entity_object = self._create_entity_object(
                base_entity=row[0]["entity_object"],
                entity_type=const.GENERATED_ENTITY + "icd10",
                text=icd_value,
                codified_as=icd_value,
                confidence=100,
                additional_metadata={
                    "card_generated": True,
                    "info": icd_description,
                    "type": "medical_code"
                }
            )
            icd_cell_object: Dict[str, Any] = self._find_cell_by_display_name(
                row, const.ICD10
            )
            icd_cell_object["entity_object"] = icd_entity_object

        return list_of_rows

    def _add_icd_to_missing_diagnosis_recommender(
            self, list_of_rows, recommender: Icd10Recommender = None
    ):
        if not recommender:
            return list_of_rows

        recommender.load_embedding_model_and_embeddings()

        batch_texts = []
        row_indices = []
        row_indices_to_remove = []
        for idx, row in enumerate(list_of_rows):
            if row[1]["entity_object"] is None and row[0].get("entity_object"):
                batch_texts.append(row[0]["entity_object"]["text"])
                row_indices.append(idx)
        if batch_texts:
            recommender.load_embedding_model_and_embeddings()

        icd10_results = (
            recommender.get_icd10_recommendation(batch_texts) if batch_texts else []
        )

        # Map predictions back to rows
        for idx, icd10 in zip(row_indices, icd10_results):
            row = list_of_rows[idx]

            # Validate prediction
            if not icd10.get("contextual_prediction"):
                row_indices_to_remove.append(idx)
                continue

            icd_value = icd10.get("icd10")
            icd_desc = icd10.get("sentence", "")
            confidence = icd10.get("confidence", 0)

            self.medical_coding_service._assign_icd_code(
                row[0]["entity_object"]["text"], icd_value, icd_desc
            )

            icd_entity_object = self._create_entity_object(
                base_entity=row[0]["entity_object"],
                entity_type=const.GENERATED_ENTITY + "icd10",
                text=icd_value,
                codified_as=icd_value,
                confidence=confidence,
                additional_metadata={
                    "card_generated": True,
                    "info": icd_desc,
                    "type": "medical_code",
                },
            )
            icd_cell: Dict[str, Any] = self._find_cell_by_display_name(
                row, const.ICD10
            )
            icd_cell['entity_object'] = icd_entity_object

            snomed_object: Dict[str, Any] = self._find_cell_by_display_name(
                row, "SNOMED"
            )

            if not snomed_object["entity_object"]:
                code_output: CodeOutput = self.medical_coding_service._get_medical_code(
                    icd_desc, "snomed_diagnosis"
                )
                snomed_value = code_output.medical_code
                snomed_score = code_output.score

                snomed_entity_object = self._create_entity_object(
                    base_entity=row[0]["entity_object"],

                    entity_type=const.GENERATED_ENTITY + "snomed",
                    text=snomed_value,
                    codified_as=snomed_value,
                    confidence=snomed_score,
                    additional_metadata={
                        "info": code_output.description,
                        "type": "medical_code",
                    },
                )
                snomed_object["entity_object"] = snomed_entity_object

            list_of_rows[idx] = row
        list_of_rows = [
            item
            for i, item in enumerate(list_of_rows)
            if i not in row_indices_to_remove
        ]
        return list_of_rows

    def _create_entity_object(
            self,
            base_entity,
            entity_type,
            text,
            codified_as,
            confidence=0,
            additional_metadata=None,
    ):

        entity = deepcopy(base_entity)
        entity["id"] = str(uuid.uuid4())
        entity["type"] = entity_type
        entity["text"] = text
        entity["codified_as"] = codified_as
        entity["confidence"] = confidence
        entity["codify"] = None
        entity["metadata"] = entity.get("metadata", {})
        if additional_metadata:
            entity["metadata"].update(additional_metadata)

        return entity

    def _filter_invalid_diagnosis_using_openai(self, list_of_rows):
        invalid_diagnosis_entities = []
        diag_list = [
            diag[0]["entity_object"]["text"]
            for diag in list_of_rows
            if diag[1]["entity_object"] is None
        ]
        if not diag_list:
            return list_of_rows, invalid_diagnosis_entities
        output_format = {
            "invalid_diagnosis": "list of invalid diagnosis",
            "valid_diagnosis": {
                "diagnosis name": {
                    "icd10": "icd10 code",
                    "description": "original description of the icd10 code",
                }
            },
        }
        input_prompt = f"""
            Given list of diagnosis {diag_list}, return json of the format {output_format}. Do not modify name of diagnosis provided.
        """

        try:
            start_time = time.time()

            print("Initializing OpenAI handler...")
            openai_handler = OpenAIRequestHandler()
            openai_handler.initialize()

            print("Sending request to OpenAI...")
            print(f"Total diagnosis sent :: {len(diag_list)}")
            response = openai_handler.get_openai_response(
                input_prompt, encoded_image_list=[]
            )

            if response:
                print("Processing response...")
                invalid_diagnosis: list = response.get("invalid_diagnosis", [])
                valid_diagnosis: dict = response.get("valid_diagnosis", {})

                invalid_diagnosis_entities = lfilter(
                    lambda diag_: diag_[0]["entity_object"]["text"]
                                  in invalid_diagnosis,
                    list_of_rows,
                )
                
                list_of_rows = lfilter(
                    lambda diag_: diag_[0]["entity_object"]["text"]
                                  not in invalid_diagnosis,
                    list_of_rows,
                )
                

                print("Updating missing diagnoses...")
                self._add_icd_to_missing_diagnosis(list_of_rows, valid_diagnosis)

            end_time = time.time()
            print(
                f"Total Time taken for OpenAI request: {end_time - start_time:.2f} seconds"
            )

        except Exception as e:
            print(f"An error occurred during OpenAI request: {e}")

        return list_of_rows, invalid_diagnosis_entities

    def _filter_invalid_diagnosis_using_icd10recommender(self, list_of_rows):
        recommender = Icd10Recommender(
            Model_Data.EMBEDDINGS_MODEL, Model_Data.ICD10_EMBEDDINGS
        )
        if self.medical_coding_service.shared_resources.semantic:
            recommender.embedding_model = (
                self.medical_coding_service.shared_resources.semantic
            )
        list_of_rows = self._add_icd_to_missing_diagnosis_recommender(
            list_of_rows, recommender
        )
        return list_of_rows

    def _insert_medical_codes(self, list_of_rows):
        for row in list_of_rows:
            icd10_cell: Dict[str, Any] = self._find_cell_by_display_name(
                row, const.ICD10
            )

            diagnosis_cell: Dict[str, Any] = self._find_cell_by_display_name(
                row, const.DIAGNOSIS
            )
            diagnosis_text: str = self._get_text_by_entity_object(diagnosis_cell)
            
            #dates
            visit_date_cell: Dict[str, Any] = self._find_cell_by_display_name(
                row, const.VISIT_DATE
            )
            visit_date_text: str = self._get_text_by_entity_object(visit_date_cell)
            
            diagnosis_date_cell: Dict[str, Any] = self._find_cell_by_display_name(
                row, const.DIAGNOSIS_DATE
            )
            diagnosis_date_text: str = self._get_text_by_entity_object(diagnosis_date_cell)
            dates_text = f"{visit_date_text}{diagnosis_date_text}"
            
            # existing icd10 from tag
            if diagnosis_text in self.tagged_icd10:
                icd10_cell["entity_object"] = self.tagged_icd10[diagnosis_text]

            if icd10_cell.get("entity_object"):
                icd10_text: str = icd10_cell.get("entity_object", {}).get("text", "")

                if dates_text:
                    self.dx_icds[icd10_text] = dates_text

                # icd chapter
                icd_chapter_cell: Dict[str, Any] = self._find_cell_by_display_name(
                    row, "ICD Chapter"
                )
                icd_chapter: Optional[str] = (
                    self.medical_coding_service.get_icd_chapter(icd10_text)
                )
                if icd_chapter and icd_chapter_cell:
                    icd_chapter_cell["entity_object"] = self._create_dx_cell(
                        deepcopy(diagnosis_cell), "icd_chapter", icd_chapter
                    )

                # icd section
                icd10_section_cell: Dict[str, Any] = self._find_cell_by_display_name(
                    row, "ICD Section"
                )
                icd_section: Optional[str] = self._get_icd_section(
                    diagnosis_text, icd10_text
                )
                if icd_section and icd10_section_cell:
                    icd10_section_cell["entity_object"] = self._create_dx_cell(
                        deepcopy(diagnosis_cell), "icd_section", icd_section
                    )

                # icd description
                icd_description_text: str = self.medical_coding_service._get_description_by_code(icd10_text, "icd10").lower()
                if icd_description_text:
                    icd_description_cell: Dict[str, Any] = self._find_cell_by_display_name(
                        row, "ICD-10 Description"
                    )
                    icd_description_cell["entity_object"] = self._create_dx_cell(
                        deepcopy(diagnosis_cell), "icd_description", icd_description_text
                    )
                    
                # condition
                diagnosis_condition_cell: Dict[str, Any] = self._find_cell_by_display_name(
                        row, "Condition"
                    )
                if icd10_text:
                    label = "Symptom" if icd10_text.startswith("R") else "Diagnosis"
                    diagnosis_condition_cell["entity_object"] = self._create_dx_cell(
                        deepcopy(diagnosis_cell), "diagnosis_condition", label
                    )

        return list_of_rows
    

    def _insert_impairment(self, impairment_dict, list_of_rows) -> dict:
        impairment_entities = {}
        if not impairment_dict:
            return impairment_entities

        for row in list_of_rows:
            diagnosis_cell: Dict[str, Any] = self._find_cell_by_display_name(
                row, const.DIAGNOSIS
            )
            diagnosis_text: str = self._get_text_by_entity_object(diagnosis_cell)
            impairment_section: str = self.icd10_sections.get(diagnosis_text, "")
            if impairment_section:
                impairment_name: str = impairment_dict[impairment_section].get(
                    "Diagnosis Impairment Category", None
                )
                impairment_cell: Dict[str, Any] = self._find_cell_by_display_name(
                    row, "Impairment"
                )
                impairment_entity = self._create_dx_cell(
                    deepcopy(diagnosis_cell), "impairment", impairment_name
                )
                impairment_cell["entity_object"] = impairment_entity
                if impairment_entity:
                    impairment_entities.setdefault((impairment_entity['claimid'], impairment_entity['abs_page']),
                                                   []).append(impairment_entity)

        return impairment_entities
    

    def _find_cell_by_display_name(
            self, row: List[Dict], display_name: str
    ) -> Optional[Dict]:
        return find(lambda schema: schema["display_key"] == display_name, row, {})

    def _get_text_by_entity_object(self, entity: Dict[str, Any]) -> str:
        if not entity.get("entity_object"):
            return ""
        return entity.get("entity_object", {}).get("text", "").lower()

    def _generate_impairment_by_openai(self, prompt_name: str = "clarice") -> dict[str, dict[str, dict[str, str]]]:

        response: dict[str, dict[str, dict[str, str]]] = {}
        grouped_icd10_sections = defaultdict(list)
        for key, value in self.icd10_sections.items():
            grouped_icd10_sections[value].append(key)

        # Check if prompt exists
        if prompt_name not in self.prompts:
            raise ValueError(f"Unknown prompt name: '{prompt_name}'. Available prompts: {list(self.prompts.keys())}")

        prompt_template = self.prompts[prompt_name]["prompt"]
        if not prompt_template:
            raise ValueError(f"Empty prompt template for prompt name: '{prompt_name}'")

        # Prepare all variables for all prompts
        input_output = {
            icd_section: {
                "Diagnosis Group Name": icd_section,
                "List of Diagnosis": ", ".join(diag_list),
                "Diagnosis Impairment Category": "",
            }
            for icd_section, diag_list in grouped_icd10_sections.items()
        }
        
        diagnoses_list = list(self.icd10_sections.keys())
        
        format_vars = {
            'input_output': json.dumps(input_output, indent=4),
            'diagnoses': json.dumps(diagnoses_list, indent=2),
            'impairments_list': json.dumps(const.IMPAIRMENTS, indent=2)
        }

        # Format the prompt
        formatted_prompt = prompt_template.format(**format_vars)
        try:
            start_time = time.time()

            print("Initializing OpenAI handler...")
            openai_handler = OpenAIRequestHandler()
            openai_handler.initialize()

            print("Sending request to OpenAI...")
            print(f"Total icd sections sent :: {len(grouped_icd10_sections)}")
            response = openai_handler.get_openai_response(
                formatted_prompt, encoded_image_list=[]
            )

            end_time = time.time()
            print(
                f"Total Time taken for OpenAI request: {end_time - start_time:.2f} seconds"
            )
        except Exception as e:
            print(f"An error occurred during OpenAI request: {e}")

        return response


    def build_diag_card(self, edoc: EDocument, ccda_data, failed_cards_list: List[str]):
        diagnosis_procedures = []
        try:
            self.card_name = Title.DIAGNOSIS
            self._prepare_blueprint_data()
            list_of_rows = self._extract_diag_rows(edoc)
            if self.retrieved_data.language == 'English':
                if Title.USE_OPEN_AI in self.retrieved_data.feature or os.getenv('FEATURE_USE_OPEN_AI'):
                    invalid_diagnosis_rows = []
                    list_of_rows, invalid_diagnosis_rows = self._filter_invalid_diagnosis_using_openai(list_of_rows)
                if Title.SEMANTIC_FILTER in self.retrieved_data.feature:
                    list_of_rows = self._filter_invalid_diagnosis_using_icd10recommender(
                        list_of_rows
                    )
                    invalid_diagnosis_rows = (
                        self._filter_invalid_diagnosis_using_icd10recommender(invalid_diagnosis_rows)
                    )
                    list_of_rows = list_of_rows + invalid_diagnosis_rows
            self._insert_medical_codes(list_of_rows)
            if Title.USE_OPEN_AI in self.retrieved_data.feature or os.getenv('FEATURE_USE_OPEN_AI'):
                # Get prompt name from environment variable or use default
                prompt_name = os.getenv('IMPAIRMENT_PROMPT_NAME', 'clarice')
                impairment_dict: Dict = self._generate_impairment_by_openai(prompt_name)
                impairment_entities = self._insert_impairment(impairment_dict, list_of_rows)
                if impairment_entities:
                    for page_of_lines in edoc:
                        first_entity = next((lst[0] for lst in page_of_lines if lst), None)
                        if not first_entity:
                            continue
                        imp_key = (first_entity['claimid'], first_entity['abs_page'])
                        if imp_key not in impairment_entities:
                            continue
                        for imp_entity in impairment_entities[imp_key]:
                            add_entity_to_page(imp_entity, page_of_lines)
            list_of_rows = self.remove_similar_codes_empty_children(list_of_rows)
            list_of_rows = self._deduplicate_diagnoses_by_icd_and_date(list_of_rows)
            list_of_rows = self._diagnosis_to_procedures(
                list_of_rows, diagnosis_procedures
            )

            list_of_rows = self._sort_by_medical_codes(list_of_rows)
            self.card_references.match_cell_codings(
                list_of_rows,
                ["SNOMED", "ICD-10"],
                "Diagnosis",
                "",
                ["Diagnosis Date", "Visit Date"],
            )
            ccda_rows = ccda_data.get(self.card_name, [])
            list_of_rows.extend(ccda_rows)
            card = self._diag_rows_to_table(list_of_rows)
            return card, diagnosis_procedures
        except Exception as e:
            print(f"ERROR: Diagnosis Card Creation Failed: {e}")
            failed_cards_list.append(
                {"card_name": "Diagnosis", "message": f"{traceback.format_exc()}"}
            )
            return None, diagnosis_procedures

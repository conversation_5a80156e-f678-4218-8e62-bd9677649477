#!/usr/bin/env python
# encoding: utf-8

# -------------  MODEL DATA INPUT FILE NAMES    --------------------------
import os

BASE_PATH = os.path.dirname(os.path.realpath(__file__))

FOLDER_NAME = "model_data"
FOLDER_PATH = os.path.join(BASE_PATH, FOLDER_NAME)
ACRONYMS = f"{FOLDER_PATH}/acronyms.json"
ALERTS_CONFIG = f"{FOLDER_PATH}/ia-dev.json"
ICD_DATA_2022 = f"{FOLDER_PATH}/icd_data_2022.json"
ICD_DATA = f"{FOLDER_PATH}/icd_data.json"
LOINC_DATA = f"{FOLDER_PATH}/loinc_data.json"
PILLS = f"{FOLDER_PATH}/pills.json"
RxNORM_DATA = f"{FOLDER_PATH}/new_rxnorm_to_medication.json"
UNWANTED_WORDS = f"{FOLDER_PATH}/unwanted_words.json"
DX_ICD = f"{FOLDER_PATH}/dx_icd.json"
RxNORM_DATA_TEST_GT = f"{FOLDER_PATH}/rx_norm_test_gt_dict.json"
SNOMED_DATA = f"{FOLDER_PATH}/snomed.json"
SNOMED_2023 = f"{FOLDER_PATH}/coding-systems/snomed_data2023.json"
MEDCAT =  f"{FOLDER_PATH}/coding-systems"
RX_CVX_DATA = f"{FOLDER_PATH}/cvx_data.json"
SNOMED_ACRONYMS = f"{FOLDER_PATH}/snomed_acronyms.json"
UMLS_DATA = f"{FOLDER_PATH}/umls.json"
ATC_DATA = f"{FOLDER_PATH}/atc.json"
ICD_SECTIONS = f"{FOLDER_PATH}/icd_sections.json"
EMBEDDINGS_MODEL = "./model_data/icd10_embedding/icd_10_sentence_transformer_128_dim_model"
SNOMED_EMBEDDINGS = "./model_data/icd10_embedding/snomed_embeddings.h5"
ICD10_EMBEDDINGS = "./model_data/icd10_embedding/icd10_embeddings.h5"
CPT_EMBEDDINGS = f"{FOLDER_PATH}/icd10_embedding/cpt_embeddings.h5"
CLASSIFICATIONS = f"{FOLDER_PATH}/classification_criteria.json"
COMPANY_NAIC_CODE_DATA = f"{FOLDER_PATH}/company_naic_codes.json"
# --------------------------------------------------------------------
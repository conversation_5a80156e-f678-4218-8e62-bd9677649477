#!/usr/bin/env python
# encoding: utf-8
# title as constants

# -----   card names   --------------------------------
SUMMARY = "Summary"
IMAGING_STUDY = 'Imaging Study'
FAMILY_HISTORY = 'Family History'
ABNORMAL_OBSERVATIONS = 'Abnormal Observations'
LABORATORY_RESULTS = 'Laboratory Results'
INSURED_DETAILS = "Insured Details"
TECHNICAL_DETAILS = 'Technical Details'
PHYSICAL_EXAMS = 'Physical Exams'
VITAL_SIGNS = 'Vital Signs'
CCDA_VITALS = 'CCDA Vitals'
DIAGNOSIS = 'Diagnoses'
CLAIMED_INJURY_DETAILS = 'Claimed Injury Details'
MEDICATION_CURRENT = 'Medication Current'
MEDICATION_HISTORY = 'Medication History'
MEDICATION_PLAN = 'Medication Plan'
MEDICATIONS = 'Medications'
LIST_OF_EVENTS = 'List of Events'
REASON_OF_VISIT = 'Reason of Visit'
OFFICE_VISITS = 'Office Visits'
CLAIM_DETAIL = 'Claim Details'
ENCOUNTER_DETAILS = 'Encounter Details'
MEDICAL_EQUIPMENT = 'Medical Equipment'
SOCIAL_HISTORY = 'Social History'
DIAGNOSTIC_PROCEDURES = 'Diagnostic Procedures'
ENCOUNTER_DX_DETAILS = 'Encounter Dx Details'
ALLERGENS = "Allergens"
IMMUNIZATIONS = "CCDA Immunizations"
DIABETES = 'Diabetes'
CANCER = 'Cancer'
CARDIOVASCULAR = 'Cardiovascular'
MENTAL_NERVOUS_DISORDER = 'Mental Nervous Disorder'
BUILD = 'Build'

DESCRIPTION = "Description"
PROVIDER_DETAILS = "Provider Details"
DENTAL_DETAILS = "Dental Details"
RX_DETAILS = "Rx Details"
CLAIMS = "Claims"
SUBJECTIVE_DETAILS = "Subjective Details"
APPLICATION_DETAILS = "Application Details"
INFORMALS = "Informals"
PROCEDURES = "Procedures"
TREATMENTS = "Treatments"

# -----   sub card names   -----------------------------
LAB_OBS_SUB_CARD_1 = 'All-Lab-Observations-1'
LAB_OBS_SUB_CARD_2 = 'All-Lab-Observations-2'
ABN_OBS_SUB_CARD_1 = 'Abnormal-Lab-Observations-1'
ABN_OBS_SUB_CARD_2 = 'Abnormal-Lab-Observations-2'

INSURED_1 = 'insured-1'
INSURED_2 = 'insured-2'
TECHNICAL_1 = 'technical-1'
TECHNICAL_2 = 'technical-2'
CLAIM_1 = 'claim-detail-1'
CLAIM_2 = 'claim-detail-2'

# ------- Features  ---------------------
DISABLE_SNOMED = 'disable_snomed'
DISABLE_LOINC = 'disable_loinc'
DISABLE_RX = 'disable_rx'
DISABLE_ATC = 'disable_atc'
DISABLE_ICD = 'disable_icd'
USER_DEFINED_CARDS = "user_defined_cards"
DISABLE_SECTION = 'disable_section'
DISABLE_IMMUNIZATIONS = 'disable_cvx'
ENABLE_MEDCAT = "enable_medcat"
DISABLE_FAMILY_TYPE = "disable_family_type"
ENABLE_IMPAIRMENT_CARDS = "enable_impairment_cards"
ENABLE_LAB_DATETIME_FORMAT = "lab_datetime_format"
UNDERWRITER_FROM_EMAIL = "underwriter_from_email"
USE_PATIENT_TAGS = "use_patient_tags"
USE_PRODUCT_NAME_FROM_TYPE = "use_product_name_from_type"
ENABLE_LAB_VALUE_INEQUALITIES = 'enable_lab_val_inequalities'
USE_OPEN_AI = 'use_open_ai'
SEMANTIC_FILTER = "semantic_filter"
SUMMARIZE_RDX = "summarize_rdx"
FOLLOWUP_SUMMARY = "followup_summary"
# ---------------------------------------
EMPTY_NAME = ''
PERFORMER = 'Performer'

# -------  list of all of the cards ----

ALL_CARDS_NAMES = [
    INSURED_DETAILS,
    TECHNICAL_DETAILS,
    ABNORMAL_OBSERVATIONS,
    LABORATORY_RESULTS,
    IMAGING_STUDY,
    FAMILY_HISTORY,
    PHYSICAL_EXAMS,
    VITAL_SIGNS,
    DIAGNOSIS,
    MEDICATION_HISTORY,
    MEDICATION_PLAN,
    MEDICATIONS,
    MEDICATION_CURRENT,
    LIST_OF_EVENTS,
    CLAIM_DETAIL,
    PROVIDER_DETAILS,
    DENTAL_DETAILS,
    RX_DETAILS,
    CLAIMS
]

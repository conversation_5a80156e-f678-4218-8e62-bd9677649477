from utils.utils import load_dot_env
load_dot_env()

import json
import os
import os.path
import shutil

from friendlylib import storage
from prettyprinter import cpprint

from app import ScoringService
from miscellaneous import MiscUtility

misc = MiscUtility()

# ----------------------------------------------------------------------------------

SS = ScoringService()


# ----------------------------------------------------------------------------------

def download_raw_data(output_folder: str, input_claim: dict):
    this_folder = f"../input_data/{output_folder}"
    shutil.rmtree(this_folder, ignore_errors=True)
    os.makedirs(this_folder, exist_ok=True)
    card_input_url = input_claim.get('card_input_url', {})
    input_data = storage.download_json(card_input_url)
    file_name = f"{this_folder}/input_file.json"
    with open(file_name, 'w') as f:
        json.dump(input_data, f, sort_keys=False, indent=4, separators=(',', ' :'))

    claims = input_data.get('claims', {})
    pages = ['flayout_output_pages', 'templated_pages']
    for claim in claims:
        for page_type in pages:
            # cpprint(page_type)
            for x in claim[page_type]:
                url = x['url']
                if url.endswith('.zjson'):
                    data = storage.download_zjson(url)
                elif url.endswith('.json'):
                    data = storage.download_zjson(url)
                else:
                    cpprint('unknow format. neither JSON nor zJSON')
                file_name = f"{this_folder}/{str(x['PageNo'])}.json"
                print(file_name)
                with open(file_name, 'w') as f:
                    json.dump(data, f, sort_keys=False, indent=4, separators=(',', ': '))


# ----------------------------------------------------------------------------------
true = True
false = False
null = None

def run(input_data):
    SS.local_testing = True
    SS.predict(input_data)

# portfolio:
t36 = {"cards_version":"V2","cards_json_url":"s3://ia-files/9c1a05d8-8641-4910-ba2c-a306292354a9/portfolio_9c1a05d8-8641-4910-ba2c-a306292354a9.json","medical_code_url":"s3://ia-files/9c1a05d8-8641-4910-ba2c-a306292354a9/medical_codes_9c1a05d8-8641-4910-ba2c-a306292354a9.json","cards_extraction_priority_dictionary_url":"s3://env-ia-dev/system-config/cards-extraction/system.json","page_classifier_display_name_mapping_url":"s3://env-ia-dev/system-config/ocr-api/document_type_mappings.json","is_cards_recall":true,"status":"success","debug":false,"filename":"ev#19_6001840_Blood_profile_Anonymized.pdf","filename_url":"s3://ia-files/9c1a05d8-8641-4910-ba2c-a306292354a9/20å9c1a05d8-8641-4910-ba2c-a306292354a9åev_19_6001840_Blood_profile_Anonymized.pdf","language":"English","document_type":"Underwriting","cards_validation_json":"s3://env-ia-dev/system-config/cards-validation/system.json","full_extraction_data":[{"ClaimFileId":"9c1a05d8-8641-4910-ba2c-a306292354a9","FileName":"ev#19_6001840_Blood_profile_Anonymized.pdf","FileNameUrl":"s3://ia-files/9c1a05d8-8641-4910-ba2c-a306292354a9/20å9c1a05d8-8641-4910-ba2c-a306292354a9åev_19_6001840_Blood_profile_Anonymized.pdf","JsonPath":"s3://ia-files/9c1a05d8-8641-4910-ba2c-a306292354a9/1/TesseractWordJson/annotatedJson_9c1a05d8-8641-4910-ba2c-a306292354a9.json","DocumentClaimId":"https://ia-dev.friendlyhealthtechnologies.com/claims-review/c25bfbef-319c-4c15-a19d-382da783e7c9/9c1a05d8-8641-4910-ba2c-a306292354a9/","templated_pages":[]},{"ClaimFileId":"0eb1ac29-b2cb-497b-85ab-066e99d23ac0","FileName":"ev#1_6001840_Application_Anonymized.pdf","FileNameUrl":"s3://ia-files/0eb1ac29-b2cb-497b-85ab-066e99d23ac0/20å0eb1ac29-b2cb-497b-85ab-066e99d23ac0åev_1_6001840_Application_Anonymized.pdf","JsonPath":"s3://ia-files/0eb1ac29-b2cb-497b-85ab-066e99d23ac0/1/TesseractWordJson/annotatedJson_0eb1ac29-b2cb-497b-85ab-066e99d23ac0.json","DocumentClaimId":"https://ia-dev.friendlyhealthtechnologies.com/claims-review/c25bfbef-319c-4c15-a19d-382da783e7c9/9c1a05d8-8641-4910-ba2c-a306292354a9/","templated_pages":[{"PageNo":1,"url":"s3://ia-files/0eb1ac29-b2cb-497b-85ab-066e99d23ac0/1/aligned/0eb1ac29-b2cb-497b-85ab-066e99d23ac0$1/tfm_output_Spellchecked.zjson","Classification":"proposal-form"},{"PageNo":2,"url":"s3://ia-files/0eb1ac29-b2cb-497b-85ab-066e99d23ac0/2/aligned/0eb1ac29-b2cb-497b-85ab-066e99d23ac0$2/tfm_output_Spellchecked.zjson","Classification":"proposal-form"},{"PageNo":3,"url":"s3://ia-files/0eb1ac29-b2cb-497b-85ab-066e99d23ac0/3/aligned/0eb1ac29-b2cb-497b-85ab-066e99d23ac0$3/tfm_output_Spellchecked.zjson","Classification":"proposal-form"},{"PageNo":4,"url":"s3://ia-files/0eb1ac29-b2cb-497b-85ab-066e99d23ac0/4/aligned/0eb1ac29-b2cb-497b-85ab-066e99d23ac0$4/tfm_output_Spellchecked.zjson","Classification":"proposal-form"},{"PageNo":5,"url":"s3://ia-files/0eb1ac29-b2cb-497b-85ab-066e99d23ac0/5/aligned/0eb1ac29-b2cb-497b-85ab-066e99d23ac0$5/tfm_output_Spellchecked.zjson","Classification":"proposal-form"},{"PageNo":6,"url":"s3://ia-files/0eb1ac29-b2cb-497b-85ab-066e99d23ac0/6/aligned/0eb1ac29-b2cb-497b-85ab-066e99d23ac0$6/tfm_output_Spellchecked.zjson","Classification":"proposal-form"},{"PageNo":7,"url":"s3://ia-files/0eb1ac29-b2cb-497b-85ab-066e99d23ac0/7/aligned/0eb1ac29-b2cb-497b-85ab-066e99d23ac0$7/tfm_output_Spellchecked.zjson","Classification":"proposal-form"},{"PageNo":25,"url":"s3://ia-files/0eb1ac29-b2cb-497b-85ab-066e99d23ac0/25/aligned/0eb1ac29-b2cb-497b-85ab-066e99d23ac0$25/tfm_output_Spellchecked.zjson","Classification":"proposal-form"},{"PageNo":33,"url":"s3://ia-files/0eb1ac29-b2cb-497b-85ab-066e99d23ac0/33/aligned/0eb1ac29-b2cb-497b-85ab-066e99d23ac0$33/tfm_output_Spellchecked.zjson","Classification":"proposal-form"}]},{"ClaimFileId":"2a540f1d-2dd6-4d78-bfcf-a1f721097d4a","FileName":"ev#38_6001840_APS_Anonymized.pdf","FileNameUrl":"s3://ia-files/2a540f1d-2dd6-4d78-bfcf-a1f721097d4a/20å2a540f1d-2dd6-4d78-bfcf-a1f721097d4aåev_38_6001840_APS_Anonymized.pdf","JsonPath":"s3://ia-files/2a540f1d-2dd6-4d78-bfcf-a1f721097d4a/1/TesseractWordJson/annotatedJson_2a540f1d-2dd6-4d78-bfcf-a1f721097d4a.json","DocumentClaimId":"https://ia-dev.friendlyhealthtechnologies.com/claims-review/c25bfbef-319c-4c15-a19d-382da783e7c9/9c1a05d8-8641-4910-ba2c-a306292354a9/","templated_pages":[{"PageNo":4,"url":"s3://ia-files/2a540f1d-2dd6-4d78-bfcf-a1f721097d4a/4/aligned/2a540f1d-2dd6-4d78-bfcf-a1f721097d4a$4/tfm_output_Spellchecked.zjson","Classification":"claim-others"}]},{"ClaimFileId":"bf2f2594-c831-46d8-a3ff-3718cc04e477","FileName":"ev#168_6001840_Coversheet_Anonymized.pdf","FileNameUrl":"s3://ia-files/bf2f2594-c831-46d8-a3ff-3718cc04e477/20åbf2f2594-c831-46d8-a3ff-3718cc04e477åev_168_6001840_Coversheet_Anonymized.pdf","JsonPath":"s3://ia-files/bf2f2594-c831-46d8-a3ff-3718cc04e477/1/TesseractWordJson/annotatedJson_bf2f2594-c831-46d8-a3ff-3718cc04e477.json","DocumentClaimId":"https://ia-dev.friendlyhealthtechnologies.com/claims-review/c25bfbef-319c-4c15-a19d-382da783e7c9/9c1a05d8-8641-4910-ba2c-a306292354a9/","templated_pages":[]}],"user_config":{"system_unit":"ENGLISH","date_format":"MM/DD/YYYY","input_date_format":"MM-DD-YYYY"},"userdefined_card_url":"s3://env-ia-dev/system-config/userdefinedcard.json","field_validations_url":"s3://env-ia-dev/system-config/common-settings/cards-document-type-validations.json","source":null,"entry_id":null,"recall_cards_list":null,"feature":"user_defined_cards,insured_details_2,application_details_3,disable_section,enable_lab_val_inequalities,conventional_naming_format,underwriter_from_email,semantic_filter,use_open_ai,enable_medcat,rdx_links"}

# cardsInput:
t5 = {"card_input_url":"s3://friendly-ner-files/3fbfa8f3-016c-48dd-8290-4b53614a53a4/cardInput_3fbfa8f3-016c-48dd-8290-4b53614a53a4.json","document_type":"annotations","cards_validation_json":"s3://unum-settings/cards-validation/ner.json","userdefined_card_url":"s3://unum-settings/userdefinedcard.json","field_validations_url":"s3://unum-settings/common-settings/cards-document-type-validations.json","feature":"user_defined_cards,insured_details_2,application_details_3,disable_section,enable_impairment_cards,tax_number_as_ssn,enable_medcat,use_open_ai,create_thyroid"}

t99 = {
    "card_input_url": "s3://ia-files/d9ba14d8-473e-40cb-b2f0-59ffe421a2d9/cardInput_d9ba14d8-473e-40cb-b2f0-59ffe421a2d9.json",
    "document_type": "Underwriting",
    "cards_validation_json": "s3://unum-settings/cards-validation/ia-dev.json"
}

claim = t99

run(claim)
# download_raw_data('raw', claim)

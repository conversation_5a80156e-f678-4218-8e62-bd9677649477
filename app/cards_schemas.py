import constants as const
import titles as Title
from blueprints import BLUEPRINTS

card_names = [Title.ENCOUNTER_DETAILS, Title.MEDICAL_EQUIPMENT, Title.PHYSICAL_EXAMS, Title.SOCIAL_HISTORY, Title.ENCOUNTER_DX_DETAILS,
              Title.DIAGNOSIS, Title.SUBJECTIVE_DETAILS, Title.CLAIMED_INJURY_DETAILS, Title.DIAGNOSTIC_PROCEDURES, Title.PROCEDURES, 
              Title.ALLERGENS, Title.IMMUNIZATIONS, Title.DIABETES, Title.CANCER, Title.CARDIOVASCULAR, Title.MENTAL_NERVOUS_DISORDER,
              Title.BUILD]

card_schema_entity_types = list(
    map(
        lambda card_nmame: list(map(
            lambda label: {"heading": label, "row_types": BLUEPRINTS[card_nmame][const.HEADERS][label] * 2},
            BLUEPRINTS[card_nmame][const.HEADERS]
        )), card_names
    ))

# from prettyprinter import cpprint
# cpprint(card_schema_entity_types[-1]); exit()

card_schemas = {

    Title.IMAGING_STUDY: {
        "entity_types": [
            {"heading": "Date", "row_types": ["imaging.date", "imaging.date"]},
            {"heading": "Exam", "row_types": ["imaging.exam", "imaging.exam"]},
            {"heading": "Finding", "row_types": ["imaging.finding", "imaging.finding"]},
            {"heading": "Impression", "row_types": ["imaging.impression", "imaging.impression"]},
            {"heading": "Reason", "row_types": ["reason", "reason"]}
        ]
    },

    Title.FAMILY_HISTORY: {
        "entity_types": [
            {"heading": "All", "row_types": ["family.all", "family.all", "age_diagnosed"]},
            {"heading": "Father", "row_types": ["family.father", "family.father", "age_diagnosed"]},
            {"heading": "Mother", "row_types": ["family.mother", "family.mother", "age_diagnosed"]},
            {"heading": "Sibling", "row_types": ["family.sibling", "family.sibling", "age_diagnosed"]}
        ]
    },

    Title.LABORATORY_RESULTS: {
        "entity_types": [
            {"heading": "Name", "row_types": ["o.name"]},
            {"heading": "Value", "row_types": ["o.value"]},
            {"heading": "Rating", "row_types": ["o.rating"]},
            {"heading": "Low", "row_types": ["o.ref_low"]},
            {"heading": "High", "row_types": ["o.ref_high"]},
            {"heading": "Descriptive Reference", "row_types": ["o.ref"]},
            {"heading": "Unit", "row_types": ["o.unit"]},
            {"heading": "Panel", "row_types": ["lab.panel"]},
            {"heading": "Date", "row_types": ["o.collected"]},
            {"heading": "Laboratory", "row_types": ["lab.performer"]},
            {"heading": "Specimen", "row_types": ["lab.specimen", "specimen"]},
            {"heading": "LOINC", "row_types": ["loinc"]},
            {"heading": "Fasting Status", "row_types": ['fasting.status.yes', 'fasting.in_hours', 'fasting.in_mins', 'fasting.date_time', 'merged_fasting']}
        ]
    },

    Title.ABNORMAL_OBSERVATIONS: {
        "entity_types": [
            {"heading": "Name", "row_types": ["o.name"]},
            {"heading": "Value", "row_types": ["o.value"]},
            {"heading": "Rating", "row_types": ["o.rating"]},
            {"heading": "Low", "row_types": ["o.ref_low"]},
            {"heading": "High", "row_types": ["o.ref_high"]},
            {"heading": "Descriptive Reference", "row_types": ["o.ref"]},
            {"heading": "Unit", "row_types": ["o.unit"]},
            {"heading": "Panel", "row_types": ["lab.panel"]},
            {"heading": "Date", "row_types": ["o.collected"]},
            {"heading": "Laboratory", "row_types": ["lab.performer"]},
            {"heading": "LOINC", "row_types": ["loinc"]},
            {"heading": "Fasting Status", "row_types": ['fasting.status.yes', 'fasting.in_hours', 'fasting.in_mins', 'fasting.date_time', 'merged_fasting']}
        ]
    },

    Title.VITAL_SIGNS: {
        "entity_types": [
            {"heading": "Date", "row_types": ["vital.date", "patient.date"]},
            {"heading": "Blood Pressure", "[types": ["vital.blood", "patient.blood"]},
            {"heading": "Weight", "row_types": ["vital.weight_lbs", "patient.weight_lbs"]},
            {"heading": "Height", "row_types": ["vital.height_in", "patient.height_in"]},
            {"heading": "BMI", "row_types": ["vital.bmi", 'patient.bmi']},
            {"heading": "Pulse", "row_types": ["vital.heart_rate", "patient.heart_rate"]},
            {"heading": "Oxygen", "row_types": ["vital.oxygen", "patient.oxygen"]},
            {"heading": "Respiration", "row_types": ["vital.respiration", "patient.respiration"]},
            {"heading": "Temperature", "row_types": ["vital.temperature_f", "patient.temperature_f"]}
        ]
    },


    Title.MEDICATIONS: {
        "entity_types": [
            {"heading": "Condition", "row_types": ["illness.condition"]},
            {"heading": "Medication", "row_types": ["current.medication", "hpi.medication_current", "history.medication", "medication.history", "plan.medication", "medication.name"]},
            {"heading": "Instruction", "row_types": ["medication.instruction"]},
            {"heading": "Duration", "row_types": ["medication.duration"]},
            {"heading": "Dosage", "row_types": ["medication.dosage", "medication.dosage.quantity"]},
            {"heading": "Unit", "row_types": ["medication.dosage.unit"]},
            {"heading": "Start", "row_types": ["medication.start"]},
            {"heading": "medication.end", "row_types": ["End"]},
            {"heading": "SNOMED", "row_types": ["snomed"]},
            {"heading": "Rx Code", "row_types": ["medication.rx_code"]}
        ]
    },

    Title.DIAGNOSIS: {
        "entity_types": card_schema_entity_types[card_names.index(Title.DIAGNOSIS)]
    },

    Title.ENCOUNTER_DX_DETAILS: {
        "entity_types": card_schema_entity_types[card_names.index(Title.ENCOUNTER_DX_DETAILS)]
    },

    Title.CLAIMED_INJURY_DETAILS: {
        "entity_types": card_schema_entity_types[card_names.index(Title.CLAIMED_INJURY_DETAILS)]
    },

    Title.REASON_OF_VISIT: {
        "entity_types": [
            {"heading": "Date", "row_types": ["encounter.date"]},
            {"heading": "Reason", "row_types": ["reason"]},
            {"heading": "Plan", "row_types": ["assessment.plan"]}
        ]
    },

    Title.ENCOUNTER_DETAILS: {
        "entity_types": card_schema_entity_types[card_names.index(Title.ENCOUNTER_DETAILS)]
    },

    Title.DIAGNOSTIC_PROCEDURES: {
        "entity_types": card_schema_entity_types[card_names.index(Title.DIAGNOSTIC_PROCEDURES)]
    },

    Title.MEDICAL_EQUIPMENT: {
        "entity_types": card_schema_entity_types[card_names.index(Title.MEDICAL_EQUIPMENT)]
    },

    Title.PHYSICAL_EXAMS: {
        "entity_types": card_schema_entity_types[card_names.index(Title.PHYSICAL_EXAMS)]
    },

    Title.DIABETES: {
        "entity_types": card_schema_entity_types[card_names.index(Title.DIABETES)]
    },

    Title.CANCER: {
        "entity_types": card_schema_entity_types[card_names.index(Title.CANCER)]
    },

    Title.CARDIOVASCULAR: {
        "entity_types": card_schema_entity_types[card_names.index(Title.CARDIOVASCULAR)]
    },

    Title.MENTAL_NERVOUS_DISORDER: {
        "entity_types": card_schema_entity_types[card_names.index(Title.MENTAL_NERVOUS_DISORDER)]
    },

    Title.BUILD: {
        "entity_types": card_schema_entity_types[card_names.index(Title.BUILD)]
    },

    Title.SUBJECTIVE_DETAILS: {
        "entity_types": card_schema_entity_types[card_names.index(Title.SUBJECTIVE_DETAILS)]
    },
    Title.PROCEDURES: {
        "entity_types": card_schema_entity_types[card_names.index(Title.PROCEDURES)]
    },
    Title.TREATMENTS: {
        "entity_types": card_schema_entity_types[card_names.index(Title.PROCEDURES)]
    },
    Title.ALLERGENS: {
        "entity_types": card_schema_entity_types[card_names.index(Title.ALLERGENS)]
    },
    Title.IMMUNIZATIONS: {
        "entity_types": card_schema_entity_types[card_names.index(Title.IMMUNIZATIONS)]
    },
    Title.SOCIAL_HISTORY: {
        "entity_types": card_schema_entity_types[card_names.index(Title.SOCIAL_HISTORY)]
    },
    Title.LIST_OF_EVENTS: {
        "entity_types": [
            {"heading": "Office Visit", "row_types": ["encounter.date", "vital.date", "encounter.performer"]},
            {"heading": "Laboratory Visit", "row_types": ["lab.collected", "lab.collected", "lab.performer"]},
            {"heading": "Surgery", "row_types": ["surgery.date", "surgery.date", "surgery.performer"]},
            {"heading": "Imagery", "row_types": ["imaging.date", "imaging.date", "imaging.performer", ]},
            {"heading": "Procedure", "row_types": ["procedure.date", "procedure.date", "procedure.performer"]},
            {"heading": "Pathology", "row_types": ["pathology.date", "pathology.date", "pathology.performer"]},
            {"heading": "Phystherapy", "row_types": ["phystherapy.date", "phystherapy.date", "phystherapy.perfomer"]}
        ]
    },

    Title.INSURED_DETAILS: {
        "entity_types": [
            {"heading": "Name", "row_types": ["patient.name", "patient.name"]},
            {"heading": "Date of birth", "row_types": ["patient.birth", "patient.birth"]},
            {"heading": "Birth Country", "row_types": ["patient.place_of_birth", "patient.place_of_birth"]},
            {"heading": "Gender", "row_types": ["patient.gender", "patient.gender"]},
            {"heading": "Address", "row_types": ["patient.address", "patient.address"]},
            {"heading": "Marital Status", "row_types": ["social.marital", "social.marital", "patient.marital", "patient.marital"]},
            {"heading": "Occupation", "row_types": ["social.occupation", "social.occupation", "patient.occupation", "patient.occupation"]},
            {"heading": "Occupation Duty", "row_types": ["patient.occupation.duty"]},
            {"heading": "Occupation Risk", "row_types": ["patient.risk"]},
            {"heading": "Weight", "row_types": ["insured_1.weight", "insured_1.weight"]},
            {"heading": "Height", "row_types": ["insured_1.height", "insured_1.height"]},
            {"heading": "Alcohol", "row_types": ["patient.alcohol.status.yes", "patient.alcohol.status.no", "patient.alcohol.status.former"]},
            {"heading": "Smoking", "row_types": ["patient.smoking.status.yes", "patient.smoking.status.no", "patient.smoking.status.former", "patient.smoking.status.passive"]},
            {"heading": "Drugs", "row_types": ["patient.drugs.status.yes", "patient.drugs.status.no", "patient.drugs.status.former"]},
            {"heading": "Avocation", "row_types": ["avocation.activity.risk_level.high", "avocation.activity.risk_level.medium", "avocation.activity.risk_level.low"]},
            {"heading": "Travel", "row_types": ["travel.international", "travel.domestic"]},
            {"heading": "Behavior", "row_types": ["lifestyle", "driving.record", "criminal.record"]},
            {"heading": "Beneficiary Relationship", "row_types": ["beneficiary.relationship_with_insured"]}
        ]
    },

    Title.TECHNICAL_DETAILS: {
        "entity_types": [
            {"heading": "Product Type", "row_types": ["product_type", "product_type"]},
            {"heading": "Annuity", "row_types": ["annuity", "annuity"]},
            {"heading": "Sum Insured", "row_types": ["sum_insured", "sum_insured"]},
            {"heading": "Duration Type", "row_types": ["duration.type", "duration.type"]},
            {"heading": "Duration", "row_types": ["duration", "policy.product.duration.in_years", "policy.product.duration.in_months", "policy.product.duration.end.age", "policy.product.duration.end.date"]},
            {"heading": "Currency", "row_types": ["currency", "currency"]},
            {"heading": "Plan Type", "row_types": ["plan.type", "plan.type"]}
        ]
    },

    Title.APPLICATION_DETAILS: {
        "entity_types": [
            {"heading": "Application Number", "row_types": ["application.number"]},
            {"heading": "Application Cedent Name", "row_types": ["company.name"]},
            {"heading": "Cedent Application ID", "row_types": ["policy_number"]}
        ]
    },

    Title.CLAIM_DETAIL: {
        "entity_types": [
            # {"heading": "Name", "row_types": ["patient.name"]},
            {"heading": "Policy Number", "row_types": ["policy_number"]},
            {"heading": "Claim Number", "row_types": ["claim_number"]},
            {"heading": "Policy Effective Date", "row_types": ["policy.effect_date"]},
            {"heading": "Claimed Date of Loss", "row_types": ["injury.date", "application.date"]},
            # {"heading": "Date of birth", "row_types": ["patient.birth"]},
            # {"heading": "Sum Insured", "row_types": ["sum_insured"]},
            # {"heading": "Annuity", "row_types": ["annuity"]},
            # {"heading": "Duration", "row_types": ["duration"]},
            # {"heading": "Currency", "row_types": ["currency"]},
            # {"heading": "Address", "row_types": ["insured_1.address"]},
            # {"heading": "Zip Code", "row_types": ["insured_1.zipcode"]},
            # {"heading": "Product Type", "row_types": ["product_type"]},
            # {"heading": "Occupation", "row_types": ["social.occupation"]},
            # {"heading": "Occupational Duties", "row_types": ["occupational.duties"]},
            {"heading": "Primary Diagnosis", "row_types": ["diagnosis.primary"]},
            {"heading": "Autopsy Performed", "row_types": ["autopsy.performed"]}
        ]
    },

    Title.PROVIDER_DETAILS: {
        "entity_types": [
            {"heading": "Doctor Name", "row_types": ["doctor.name"]},
            {"heading": "Doctor Registration Number", "row_types": ["doctor.license_number"]},
            {"heading": "Facility Name", "row_types": ["encounter.performer"]},
            {"heading": "Facility Address", "row_types": ["encounter.address"]}
        ]
    },

    Title.DENTAL_DETAILS: {
        "entity_types": [
            {"heading": "Type of Service", "row_types": ["type_of_service"]},
            {"heading": "Date of Service", "row_types": ["encounter.date"]},
            {"heading": "Tooth Code", "row_types": ["tooth_code"]},
            {"heading": "Tooth Surfaces", "row_types": ["tooth_surfaces"]},
            {"heading": "COB Amount", "row_types": ["cob.amount"]},
            {"heading": "Quantity", "row_types": ["quantity"]},
            {"heading": "Lab Charge", "row_types": ["lab_charge"]},
            {"heading": "Total Amount", "row_types": ["total_amount"]},
            {"heading": "Procedure Code", "row_types": ["procedure.code"]},
            {"heading": "Tax", "row_types": ["tax"]},
            {"heading": "Units", "row_types": ["units"]},
            {"heading": "Patient Name", "row_types": ["patient.name"]}
        ]
    },

    Title.RX_DETAILS: {
        "entity_types": [
            {"heading": "Type of Service", "row_types": ["type_of_service"]},
            {"heading": "Date of Service", "row_types": ["encounter.date"]},
            {"heading": "Fee", "row_types": ["rx.fee"]},
            {"heading": "Cost", "row_types": ["rx.cost"]},
            {"heading": "DIN Number", "row_types": ["rx.din"]},
            {"heading": "Quantity", "row_types": ["quantity"]},
            {"heading": "COB Amount", "row_types": ["cob.amount"]},
            {"heading": "Patient Name", "row_types": ["patient.name"]},
            {"heading": "Doctor Name", "row_types": ["doctor.name"]},
            {"heading": "Tax", "row_types": ["tax"]},
            {"heading": "Units", "row_types": ["units"]}
        ]
    },

    Title.CLAIMS: {
        "entity_types": [
            {"heading": "Type of Service", "row_types": ["type_of_service"]},
            {"heading": "Date of Service", "row_types": ["encounter.date"]},
            {"heading": "Quantity", "row_types": ["quantity"]},
            {"heading": "COB Amount", "row_types": ["cob.amount"]},
            {"heading": "Total Amount", "row_types": ["total_amount"]},
            {"heading": "Patient Name", "row_types": ["patient.name"]},
            {"heading": "Doctor Name", "row_types": ["doctor.name"]},
            {"heading": "Tax", "row_types": ["tax"]},
            {"heading": "Units", "row_types": ["units"]}
        ]
    }

}
horizontal_vertical_header_schemas = {
    Title.FAMILY_HISTORY : [
        {const.HEADERS: 'Mother', const.ENTITY_TYPE : ['family.mother']},
        {const.HEADERS: 'Father', const.ENTITY_TYPE : ['family.father']},
        {const.HEADERS: 'All', const.ENTITY_TYPE : ['family.all']},
        {const.HEADERS: 'Sibling', const.ENTITY_TYPE : ['family.sibling']}
    ],
    Title.SOCIAL_HISTORY : [
        {const.HEADERS : "Smoking",  
            const.ENTITY_TYPE : ['patient.smoking.status.yes', 'patient.smoking.status.no', 'patient.smoking.status.former', 'patient.smoking.status.passive', 
                    'insured_1.smoking.status.yes', 'insured_1.smoking.status.no', 'insured_1.smoking.status.former', 'insured_1.smoking.status.passive',
                    'insured_2.smoking.status.yes', 'insured_2.smoking.status.no', 'insured_2.smoking.status.former', 'insured_2.smoking.status.passive',
                    'applicant.smoking.status.yes', 'applicant.smoking.status.no', 'applicant.smoking.status.former', 'applicant.smoking.status.passive']
        },
        {const.HEADERS : "Smoking Device",
            const.ENTITY_TYPE : ['patient.smoking.device', 'insured_1.smoking.device', 'insured_2.smoking.device', 'applicant.smoking.device']},
        {const.HEADERS: "Tobacco Status",  
            const.ENTITY_TYPE : ['patient.tobacco.status.yes', 'patient.tobacco.status.no', 'patient.tobacco.status.former',
                    'insured_1.tobacco.status.yes', 'insured_1.tobacco.status.no', 'insured_1.tobacco.status.former',
                    'insured_2.tobacco.status.yes', 'insured_2.tobacco.status.no', 'insured_2.tobacco.status.former',
                    'applicant.tobacco.status.yes', 'applicant.tobacco.status.no', 'applicant.tobacco.status.former']},
        {const.HEADERS: "Tobacco", 
            const.ENTITY_TYPE : ['patient.tobacco.product', 'insured_1.tobacco.product', 'insured_2.tobacco.product', 'applicant.tobacco.product']},
        {const.HEADERS: "Tobacco Product",  
            const.ENTITY_TYPE : ['patient.tobacco.product', 'insured_1.tobacco.product', 'insured_2.tobacco.product', 'applicant.tobacco.product']},
        {const.HEADERS: "Alcohol",  
            const.ENTITY_TYPE : ['patient.alcohol.status.yes', 'patient.alcohol.status.no', 'patient.alcohol.status.former',
                    'insured_1.alcohol.status.yes', 'insured_1.alcohol.status.no', 'insured_1.alcohol.status.former',
                    'insured_2.alcohol.status.yes', 'insured_2.alcohol.status.no', 'insured_2.alcohol.status.former',
                    'applicant.alcohol.status.yes', 'applicant.alcohol.status.no', 'applicant.alcohol.status.former']},
        {const.HEADERS: "Alcohol Beverage",  
            const.ENTITY_TYPE : ['patient.alcohol.beverage', 'insured_1.alcohol.beverage', 'insured_2.alcohol.beverage', 'applicant.alcohol.beverage']},
        {const.HEADERS: "Drugs",  
            const.ENTITY_TYPE : ['patient.drugs.status.yes', 'patient.drugs.status.no', 'patient.drugs.status.former',
                    'insured_1.drugs.status.yes', 'insured_1.drugs.status.no', 'insured_1.drugs.status.former',
                    'insured_2.drugs.status.yes', 'insured_2.drugs.status.no', 'insured_2.drugs.status.former',
                    'applicant.drugs.status.yes', 'applicant.drugs.status.no', 'applicant.drugs.status.former',
                    'patient.drugs.product', 'insured_1.drugs.product', 'insured_2.drugs.product', 'applicant.drugs.product']},
        {const.HEADERS: "Drugs Product",  const.ENTITY_TYPE : ['patient.drugs.product', 'insured_1.drugs.product', 'insured_2.drugs.product', 'applicant.drugs.product']}
    ]
}
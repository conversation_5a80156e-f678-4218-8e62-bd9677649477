# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Overview

This is the **Cards** service - a medical document processing system that extracts structured data from medical documents using NER (Named Entity Recognition) and applies medical coding standards (SNOMED, ICD-10, LOINC, RxNorm).

## Key Commands

### Build and Run
```bash
# Build Docker image
make build

# Run locally for development
python app/app_flask.py

# Run tests locally
make run-test-local

# Run unit tests with coverage
make run-unit-tests

# Download model dependencies from S3
make download-build-dependencies
```

### Testing
```bash
# Run all tests
make run-test-local

# Run specific test file
python -m pytest app/tests/test_medications.py -v

# Run tests in Kubernetes environment
make run-test-k8s
```

### Linting and Type Checking

**IMPORTANT: When generating or modifying code, ALWAYS run both pyright and ruff to check for issues.**

```bash
# Run pyright for type checking
pyright app/

# Run ruff for linting
ruff check app/

# Auto-fix linting issues (but NOT PEP-8 formatting errors)
# This will fix non-formatting issues while preserving E and W errors
ruff check --fix app/ --extend-select=ALL --ignore=E,W
```

**Code Quality Requirements:**
- ALWAYS run `pyright` after generating or changing code to catch type errors
- ALWAYS run `ruff check` after generating or changing code to catch linting issues
- Fix ALL issues reported by pyright and ruff EXCEPT:
  - PEP-8 formatting errors (E codes like E501 for line length)
  - PEP-8 formatting warnings (W codes like W291 for trailing whitespace)
- These formatting issues should be flagged but NOT auto-fixed
- Only use `ruff check --fix` with `--ignore=E,W` to preserve formatting while fixing other issues

## Architecture

### Core Components

1. **Entry Points**
   - `app/app_flask.py` - REST API server
   - `app/app_sqs.py` - SQS queue worker
   - Both use `app/app.py` for core processing logic

2. **Data Flow**
   - Input: "Immediate JSON" containing document data
   - Processing: Extract entities, apply medical coding, build structured cards
   - Output: "Portfolio" JSON with words, entities, and cards

3. **Key Utilities**
   - `*_utility.py` files - Domain-specific extraction logic (medications, diagnoses, labs, etc.)
   - `card/card.py` - Card data structure and validation
   - `cloud/` - Data retrieval and format handling (V1/V2 recall formats)

4. **Medical Coding**
   - `medical_codes/` - Medical coding systems integration
   - `dx_to_icd.py` - ICD-10 mapping
   - `snomed_utility.py` - SNOMED-CT integration
   - `lab_loinc.py` - LOINC code mapping

### Important Conventions

1. **Terminology**
   - Use "Case" (not claim)
   - Use "File" (not document)
   - Use "Page" (not page of document)
   - Numbers start at 1, indices start at 0

2. **Card Schema**
   - All cards follow schema in `cards-schema-v2.json`
   - Cards must include proper entity references
   - Use `cards_schemas_v2_utils.py` for validation

3. **Error Handling**
   - Log errors but continue processing
   - Return partial results rather than failing completely
   - Include error details in response metadata

## Environment Configuration

The service uses configuration files in `config/`:
- `qconfig.dev.json` - Development environment
- `qconfig.qa.json` - QA environment
- `qconfig.prod.json` - Production environment

Key environment variables:
- `QCONFIG_FILE` - Path to configuration file
- `OPENAI_API_KEY` - Required for AI-based impairment mapping

## Development Workflow

1. Create feature branch: `git checkout -b username/JIRA-ID-description`
2. Make changes and test locally
3. Push to GitHub - triggers automatic DEV deployment
4. Create PR to master - auto-deploys to QA on merge
5. Deploy to PROD via Jenkins after QA approval

## Common Tasks

### Adding a New Card Type
1. Define the schema in `cards-schema-v2.json`
2. Create utility file `app/new_feature_utility.py`
3. Add extraction logic following existing patterns
4. Update `app/app.py` to call your utility
5. Add comprehensive unit tests

### Debugging Extraction Issues
1. Check the input immediate JSON structure
2. Verify entity extraction in the portfolio
3. Review utility function logic and filters
4. Check medical coding mappings
5. Validate output against schema

### Working with Medical Codes
- ICD-10: See `app/dx_to_icd.py` and `app/icd-files/`
- SNOMED: See `app/snomed_utility.py` and `app/snomed-files/`
- LOINC: See `app/lab_loinc.py` and `app/loinc-files/`
- RxNorm: See `app/medical_codes/rx_norm.py`
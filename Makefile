# settings specific for this project
DOCKER_IMAGE=cards
REGION=us-east-1
TESTSCRIPT=test.py
LOCALPORT=5280

include deployment/context.mk

K8S_TEST_SERVICE=test-cards
K8S_TEST_PORT=4020

K8S_CONTEXT_SHORT_TEST=friendly-prod-v2
K8S_NAMESPACE_TEST=test

K8S_CONTEXT_SHORT_DEV=friendly-prod-v2
K8S_NAMESPACE_DEV=dev

K8S_CONTEXT_SHORT_QA=friendly-prod-v2
K8S_NAMESPACE_QA=qa

K8S_CONTEXT_SHORT_PROD=friendly-prod-v2
K8S_NAMESPACE_PROD=prod

TEST_DOCKER_IMAGE=cards-unittest

K8S_CONTEXT_SHORT_DEV=friendly-prod-v2
K8S_NAMESPACE_DEV=dev

K8S_CONTEXT_SHORT_QA=friendly-prod-v2
K8S_NAMESPACE_QA=qa

K8S_CONTEXT_SHORT_PROD=friendly-prod-v2
K8S_NAMESPACE_PROD=prod

K8S_CONTEXT_TEST=--context=arn:aws:eks:$(REGION):************:cluster/$(K8S_CONTEXT_SHORT_TEST) --namespace=$(K8S_NAMESPACE_TEST)
K8S_CONTEXT_DEV=--context=arn:aws:eks:$(REGION):************:cluster/$(K8S_CONTEXT_SHORT_DEV) --namespace=$(K8S_NAMESPACE_DEV)
K8S_CONTEXT_QA=--context=arn:aws:eks:$(REGION):************:cluster/$(K8S_CONTEXT_SHORT_QA) --namespace=$(K8S_NAMESPACE_QA)
K8S_CONTEXT_PROD=--context=arn:aws:eks:$(REGION):************:cluster/$(K8S_CONTEXT_SHORT_PROD) --namespace=$(K8S_NAMESPACE_PROD)


# derived variables
ECR_ACCOUNT=************.dkr.ecr.$(REGION).amazonaws.com
ECR_NAME=$(DOCKER_IMAGE)

VERSION?=$(shell python3 model-common/make/tools/version.py show VERSION.json)

ECR_FULL_NAME=$(ECR_ACCOUNT)/$(ECR_NAME):$(VERSION)
ECR_FULL_NAME_PRTEST=$(ECR_ACCOUNT)/$(ECR_NAME):prtest

AZURE_CR?=friendlycr.azurecr.io
AZURE_FULL_NAME=$(AZURE_CR)/$(DOCKER_IMAGE):$(VERSION)

# other settings
#USE_GPU=--runtime=nvidia

ifeq ($(OS),Windows_NT)
AUTH_MAPPING=-e HOME=/home/<USER>/.aws:/home/<USER>/.aws
else
AUTH_MAPPING=-e HOME=/home/<USER>/.aws:/home/<USER>/.aws
endif

ONPREM_ENV=-e USE_CLOUDWATCH=FALSE \
           -v $(shell pwd)/test/files:/files
ONPREMPORT=6080

default: usage

LOCAL_TOOLS_DIR=tools
HELM_VALUES=helm/values/friendly
HELM_CHART=helm/cards
HELM_DEPLOYMENT=cards

ENTITY_MAPPING_ARTIFACT=s3://bulk-test/cards/field_id_entity_id_mapping

generate-config:
	python3 model-common/friendlylib/friendlylib/id.py app/ep_config.json Cards

include model-common/make/common.mk


run-unit-tests: ## run the unit tests in docker image
	docker build . -f test.Dockerfile -t $(TEST_DOCKER_IMAGE)
	docker run --rm -v $(PWD)/coverage:/app/coverage -t $(TEST_DOCKER_IMAGE) /bin/sh -c "\
		coverage run -m unittest discover && \
		coverage report -i && \
		coverage json -i && \
		jq -r '.totals.percent_covered_display' coverage.json > /app/coverage/coverage.txt"


run-unit-tests-local: ## run the unit tests in docker image
	docker build . -f test.Dockerfile -t $(TEST_DOCKER_IMAGE)
	docker run -t $(AUTH_MAPPING) $(TEST_DOCKER_IMAGE) \
		python3 -m unittest

download-build-dependencies:
	aws s3 sync s3://modeldata-us-east-1/models/cards/coding-systems/ app/model_data/coding-systems
	aws s3 sync s3://modeldata-us-east-1/models/cards/icd10_embedding/ app/model_data/icd10_embedding
update-entity-mappings:
	docker run -t --entrypoint /usr/bin/python3 $(AUTH_MAPPING) $(DOCKER_IMAGE) update_system_entity_mapping.py $(ENTITY_MAPPING_ARTIFACT)_$(BUILD_ENV).json

update-artifacts:
	make build
	make update-entity-mappings

update-artifacts-dev:
	BUILD_ENV=dev make update-artifacts

update-artifacts-qa:
	BUILD_ENV=qa make update-artifacts

update-artifacts-prod:
	BUILD_ENV=prod make update-artifacts

###############################################################################
# PR Test 
start-k8s-test:
	make update-kubeconfig-dev
	# python3 $(TOOLS_DIR)/update-image.py deployment/test/deployment.yaml "$(ECR_FULL_NAME_PRTEST)"
	# kubectl $(K8S_CONTEXT_DEV) apply -f deployment/test/deployment.yaml
	helm upgrade --install --kube-context $(CLUSTER_NAME) \
	    --namespace $(K8S_NAMESPACE_TEST) \
	    --values $(HELM_VALUES)/values-test.yaml \
	    --set image.tag=prtest \
	    $(K8S_TEST_SERVICE) $(HELM_CHART)

test-k8s-test:
	sh $(LOCAL_TOOLS_DIR)/do_k8s_test_pf_v2.sh \
		$(REGION) $(K8S_CONTEXT_SHORT_TEST) $(K8S_NAMESPACE_TEST) \
		app.kubernetes.io/instance=$(K8S_TEST_SERVICE) \
		$(TESTSCRIPT) $(K8S_TEST_PORT)

stop-k8s-test:
	# kubectl $(K8S_CONTEXT_DEV) delete -f deployment/test/deployment.yaml
	helm delete --kube-context $(CLUSTER_NAME) \
	    --namespace $(K8S_NAMESPACE_TEST) \
	    $(K8S_TEST_SERVICE)

run-test-k8s:
	make start-k8s-test
	sleep 5s
	sh  $(LOCAL_TOOLS_DIR)/wait_pod_ready_v2.sh \
		"$(K8S_CONTEXT_TEST)" \
		app.kubernetes.io/instance=$(K8S_TEST_SERVICE)
	sleep 80s
	make test-k8s-test
	make stop-k8s-test

logs-k8s-test:
	sh $(LOCAL_TOOLS_DIR)/k8s_test_logs_v3.sh \
		$(REGION) \
		$(K8S_CONTEXT_SHORT_TEST) \
		$(K8S_NAMESPACE_TEST) \
		app.kubernetes.io/instance=$(K8S_TEST_SERVICE)


###############################################################################
# DEV deployment
k8s-deploy-dev:
	make update-kubeconfig-dev
	# python3 $(TOOLS_DIR)/update-image.py deployment/dev/deployment.yaml "$(ECR_ACCOUNT)/$(ECR_NAME):$(VERSION)"
	# kubectl $(K8S_CONTEXT_DEV) apply -f deployment/dev/deployment.yaml
	helm upgrade --install --kube-context $(CLUSTER_NAME) \
	    --namespace $(K8S_NAMESPACE_DEV) \
	    --values $(HELM_VALUES)/values-dev.yaml \
	    --set image.tag=$(VERSION) \
	    $(HELM_DEPLOYMENT) $(HELM_CHART)
	IMAGE_TAG=$(VERSION) NAMESPACE=$(K8S_NAMESPACE_DEV) make mark-deployment-image


###############################################################################
# QA deployment
k8s-deploy-qa:
	make create-queue-qa
	make update-kubeconfig-qa
	# python3 $(TOOLS_DIR)/update-image.py deployment/qa/deployment.yaml "$(ECR_ACCOUNT)/$(ECR_NAME):$(VERSION)qa"
	# kubectl $(K8S_CONTEXT_QA) apply -f deployment/qa/deployment.yaml
	helm upgrade --install --kube-context $(CLUSTER_NAME) \
	    --namespace $(K8S_NAMESPACE_QA) \
	    --values $(HELM_VALUES)/values-qa.yaml \
	    --set image.tag=$(VERSION)qa \
	    $(HELM_DEPLOYMENT) $(HELM_CHART)
	IMAGE_TAG=$(VERSION)qa NAMESPACE=$(K8S_NAMESPACE_QA) make mark-deployment-image


###############################################################################
# PROD deployment
k8s-deploy-prod:
	make update-kubeconfig-prod
	# python3 $(TOOLS_DIR)/update-image.py deployment/prod/deployment.yaml "$(ECR_ACCOUNT)/$(ECR_NAME):$(VERSION)p"
	# kubectl $(K8S_CONTEXT_PROD) apply -f deployment/prod/deployment.yaml
	helm upgrade --install --kube-context $(CLUSTER_NAME) \
	    --namespace $(K8S_NAMESPACE_PROD) \
	    --values $(HELM_VALUES)/values-prod.yaml \
	    --set image.tag=$(VERSION)p \
	    $(HELM_DEPLOYMENT) $(HELM_CHART)
	IMAGE_TAG=$(VERSION)p NAMESPACE=$(K8S_NAMESPACE_PROD) make mark-deployment-image



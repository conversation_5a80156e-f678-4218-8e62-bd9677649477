# Cards

Cards creates associations between NER (Named Entity Recognition) objects
from the LayoutLM/LayoutXLM model.


## Build and test locally
These are fairly minimal tests. More tests (and unit tests) would be better.

First build the docker image (Linux/WSL)
```
make build
```

Run the tests (Linux/WSL)
```
make run-test-local
```


## Deployment
Description of the deployment process is here: https://friendlyhealth.atlassian.net/wiki/spaces/UNUM2018/pages/**********/Model+CI+CD+Process

Cards follows a typical CI/CD pipeline with the following stages: DEV -> QA -> PROD.


### Deployment to DEV
Deploying to dev:
1. Create a new git branch (`git checkout -b <username>/<short-description>`)
2. Make some changes. Test locally. Commit changes locally (`git add .`, `git commit`)
3. Push changes to github (`git push origin HEAD`)
4. Run the deploy to dev Jenkins job on that branch (https://jenkins-linux.friendlyhealthtechnologies.com:8487/view/cards/job/cards-branch-dev/)

### Deployment to QA
1. Create a PR for your branch. Get it reviewed. Fix any issues. Make sure tests pass.
2. When is <PERSON><PERSON><PERSON> to "Merge to Master" (button on the PR). Then merge. QA deployment will happen automatically.

### Deployment to PROD
1. More QA testing. More accuracy and system testing. Get sign-off to deploy to PROD.
2. Go to the Jenkins job to deploy to PROD and run it (https://jenkins-linux.friendlyhealthtechnologies.com:8487/view/cards/job/cards-deploy-prod/)


## Service Logins

### OpenAI secrets

To use OpenAI locally:

```
az login
```

### AWS access

```
To get to things like s3 locally (e.g., for loading a case):

make ecr-login
```


# Naming conventions
### hyperlink
A dict that has a text and a url {"text": "", "url": ""}

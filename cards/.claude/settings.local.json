{"permissions": {"allow": ["Bash(aws sts get-caller-identity:*)", "Bash(AWS_PROFILE=friendly make -C .. ecr-login)", "Bash(find:*)", "Bash(ls:*)", "Bash(grep:*)", "<PERSON><PERSON>(source:*)", "<PERSON><PERSON>(python:*)", "Bash(pip install:*)", "Bash(PYTHONPATH=/Users/<USER>/Source/friendly/cards/app:$PYTHONPATH python -m pytest ../app/tests/test_diagnosis_impairment_mapping.py::TestDiagnosisImpairmentMapping::test_free_form_mapping_without_constraints -v)", "Bash(git checkout:*)", "Bash(PYTHONPATH=/Users/<USER>/Source/friendly/cards/app:$PYTHONPATH python -m pytest ../app/tests/test_diagnosis_impairment_mapping.py::TestDiagnosisImpairmentMapping::test_free_form_mapping_without_constraints -v -s)", "Bash(PYTHONPATH=/Users/<USER>/Source/friendly/cards/app:$PYTHONPATH python -m pytest ../app/tests/test_diagnosis_impairment_mapping.py::TestDiagnosisImpairmentMapping::test_free_form_mapping_without_constraints -v -s 2 >& 1)", "Bash(PYTHONPATH=/Users/<USER>/Source/friendly/cards/app:$PYTHONPATH python -m pytest ../app/tests/test_diagnosis_impairment_mapping.py::TestDiagnosisImpairmentMapping::test_constrained_mapping_with_icd_categories -v -s)", "Bash(pyright:*)", "Bash(rm:*)"], "deny": []}, "enableAllProjectMcpServers": false}
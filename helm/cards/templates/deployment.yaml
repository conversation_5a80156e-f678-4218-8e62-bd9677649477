apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "cards.fullname" . }}
  labels:
    app: cards
  {{- include "cards.labels" . | nindent 4 }}
spec:
  replicas: {{ .Values.replicaCount }}
  selector:
    matchLabels:
      app: cards
    {{- include "cards.selectorLabels" . | nindent 6 }}
  template:
    metadata:
      labels:
        app: cards
      {{- include "cards.selectorLabels" . | nindent 8 }}
    spec:
      automountServiceAccountToken: false
      containers:
      - name: cards
        image: "{{ .Values.image.repository }}:{{ .Values.image.tag | default .Chart.AppVersion }}"
        imagePullPolicy: {{ .Values.image.pullPolicy }}
        {{- with .Values.args }}
        args:
          {{- toYaml . | nindent 10 }}
        {{- end }}
        env:
        - name: ENVIRONMENT
          value: {{ .Values.deploymentEnvironment }}
        - name: MY_POD_NAME
          valueFrom:
            fieldRef:
              fieldPath: metadata.name
        - name: PYTHONUNBUFFERED
          value: "1"
        - name: QCONFIG_FILE
          value: "/config/qconfig.json"
        {{- if .Values.statsdMetrics.enabled }}
        - name: METRICS_PREFIX
          value: {{ quote .Values.statsdMetrics.metricsPrefix }}
        - name: METRICS_CONTEXT
          value: {{ quote .Values.statsdMetrics.metricsContext }}
        - name: STATSD_URL
          value: {{ quote .Values.statsdMetrics.statsdUrl }}
        {{- end }}
        - name: MODEL_STORAGE_DATA
          value: "/model-data"
        - name: FRIENDLY_STORAGE_CACHE_PATH
          value: "/model-data/.friendly/storage-cache/"
        - name: FRIENDLY_STORAGE_CACHE_ENABLED
          value: "{{ .Values.internalFileCache.enabled }}"
        - name: PAGE_DOWNLOAD_THREADS
          value: "{{ .Values.envVars.pageDownloadThreads }}"
        - name: GOOGLE_PROJECT_ID
          value: "{{ .Values.gemini_config.googleProjectID }}"
        - name: GOOGLE_PROJECT_LOCATION
          value: "{{ .Values.gemini_config.googleProjectLocation }}"
        - name: GOOGLE_SERVICE_ACCOUNT
          value: /app/secrets/google/google-cloud-service-account.json
        - name: OPEN_AI_ENDPOINT
          value: {{ quote .Values.openaiconfig.endpoint }}
        - name: OPEN_AI_DEPLOYMENT_NAME
          value: {{ quote .Values.openaiconfig.deployment_name }}
        - name: OPEN_AI_API_VERSION
          value: {{ quote .Values.openaiconfig.api_version }}
        - name: OPEN_AI_TIMEOUT
          value: {{ quote .Values.openaiconfig.timeout }}
        - name: AZURE_CLIENT_ID
          valueFrom:
            secretKeyRef:
              name: azure-openai
              key: CLIENT_ID
        - name: AZURE_TENANT_ID
          valueFrom:
            secretKeyRef:
              name: azure-openai
              key: TENANT_ID
        - name: AZURE_CLIENT_SECRET
          valueFrom:
            secretKeyRef:
              name: azure-openai
              key: CLIENT_SECRET
        {{- if eq .Values.storage.type "adl2" }}
        - name: AZURE_DATALAKE_ACCOUNTNAME
          valueFrom:
            secretKeyRef:
              key: DATALAKE_ACCOUNT
              name: azure-ad-access
        - name: AZURE_AD_CLIENT
          valueFrom:
            secretKeyRef:
              key: CLIENT_ID
              name: azure-ad-access
        - name: AZURE_AD_SECRET
          valueFrom:
            secretKeyRef:
              key: CLIENT_SECRET
              name: azure-ad-access
        - name: AZURE_AD_TENANT
          valueFrom:
            secretKeyRef:
              key: TENANT_ID
              name: azure-ad-access
        {{- end }}
        {{- with .Values.env }}
        {{- toYaml . | nindent 8 }}
        {{- end }}
        resources: {{- toYaml .Values.resources | nindent 10 }}
        securityContext:
          {{- toYaml .Values.securityContext | nindent 10 }}
        volumeMounts:
        - name: config-volume
          mountPath: /config
        - mountPath: /model-data
          name: model-data
        - mountPath: /tmp
          name: tmp
        - name: google-credentials
          mountPath: /app/secrets/google/google-cloud-service-account.json
          subPath: google-cloud-service-account.json
          readOnly: true
        {{- with .Values.volumeMounts }}
          {{- toYaml . | nindent 10 }}
        {{- end }}
      securityContext:
        {{- toYaml .Values.podSecurityContext | nindent 8 }}
      terminationGracePeriodSeconds: 60
      volumes:
      - name: config-volume
        configMap:
          name: {{ include "cards.fullname" . }}-qconfig
      - emptyDir: {}
        name: model-data
      - emptyDir: {}
        name: tmp
      - name: google-credentials
        secret:
          secretName: google-cloud-credentials
      {{- with .Values.volumes }}
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}

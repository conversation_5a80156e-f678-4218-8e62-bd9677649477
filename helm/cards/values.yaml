kubernetesClusterDomain: cluster.local

queueconfig:
  type: sqsdynamodb
  sqs:
    region: us-east-1
    visibilityTimeout: 15000
    waitTime: 20
    messageRetentionPeriod: 14400
  dynamodb:
    useLocalUrl: false
    url: https://dynamodb.us-east-1.amazonaws.com/
    region: us-east-1
    table: WorkItems
  workservice:
    url: http://workservice:4000/cards.fifo
  starveTime: 30
  sleepTime: 2


replicaCount: 0

deploymentEnvironment: test

openaiconfig:
  endpoint: https://friendly-openai-experiments.openai.azure.com/
  deployment_name: friendly-openai-experiments-gpt4o
  api_version: 2023-03-15-preview
  timeout: 300

gemini_config:
  googleProjectID: "661873660099"
  googleProjectLocation: "us-central1"

image:
  repository: cards
  pullPolicy: IfNotPresent
  # Overrides the image tag whose default is the chart appVersion.
  tag: ""

statsdMetrics:
  enabled: false
  statsdUrl: statsd-exporter.monitoring
  metricsPrefix: model
  metricsContext: env=unknown,ns=unknown,dep=cards

internalFileCache:
  enabled: false

resources:
  limits:
    cpu: "5"
    memory: "10Gi"
    ephemeral-storage: "500Mi"
  requests:
    cpu: "1"
    memory: "500Mi"
    ephemeral-storage: "500Mi"


podSecurityContext:
  fsGroup: 10000
  runAsGroup: 10000
  runAsNonRoot: true
  runAsUser: 10000

securityContext:
  allowPrivilegeEscalation: false
  capabilities:
    drop:
    - all
  readOnlyRootFilesystem: true

envVars:
  pageDownloadThreads: 7

# Additional volumes on the output Deployment definition.
volumes: []
# - name: foo
#   secret:
#     secretName: mysecret
#     optional: false

# Additional volumeMounts on the output Deployment definition.
volumeMounts: []
# - name: foo
#   mountPath: "/etc/foo"
#   readOnly: true

nodeSelector: {}

tolerations: []

affinity: {}

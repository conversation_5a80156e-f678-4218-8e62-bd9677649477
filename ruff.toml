target-version = "py310"

# https://beta.ruff.rs/docs/rules/ for explanations!

line-length = 120

exclude = [
        "build",
]

[lint]
##################
# Check whitelist:
##################
select = [
    "B",      # flake8-bugbear
# TODO: put back!     "C4",     # flake8-comprehensions
    "E",      # pycodestyle (PEP8) 'errors'
    "F",      # Pyflakes
# TODO: put back!     "G",      # flake8-logging-format
    "ISC",    # flake8-implicit-str-concat
# TODO: put back!     "N",      # pep8-naming
    "NPY",    # modern numpy
# TODO: put back!     "RUF",    # Ruff-specific rules
    "RUF008", # Do not use mutable default values for dataclass attributes
    "RUF009", # Do not perform function call {name} in dataclass defaults
    "RUF012", # Mutable class attributes should be annotated with typing.ClassVar
    "RUF013", # PEP 484 prohibits implicit Optional
    "RUF100", # Unused noqa directive
    "RUF200", # Failed to parse ruff.toml: {}
# TODO: put back!     "SIM",    # flake8-simplify; onxx uses SIM but PyTorch uses SIM1
# TODO: put back!     "TID252", # Relative imports
# TODO: put back!     "UP",     # pyupgrade
    "W",      # pycodestyle (PEP8) 'warnings'
    "YTT",    # flake8-2020
    # Not included in flake8
    "PLE",
    "TRY203",
]

##################
# Check blacklist:
##################
ignore = [

#   "B007", # Loop control variable {name} not used within loop body
#   "B008", # Do not perform function call {name} in argument defaults
#   "B017", # {assertion}({exception}) should be considered evil
#   "B018", # Found useless expression. Either assign it to a variable or remove it.
#   "B019", # Use of functools.lru_cache or functools.cache on methods can lead to memory leaks
#   "B020", # Loop control variable {name} overrides iterable it iterates
#   "B023", # Function definition does not bind loop variable {name}
#   "B024", # {name} is an abstract base class, but it has no abstract methods
#   "B026", # Star-arg unpacking after a keyword argument is strongly discouraged
#   "B028", # No explicit `stacklevel` keyword argument found
#   "B904", # Within an except clause, raise exceptions with raise ... from err or raise ... from None to distinguish them from errors in exception handling
#   "B905", # zip() without an explicit strict= parameter
#   "C408", # Unnecessary {obj_type} call (rewrite as a literal)
#   "E402", # Module level import not at top of file
#   "E501", # Line too long ({width} > {limit} characters)
#   "E721", # Do not compare types, use isinstance()
#   "E731", # Do not assign a lambda expression, use a def
#   "E741", # Ambiguous variable name: {name}
#   "EXE001", # Shebang is present but file is not executable
#   "F405", # {name} may be undefined, or defined from star imports:
#   "F821", # Undefined name {name}
#   "F841", # Local variable {name} is assigned to but never used
#   "G101", # Logging statement uses an extra field that clashes with a LogRecord field: {key}
#   "G201", # Logging .exception(...) should be used instead of .error(..., exc_info=True)
#   "G202", # Logging statement has redundant exc_info

#   "SIM102", # Use a single if-statement instead of nested if-statements
#   "SIM103", # Return the boolean condition directly
    "SIM105", # Use 'contextlib.suppress(...)' instead of try-except-pass
    "SIM108", # Use the ternary operator if it's reasonable
    "SIM110", # Use any(...)
    "SIM112", # Use CAPITAL environment variables
#   "SIM114", # Combine `if` branches using logical `or` operator
#   "SIM115", # Use context handler for opening files
    "SIM116", # Use a dictionary instead of consecutive `if` statements
    "SIM117", # Merge with-statements that use the same scope
#   "SIM118", # Use 'key in dict' instead of 'key in dict.keys()

    # naming conventions ignored by onnx:
    #    "N803", # Argument casing
    #    "N806", # Relax: Variable name in function should be lowercase
    #    "N999", # Module names
]
